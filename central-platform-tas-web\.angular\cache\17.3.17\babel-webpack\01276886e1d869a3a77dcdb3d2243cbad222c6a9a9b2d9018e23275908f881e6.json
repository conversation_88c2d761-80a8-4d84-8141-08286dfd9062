{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    var result = \"'läschte\";\n    if (day === 2 || day === 4) {\n      // Eifeler Regel: Add an n before the consonant d; Here \"Dënschdeg\" \"and <PERSON><PERSON><PERSON><PERSON>\".\n      result += 'n';\n    }\n    result += \"' eeee 'um' p\";\n    return result;\n  },\n  yesterday: \"'gëschter um' p\",\n  today: \"'haut um' p\",\n  tomorrow: \"'moien um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "day", "getUTCDay", "result", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/lb/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    var result = \"'läschte\";\n    if (day === 2 || day === 4) {\n      // Eifeler Regel: Add an n before the consonant d; Here \"Dënschdeg\" \"and <PERSON><PERSON><PERSON><PERSON>\".\n      result += 'n';\n    }\n    result += \"' eeee 'um' p\";\n    return result;\n  },\n  yesterday: \"'gëschter um' p\",\n  today: \"'haut um' p\",\n  tomorrow: \"'moien um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,IAAIC,MAAM,GAAG,UAAU;IACvB,IAAIF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAC1B;MACAE,MAAM,IAAI,GAAG;IACf;IACAA,MAAM,IAAI,eAAe;IACzB,OAAOA,MAAM;EACf,CAAC;EACDC,SAAS,EAAE,iBAAiB;EAC5BC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEW,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGf,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACb,IAAI,CAAC;EACrB;EACA,OAAOa,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}