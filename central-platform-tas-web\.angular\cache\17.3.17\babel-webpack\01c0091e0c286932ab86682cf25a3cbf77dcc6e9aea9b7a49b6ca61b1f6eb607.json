{"ast": null, "code": "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(º|ª)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes da era comum|depois de cristo|era comum)/i\n};\nvar parseEraPatterns = {\n  any: [/^ac/i, /^dc/i],\n  wide: [/^(antes de cristo|antes da era comum)/i, /^(depois de cristo|era comum)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º|ª)? trimestre/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,\n  wide: /^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ab/i, /^mai/i, /^jun/i, /^jul/i, /^ag/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[dstq]/i,\n  short: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  abbreviated: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  wide: /^(domingo|segunda-?\\s?feira|terça-?\\s?feira|quarta-?\\s?feira|quinta-?\\s?feira|sexta-?\\s?feira|s[áa]bado)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^d/i, /^s/i, /^t/i, /^q/i, /^q/i, /^s/i, /^s/i],\n  any: [/^d/i, /^seg/i, /^t/i, /^qua/i, /^qui/i, /^sex/i, /^s[áa]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i,\n  any: /^([ap]\\.?\\s?m\\.?|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^meia/i,\n    noon: /^meio/i,\n    morning: /manh[ãa]/i,\n    afternoon: /tarde/i,\n    evening: /noite/i,\n    night: /madrugada/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/pt/_lib/match/index.js"], "sourcesContent": ["import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(º|ª)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes da era comum|depois de cristo|era comum)/i\n};\nvar parseEraPatterns = {\n  any: [/^ac/i, /^dc/i],\n  wide: [/^(antes de cristo|antes da era comum)/i, /^(depois de cristo|era comum)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º|ª)? trimestre/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,\n  wide: /^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ab/i, /^mai/i, /^jun/i, /^jul/i, /^ag/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[dstq]/i,\n  short: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  abbreviated: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  wide: /^(domingo|segunda-?\\s?feira|terça-?\\s?feira|quarta-?\\s?feira|quinta-?\\s?feira|sexta-?\\s?feira|s[áa]bado)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^d/i, /^s/i, /^t/i, /^q/i, /^q/i, /^s/i, /^s/i],\n  any: [/^d/i, /^seg/i, /^t/i, /^qua/i, /^qui/i, /^sex/i, /^s[áa]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i,\n  any: /^([ap]\\.?\\s?m\\.?|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^meia/i,\n    noon: /^meio/i,\n    morning: /manh[ãa]/i,\n    afternoon: /tarde/i,\n    evening: /noite/i,\n    night: /madrugada/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,IAAIC,yBAAyB,GAAG,eAAe;AAC/C,IAAIC,yBAAyB,GAAG,MAAM;AACtC,IAAIC,gBAAgB,GAAG;EACrBC,MAAM,EAAE,eAAe;EACvBC,WAAW,EAAE,4DAA4D;EACzEC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,gBAAgB,GAAG;EACrBC,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACrBF,IAAI,EAAE,CAAC,wCAAwC,EAAE,gCAAgC;AACnF,CAAC;AACD,IAAIG,oBAAoB,GAAG;EACzBL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,IAAII,oBAAoB,GAAG;EACzBF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AACD,IAAIG,kBAAkB,GAAG;EACvBP,MAAM,EAAE,cAAc;EACtBC,WAAW,EAAE,qDAAqD;EAClEC,IAAI,EAAE;AACR,CAAC;AACD,IAAIM,kBAAkB,GAAG;EACvBR,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5FI,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACrG,CAAC;AACD,IAAIK,gBAAgB,GAAG;EACrBT,MAAM,EAAE,UAAU;EAClBU,KAAK,EAAE,oCAAoC;EAC3CT,WAAW,EAAE,oCAAoC;EACjDC,IAAI,EAAE;AACR,CAAC;AACD,IAAIS,gBAAgB,GAAG;EACrBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS;AACnE,CAAC;AACD,IAAIQ,sBAAsB,GAAG;EAC3BZ,MAAM,EAAE,2EAA2E;EACnFI,GAAG,EAAE;AACP,CAAC;AACD,IAAIS,sBAAsB,GAAG;EAC3BT,GAAG,EAAE;IACHU,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,KAAK,GAAG;EACVC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAE,SAASA,aAAaA,CAACC,KAAK,EAAE;MAC3C,OAAOC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;IAC5B;EACF,CAAC,CAAC;EACFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAE,SAASA,aAAaA,CAACS,KAAK,EAAE;MAC3C,OAAOA,KAAK,GAAG,CAAC;IAClB;EACF,CAAC,CAAC;EACFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC;AACD,eAAeX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}