{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { TallyLocationComponent } from './tallylocation.component';\nimport { TallyLocationEditComponent } from '@business/tas/tallylocation/tallylocation-edit/tallylocation-edit.component';\nimport { TallyLocationRoutingModule } from './tallylocation-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [TallyLocationComponent, TallyLocationEditComponent];\nexport class TallyLocationModule {\n  static {\n    this.ɵfac = function TallyLocationModule_Factory(t) {\n      return new (t || TallyLocationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TallyLocationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, TallyLocationRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TallyLocationModule, {\n    declarations: [TallyLocationComponent, TallyLocationEditComponent],\n    imports: [SharedModule, TallyLocationRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "TallyLocationComponent", "TallyLocationEditComponent", "TallyLocationRoutingModule", "COMPONENTS", "TallyLocationModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\tallylocation\\tallylocation.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { TallyLocationComponent } from './tallylocation.component';\r\nimport { TallyLocationEditComponent } from '@business/tas/tallylocation/tallylocation-edit/tallylocation-edit.component';\r\nimport { TallyLocationRoutingModule } from './tallylocation-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  TallyLocationComponent,\r\n  TallyLocationEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, TallyLocationRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class TallyLocationModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,0BAA0B,QAAQ,6EAA6E;AACxH,SAASC,0BAA0B,QAAQ,gCAAgC;;AAE3E,MAAMC,UAAU,GAAG,CACjBH,sBAAsB,EACtBC,0BAA0B,CAC3B;AAMD,OAAM,MAAOG,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBN,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;IAAA;EAAA;;;2EAGrDK,mBAAmB;IAAAC,YAAA,GAR9BL,sBAAsB,EACtBC,0BAA0B;IAAAK,OAAA,GAIhBR,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}