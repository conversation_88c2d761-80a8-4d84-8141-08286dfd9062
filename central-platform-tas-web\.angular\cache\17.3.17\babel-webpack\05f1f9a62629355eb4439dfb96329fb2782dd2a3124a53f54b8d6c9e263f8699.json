{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { WorkclassesComponent } from './workclasses.component';\nimport { WorkclassesEditComponent } from '@business/tas/workclasses/workclasses-edit/workclasses-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: WorkclassesComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: WorkclassesEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class WorkclassesRoutingModule {\n  static {\n    this.ɵfac = function WorkclassesRoutingModule_Factory(t) {\n      return new (t || WorkclassesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WorkclassesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WorkclassesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "WorkclassesComponent", "WorkclassesEditComponent", "routes", "path", "component", "data", "cache", "WorkclassesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\workclasses\\workclasses-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { WorkclassesComponent } from './workclasses.component';\r\nimport { WorkclassesEditComponent } from '@business/tas/workclasses/workclasses-edit/workclasses-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: WorkclassesComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: WorkclassesEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class WorkclassesRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,uEAAuE;;;AAChH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,oBAAoB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACxE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,wBAAwB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAClF;AAMD,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAHzBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,wBAAwB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFzBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}