{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_VSLVOY extends CwfStore {\n  constructor() {\n    super({\n      id: '',\n      vesselCd: '',\n      // 船舶代码\n      vesselNm: '',\n      // 船舶名称\n      vesselNmEn: '',\n      // 船舶英文名称\n      voyage: '',\n      // 航次\n      vesselNo: '',\n      // 船号（一般跟码头做关联使用）\n      imo: '',\n      // IMO\n      shipLineCd: '',\n      // 航线代码\n      shipLineNm: '',\n      // 航线名称\n      shipLineNmEn: '',\n      // 航线英文名称\n      shipLineClassCd: '',\n      // 航线大类代码\n      shipLineClassNm: '',\n      // 航线大类名称\n      shipLineClassNmEn: '',\n      // 航线大类英文名称\n      shipLineTypeCd: '',\n      // 航线类型代码\n      shipLineTypeNm: '',\n      // 航线类型名称\n      shipLineTypeNmEn: '',\n      // 航线类型英文名称\n      ioId: '',\n      // 进出口标识代码\n      tradeId: '',\n      // 内外贸标识代码\n      portCd: '',\n      // 港口代码\n      portNm: '',\n      // 港口名称\n      portNmEn: '',\n      // 港口英文名称\n      wharfCd: '',\n      // 码头代码\n      wharfNm: '',\n      // 码头名称\n      wharfNmEn: '',\n      // 码头英文名称\n      berthCd: '',\n      // 泊位代码\n      berthNm: '',\n      // 泊位名称\n      berthNmEn: '',\n      // 泊位英文名称\n      eta: '',\n      // 计划到港时间\n      ata: '',\n      // 实际到港时间\n      etd: '',\n      // 计划离港时间\n      atd: '',\n      // 实际离港时间\n      pst: '',\n      // 计划开工时间\n      ast: '',\n      // 实际开工时间\n      pet: '',\n      // 计划完工时间\n      aet: '',\n      // 实际完工时间\n      atb: '',\n      // 靠泊时间\n      captain: '',\n      // 船长(大副)\n      captainTel: '',\n      // 船长(大副)联系电话\n      captainEmail: '',\n      // 邮箱\n      shipagentCd: '',\n      // 船舶代理代码\n      shipagentNm: '',\n      // 船舶代理名称\n      shipagentNmEn: '',\n      // 船舶代理英文名称\n      carrierCd: '',\n      // 承运人代码\n      carrierNm: '',\n      // 承运人名称\n      carrierNmEn: '',\n      // 承运人英文名称\n      reportTag: '',\n      // 是否需要发送理货报告标识\n      printTag: '',\n      // 是否需要打印船图标识\n      vesselTypeCode: '',\n      // 船舶类型 代码\n      vesselTypeName: '',\n      // 船舶类型名称\n      vesselTypeNameEn: '',\n      // 船舶类型英文名称\n      vesselAttrCd: '',\n      // 船舶属性代码\n      vesselAttrNm: '',\n      // 船舶属性名称\n      vesselAttrNmEn: '',\n      // 船舶属性英文名称\n      vesselNatureCd: '',\n      // 船舶性质代码\n      vesselNatureNm: '',\n      // 船舶性质名称\n      vesselNatureNmEn: '',\n      // 船舶性质英文名称\n      operationNatureCd: '',\n      // 营运性质代码\n      operationNatureNm: '',\n      // 营运性质名称\n      operationNatureNmEn: '',\n      // 营运性质英文名称\n      workNatureCd: '',\n      // 作业性质代码\n      workNatureNm: '',\n      // 作业性质名称\n      workNatureNmEn: '',\n      // 作业性质英文名称\n      vesselFlagCd: '',\n      // 船期(国家)代码\n      vesselFlagNm: '',\n      // 船期(国家)名称\n      vesselFlagNmEn: '',\n      // 船期(国家)英文名称\n      tallyEsigfnNm: '',\n      // 理货签证人\n      tallyEsignDt: '',\n      // 理货签证时间\n      captainEsignNm: '',\n      // 大副签证人\n      captainEsignDt: '',\n      // 大副签证时间\n      nextPortCd: '',\n      // 下一港代码\n      nextPortNm: '',\n      // 下一港名称\n      nextPortNmEn: '',\n      // 下一港英文名称\n      bsCd: '',\n      // 业务场景代码\n      bsNm: '',\n      // 业务场景名称\n      bsNmEn: '',\n      // 业务场景英文名称\n      confirmTag: '',\n      // 确报标识\n      splitTag: '',\n      // 是否是分船航次标志，空 无分船 ， M 分船-主航次， S 分船-子航次\n      orVlsvoyId: '',\n      // 原始航次id\n      orVesselCd: '',\n      // 原始航次船舶代码\n      orVesselNm: '',\n      // 原始航次船舶名称\n      orVoyage: '',\n      // 原始航次航次号\n      secondShipTag: '',\n      // 是否二程船\n      masterId: '',\n      // 大船船期主键\n      masterVesselCd: '',\n      // 大船船舶代码\n      masterVesselNm: '',\n      // 大船船舶名称\n      masterVesselNmEn: '',\n      // 大船船舶英文名称\n      masterVoyage: '',\n      // 大船航次\n      ships: '',\n      // 艘次\n      dossierNo: '',\n      // 档案号\n      dossierDt: '',\n      // 档案生成时间\n      dossierUserCd: '',\n      // 档案生成人代码\n      dossierUserNm: '',\n      // 档案生成人名称\n      dossierPosition: '',\n      // 档案位置\n      endTag: '',\n      // 结航标识\n      endOperatorCd: '',\n      // 结航操作人代码\n      endOperatorNm: '',\n      // 结航操作人名称\n      endDt: '',\n      // 结航时间\n      completeTag: '',\n      // 现场完工标识\n      completeOperatorCd: '',\n      // 现场完工操作人代码\n      completeOperatorNm: '',\n      // 现场完工操作人名称\n      completeDt: '',\n      // 现场完工操作时间\n      internalTransferNum: '',\n      // 内支线中转箱数\n      buCd: '',\n      // bu代码\n      buNm: '',\n      // bu名称\n      buNmEn: '',\n      // bu英文名称\n      ouCd: '',\n      // ou代码\n      ouNm: '',\n      // ou名称\n      ouNmEn: '',\n      // ou英文名称\n      orgId: '',\n      // 所属组织机构主键\n      orgLevelNo: '',\n      // 所属组织机构代码\n      entLevelNo: '',\n      // 所属公司代码\n      remark: '',\n      // 备注\n      createdUser: '',\n      // 创建人\n      createdTime: '',\n      // 创建时间\n      modifiedUser: '',\n      // 修改人\n      modifiedTime: '',\n      // 修改时间\n      version: '',\n      // 版本号\n      isDelete: '',\n      // 是否删除\n      region: '',\n      tenantId: '',\n      isuniversal: ''\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_VSLVOY'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_VSLVOY", "constructor", "id", "vesselCd", "vesselNm", "vesselNmEn", "voyage", "vesselNo", "imo", "shipLineCd", "shipLineNm", "shipLineNmEn", "shipLineClassCd", "shipLineClassNm", "shipLineClassNmEn", "shipLineTypeCd", "shipLineTypeNm", "shipLineTypeNmEn", "ioId", "tradeId", "portCd", "portNm", "portNmEn", "wharfCd", "wharfNm", "wharfNmEn", "berthCd", "berthNm", "berthNmEn", "eta", "ata", "etd", "atd", "pst", "ast", "pet", "aet", "atb", "captain", "captain<PERSON><PERSON>", "captain<PERSON><PERSON>", "shipagentCd", "shipagentNm", "shipagentNmEn", "carrierCd", "carrierNm", "carrierNmEn", "reportTag", "printTag", "vesselTypeCode", "vesselTypeName", "vesselTypeNameEn", "vesselAttrCd", "vesselAttrNm", "vesselAttrNmEn", "vesselNatureCd", "vesselNatureNm", "vesselNatureNmEn", "operationNatureCd", "operationNatureNm", "operationNatureNmEn", "workNatureCd", "workNatureNm", "workNatureNmEn", "vesselFlagCd", "vesselFlagNm", "vesselFlagNmEn", "tallyEsigfnNm", "tallyEsignDt", "captain<PERSON><PERSON><PERSON><PERSON>", "captain<PERSON><PERSON><PERSON><PERSON>", "nextPortCd", "nextPortNm", "nextPortNmEn", "bsCd", "bsNm", "bsNmEn", "confirmTag", "splitTag", "orVlsvoyId", "orVesselCd", "orVesselNm", "orVoyage", "secondShipTag", "masterId", "masterVesselCd", "masterVesselNm", "masterVesselNmEn", "masterVoyage", "ships", "dossierNo", "dossierDt", "dossierUserCd", "dossierUserNm", "dossierPosition", "endTag", "endOperatorCd", "endOperatorNm", "endDt", "completeTag", "completeOperatorCd", "completeOperatorNm", "completeDt", "internalTransferNum", "buCd", "buNm", "buNmEn", "ouCd", "ouNm", "ouNmEn", "orgId", "orgLevelNo", "entLevelNo", "remark", "created<PERSON>ser", "createdTime", "modifiedUser", "modifiedTime", "version", "isDelete", "region", "tenantId", "isuniversal", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\TAS\\TAS_T_VSLVOY.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_VSLVOY extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_VSLVOY'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      id: '',\r\n      vesselCd: '', // 船舶代码\r\n      vesselNm: '', // 船舶名称\r\n      vesselNmEn: '', // 船舶英文名称\r\n      voyage: '', // 航次\r\n      vesselNo: '', // 船号（一般跟码头做关联使用）\r\n      imo: '', // IMO\r\n      shipLineCd: '', // 航线代码\r\n      shipLineNm: '', // 航线名称\r\n      shipLineNmEn: '', // 航线英文名称\r\n      shipLineClassCd: '', // 航线大类代码\r\n      shipLineClassNm: '', // 航线大类名称\r\n      shipLineClassNmEn: '', // 航线大类英文名称\r\n      shipLineTypeCd: '', // 航线类型代码\r\n      shipLineTypeNm: '', // 航线类型名称\r\n      shipLineTypeNmEn: '', // 航线类型英文名称\r\n      ioId: '', // 进出口标识代码\r\n      tradeId: '', // 内外贸标识代码\r\n      portCd: '', // 港口代码\r\n      portNm: '', // 港口名称\r\n      portNmEn: '', // 港口英文名称\r\n      wharfCd: '', // 码头代码\r\n      wharfNm: '', // 码头名称\r\n      wharfNmEn: '', // 码头英文名称\r\n      berthCd: '', // 泊位代码\r\n      berthNm: '', // 泊位名称\r\n      berthNmEn: '', // 泊位英文名称\r\n      eta: '', // 计划到港时间\r\n      ata: '', // 实际到港时间\r\n      etd: '', // 计划离港时间\r\n      atd: '', // 实际离港时间\r\n      pst: '', // 计划开工时间\r\n      ast: '', // 实际开工时间\r\n      pet: '', // 计划完工时间\r\n      aet: '', // 实际完工时间\r\n      atb: '', // 靠泊时间\r\n      captain: '', // 船长(大副)\r\n      captainTel: '', // 船长(大副)联系电话\r\n      captainEmail: '', // 邮箱\r\n      shipagentCd: '', // 船舶代理代码\r\n      shipagentNm: '', // 船舶代理名称\r\n      shipagentNmEn: '', // 船舶代理英文名称\r\n      carrierCd: '', // 承运人代码\r\n      carrierNm: '', // 承运人名称\r\n      carrierNmEn: '', // 承运人英文名称\r\n      reportTag: '', // 是否需要发送理货报告标识\r\n      printTag: '', // 是否需要打印船图标识\r\n      vesselTypeCode: '', // 船舶类型 代码\r\n      vesselTypeName: '', // 船舶类型名称\r\n      vesselTypeNameEn: '', // 船舶类型英文名称\r\n      vesselAttrCd: '', // 船舶属性代码\r\n      vesselAttrNm: '', // 船舶属性名称\r\n      vesselAttrNmEn: '', // 船舶属性英文名称\r\n      vesselNatureCd: '', // 船舶性质代码\r\n      vesselNatureNm: '', // 船舶性质名称\r\n      vesselNatureNmEn: '', // 船舶性质英文名称\r\n      operationNatureCd: '', // 营运性质代码\r\n      operationNatureNm: '', // 营运性质名称\r\n      operationNatureNmEn: '', // 营运性质英文名称\r\n      workNatureCd: '', // 作业性质代码\r\n      workNatureNm: '', // 作业性质名称\r\n      workNatureNmEn: '', // 作业性质英文名称\r\n      vesselFlagCd: '', // 船期(国家)代码\r\n      vesselFlagNm: '', // 船期(国家)名称\r\n      vesselFlagNmEn: '', // 船期(国家)英文名称\r\n      tallyEsigfnNm: '', // 理货签证人\r\n      tallyEsignDt: '', // 理货签证时间\r\n      captainEsignNm: '', // 大副签证人\r\n      captainEsignDt: '', // 大副签证时间\r\n      nextPortCd: '', // 下一港代码\r\n      nextPortNm: '', // 下一港名称\r\n      nextPortNmEn: '', // 下一港英文名称\r\n      bsCd: '', // 业务场景代码\r\n      bsNm: '', // 业务场景名称\r\n      bsNmEn: '', // 业务场景英文名称\r\n      confirmTag: '', // 确报标识\r\n      splitTag: '', // 是否是分船航次标志，空 无分船 ， M 分船-主航次， S 分船-子航次\r\n      orVlsvoyId: '', // 原始航次id\r\n      orVesselCd: '', // 原始航次船舶代码\r\n      orVesselNm: '', // 原始航次船舶名称\r\n      orVoyage: '', // 原始航次航次号\r\n      secondShipTag: '', // 是否二程船\r\n      masterId: '', // 大船船期主键\r\n      masterVesselCd: '', // 大船船舶代码\r\n      masterVesselNm: '', // 大船船舶名称\r\n      masterVesselNmEn: '', // 大船船舶英文名称\r\n      masterVoyage: '', // 大船航次\r\n      ships: '', // 艘次\r\n      dossierNo: '', // 档案号\r\n      dossierDt: '', // 档案生成时间\r\n      dossierUserCd: '', // 档案生成人代码\r\n      dossierUserNm: '', // 档案生成人名称\r\n      dossierPosition: '', // 档案位置\r\n      endTag: '', // 结航标识\r\n      endOperatorCd: '', // 结航操作人代码\r\n      endOperatorNm: '', // 结航操作人名称\r\n      endDt: '', // 结航时间\r\n      completeTag: '', // 现场完工标识\r\n      completeOperatorCd: '', // 现场完工操作人代码\r\n      completeOperatorNm: '', // 现场完工操作人名称\r\n      completeDt: '', // 现场完工操作时间\r\n      internalTransferNum: '', // 内支线中转箱数\r\n      buCd: '', // bu代码\r\n      buNm: '', // bu名称\r\n      buNmEn: '', // bu英文名称\r\n      ouCd: '', // ou代码\r\n      ouNm: '', // ou名称\r\n      ouNmEn: '', // ou英文名称\r\n      orgId: '', // 所属组织机构主键\r\n      orgLevelNo: '', // 所属组织机构代码\r\n      entLevelNo: '', // 所属公司代码\r\n      remark: '', // 备注\r\n      createdUser: '', // 创建人\r\n      createdTime: '', // 创建时间\r\n      modifiedUser: '', // 修改人\r\n      modifiedTime: '', // 修改时间\r\n      version: '', // 版本号\r\n      isDelete: '', // 是否删除\r\n      region: '',\r\n      tenantId: '',\r\n      isuniversal: '',\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,YAAa,SAAQD,QAAQ;EAQxCE,YAAA;IACE,KAAK,CAAC;MACJC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,EAAE;MAAE;MACdC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAE;MAAE;MAChBC,MAAM,EAAE,EAAE;MAAE;MACZC,QAAQ,EAAE,EAAE;MAAE;MACdC,GAAG,EAAE,EAAE;MAAE;MACTC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,EAAE;MAAE;MAChBC,YAAY,EAAE,EAAE;MAAE;MAClBC,eAAe,EAAE,EAAE;MAAE;MACrBC,eAAe,EAAE,EAAE;MAAE;MACrBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,IAAI,EAAE,EAAE;MAAE;MACVC,OAAO,EAAE,EAAE;MAAE;MACbC,MAAM,EAAE,EAAE;MAAE;MACZC,MAAM,EAAE,EAAE;MAAE;MACZC,QAAQ,EAAE,EAAE;MAAE;MACdC,OAAO,EAAE,EAAE;MAAE;MACbC,OAAO,EAAE,EAAE;MAAE;MACbC,SAAS,EAAE,EAAE;MAAE;MACfC,OAAO,EAAE,EAAE;MAAE;MACbC,OAAO,EAAE,EAAE;MAAE;MACbC,SAAS,EAAE,EAAE;MAAE;MACfC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,GAAG,EAAE,EAAE;MAAE;MACTC,OAAO,EAAE,EAAE;MAAE;MACbC,UAAU,EAAE,EAAE;MAAE;MAChBC,YAAY,EAAE,EAAE;MAAE;MAClBC,WAAW,EAAE,EAAE;MAAE;MACjBC,WAAW,EAAE,EAAE;MAAE;MACjBC,aAAa,EAAE,EAAE;MAAE;MACnBC,SAAS,EAAE,EAAE;MAAE;MACfC,SAAS,EAAE,EAAE;MAAE;MACfC,WAAW,EAAE,EAAE;MAAE;MACjBC,SAAS,EAAE,EAAE;MAAE;MACfC,QAAQ,EAAE,EAAE;MAAE;MACdC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAAE;MAClBC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,mBAAmB,EAAE,EAAE;MAAE;MACzBC,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAAE;MAClBC,cAAc,EAAE,EAAE;MAAE;MACpBC,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAAE;MAClBC,cAAc,EAAE,EAAE;MAAE;MACpBC,aAAa,EAAE,EAAE;MAAE;MACnBC,YAAY,EAAE,EAAE;MAAE;MAClBC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,EAAE;MAAE;MAChBC,YAAY,EAAE,EAAE;MAAE;MAClBC,IAAI,EAAE,EAAE;MAAE;MACVC,IAAI,EAAE,EAAE;MAAE;MACVC,MAAM,EAAE,EAAE;MAAE;MACZC,UAAU,EAAE,EAAE;MAAE;MAChBC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,EAAE;MAAE;MAChBC,QAAQ,EAAE,EAAE;MAAE;MACdC,aAAa,EAAE,EAAE;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;MACdC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE;MAAE;MACpBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,YAAY,EAAE,EAAE;MAAE;MAClBC,KAAK,EAAE,EAAE;MAAE;MACXC,SAAS,EAAE,EAAE;MAAE;MACfC,SAAS,EAAE,EAAE;MAAE;MACfC,aAAa,EAAE,EAAE;MAAE;MACnBC,aAAa,EAAE,EAAE;MAAE;MACnBC,eAAe,EAAE,EAAE;MAAE;MACrBC,MAAM,EAAE,EAAE;MAAE;MACZC,aAAa,EAAE,EAAE;MAAE;MACnBC,aAAa,EAAE,EAAE;MAAE;MACnBC,KAAK,EAAE,EAAE;MAAE;MACXC,WAAW,EAAE,EAAE;MAAE;MACjBC,kBAAkB,EAAE,EAAE;MAAE;MACxBC,kBAAkB,EAAE,EAAE;MAAE;MACxBC,UAAU,EAAE,EAAE;MAAE;MAChBC,mBAAmB,EAAE,EAAE;MAAE;MACzBC,IAAI,EAAE,EAAE;MAAE;MACVC,IAAI,EAAE,EAAE;MAAE;MACVC,MAAM,EAAE,EAAE;MAAE;MACZC,IAAI,EAAE,EAAE;MAAE;MACVC,IAAI,EAAE,EAAE;MAAE;MACVC,MAAM,EAAE,EAAE;MAAE;MACZC,KAAK,EAAE,EAAE;MAAE;MACXC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,EAAE;MAAE;MAChBC,MAAM,EAAE,EAAE;MAAE;MACZC,WAAW,EAAE,EAAE;MAAE;MACjBC,WAAW,EAAE,EAAE;MAAE;MACjBC,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAAE;MAClBC,OAAO,EAAE,EAAE;MAAE;MACbC,QAAQ,EAAE,EAAE;MAAE;MACdC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;KACd,CAAC;IAjIJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,cAAc,CAAC,CAAC;IAC5B,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EA8HnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}