{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'pasado a la' p\",\n  yesterday: \"'ayer a la' p\",\n  today: \"'hoy a la' p\",\n  tomorrow: \"'mañana a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'pasado a las' p\",\n  yesterday: \"'ayer a las' p\",\n  today: \"'hoy a las' p\",\n  tomorrow: \"'mañana a las' p\",\n  nextWeek: \"eeee 'a las' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  } else {\n    return formatRelativeLocale[token];\n  }\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getUTCHours"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/es/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'pasado a la' p\",\n  yesterday: \"'ayer a la' p\",\n  today: \"'hoy a la' p\",\n  tomorrow: \"'mañana a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'pasado a las' p\",\n  yesterday: \"'ayer a las' p\",\n  today: \"'hoy a las' p\",\n  tomorrow: \"'mañana a las' p\",\n  nextWeek: \"eeee 'a las' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  } else {\n    return formatRelativeLocale[token];\n  }\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,2BAA2B;EACrCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,0BAA0B,GAAG;EAC/BN,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,gBAAgB;EAC1BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIF,IAAI,CAACG,WAAW,CAAC,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C,CAAC,MAAM;IACL,OAAOT,oBAAoB,CAACS,KAAK,CAAC;EACpC;AACF,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}