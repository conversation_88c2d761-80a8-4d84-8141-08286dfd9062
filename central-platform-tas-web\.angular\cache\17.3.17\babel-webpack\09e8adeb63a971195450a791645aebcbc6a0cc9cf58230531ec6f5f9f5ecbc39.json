{"ast": null, "code": "export default function toInteger(dirtyNumber) {\n  if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {\n    return NaN;\n  }\n  var number = Number(dirtyNumber);\n  if (isNaN(number)) {\n    return number;\n  }\n  return number < 0 ? Math.ceil(number) : Math.floor(number);\n}", "map": {"version": 3, "names": ["toInteger", "dirtyNumber", "NaN", "number", "Number", "isNaN", "Math", "ceil", "floor"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/_lib/toInteger/index.js"], "sourcesContent": ["export default function toInteger(dirtyNumber) {\n  if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {\n    return NaN;\n  }\n  var number = Number(dirtyNumber);\n  if (isNaN(number)) {\n    return number;\n  }\n  return number < 0 ? Math.ceil(number) : Math.floor(number);\n}"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,WAAW,EAAE;EAC7C,IAAIA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,EAAE;IACzE,OAAOC,GAAG;EACZ;EACA,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,KAAK,CAACF,MAAM,CAAC,EAAE;IACjB,OAAOA,MAAM;EACf;EACA,OAAOA,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC,IAAI,CAACJ,MAAM,CAAC,GAAGG,IAAI,CAACE,KAAK,CAACL,MAAM,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}