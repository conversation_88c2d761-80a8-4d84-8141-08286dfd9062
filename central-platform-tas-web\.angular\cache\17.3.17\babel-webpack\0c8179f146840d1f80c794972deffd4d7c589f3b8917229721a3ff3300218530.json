{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { ShipLineComponent } from './shipline.component';\nimport { ShipLineEditComponent } from '@business/tas/shipline/shipline-edit/shipline-edit.component';\nimport { ShipLineRoutingModule } from './shipline-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [ShipLineComponent, ShipLineEditComponent];\nexport class ShipLineModule {\n  static {\n    this.ɵfac = function ShipLineModule_Factory(t) {\n      return new (t || ShipLineModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ShipLineModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, ShipLineRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ShipLineModule, {\n    declarations: [ShipLineComponent, ShipLineEditComponent],\n    imports: [SharedModule, ShipLineRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "ShipLineComponent", "ShipLineEditComponent", "ShipLineRoutingModule", "COMPONENTS", "ShipLineModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\shipline\\shipline.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { ShipLineComponent } from './shipline.component';\r\nimport { ShipLineEditComponent } from '@business/tas/shipline/shipline-edit/shipline-edit.component';\r\nimport { ShipLineRoutingModule } from './shipline-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  ShipLineComponent,\r\n  ShipLineEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, ShipLineRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class ShipLineModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,qBAAqB,QAAQ,2BAA2B;;AAEjE,MAAMC,UAAU,GAAG,CACjBH,iBAAiB,EACjBC,qBAAqB,CACtB;AAMD,OAAM,MAAOG,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAHfN,YAAY,EAAEI,qBAAqB,EAAEH,YAAY;IAAA;EAAA;;;2EAGhDK,cAAc;IAAAC,YAAA,GARzBL,iBAAiB,EACjBC,qBAAqB;IAAAK,OAAA,GAIXR,YAAY,EAAEI,qBAAqB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}