{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { hex2b64 } from \"./lib/jsbn/base64\";\nimport { Hex } from \"./lib/asn1js/hex\";\nimport { Base64 } from \"./lib/asn1js/base64\";\nimport { ASN1 } from \"./lib/asn1js/asn1\";\nimport { RSAKey } from \"./lib/jsbn/rsa\";\nimport { parseBigInt } from \"./lib/jsbn/jsbn\";\nimport { KJUR } from \"./lib/jsrsasign/asn1-1.0\";\n/**\n * Create a new JSEncryptRSAKey that extends Tom Wu's RSA key object.\n * This object is just a decorator for parsing the key parameter\n * @param {string|Object} key - The key in string format, or an object containing\n * the parameters needed to build a RSAKey object.\n * @constructor\n */\nvar JSEncryptRSAKey = /** @class */function (_super) {\n  __extends(JSEncryptRSAKey, _super);\n  function JSEncryptRSAKey(key) {\n    var _this = _super.call(this) || this;\n    // Call the super constructor.\n    //  RSAKey.call(this);\n    // If a key key was provided.\n    if (key) {\n      // If this is a string...\n      if (typeof key === \"string\") {\n        _this.parseKey(key);\n      } else if (JSEncryptRSAKey.hasPrivateKeyProperty(key) || JSEncryptRSAKey.hasPublicKeyProperty(key)) {\n        // Set the values for the key.\n        _this.parsePropertiesFrom(key);\n      }\n    }\n    return _this;\n  }\n  /**\n   * Method to parse a pem encoded string containing both a public or private key.\n   * The method will translate the pem encoded string in a der encoded string and\n   * will parse private key and public key parameters. This method accepts public key\n   * in the rsaencryption pkcs #1 format (oid: 1.2.840.113549.1.1.1).\n   *\n   * @todo Check how many rsa formats use the same format of pkcs #1.\n   *\n   * The format is defined as:\n   * PublicKeyInfo ::= SEQUENCE {\n   *   algorithm       AlgorithmIdentifier,\n   *   PublicKey       BIT STRING\n   * }\n   * Where AlgorithmIdentifier is:\n   * AlgorithmIdentifier ::= SEQUENCE {\n   *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n   *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n   * }\n   * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n   * RSAPublicKey ::= SEQUENCE {\n   *   modulus           INTEGER,  -- n\n   *   publicExponent    INTEGER   -- e\n   * }\n   * it's possible to examine the structure of the keys obtained from openssl using\n   * an asn.1 dumper as the one used here to parse the components: http://lapo.it/asn1js/\n   * @argument {string} pem the pem encoded string, can include the BEGIN/END header/footer\n   * @private\n   */\n  JSEncryptRSAKey.prototype.parseKey = function (pem) {\n    try {\n      var modulus = 0;\n      var public_exponent = 0;\n      var reHex = /^\\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\\s*)+$/;\n      var der = reHex.test(pem) ? Hex.decode(pem) : Base64.unarmor(pem);\n      var asn1 = ASN1.decode(der);\n      // Fixes a bug with OpenSSL 1.0+ private keys\n      if (asn1.sub.length === 3) {\n        asn1 = asn1.sub[2].sub[0];\n      }\n      if (asn1.sub.length === 9) {\n        // Parse the private key.\n        modulus = asn1.sub[1].getHexStringValue(); // bigint\n        this.n = parseBigInt(modulus, 16);\n        public_exponent = asn1.sub[2].getHexStringValue(); // int\n        this.e = parseInt(public_exponent, 16);\n        var private_exponent = asn1.sub[3].getHexStringValue(); // bigint\n        this.d = parseBigInt(private_exponent, 16);\n        var prime1 = asn1.sub[4].getHexStringValue(); // bigint\n        this.p = parseBigInt(prime1, 16);\n        var prime2 = asn1.sub[5].getHexStringValue(); // bigint\n        this.q = parseBigInt(prime2, 16);\n        var exponent1 = asn1.sub[6].getHexStringValue(); // bigint\n        this.dmp1 = parseBigInt(exponent1, 16);\n        var exponent2 = asn1.sub[7].getHexStringValue(); // bigint\n        this.dmq1 = parseBigInt(exponent2, 16);\n        var coefficient = asn1.sub[8].getHexStringValue(); // bigint\n        this.coeff = parseBigInt(coefficient, 16);\n      } else if (asn1.sub.length === 2) {\n        if (asn1.sub[0].sub) {\n          // Parse ASN.1 SubjectPublicKeyInfo type as defined by X.509\n          var bit_string = asn1.sub[1];\n          var sequence = bit_string.sub[0];\n          modulus = sequence.sub[0].getHexStringValue();\n          this.n = parseBigInt(modulus, 16);\n          public_exponent = sequence.sub[1].getHexStringValue();\n          this.e = parseInt(public_exponent, 16);\n        } else {\n          // Parse ASN.1 RSAPublicKey type as defined by PKCS #1\n          modulus = asn1.sub[0].getHexStringValue();\n          this.n = parseBigInt(modulus, 16);\n          public_exponent = asn1.sub[1].getHexStringValue();\n          this.e = parseInt(public_exponent, 16);\n        }\n      } else {\n        return false;\n      }\n      return true;\n    } catch (ex) {\n      return false;\n    }\n  };\n  /**\n   * Translate rsa parameters in a hex encoded string representing the rsa key.\n   *\n   * The translation follow the ASN.1 notation :\n   * RSAPrivateKey ::= SEQUENCE {\n   *   version           Version,\n   *   modulus           INTEGER,  -- n\n   *   publicExponent    INTEGER,  -- e\n   *   privateExponent   INTEGER,  -- d\n   *   prime1            INTEGER,  -- p\n   *   prime2            INTEGER,  -- q\n   *   exponent1         INTEGER,  -- d mod (p1)\n   *   exponent2         INTEGER,  -- d mod (q-1)\n   *   coefficient       INTEGER,  -- (inverse of q) mod p\n   * }\n   * @returns {string}  DER Encoded String representing the rsa private key\n   * @private\n   */\n  JSEncryptRSAKey.prototype.getPrivateBaseKey = function () {\n    var options = {\n      array: [new KJUR.asn1.DERInteger({\n        int: 0\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.n\n      }), new KJUR.asn1.DERInteger({\n        int: this.e\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.d\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.p\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.q\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.dmp1\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.dmq1\n      }), new KJUR.asn1.DERInteger({\n        bigint: this.coeff\n      })]\n    };\n    var seq = new KJUR.asn1.DERSequence(options);\n    return seq.getEncodedHex();\n  };\n  /**\n   * base64 (pem) encoded version of the DER encoded representation\n   * @returns {string} pem encoded representation without header and footer\n   * @public\n   */\n  JSEncryptRSAKey.prototype.getPrivateBaseKeyB64 = function () {\n    return hex2b64(this.getPrivateBaseKey());\n  };\n  /**\n   * Translate rsa parameters in a hex encoded string representing the rsa public key.\n   * The representation follow the ASN.1 notation :\n   * PublicKeyInfo ::= SEQUENCE {\n   *   algorithm       AlgorithmIdentifier,\n   *   PublicKey       BIT STRING\n   * }\n   * Where AlgorithmIdentifier is:\n   * AlgorithmIdentifier ::= SEQUENCE {\n   *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n   *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n   * }\n   * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n   * RSAPublicKey ::= SEQUENCE {\n   *   modulus           INTEGER,  -- n\n   *   publicExponent    INTEGER   -- e\n   * }\n   * @returns {string} DER Encoded String representing the rsa public key\n   * @private\n   */\n  JSEncryptRSAKey.prototype.getPublicBaseKey = function () {\n    var first_sequence = new KJUR.asn1.DERSequence({\n      array: [new KJUR.asn1.DERObjectIdentifier({\n        oid: \"1.2.840.113549.1.1.1\"\n      }), new KJUR.asn1.DERNull()]\n    });\n    var second_sequence = new KJUR.asn1.DERSequence({\n      array: [new KJUR.asn1.DERInteger({\n        bigint: this.n\n      }), new KJUR.asn1.DERInteger({\n        int: this.e\n      })]\n    });\n    var bit_string = new KJUR.asn1.DERBitString({\n      hex: \"00\" + second_sequence.getEncodedHex()\n    });\n    var seq = new KJUR.asn1.DERSequence({\n      array: [first_sequence, bit_string]\n    });\n    return seq.getEncodedHex();\n  };\n  /**\n   * base64 (pem) encoded version of the DER encoded representation\n   * @returns {string} pem encoded representation without header and footer\n   * @public\n   */\n  JSEncryptRSAKey.prototype.getPublicBaseKeyB64 = function () {\n    return hex2b64(this.getPublicBaseKey());\n  };\n  /**\n   * wrap the string in block of width chars. The default value for rsa keys is 64\n   * characters.\n   * @param {string} str the pem encoded string without header and footer\n   * @param {Number} [width=64] - the length the string has to be wrapped at\n   * @returns {string}\n   * @private\n   */\n  JSEncryptRSAKey.wordwrap = function (str, width) {\n    width = width || 64;\n    if (!str) {\n      return str;\n    }\n    var regex = \"(.{1,\" + width + \"})( +|$\\n?)|(.{1,\" + width + \"})\";\n    return str.match(RegExp(regex, \"g\")).join(\"\\n\");\n  };\n  /**\n   * Retrieve the pem encoded private key\n   * @returns {string} the pem encoded private key with header/footer\n   * @public\n   */\n  JSEncryptRSAKey.prototype.getPrivateKey = function () {\n    var key = \"-----BEGIN RSA PRIVATE KEY-----\\n\";\n    key += JSEncryptRSAKey.wordwrap(this.getPrivateBaseKeyB64()) + \"\\n\";\n    key += \"-----END RSA PRIVATE KEY-----\";\n    return key;\n  };\n  /**\n   * Retrieve the pem encoded public key\n   * @returns {string} the pem encoded public key with header/footer\n   * @public\n   */\n  JSEncryptRSAKey.prototype.getPublicKey = function () {\n    var key = \"-----BEGIN PUBLIC KEY-----\\n\";\n    key += JSEncryptRSAKey.wordwrap(this.getPublicBaseKeyB64()) + \"\\n\";\n    key += \"-----END PUBLIC KEY-----\";\n    return key;\n  };\n  /**\n   * Check if the object contains the necessary parameters to populate the rsa modulus\n   * and public exponent parameters.\n   * @param {Object} [obj={}] - An object that may contain the two public key\n   * parameters\n   * @returns {boolean} true if the object contains both the modulus and the public exponent\n   * properties (n and e)\n   * @todo check for types of n and e. N should be a parseable bigInt object, E should\n   * be a parseable integer number\n   * @private\n   */\n  JSEncryptRSAKey.hasPublicKeyProperty = function (obj) {\n    obj = obj || {};\n    return obj.hasOwnProperty(\"n\") && obj.hasOwnProperty(\"e\");\n  };\n  /**\n   * Check if the object contains ALL the parameters of an RSA key.\n   * @param {Object} [obj={}] - An object that may contain nine rsa key\n   * parameters\n   * @returns {boolean} true if the object contains all the parameters needed\n   * @todo check for types of the parameters all the parameters but the public exponent\n   * should be parseable bigint objects, the public exponent should be a parseable integer number\n   * @private\n   */\n  JSEncryptRSAKey.hasPrivateKeyProperty = function (obj) {\n    obj = obj || {};\n    return obj.hasOwnProperty(\"n\") && obj.hasOwnProperty(\"e\") && obj.hasOwnProperty(\"d\") && obj.hasOwnProperty(\"p\") && obj.hasOwnProperty(\"q\") && obj.hasOwnProperty(\"dmp1\") && obj.hasOwnProperty(\"dmq1\") && obj.hasOwnProperty(\"coeff\");\n  };\n  /**\n   * Parse the properties of obj in the current rsa object. Obj should AT LEAST\n   * include the modulus and public exponent (n, e) parameters.\n   * @param {Object} obj - the object containing rsa parameters\n   * @private\n   */\n  JSEncryptRSAKey.prototype.parsePropertiesFrom = function (obj) {\n    this.n = obj.n;\n    this.e = obj.e;\n    if (obj.hasOwnProperty(\"d\")) {\n      this.d = obj.d;\n      this.p = obj.p;\n      this.q = obj.q;\n      this.dmp1 = obj.dmp1;\n      this.dmq1 = obj.dmq1;\n      this.coeff = obj.coeff;\n    }\n  };\n  return JSEncryptRSAKey;\n}(RSAKey);\nexport { JSEncryptRSAKey };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "hex2b64", "Hex", "Base64", "ASN1", "RSAKey", "parseBigInt", "KJUR", "JSEncryptRSAKey", "_super", "key", "_this", "parse<PERSON>ey", "hasPrivateKeyProperty", "hasPublicKeyProperty", "parsePropertiesFrom", "pem", "modulus", "public_exponent", "reHex", "der", "test", "decode", "unarmor", "asn1", "sub", "length", "getHexStringValue", "n", "e", "parseInt", "private_exponent", "prime1", "prime2", "q", "exponent1", "dmp1", "exponent2", "dmq1", "coefficient", "coeff", "bit_string", "sequence", "ex", "getPrivateBaseKey", "options", "array", "DERInteger", "int", "bigint", "seq", "DERSequence", "getEncodedHex", "getPrivateBaseKeyB64", "getPublicBaseKey", "first_sequence", "DERObjectIdentifier", "oid", "DERNull", "second_sequence", "DERBitString", "hex", "getPublicBaseKeyB64", "wordwrap", "str", "width", "regex", "match", "RegExp", "join", "getPrivateKey", "getPublicKey", "obj"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/JSEncryptRSAKey.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { hex2b64 } from \"./lib/jsbn/base64\";\nimport { Hex } from \"./lib/asn1js/hex\";\nimport { Base64 } from \"./lib/asn1js/base64\";\nimport { ASN1 } from \"./lib/asn1js/asn1\";\nimport { RSAKey } from \"./lib/jsbn/rsa\";\nimport { parseBigInt } from \"./lib/jsbn/jsbn\";\nimport { KJUR } from \"./lib/jsrsasign/asn1-1.0\";\n/**\n * Create a new JSEncryptRSAKey that extends Tom Wu's RSA key object.\n * This object is just a decorator for parsing the key parameter\n * @param {string|Object} key - The key in string format, or an object containing\n * the parameters needed to build a RSAKey object.\n * @constructor\n */\nvar JSEncryptRSAKey = /** @class */ (function (_super) {\n    __extends(JSEncryptRSAKey, _super);\n    function JSEncryptRSAKey(key) {\n        var _this = _super.call(this) || this;\n        // Call the super constructor.\n        //  RSAKey.call(this);\n        // If a key key was provided.\n        if (key) {\n            // If this is a string...\n            if (typeof key === \"string\") {\n                _this.parseKey(key);\n            }\n            else if (JSEncryptRSAKey.hasPrivateKeyProperty(key) ||\n                JSEncryptRSAKey.hasPublicKeyProperty(key)) {\n                // Set the values for the key.\n                _this.parsePropertiesFrom(key);\n            }\n        }\n        return _this;\n    }\n    /**\n     * Method to parse a pem encoded string containing both a public or private key.\n     * The method will translate the pem encoded string in a der encoded string and\n     * will parse private key and public key parameters. This method accepts public key\n     * in the rsaencryption pkcs #1 format (oid: 1.2.840.113549.1.1.1).\n     *\n     * @todo Check how many rsa formats use the same format of pkcs #1.\n     *\n     * The format is defined as:\n     * PublicKeyInfo ::= SEQUENCE {\n     *   algorithm       AlgorithmIdentifier,\n     *   PublicKey       BIT STRING\n     * }\n     * Where AlgorithmIdentifier is:\n     * AlgorithmIdentifier ::= SEQUENCE {\n     *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n     *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n     * }\n     * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n     * RSAPublicKey ::= SEQUENCE {\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER   -- e\n     * }\n     * it's possible to examine the structure of the keys obtained from openssl using\n     * an asn.1 dumper as the one used here to parse the components: http://lapo.it/asn1js/\n     * @argument {string} pem the pem encoded string, can include the BEGIN/END header/footer\n     * @private\n     */\n    JSEncryptRSAKey.prototype.parseKey = function (pem) {\n        try {\n            var modulus = 0;\n            var public_exponent = 0;\n            var reHex = /^\\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\\s*)+$/;\n            var der = reHex.test(pem) ? Hex.decode(pem) : Base64.unarmor(pem);\n            var asn1 = ASN1.decode(der);\n            // Fixes a bug with OpenSSL 1.0+ private keys\n            if (asn1.sub.length === 3) {\n                asn1 = asn1.sub[2].sub[0];\n            }\n            if (asn1.sub.length === 9) {\n                // Parse the private key.\n                modulus = asn1.sub[1].getHexStringValue(); // bigint\n                this.n = parseBigInt(modulus, 16);\n                public_exponent = asn1.sub[2].getHexStringValue(); // int\n                this.e = parseInt(public_exponent, 16);\n                var private_exponent = asn1.sub[3].getHexStringValue(); // bigint\n                this.d = parseBigInt(private_exponent, 16);\n                var prime1 = asn1.sub[4].getHexStringValue(); // bigint\n                this.p = parseBigInt(prime1, 16);\n                var prime2 = asn1.sub[5].getHexStringValue(); // bigint\n                this.q = parseBigInt(prime2, 16);\n                var exponent1 = asn1.sub[6].getHexStringValue(); // bigint\n                this.dmp1 = parseBigInt(exponent1, 16);\n                var exponent2 = asn1.sub[7].getHexStringValue(); // bigint\n                this.dmq1 = parseBigInt(exponent2, 16);\n                var coefficient = asn1.sub[8].getHexStringValue(); // bigint\n                this.coeff = parseBigInt(coefficient, 16);\n            }\n            else if (asn1.sub.length === 2) {\n                if (asn1.sub[0].sub) {\n                    // Parse ASN.1 SubjectPublicKeyInfo type as defined by X.509\n                    var bit_string = asn1.sub[1];\n                    var sequence = bit_string.sub[0];\n                    modulus = sequence.sub[0].getHexStringValue();\n                    this.n = parseBigInt(modulus, 16);\n                    public_exponent = sequence.sub[1].getHexStringValue();\n                    this.e = parseInt(public_exponent, 16);\n                }\n                else {\n                    // Parse ASN.1 RSAPublicKey type as defined by PKCS #1\n                    modulus = asn1.sub[0].getHexStringValue();\n                    this.n = parseBigInt(modulus, 16);\n                    public_exponent = asn1.sub[1].getHexStringValue();\n                    this.e = parseInt(public_exponent, 16);\n                }\n            }\n            else {\n                return false;\n            }\n            return true;\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Translate rsa parameters in a hex encoded string representing the rsa key.\n     *\n     * The translation follow the ASN.1 notation :\n     * RSAPrivateKey ::= SEQUENCE {\n     *   version           Version,\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER,  -- e\n     *   privateExponent   INTEGER,  -- d\n     *   prime1            INTEGER,  -- p\n     *   prime2            INTEGER,  -- q\n     *   exponent1         INTEGER,  -- d mod (p1)\n     *   exponent2         INTEGER,  -- d mod (q-1)\n     *   coefficient       INTEGER,  -- (inverse of q) mod p\n     * }\n     * @returns {string}  DER Encoded String representing the rsa private key\n     * @private\n     */\n    JSEncryptRSAKey.prototype.getPrivateBaseKey = function () {\n        var options = {\n            array: [\n                new KJUR.asn1.DERInteger({ int: 0 }),\n                new KJUR.asn1.DERInteger({ bigint: this.n }),\n                new KJUR.asn1.DERInteger({ int: this.e }),\n                new KJUR.asn1.DERInteger({ bigint: this.d }),\n                new KJUR.asn1.DERInteger({ bigint: this.p }),\n                new KJUR.asn1.DERInteger({ bigint: this.q }),\n                new KJUR.asn1.DERInteger({ bigint: this.dmp1 }),\n                new KJUR.asn1.DERInteger({ bigint: this.dmq1 }),\n                new KJUR.asn1.DERInteger({ bigint: this.coeff }),\n            ],\n        };\n        var seq = new KJUR.asn1.DERSequence(options);\n        return seq.getEncodedHex();\n    };\n    /**\n     * base64 (pem) encoded version of the DER encoded representation\n     * @returns {string} pem encoded representation without header and footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPrivateBaseKeyB64 = function () {\n        return hex2b64(this.getPrivateBaseKey());\n    };\n    /**\n     * Translate rsa parameters in a hex encoded string representing the rsa public key.\n     * The representation follow the ASN.1 notation :\n     * PublicKeyInfo ::= SEQUENCE {\n     *   algorithm       AlgorithmIdentifier,\n     *   PublicKey       BIT STRING\n     * }\n     * Where AlgorithmIdentifier is:\n     * AlgorithmIdentifier ::= SEQUENCE {\n     *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n     *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n     * }\n     * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n     * RSAPublicKey ::= SEQUENCE {\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER   -- e\n     * }\n     * @returns {string} DER Encoded String representing the rsa public key\n     * @private\n     */\n    JSEncryptRSAKey.prototype.getPublicBaseKey = function () {\n        var first_sequence = new KJUR.asn1.DERSequence({\n            array: [\n                new KJUR.asn1.DERObjectIdentifier({ oid: \"1.2.840.113549.1.1.1\" }),\n                new KJUR.asn1.DERNull(),\n            ],\n        });\n        var second_sequence = new KJUR.asn1.DERSequence({\n            array: [\n                new KJUR.asn1.DERInteger({ bigint: this.n }),\n                new KJUR.asn1.DERInteger({ int: this.e }),\n            ],\n        });\n        var bit_string = new KJUR.asn1.DERBitString({\n            hex: \"00\" + second_sequence.getEncodedHex(),\n        });\n        var seq = new KJUR.asn1.DERSequence({\n            array: [first_sequence, bit_string],\n        });\n        return seq.getEncodedHex();\n    };\n    /**\n     * base64 (pem) encoded version of the DER encoded representation\n     * @returns {string} pem encoded representation without header and footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPublicBaseKeyB64 = function () {\n        return hex2b64(this.getPublicBaseKey());\n    };\n    /**\n     * wrap the string in block of width chars. The default value for rsa keys is 64\n     * characters.\n     * @param {string} str the pem encoded string without header and footer\n     * @param {Number} [width=64] - the length the string has to be wrapped at\n     * @returns {string}\n     * @private\n     */\n    JSEncryptRSAKey.wordwrap = function (str, width) {\n        width = width || 64;\n        if (!str) {\n            return str;\n        }\n        var regex = \"(.{1,\" + width + \"})( +|$\\n?)|(.{1,\" + width + \"})\";\n        return str.match(RegExp(regex, \"g\")).join(\"\\n\");\n    };\n    /**\n     * Retrieve the pem encoded private key\n     * @returns {string} the pem encoded private key with header/footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPrivateKey = function () {\n        var key = \"-----BEGIN RSA PRIVATE KEY-----\\n\";\n        key += JSEncryptRSAKey.wordwrap(this.getPrivateBaseKeyB64()) + \"\\n\";\n        key += \"-----END RSA PRIVATE KEY-----\";\n        return key;\n    };\n    /**\n     * Retrieve the pem encoded public key\n     * @returns {string} the pem encoded public key with header/footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPublicKey = function () {\n        var key = \"-----BEGIN PUBLIC KEY-----\\n\";\n        key += JSEncryptRSAKey.wordwrap(this.getPublicBaseKeyB64()) + \"\\n\";\n        key += \"-----END PUBLIC KEY-----\";\n        return key;\n    };\n    /**\n     * Check if the object contains the necessary parameters to populate the rsa modulus\n     * and public exponent parameters.\n     * @param {Object} [obj={}] - An object that may contain the two public key\n     * parameters\n     * @returns {boolean} true if the object contains both the modulus and the public exponent\n     * properties (n and e)\n     * @todo check for types of n and e. N should be a parseable bigInt object, E should\n     * be a parseable integer number\n     * @private\n     */\n    JSEncryptRSAKey.hasPublicKeyProperty = function (obj) {\n        obj = obj || {};\n        return obj.hasOwnProperty(\"n\") && obj.hasOwnProperty(\"e\");\n    };\n    /**\n     * Check if the object contains ALL the parameters of an RSA key.\n     * @param {Object} [obj={}] - An object that may contain nine rsa key\n     * parameters\n     * @returns {boolean} true if the object contains all the parameters needed\n     * @todo check for types of the parameters all the parameters but the public exponent\n     * should be parseable bigint objects, the public exponent should be a parseable integer number\n     * @private\n     */\n    JSEncryptRSAKey.hasPrivateKeyProperty = function (obj) {\n        obj = obj || {};\n        return (obj.hasOwnProperty(\"n\") &&\n            obj.hasOwnProperty(\"e\") &&\n            obj.hasOwnProperty(\"d\") &&\n            obj.hasOwnProperty(\"p\") &&\n            obj.hasOwnProperty(\"q\") &&\n            obj.hasOwnProperty(\"dmp1\") &&\n            obj.hasOwnProperty(\"dmq1\") &&\n            obj.hasOwnProperty(\"coeff\"));\n    };\n    /**\n     * Parse the properties of obj in the current rsa object. Obj should AT LEAST\n     * include the modulus and public exponent (n, e) parameters.\n     * @param {Object} obj - the object containing rsa parameters\n     * @private\n     */\n    JSEncryptRSAKey.prototype.parsePropertiesFrom = function (obj) {\n        this.n = obj.n;\n        this.e = obj.e;\n        if (obj.hasOwnProperty(\"d\")) {\n            this.d = obj.d;\n            this.p = obj.p;\n            this.q = obj.q;\n            this.dmp1 = obj.dmp1;\n            this.dmq1 = obj.dmq1;\n            this.coeff = obj.coeff;\n        }\n    };\n    return JSEncryptRSAKey;\n}(RSAKey));\nexport { JSEncryptRSAKey };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASG,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,IAAI,QAAQ,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDzB,SAAS,CAACwB,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,GAAG,EAAE;IAC1B,IAAIC,KAAK,GAAGF,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC;IACA;IACA;IACA,IAAIe,GAAG,EAAE;MACL;MACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACzBC,KAAK,CAACC,QAAQ,CAACF,GAAG,CAAC;MACvB,CAAC,MACI,IAAIF,eAAe,CAACK,qBAAqB,CAACH,GAAG,CAAC,IAC/CF,eAAe,CAACM,oBAAoB,CAACJ,GAAG,CAAC,EAAE;QAC3C;QACAC,KAAK,CAACI,mBAAmB,CAACL,GAAG,CAAC;MAClC;IACJ;IACA,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,eAAe,CAACf,SAAS,CAACmB,QAAQ,GAAG,UAAUI,GAAG,EAAE;IAChD,IAAI;MACA,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,KAAK,GAAG,qCAAqC;MACjD,IAAIC,GAAG,GAAGD,KAAK,CAACE,IAAI,CAACL,GAAG,CAAC,GAAGd,GAAG,CAACoB,MAAM,CAACN,GAAG,CAAC,GAAGb,MAAM,CAACoB,OAAO,CAACP,GAAG,CAAC;MACjE,IAAIQ,IAAI,GAAGpB,IAAI,CAACkB,MAAM,CAACF,GAAG,CAAC;MAC3B;MACA,IAAII,IAAI,CAACC,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;QACvBF,IAAI,GAAGA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC;MAC7B;MACA,IAAID,IAAI,CAACC,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;QACvB;QACAT,OAAO,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,CAACC,CAAC,GAAGtB,WAAW,CAACW,OAAO,EAAE,EAAE,CAAC;QACjCC,eAAe,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,CAACE,CAAC,GAAGC,QAAQ,CAACZ,eAAe,EAAE,EAAE,CAAC;QACtC,IAAIa,gBAAgB,GAAGP,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAACzC,CAAC,GAAGoB,WAAW,CAACyB,gBAAgB,EAAE,EAAE,CAAC;QAC1C,IAAIC,MAAM,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACnC,CAAC,GAAGc,WAAW,CAAC0B,MAAM,EAAE,EAAE,CAAC;QAChC,IAAIC,MAAM,GAAGT,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACO,CAAC,GAAG5B,WAAW,CAAC2B,MAAM,EAAE,EAAE,CAAC;QAChC,IAAIE,SAAS,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAACS,IAAI,GAAG9B,WAAW,CAAC6B,SAAS,EAAE,EAAE,CAAC;QACtC,IAAIE,SAAS,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAACW,IAAI,GAAGhC,WAAW,CAAC+B,SAAS,EAAE,EAAE,CAAC;QACtC,IAAIE,WAAW,GAAGf,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,CAACa,KAAK,GAAGlC,WAAW,CAACiC,WAAW,EAAE,EAAE,CAAC;MAC7C,CAAC,MACI,IAAIf,IAAI,CAACC,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAIF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACA,GAAG,EAAE;UACjB;UACA,IAAIgB,UAAU,GAAGjB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;UAC5B,IAAIiB,QAAQ,GAAGD,UAAU,CAAChB,GAAG,CAAC,CAAC,CAAC;UAChCR,OAAO,GAAGyB,QAAQ,CAACjB,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC;UAC7C,IAAI,CAACC,CAAC,GAAGtB,WAAW,CAACW,OAAO,EAAE,EAAE,CAAC;UACjCC,eAAe,GAAGwB,QAAQ,CAACjB,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC;UACrD,IAAI,CAACE,CAAC,GAAGC,QAAQ,CAACZ,eAAe,EAAE,EAAE,CAAC;QAC1C,CAAC,MACI;UACD;UACAD,OAAO,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC;UACzC,IAAI,CAACC,CAAC,GAAGtB,WAAW,CAACW,OAAO,EAAE,EAAE,CAAC;UACjCC,eAAe,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAACE,iBAAiB,CAAC,CAAC;UACjD,IAAI,CAACE,CAAC,GAAGC,QAAQ,CAACZ,eAAe,EAAE,EAAE,CAAC;QAC1C;MACJ,CAAC,MACI;QACD,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CACD,OAAOyB,EAAE,EAAE;MACP,OAAO,KAAK;IAChB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInC,eAAe,CAACf,SAAS,CAACmD,iBAAiB,GAAG,YAAY;IACtD,IAAIC,OAAO,GAAG;MACVC,KAAK,EAAE,CACH,IAAIvC,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC,CAAC,EACpC,IAAIzC,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACrB;MAAE,CAAC,CAAC,EAC5C,IAAIrB,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEC,GAAG,EAAE,IAAI,CAACnB;MAAE,CAAC,CAAC,EACzC,IAAItB,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAAC/D;MAAE,CAAC,CAAC,EAC5C,IAAIqB,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACzD;MAAE,CAAC,CAAC,EAC5C,IAAIe,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACf;MAAE,CAAC,CAAC,EAC5C,IAAI3B,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACb;MAAK,CAAC,CAAC,EAC/C,IAAI7B,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACX;MAAK,CAAC,CAAC,EAC/C,IAAI/B,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACT;MAAM,CAAC,CAAC;IAExD,CAAC;IACD,IAAIU,GAAG,GAAG,IAAI3C,IAAI,CAACiB,IAAI,CAAC2B,WAAW,CAACN,OAAO,CAAC;IAC5C,OAAOK,GAAG,CAACE,aAAa,CAAC,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI5C,eAAe,CAACf,SAAS,CAAC4D,oBAAoB,GAAG,YAAY;IACzD,OAAOpD,OAAO,CAAC,IAAI,CAAC2C,iBAAiB,CAAC,CAAC,CAAC;EAC5C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpC,eAAe,CAACf,SAAS,CAAC6D,gBAAgB,GAAG,YAAY;IACrD,IAAIC,cAAc,GAAG,IAAIhD,IAAI,CAACiB,IAAI,CAAC2B,WAAW,CAAC;MAC3CL,KAAK,EAAE,CACH,IAAIvC,IAAI,CAACiB,IAAI,CAACgC,mBAAmB,CAAC;QAAEC,GAAG,EAAE;MAAuB,CAAC,CAAC,EAClE,IAAIlD,IAAI,CAACiB,IAAI,CAACkC,OAAO,CAAC,CAAC;IAE/B,CAAC,CAAC;IACF,IAAIC,eAAe,GAAG,IAAIpD,IAAI,CAACiB,IAAI,CAAC2B,WAAW,CAAC;MAC5CL,KAAK,EAAE,CACH,IAAIvC,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEE,MAAM,EAAE,IAAI,CAACrB;MAAE,CAAC,CAAC,EAC5C,IAAIrB,IAAI,CAACiB,IAAI,CAACuB,UAAU,CAAC;QAAEC,GAAG,EAAE,IAAI,CAACnB;MAAE,CAAC,CAAC;IAEjD,CAAC,CAAC;IACF,IAAIY,UAAU,GAAG,IAAIlC,IAAI,CAACiB,IAAI,CAACoC,YAAY,CAAC;MACxCC,GAAG,EAAE,IAAI,GAAGF,eAAe,CAACP,aAAa,CAAC;IAC9C,CAAC,CAAC;IACF,IAAIF,GAAG,GAAG,IAAI3C,IAAI,CAACiB,IAAI,CAAC2B,WAAW,CAAC;MAChCL,KAAK,EAAE,CAACS,cAAc,EAAEd,UAAU;IACtC,CAAC,CAAC;IACF,OAAOS,GAAG,CAACE,aAAa,CAAC,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI5C,eAAe,CAACf,SAAS,CAACqE,mBAAmB,GAAG,YAAY;IACxD,OAAO7D,OAAO,CAAC,IAAI,CAACqD,gBAAgB,CAAC,CAAC,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI9C,eAAe,CAACuD,QAAQ,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC7CA,KAAK,GAAGA,KAAK,IAAI,EAAE;IACnB,IAAI,CAACD,GAAG,EAAE;MACN,OAAOA,GAAG;IACd;IACA,IAAIE,KAAK,GAAG,OAAO,GAAGD,KAAK,GAAG,mBAAmB,GAAGA,KAAK,GAAG,IAAI;IAChE,OAAOD,GAAG,CAACG,KAAK,CAACC,MAAM,CAACF,KAAK,EAAE,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;EACnD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI7D,eAAe,CAACf,SAAS,CAAC6E,aAAa,GAAG,YAAY;IAClD,IAAI5D,GAAG,GAAG,mCAAmC;IAC7CA,GAAG,IAAIF,eAAe,CAACuD,QAAQ,CAAC,IAAI,CAACV,oBAAoB,CAAC,CAAC,CAAC,GAAG,IAAI;IACnE3C,GAAG,IAAI,+BAA+B;IACtC,OAAOA,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,eAAe,CAACf,SAAS,CAAC8E,YAAY,GAAG,YAAY;IACjD,IAAI7D,GAAG,GAAG,8BAA8B;IACxCA,GAAG,IAAIF,eAAe,CAACuD,QAAQ,CAAC,IAAI,CAACD,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI;IAClEpD,GAAG,IAAI,0BAA0B;IACjC,OAAOA,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,eAAe,CAACM,oBAAoB,GAAG,UAAU0D,GAAG,EAAE;IAClDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,OAAOA,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IAAI8E,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC;EAC7D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,eAAe,CAACK,qBAAqB,GAAG,UAAU2D,GAAG,EAAE;IACnDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,OAAQA,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IAC3B8E,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IACvB8E,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IACvB8E,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IACvB8E,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,IACvB8E,GAAG,CAAC9E,cAAc,CAAC,MAAM,CAAC,IAC1B8E,GAAG,CAAC9E,cAAc,CAAC,MAAM,CAAC,IAC1B8E,GAAG,CAAC9E,cAAc,CAAC,OAAO,CAAC;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIc,eAAe,CAACf,SAAS,CAACsB,mBAAmB,GAAG,UAAUyD,GAAG,EAAE;IAC3D,IAAI,CAAC5C,CAAC,GAAG4C,GAAG,CAAC5C,CAAC;IACd,IAAI,CAACC,CAAC,GAAG2C,GAAG,CAAC3C,CAAC;IACd,IAAI2C,GAAG,CAAC9E,cAAc,CAAC,GAAG,CAAC,EAAE;MACzB,IAAI,CAACR,CAAC,GAAGsF,GAAG,CAACtF,CAAC;MACd,IAAI,CAACM,CAAC,GAAGgF,GAAG,CAAChF,CAAC;MACd,IAAI,CAAC0C,CAAC,GAAGsC,GAAG,CAACtC,CAAC;MACd,IAAI,CAACE,IAAI,GAAGoC,GAAG,CAACpC,IAAI;MACpB,IAAI,CAACE,IAAI,GAAGkC,GAAG,CAAClC,IAAI;MACpB,IAAI,CAACE,KAAK,GAAGgC,GAAG,CAAChC,KAAK;IAC1B;EACJ,CAAC;EACD,OAAOhC,eAAe;AAC1B,CAAC,CAACH,MAAM,CAAE;AACV,SAASG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}