{"ast": null, "code": "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n  const newColor = {\n    r: convertToPercentage(ratio.r),\n    g: convertToPercentage(ratio.g),\n    b: convertToPercentage(ratio.b)\n  };\n  if (ratio.a !== undefined) {\n    newColor.a = Number(ratio.a);\n  }\n  return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n  return new TinyColor({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n}", "map": {"version": 3, "names": ["TinyColor", "convertToPercentage", "fromRatio", "ratio", "opts", "newColor", "r", "g", "b", "a", "undefined", "Number", "legacyRandom", "Math", "random"], "sources": ["G:/web/central-platform-tas-web/node_modules/@ctrl/tinycolor/dist/module/from-ratio.js"], "sourcesContent": ["import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    const newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,mBAAmB,QAAQ,WAAW;AAC/C;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACnC,MAAMC,QAAQ,GAAG;IACbC,CAAC,EAAEL,mBAAmB,CAACE,KAAK,CAACG,CAAC,CAAC;IAC/BC,CAAC,EAAEN,mBAAmB,CAACE,KAAK,CAACI,CAAC,CAAC;IAC/BC,CAAC,EAAEP,mBAAmB,CAACE,KAAK,CAACK,CAAC;EAClC,CAAC;EACD,IAAIL,KAAK,CAACM,CAAC,KAAKC,SAAS,EAAE;IACvBL,QAAQ,CAACI,CAAC,GAAGE,MAAM,CAACR,KAAK,CAACM,CAAC,CAAC;EAChC;EACA,OAAO,IAAIT,SAAS,CAACK,QAAQ,EAAED,IAAI,CAAC;AACxC;AACA;AACA,OAAO,SAASQ,YAAYA,CAAA,EAAG;EAC3B,OAAO,IAAIZ,SAAS,CAAC;IACjBM,CAAC,EAAEO,IAAI,CAACC,MAAM,CAAC,CAAC;IAChBP,CAAC,EAAEM,IAAI,CAACC,MAAM,CAAC,CAAC;IAChBN,CAAC,EAAEK,IAAI,CAACC,MAAM,CAAC;EACnB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}