{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CraneComponent } from './crane.component';\nimport { CraneEditComponent } from '@business/tas/crane/crane-edit/crane-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: CraneComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: CraneEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class CraneRoutingModule {\n  static {\n    this.ɵfac = function CraneRoutingModule_Factory(t) {\n      return new (t || CraneRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CraneRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CraneRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CraneComponent", "CraneEditComponent", "routes", "path", "component", "data", "cache", "CraneRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\crane\\crane-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CraneComponent } from './crane.component';\r\nimport {CraneEditComponent} from '@business/tas/crane/crane-edit/crane-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: CraneComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: CraneEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CraneRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAAQC,kBAAkB,QAAO,qDAAqD;;;AACtF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,cAAc;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EAClE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,kBAAkB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAC5E;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFnBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}