{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['B', 'คศ'],\n  abbreviated: ['BC', 'ค.ศ.'],\n  wide: ['ปีก่อนคริสตกาล', 'คริสต์ศักราช']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['ไตรมาสแรก', 'ไตรมาสที่สอง', 'ไตรมาสที่สาม', 'ไตรมาสที่สี่']\n};\nvar dayValues = {\n  narrow: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  short: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  abbreviated: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  wide: ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์']\n};\nvar monthValues = {\n  narrow: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  abbreviated: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  wide: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "dayV<PERSON><PERSON>", "short", "month<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/th/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['B', 'คศ'],\n  abbreviated: ['BC', 'ค.ศ.'],\n  wide: ['ปีก่อนคริสตกาล', 'คริสต์ศักราช']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['ไตรมาสแรก', 'ไตรมาสที่สอง', 'ไตรมาสที่สาม', 'ไตรมาสที่สี่']\n};\nvar dayValues = {\n  narrow: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  short: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  abbreviated: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  wide: ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์']\n};\nvar monthValues = {\n  narrow: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  abbreviated: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  wide: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACnBC,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BC,IAAI,EAAE,CAAC,gBAAgB,EAAE,cAAc;AACzC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACpE,CAAC;AACD,IAAIE,SAAS,GAAG;EACdJ,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACpDK,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACnDJ,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACzDC,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO;AAC3E,CAAC;AACD,IAAII,WAAW,GAAG;EAChBN,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC3GC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAChHC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS;AAC7I,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEjB,WAAW;IACnBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}