{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { KaPartnerComponent } from './kaPartner.component';\nimport { KaPartnerRoutingModule } from './kaPartner-routing.module';\nimport { KaPartnerEditComponent } from '@business/tas/kaPartner/kaPartner-edit/kaPartner-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [KaPartnerComponent, KaPartnerEditComponent];\nexport class KaPartnerModule {\n  static {\n    this.ɵfac = function KaPartnerModule_Factory(t) {\n      return new (t || KaPartnerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: KaPartnerModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, KaPartnerRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(KaPartnerModule, {\n    declarations: [KaPartnerComponent, KaPartnerEditComponent],\n    imports: [SharedModule, KaPartnerRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "KaPartnerComponent", "KaPartnerRoutingModule", "KaPartnerEditComponent", "COMPONENTS", "KaPartnerModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\kaPartner\\kaPartner.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { KaPartnerComponent } from './kaPartner.component';\r\nimport { KaPartnerRoutingModule } from './kaPartner-routing.module';\r\nimport {KaPartnerEditComponent} from '@business/tas/kaPartner/kaPartner-edit/kaPartner-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  KaPartnerComponent,\r\n  KaPartnerEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, KaPartnerRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class KaPartnerModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}