{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'ಕಳೆದ' eeee p 'ಕ್ಕೆ'\",\n  yesterday: \"'ನಿನ್ನೆ' p 'ಕ್ಕೆ'\",\n  today: \"'ಇಂದು' p 'ಕ್ಕೆ'\",\n  tomorrow: \"'ನಾಳೆ' p 'ಕ್ಕೆ'\",\n  nextWeek: \"eeee p 'ಕ್ಕೆ'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/kn/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'ಕಳೆದ' eeee p 'ಕ್ಕೆ'\",\n  yesterday: \"'ನಿನ್ನೆ' p 'ಕ್ಕೆ'\",\n  today: \"'ಇಂದು' p 'ಕ್ಕೆ'\",\n  tomorrow: \"'ನಾಳೆ' p 'ಕ್ಕೆ'\",\n  nextWeek: \"eeee p 'ಕ್ಕೆ'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,sBAAsB;EAChCC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}