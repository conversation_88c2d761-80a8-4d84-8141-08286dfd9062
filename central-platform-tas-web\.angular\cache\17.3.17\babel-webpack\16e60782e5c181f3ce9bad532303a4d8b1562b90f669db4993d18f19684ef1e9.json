{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { WoodComponent } from './wood.component';\nimport { WoodEditComponent } from '@business/tas/wood/wood-edit/wood-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: WoodComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: WoodEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class WoodRoutingModule {\n  static {\n    this.ɵfac = function WoodRoutingModule_Factory(t) {\n      return new (t || WoodRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WoodRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WoodRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "WoodComponent", "WoodEditComponent", "routes", "path", "component", "data", "cache", "WoodRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\wood\\wood-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { WoodComponent } from './wood.component';\r\nimport {WoodEditComponent} from '@business/tas/wood/wood-edit/wood-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: WoodComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: WoodEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class WoodRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAAQC,iBAAiB,QAAO,kDAAkD;;;AAClF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,aAAa;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACjE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,iBAAiB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAC3E;AAMD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,iBAAiB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFlBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}