{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { CompareCodeComponent } from './comparecode.component';\nimport { CompareCodeRoutingModule } from './comparecode-routing.module';\nimport { CompareCodeEditComponent } from '@business/tas/comparecode/comparecode-edit/comparecode-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [CompareCodeComponent, CompareCodeEditComponent];\nexport class CompareCodeModule {\n  static {\n    this.ɵfac = function CompareCodeModule_Factory(t) {\n      return new (t || CompareCodeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CompareCodeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CompareCodeRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CompareCodeModule, {\n    declarations: [CompareCodeComponent, CompareCodeEditComponent],\n    imports: [SharedModule, CompareCodeRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "CompareCodeComponent", "CompareCodeRoutingModule", "CompareCodeEditComponent", "COMPONENTS", "CompareCodeModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\comparecode\\comparecode.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { CompareCodeComponent } from './comparecode.component';\r\nimport { CompareCodeRoutingModule } from './comparecode-routing.module';\r\nimport {CompareCodeEditComponent} from '@business/tas/comparecode/comparecode-edit/comparecode-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  CompareCodeComponent,\r\n  CompareCodeEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, CompareCodeRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class CompareCodeModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}