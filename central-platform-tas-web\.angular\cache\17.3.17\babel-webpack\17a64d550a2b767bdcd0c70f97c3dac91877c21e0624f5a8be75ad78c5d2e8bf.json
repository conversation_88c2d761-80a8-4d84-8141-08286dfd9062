{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.е.', 'н.е.'],\n  abbreviated: ['до н. е.', 'н. е.'],\n  wide: ['до нашої ери', 'нашої ери']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  // ДСТУ 3582:2013\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січень', 'лютий', 'березень', 'квітень', 'травень', 'червень', 'липень', 'серпень', 'вересень', 'жовтень', 'листопад', 'грудень']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вів', 'сер', 'чтв', 'птн', 'суб'],\n  wide: ['неділя', 'понеділок', 'вівторок', 'середа', 'четвер', 'п’ятниця', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'вечір',\n    night: 'ніч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n  if (unit === 'date') {\n    if (number === 3 || number === 23) {\n      suffix = '-є';\n    } else {\n      suffix = '-е';\n    }\n  } else if (unit === 'minute' || unit === 'second' || unit === 'hour') {\n    suffix = '-а';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/uk/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.е.', 'н.е.'],\n  abbreviated: ['до н. е.', 'н. е.'],\n  wide: ['до нашої ери', 'нашої ери']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  // ДСТУ 3582:2013\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січень', 'лютий', 'березень', 'квітень', 'травень', 'червень', 'липень', 'серпень', 'вересень', 'жовтень', 'листопад', 'грудень']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вів', 'сер', 'чтв', 'птн', 'суб'],\n  wide: ['неділя', 'понеділок', 'вівторок', 'середа', 'четвер', 'п’ятниця', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'вечір',\n    night: 'ніч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n  if (unit === 'date') {\n    if (number === 3 || number === 23) {\n      suffix = '-є';\n    } else {\n      suffix = '-е';\n    }\n  } else if (unit === 'minute' || unit === 'second' || unit === 'hour') {\n    suffix = '-а';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChB;EACAJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;EAC1HC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS;AAC1I,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;EAC1HC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ;AAClI,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ;AACpF,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,IAAI,GAAGC,MAAM,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,IAAI,CAAC;EACjF,IAAIE,MAAM,GAAGC,MAAM,CAACL,WAAW,CAAC;EAChC,IAAIM,MAAM;EACV,IAAIJ,IAAI,KAAK,MAAM,EAAE;IACnB,IAAIE,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,EAAE,EAAE;MACjCE,MAAM,GAAG,IAAI;IACf,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI;IACf;EACF,CAAC,MAAM,IAAIJ,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,MAAM,EAAE;IACpEI,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GAAG,IAAI;EACf;EACA,OAAOF,MAAM,GAAGE,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbR,aAAa,EAAEA,aAAa;EAC5BS,GAAG,EAAE7B,eAAe,CAAC;IACnB8B,MAAM,EAAE7B,SAAS;IACjB8B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAEhC,eAAe,CAAC;IACvB8B,MAAM,EAAEzB,aAAa;IACrB0B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAElC,eAAe,CAAC;IACrB8B,MAAM,EAAExB,WAAW;IACnByB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE5B,qBAAqB;IACvC6B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAErC,eAAe,CAAC;IACnB8B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAEtC,eAAe,CAAC;IACzB8B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}