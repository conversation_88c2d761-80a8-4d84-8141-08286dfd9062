{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EmailTemplateComponent } from './emailtemplate.component';\nimport { EmailTemplateEditComponent } from '@business/tas/emailtemplate/emailtemplate-edit/emailtemplate-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: EmailTemplateComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: EmailTemplateEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class EmailTemplateRoutingModule {\n  static {\n    this.ɵfac = function EmailTemplateRoutingModule_Factory(t) {\n      return new (t || EmailTemplateRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EmailTemplateRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EmailTemplateRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "EmailTemplateComponent", "EmailTemplateEditComponent", "routes", "path", "component", "data", "cache", "EmailTemplateRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\emailtemplate\\emailtemplate-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { EmailTemplateComponent } from './emailtemplate.component';\r\nimport {EmailTemplateEditComponent} from '@business/tas/emailtemplate/emailtemplate-edit/emailtemplate-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: EmailTemplateComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: EmailTemplateEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class EmailTemplateRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAAQC,0BAA0B,QAAO,6EAA6E;;;AACtH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,sBAAsB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EAC1E;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,0BAA0B;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACpF;AAMD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAF3BZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}