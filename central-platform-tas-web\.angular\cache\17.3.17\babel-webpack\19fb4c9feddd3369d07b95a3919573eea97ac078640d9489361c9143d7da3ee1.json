{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { KaPartnerComponent } from './kaPartner.component';\nimport { KaPartnerEditComponent } from '@business/tas/kaPartner/kaPartner-edit/kaPartner-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: KaPartnerComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: KaPartnerEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class KaPartnerRoutingModule {\n  static {\n    this.ɵfac = function KaPartnerRoutingModule_Factory(t) {\n      return new (t || KaPartnerRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: KaPartnerRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(KaPartnerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "KaPartnerComponent", "KaPartnerEditComponent", "routes", "path", "component", "data", "cache", "KaPartnerRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\kaPartner\\kaPartner-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { KaPartnerComponent } from './kaPartner.component';\r\nimport {KaPartnerEditComponent} from '@business/tas/kaPartner/kaPartner-edit/kaPartner-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: KaPartnerComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: KaPartnerEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class KaPartnerRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAAQC,sBAAsB,QAAO,iEAAiE;;;AACtG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,kBAAkB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACtE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,sBAAsB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAChF;AAMD,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFvBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}