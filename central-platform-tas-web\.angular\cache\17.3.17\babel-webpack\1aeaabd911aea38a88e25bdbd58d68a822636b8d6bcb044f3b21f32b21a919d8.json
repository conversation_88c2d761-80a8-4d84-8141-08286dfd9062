{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_INTERCONFIG extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"接口配置表主键\",\n      \"interface_nm\": \"接口名称\",\n      \"docking_party\": \"对接方\",\n      \"svg_tag\": \"发送/接收类型\",\n      \"implementation\": \"接口实现方式\",\n      \"class_nm\": \"接口类名\",\n      \"method_nm\": \"接口方法名\",\n      \"org_id\": \"所属组织主键\",\n      \"org_nm\": \"所属组织机构名称\",\n      \"org_level_no\": \"所属组织机构代码\",\n      \"ent_level_no\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_INTERFACE_CONFIG'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_INTERCONFIG", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_INTERCONFIG.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_INTERCONFIG extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_INTERFACE_CONFIG'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"接口配置表主键\",\r\n      \"interface_nm\":\"接口名称\",\r\n      \"docking_party\":\"对接方\",\r\n      \"svg_tag\":\"发送/接收类型\",\r\n      \"implementation\":\"接口实现方式\",\r\n      \"class_nm\":\"接口类名\",\r\n      \"method_nm\":\"接口方法名\",\r\n      \"org_id\":\"所属组织主键\",\r\n      \"org_nm\":\"所属组织机构名称\",\r\n      \"org_level_no\":\"所属组织机构代码\",\r\n      \"ent_level_no\":\"所属公司代码\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,kBAAmB,SAAQD,QAAQ;EAQ9CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,SAAS,EAAC,SAAS;MACnB,gBAAgB,EAAC,QAAQ;MACzB,UAAU,EAAC,MAAM;MACjB,WAAW,EAAC,OAAO;MACnB,QAAQ,EAAC,QAAQ;MACjB,QAAQ,EAAC,UAAU;MACnB,cAAc,EAAC,UAAU;MACzB,cAAc,EAAC,QAAQ;MACvB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IA3BJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,yBAAyB,CAAC,CAAC;IACvC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAwBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}