{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { IngeneralcargoComponent } from './ingeneralcargo.component';\nimport { IngeneralcargoRoutingModule } from './ingeneralcargo-routing.module';\nimport { IngeneralcargoEditComponent } from '@business/tas/ingeneralcargo/ingeneralcargo-edit/ingeneralcargo-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [IngeneralcargoComponent, IngeneralcargoEditComponent];\nexport class IngeneralcargoModule {\n  static {\n    this.ɵfac = function IngeneralcargoModule_Factory(t) {\n      return new (t || IngeneralcargoModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IngeneralcargoModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, IngeneralcargoRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IngeneralcargoModule, {\n    declarations: [IngeneralcargoComponent, IngeneralcargoEditComponent],\n    imports: [SharedModule, IngeneralcargoRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "IngeneralcargoComponent", "IngeneralcargoRoutingModule", "IngeneralcargoEditComponent", "COMPONENTS", "IngeneralcargoModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\ingeneralcargo\\ingeneralcargo.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { IngeneralcargoComponent } from './ingeneralcargo.component';\r\nimport { IngeneralcargoRoutingModule } from './ingeneralcargo-routing.module';\r\nimport {IngeneralcargoEditComponent} from '@business/tas/ingeneralcargo/ingeneralcargo-edit/ingeneralcargo-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  IngeneralcargoComponent,\r\n  IngeneralcargoEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, IngeneralcargoRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class IngeneralcargoModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAAQC,2BAA2B,QAAO,gFAAgF;;AAE1H,MAAMC,UAAU,GAAG,CACjBH,uBAAuB,EACvBE,2BAA2B,CAC5B;AAMD,OAAM,MAAOE,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBN,YAAY,EAAEG,2BAA2B,EAAEF,YAAY;IAAA;EAAA;;;2EAGtDK,oBAAoB;IAAAC,YAAA,GAR/BL,uBAAuB,EACvBE,2BAA2B;IAAAI,OAAA,GAIjBR,YAAY,EAAEG,2BAA2B,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}