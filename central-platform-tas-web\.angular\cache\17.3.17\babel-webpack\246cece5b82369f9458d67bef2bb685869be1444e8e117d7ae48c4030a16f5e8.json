{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { WoodComponent } from './wood.component';\nimport { WoodRoutingModule } from './wood-routing.module';\nimport { WoodEditComponent } from '@business/tas/wood/wood-edit/wood-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [WoodComponent, WoodEditComponent];\nexport class WoodModule {\n  static {\n    this.ɵfac = function WoodModule_Factory(t) {\n      return new (t || WoodModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WoodModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, WoodRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WoodModule, {\n    declarations: [WoodComponent, WoodEditComponent],\n    imports: [SharedModule, WoodRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "WoodComponent", "WoodRoutingModule", "WoodEditComponent", "COMPONENTS", "WoodModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\wood\\wood.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { WoodComponent } from './wood.component';\r\nimport { WoodRoutingModule } from './wood-routing.module';\r\nimport {WoodEditComponent} from '@business/tas/wood/wood-edit/wood-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  WoodComponent,\r\n  WoodEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, WoodRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class WoodModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAAQC,iBAAiB,QAAO,kDAAkD;;AAElF,MAAMC,UAAU,GAAG,CACjBH,aAAa,EACbE,iBAAiB,CAClB;AAMD,OAAM,MAAOE,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAHXN,YAAY,EAAEG,iBAAiB,EAAEF,YAAY;IAAA;EAAA;;;2EAG5CK,UAAU;IAAAC,YAAA,GARrBL,aAAa,EACbE,iBAAiB;IAAAI,OAAA,GAIPR,YAAY,EAAEG,iBAAiB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}