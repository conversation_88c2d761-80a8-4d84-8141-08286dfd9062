{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    },\n    withPreposition: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    }\n  },\n  halfAMinute: {\n    standalone: 'halbe Minute',\n    withPreposition: 'halben Minute'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    },\n    withPreposition: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    },\n    withPreposition: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    },\n    withPreposition: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: '1 Tag',\n      other: '{{count}} Tage'\n    },\n    withPreposition: {\n      one: '1 Tag',\n      other: '{{count}} Tagen'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monate'\n    },\n    withPreposition: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monaten'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: '1 Monat',\n      other: '{{count}} Monate'\n    },\n    withPreposition: {\n      one: '1 Monat',\n      other: '{{count}} Monaten'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahren'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: '1 Jahr',\n      other: '{{count}} Jahre'\n    },\n    withPreposition: {\n      one: '1 Jahr',\n      other: '{{count}} Jahren'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahren'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahren'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return 'vor ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "replace", "String", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/de/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    },\n    withPreposition: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    }\n  },\n  halfAMinute: {\n    standalone: 'halbe Minute',\n    withPreposition: 'halben Minute'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    },\n    withPreposition: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    },\n    withPreposition: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    },\n    withPreposition: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: '1 Tag',\n      other: '{{count}} Tage'\n    },\n    withPreposition: {\n      one: '1 Tag',\n      other: '{{count}} Tagen'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monate'\n    },\n    withPreposition: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monaten'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: '1 Monat',\n      other: '{{count}} Monate'\n    },\n    withPreposition: {\n      one: '1 Monat',\n      other: '{{count}} Monaten'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahren'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: '1 Jahr',\n      other: '{{count}} Jahre'\n    },\n    withPreposition: {\n      one: '1 Jahr',\n      other: '{{count}} Jahren'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahren'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahren'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return 'vor ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EACDE,QAAQ,EAAE;IACRJ,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,UAAU,EAAE,cAAc;IAC1BG,eAAe,EAAE;EACnB,CAAC;EACDG,gBAAgB,EAAE;IAChBN,UAAU,EAAE;MACVC,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT;EACF,CAAC;EACDK,QAAQ,EAAE;IACRP,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT;EACF,CAAC;EACDM,WAAW,EAAE;IACXR,UAAU,EAAE;MACVC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EACDO,MAAM,EAAE;IACNT,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT;EACF,CAAC;EACDQ,KAAK,EAAE;IACLV,UAAU,EAAE;MACVC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;IACT;EACF,CAAC;EACDS,WAAW,EAAE;IACXX,UAAU,EAAE;MACVC,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EACDU,MAAM,EAAE;IACNZ,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACDW,YAAY,EAAE;IACZb,UAAU,EAAE;MACVC,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EACDY,OAAO,EAAE;IACPd,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACDa,WAAW,EAAE;IACXf,UAAU,EAAE;MACVC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDc,MAAM,EAAE;IACNhB,UAAU,EAAE;MACVC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;IACT;EACF,CAAC;EACDe,UAAU,EAAE;IACVjB,UAAU,EAAE;MACVC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT;EACF,CAAC;EACDgB,YAAY,EAAE;IACZlB,UAAU,EAAE;MACVC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,GAAG3B,oBAAoB,CAACsB,KAAK,CAAC,CAACjB,eAAe,GAAGL,oBAAoB,CAACsB,KAAK,CAAC,CAACpB,UAAU;EACnK,IAAI,OAAOwB,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACLsB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;IAC/D,IAAIH,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,MAAM,GAAGA,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}