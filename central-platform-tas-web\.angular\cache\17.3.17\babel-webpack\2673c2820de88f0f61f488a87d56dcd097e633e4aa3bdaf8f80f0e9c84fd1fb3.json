{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { CraneComponent } from './crane.component';\nimport { CraneRoutingModule } from './crane-routing.module';\nimport { CraneEditComponent } from '@business/tas/crane/crane-edit/crane-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [CraneComponent, CraneEditComponent];\nexport class CraneModule {\n  static {\n    this.ɵfac = function CraneModule_Factory(t) {\n      return new (t || CraneModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CraneModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CraneRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CraneModule, {\n    declarations: [CraneComponent, CraneEditComponent],\n    imports: [SharedModule, CraneRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "CraneComponent", "CraneRoutingModule", "CraneEditComponent", "COMPONENTS", "CraneModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\crane\\crane.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { CraneComponent } from './crane.component';\r\nimport { CraneRoutingModule } from './crane-routing.module';\r\nimport {CraneEditComponent} from '@business/tas/crane/crane-edit/crane-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  CraneComponent,\r\n  CraneEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, CraneRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class CraneModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAAQC,kBAAkB,QAAO,qDAAqD;;AAEtF,MAAMC,UAAU,GAAG,CACjBH,cAAc,EACdE,kBAAkB,CACnB;AAMD,OAAM,MAAOE,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZN,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;IAAA;EAAA;;;2EAG7CK,WAAW;IAAAC,YAAA,GARtBL,cAAc,EACdE,kBAAkB;IAAAI,OAAA,GAIRR,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}