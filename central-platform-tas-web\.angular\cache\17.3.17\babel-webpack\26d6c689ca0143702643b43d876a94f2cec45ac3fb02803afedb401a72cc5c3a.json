{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { TruckComponent } from './truck.component';\nimport { TruckRoutingModule } from './truck-routing.module';\nimport { TruckEditComponent } from '@business/tas/truck/truck-edit/truck-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [TruckComponent, TruckEditComponent];\nexport class TruckModule {\n  static {\n    this.ɵfac = function TruckModule_Factory(t) {\n      return new (t || TruckModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TruckModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, TruckRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TruckModule, {\n    declarations: [TruckComponent, TruckEditComponent],\n    imports: [SharedModule, TruckRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "TruckComponent", "TruckRoutingModule", "TruckEditComponent", "COMPONENTS", "TruckModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\truck\\truck.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { TruckComponent } from './truck.component';\r\nimport { TruckRoutingModule } from './truck-routing.module';\r\nimport {TruckEditComponent} from '@business/tas/truck/truck-edit/truck-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  TruckComponent,\r\n  TruckEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, TruckRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class TruckModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAAQC,kBAAkB,QAAO,qDAAqD;;AAEtF,MAAMC,UAAU,GAAG,CACjBH,cAAc,EACdE,kBAAkB,CACnB;AAMD,OAAM,MAAOE,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZN,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;IAAA;EAAA;;;2EAG7CK,WAAW;IAAAC,YAAA,GARtBL,cAAc,EACdE,kBAAkB;IAAAI,OAAA,GAIRR,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}