{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\n/**\n * 空模板\n */\nexport class EMPTY_TEMPLATE extends CwfStore {\n  constructor() {\n    super(\n    // 字段信息\n    {\n      \"id\": \"\"\n    });\n    this.actionId = \"\";\n    this.queryOperation = \"\";\n    this.saveOperation = \"\";\n    this.talbeName = \"EMPTY_TEMPLATE\";\n    this.serviceName = \"\";\n    this.idProperty = \"id\";\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "EMPTY_TEMPLATE", "constructor", "actionId", "queryOperation", "saveOperation", "talbeName", "serviceName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\EMPTY_TEMPLATE.ts"], "sourcesContent": ["import {CwfStore} from 'cwf-ng-library';\r\n\r\n/**\r\n * 空模板\r\n */\r\nexport class EMPTY_TEMPLATE extends CwfStore {\r\n    actionId = \"\";\r\n    queryOperation = \"\";\r\n    saveOperation = \"\";\r\n    talbeName = \"EMPTY_TEMPLATE\";\r\n    serviceName = \"\";\r\n    idProperty = \"id\";\r\n\r\n    constructor() {\r\n        super(\r\n            // 字段信息\r\n            {\r\n                \"id\": \"\",\r\n            });\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,gBAAgB;AAEvC;;;AAGA,OAAM,MAAOC,cAAe,SAAQD,QAAQ;EAQxCE,YAAA;IACI,KAAK;IACD;IACA;MACI,IAAI,EAAE;KACT,CAAC;IAZV,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,SAAS,GAAG,gBAAgB;IAC5B,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,UAAU,GAAG,IAAI;EAQjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}