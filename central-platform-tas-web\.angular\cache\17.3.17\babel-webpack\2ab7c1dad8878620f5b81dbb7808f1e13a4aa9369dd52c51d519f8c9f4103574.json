{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_VESSEL_BAY extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"主键\",\n      \"vesselId\": \"船舶主表主键\",\n      \"bayNo\": \"贝号\",\n      \"rowtier\": \"贝位信息\",\n      \"dtier\": \"甲板层数\",\n      \"htier\": \"舱底层数\",\n      \"drow\": \"甲板行数\",\n      \"hrow\": \"舱底行数\",\n      \"dfrom\": \"甲板起始坐标\",\n      \"dstep\": \"甲板步长\",\n      \"hfrom\": \"舱底起始坐标\",\n      \"hstep\": \"舱底步长\",\n      \"bigctnTag\": \"是否只装大箱\",\n      \"tiermark\": \"层标记\",\n      \"dtierDetail\": \"甲板层数明细，用逗号拼接\",\n      \"drowDetail\": \"甲板行数明细，用逗号拼接\",\n      \"htierDetail\": \"舱底层数明细，用逗号拼接\",\n      \"hrowDetail\": \"舱底行数明细，用逗号拼接\",\n      \"drowFromZeroTag\": \"甲板行数是否从0开始:Y/N\",\n      \"hrowFromZeroTag\": \"舱底行数是否从0开始:Y/N\",\n      \"orgId\": \"所属组织机构主键\",\n      \"orgLevelNo\": \"所属组织机构代码\",\n      \"entLevelNo\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"isDelete\": \"是否删除,1：是,0：否\",\n      \"version\": \"版本号\",\n      \"tenantId\": \"租户ID\",\n      \"createdUser\": \"新建人ID\",\n      \"createdTime\": \"创建时间\",\n      \"modifiedUser\": \"修改人ID\",\n      \"modifiedTime\": \"修改时间\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_VESSEL_BAY'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_VESSEL_BAY", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\TAS\\TAS_T_VESSEL_BAY.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_VESSEL_BAY extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_VESSEL_BAY'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\": \"主键\",\r\n      \"vesselId\": \"船舶主表主键\",\r\n      \"bayNo\": \"贝号\",\r\n      \"rowtier\": \"贝位信息\",\r\n      \"dtier\": \"甲板层数\",\r\n      \"htier\": \"舱底层数\",\r\n      \"drow\": \"甲板行数\",\r\n      \"hrow\": \"舱底行数\",\r\n      \"dfrom\": \"甲板起始坐标\",\r\n      \"dstep\": \"甲板步长\",\r\n      \"hfrom\": \"舱底起始坐标\",\r\n      \"hstep\": \"舱底步长\",\r\n      \"bigctnTag\": \"是否只装大箱\",\r\n      \"tiermark\": \"层标记\",\r\n      \"dtierDetail\": \"甲板层数明细，用逗号拼接\",\r\n      \"drowDetail\": \"甲板行数明细，用逗号拼接\",\r\n      \"htierDetail\": \"舱底层数明细，用逗号拼接\",\r\n      \"hrowDetail\": \"舱底行数明细，用逗号拼接\",\r\n      \"drowFromZeroTag\": \"甲板行数是否从0开始:Y/N\",\r\n      \"hrowFromZeroTag\": \"舱底行数是否从0开始:Y/N\",\r\n      \"orgId\": \"所属组织机构主键\",\r\n      \"orgLevelNo\": \"所属组织机构代码\",\r\n      \"entLevelNo\": \"所属公司代码\",\r\n      \"remark\": \"备注\",\r\n      \"isDelete\": \"是否删除,1：是,0：否\",\r\n      \"version\": \"版本号\",\r\n      \"tenantId\": \"租户ID\",\r\n      \"createdUser\": \"新建人ID\",\r\n      \"createdTime\": \"创建时间\",\r\n      \"modifiedUser\": \"修改人ID\",\r\n      \"modifiedTime\": \"修改时间\"\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,gBAAiB,SAAQD,QAAQ;EAQ5CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAE,IAAI;MACV,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,MAAM;MACjB,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,MAAM,EAAE,MAAM;MACd,MAAM,EAAE,MAAM;MACd,OAAO,EAAE,QAAQ;MACjB,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,QAAQ;MACjB,OAAO,EAAE,MAAM;MACf,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,KAAK;MACjB,aAAa,EAAE,cAAc;MAC7B,YAAY,EAAE,cAAc;MAC5B,aAAa,EAAE,cAAc;MAC7B,YAAY,EAAE,cAAc;MAC5B,iBAAiB,EAAE,gBAAgB;MACnC,iBAAiB,EAAE,gBAAgB;MACnC,OAAO,EAAE,UAAU;MACnB,YAAY,EAAE,UAAU;MACxB,YAAY,EAAE,QAAQ;MACtB,QAAQ,EAAE,IAAI;MACd,UAAU,EAAE,cAAc;MAC1B,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,MAAM;MAClB,aAAa,EAAE,OAAO;MACtB,aAAa,EAAE,MAAM;MACrB,cAAc,EAAE,OAAO;MACvB,cAAc,EAAE;KACjB,CAAC;IAvCJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC;IAChC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAoCnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}