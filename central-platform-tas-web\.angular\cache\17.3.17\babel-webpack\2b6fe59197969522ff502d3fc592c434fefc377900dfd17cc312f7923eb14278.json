{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_SA_TIMELIMIT extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"上报时间表主键\",\n      \"report_ym\": \"上报月\",\n      \"start_dt\": \"统计开始时间\",\n      \"end_dt\": \"统计截止时间\",\n      \"pm_dt\": \"禁止修改时间\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_SA_TIMELIMIT'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_SA_TIMELIMIT", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\TAS_T_SA_TIMELIMIT.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_SA_TIMELIMIT extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_SA_TIMELIMIT'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"上报时间表主键\",\r\n      \"report_ym\":\"上报月\",\r\n      \"start_dt\":\"统计开始时间\",\r\n      \"end_dt\":\"统计截止时间\",\r\n      \"pm_dt\":\"禁止修改时间\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,kBAAmB,SAAQD,QAAQ;EAQ9CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,WAAW,EAAC,KAAK;MACjB,UAAU,EAAC,QAAQ;MACnB,QAAQ,EAAC,QAAQ;MACjB,OAAO,EAAC,QAAQ;MAChB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IArBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,oBAAoB,CAAC,CAAC;IAClC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAkBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}