{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['преди н. е.', 'н. е.'],\n  wide: ['преди новата ера', 'новата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-во тримес.', '2-ро тримес.', '3-то тримес.', '4-то тримес.'],\n  wide: ['1-во тримесечие', '2-ро тримесечие', '3-то тримесечие', '4-то тримесечие']\n};\nvar monthValues = {\n  abbreviated: ['яну', 'фев', 'мар', 'апр', 'май', 'юни', 'юли', 'авг', 'сеп', 'окт', 'ное', 'дек'],\n  wide: ['януари', 'февруари', 'март', 'април', 'май', 'юни', 'юли', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вто', 'сря', 'чет', 'пет', 'съб'],\n  wide: ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'преди обяд',\n    pm: 'след обяд',\n    midnight: 'в полунощ',\n    noon: 'на обяд',\n    morning: 'сутринта',\n    afternoon: 'следобед',\n    evening: 'вечерта',\n    night: 'през нощта'\n  }\n};\nfunction isFeminine(unit) {\n  return unit === 'year' || unit === 'week' || unit === 'minute' || unit === 'second';\n}\nfunction isNeuter(unit) {\n  return unit === 'quarter';\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  var suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n  return number + '-' + suffix;\n}\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) {\n    return numberWithSuffix(0, unit, 'ев', 'ева', 'ево');\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, 'ен', 'на', 'но');\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, 'тен', 'тна', 'тно');\n  }\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, 'ви', 'ва', 'во');\n      case 2:\n        return numberWithSuffix(number, unit, 'ри', 'ра', 'ро');\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, 'ми', 'ма', 'мо');\n    }\n  }\n  return numberWithSuffix(number, unit, 'ти', 'та', 'то');\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "isFeminine", "unit", "isNeuter", "numberWithSuffix", "number", "masculine", "feminine", "neuter", "suffix", "ordinalNumber", "dirtyNumber", "options", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/bg/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['преди н. е.', 'н. е.'],\n  wide: ['преди новата ера', 'новата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-во тримес.', '2-ро тримес.', '3-то тримес.', '4-то тримес.'],\n  wide: ['1-во тримесечие', '2-ро тримесечие', '3-то тримесечие', '4-то тримесечие']\n};\nvar monthValues = {\n  abbreviated: ['яну', 'фев', 'мар', 'апр', 'май', 'юни', 'юли', 'авг', 'сеп', 'окт', 'ное', 'дек'],\n  wide: ['януари', 'февруари', 'март', 'април', 'май', 'юни', 'юли', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вто', 'сря', 'чет', 'пет', 'съб'],\n  wide: ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'преди обяд',\n    pm: 'след обяд',\n    midnight: 'в полунощ',\n    noon: 'на обяд',\n    morning: 'сутринта',\n    afternoon: 'следобед',\n    evening: 'вечерта',\n    night: 'през нощта'\n  }\n};\nfunction isFeminine(unit) {\n  return unit === 'year' || unit === 'week' || unit === 'minute' || unit === 'second';\n}\nfunction isNeuter(unit) {\n  return unit === 'quarter';\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  var suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n  return number + '-' + suffix;\n}\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) {\n    return numberWithSuffix(0, unit, 'ев', 'ева', 'ево');\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, 'ен', 'на', 'но');\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, 'тен', 'тна', 'тно');\n  }\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, 'ви', 'ва', 'во');\n      case 2:\n        return numberWithSuffix(number, unit, 'ри', 'ра', 'ро');\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, 'ми', 'ма', 'мо');\n    }\n  }\n  return numberWithSuffix(number, unit, 'ти', 'та', 'то');\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;EACrCC,IAAI,EAAE,CAAC,kBAAkB,EAAE,YAAY;AACzC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;EAC7EC,IAAI,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB;AACnF,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBH,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU;AAC7H,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ;AACnF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBL,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,WAAW;IACfC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;AACrF;AACA,SAASC,QAAQA,CAACD,IAAI,EAAE;EACtB,OAAOA,IAAI,KAAK,SAAS;AAC3B;AACA,SAASE,gBAAgBA,CAACC,MAAM,EAAEH,IAAI,EAAEI,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACnE,IAAIC,MAAM,GAAGN,QAAQ,CAACD,IAAI,CAAC,GAAGM,MAAM,GAAGP,UAAU,CAACC,IAAI,CAAC,GAAGK,QAAQ,GAAGD,SAAS;EAC9E,OAAOD,MAAM,GAAG,GAAG,GAAGI,MAAM;AAC9B;AACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIP,MAAM,GAAGQ,MAAM,CAACF,WAAW,CAAC;EAChC,IAAIT,IAAI,GAAGU,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACV,IAAI;EACzE,IAAIG,MAAM,KAAK,CAAC,EAAE;IAChB,OAAOD,gBAAgB,CAAC,CAAC,EAAEF,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;EACtD,CAAC,MAAM,IAAIG,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE;IAC9B,OAAOD,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzD,CAAC,MAAM,IAAIG,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;IAC7B,OAAOD,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5D;EACA,IAAIY,MAAM,GAAGT,MAAM,GAAG,GAAG;EACzB,IAAIS,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;QACJ,OAAOV,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzD,KAAK,CAAC;QACJ,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzD,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3D;EACF;EACA,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,IAAIa,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEjC,eAAe,CAAC;IACnBkC,MAAM,EAAEjC,SAAS;IACjBkC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAEpC,eAAe,CAAC;IACvBkC,MAAM,EAAE7B,aAAa;IACrB8B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEtC,eAAe,CAAC;IACrBkC,MAAM,EAAE5B,WAAW;IACnB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAEvC,eAAe,CAAC;IACnBkC,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAExC,eAAe,CAAC;IACzBkC,MAAM,EAAEzB,eAAe;IACvB0B,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}