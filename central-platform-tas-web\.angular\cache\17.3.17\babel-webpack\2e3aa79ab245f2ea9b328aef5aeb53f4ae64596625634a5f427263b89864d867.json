{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SuperviseComponent } from './supervise.component';\nimport { SuperviseEditComponent } from '@business/tas/supervise/supervise-edit/supervise-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: SuperviseComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: SuperviseEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class SuperviseRoutingModule {\n  static {\n    this.ɵfac = function SuperviseRoutingModule_Factory(t) {\n      return new (t || SuperviseRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SuperviseRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SuperviseRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SuperviseComponent", "SuperviseEditComponent", "routes", "path", "component", "data", "cache", "SuperviseRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SuperviseComponent } from './supervise.component';\r\nimport {SuperviseEditComponent} from '@business/tas/supervise/supervise-edit/supervise-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: SuperviseComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: SuperviseEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SuperviseRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAAQC,sBAAsB,QAAO,iEAAiE;;;AACtG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,kBAAkB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACtE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,sBAAsB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAChF;AAMD,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFvBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}