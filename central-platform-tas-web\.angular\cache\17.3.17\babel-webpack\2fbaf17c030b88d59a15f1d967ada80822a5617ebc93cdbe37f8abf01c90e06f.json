{"ast": null, "code": "import { numberToLocale } from \"../localize/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/hi.html\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '१ सेकंड से कम',\n    // CLDR #1310\n    other: '{{count}} सेकंड से कम'\n  },\n  xSeconds: {\n    one: '१ सेकंड',\n    other: '{{count}} सेकंड'\n  },\n  halfAMinute: 'आधा मिनट',\n  lessThanXMinutes: {\n    one: '१ मिनट से कम',\n    other: '{{count}} मिनट से कम'\n  },\n  xMinutes: {\n    one: '१ मिनट',\n    // CLDR #1307\n    other: '{{count}} मिनट'\n  },\n  aboutXHours: {\n    one: 'लगभग १ घंटा',\n    other: 'लगभग {{count}} घंटे'\n  },\n  xHours: {\n    one: '१ घंटा',\n    // CLDR #1304\n    other: '{{count}} घंटे' // CLDR #4467\n  },\n  xDays: {\n    one: '१ दिन',\n    // CLDR #1286\n    other: '{{count}} दिन'\n  },\n  aboutXWeeks: {\n    one: 'लगभग १ सप्ताह',\n    other: 'लगभग {{count}} सप्ताह'\n  },\n  xWeeks: {\n    one: '१ सप्ताह',\n    other: '{{count}} सप्ताह'\n  },\n  aboutXMonths: {\n    one: 'लगभग १ महीना',\n    other: 'लगभग {{count}} महीने'\n  },\n  xMonths: {\n    one: '१ महीना',\n    other: '{{count}} महीने'\n  },\n  aboutXYears: {\n    one: 'लगभग १ वर्ष',\n    other: 'लगभग {{count}} वर्ष' // CLDR #4823\n  },\n  xYears: {\n    one: '१ वर्ष',\n    other: '{{count}} वर्ष'\n  },\n  overXYears: {\n    one: '१ वर्ष से अधिक',\n    other: '{{count}} वर्ष से अधिक'\n  },\n  almostXYears: {\n    one: 'लगभग १ वर्ष',\n    other: 'लगभग {{count}} वर्ष'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', numberToLocale(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'मे ';\n    } else {\n      return result + ' पहले';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["numberToLocale", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/hi/_lib/formatDistance/index.js"], "sourcesContent": ["import { numberToLocale } from \"../localize/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/hi.html\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '१ सेकंड से कम',\n    // CLDR #1310\n    other: '{{count}} सेकंड से कम'\n  },\n  xSeconds: {\n    one: '१ सेकंड',\n    other: '{{count}} सेकंड'\n  },\n  halfAMinute: 'आधा मिनट',\n  lessThanXMinutes: {\n    one: '१ मिनट से कम',\n    other: '{{count}} मिनट से कम'\n  },\n  xMinutes: {\n    one: '१ मिनट',\n    // CLDR #1307\n    other: '{{count}} मिनट'\n  },\n  aboutXHours: {\n    one: 'लगभग १ घंटा',\n    other: 'लगभग {{count}} घंटे'\n  },\n  xHours: {\n    one: '१ घंटा',\n    // CLDR #1304\n    other: '{{count}} घंटे' // CLDR #4467\n  },\n\n  xDays: {\n    one: '१ दिन',\n    // CLDR #1286\n    other: '{{count}} दिन'\n  },\n  aboutXWeeks: {\n    one: 'लगभग १ सप्ताह',\n    other: 'लगभग {{count}} सप्ताह'\n  },\n  xWeeks: {\n    one: '१ सप्ताह',\n    other: '{{count}} सप्ताह'\n  },\n  aboutXMonths: {\n    one: 'लगभग १ महीना',\n    other: 'लगभग {{count}} महीने'\n  },\n  xMonths: {\n    one: '१ महीना',\n    other: '{{count}} महीने'\n  },\n  aboutXYears: {\n    one: 'लगभग १ वर्ष',\n    other: 'लगभग {{count}} वर्ष' // CLDR #4823\n  },\n\n  xYears: {\n    one: '१ वर्ष',\n    other: '{{count}} वर्ष'\n  },\n  overXYears: {\n    one: '१ वर्ष से अधिक',\n    other: '{{count}} वर्ष से अधिक'\n  },\n  almostXYears: {\n    one: 'लगभग १ वर्ष',\n    other: 'लगभग {{count}} वर्ष'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', numberToLocale(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'मे ';\n    } else {\n      return result + ' पहले';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB,CAAC,CAAC;AACvD,IAAIC,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,eAAe;IACpB;IACAC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACb;IACAC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACb;IACAC,KAAK,EAAE,gBAAgB,CAAC;EAC1B,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZ;IACAC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,qBAAqB,CAAC;EAC/B,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAE1B,cAAc,CAACsB,KAAK,CAAC,CAAC;EACvE;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACI,SAAS,EAAE;IAC/D,IAAIJ,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOJ,MAAM,GAAG,KAAK;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}