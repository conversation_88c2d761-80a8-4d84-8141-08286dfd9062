{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '1초 미만',\n    other: '{{count}}초 미만'\n  },\n  xSeconds: {\n    one: '1초',\n    other: '{{count}}초'\n  },\n  halfAMinute: '30초',\n  lessThanXMinutes: {\n    one: '1분 미만',\n    other: '{{count}}분 미만'\n  },\n  xMinutes: {\n    one: '1분',\n    other: '{{count}}분'\n  },\n  aboutXHours: {\n    one: '약 1시간',\n    other: '약 {{count}}시간'\n  },\n  xHours: {\n    one: '1시간',\n    other: '{{count}}시간'\n  },\n  xDays: {\n    one: '1일',\n    other: '{{count}}일'\n  },\n  aboutXWeeks: {\n    one: '약 1주',\n    other: '약 {{count}}주'\n  },\n  xWeeks: {\n    one: '1주',\n    other: '{{count}}주'\n  },\n  aboutXMonths: {\n    one: '약 1개월',\n    other: '약 {{count}}개월'\n  },\n  xMonths: {\n    one: '1개월',\n    other: '{{count}}개월'\n  },\n  aboutXYears: {\n    one: '약 1년',\n    other: '약 {{count}}년'\n  },\n  xYears: {\n    one: '1년',\n    other: '{{count}}년'\n  },\n  overXYears: {\n    one: '1년 이상',\n    other: '{{count}}년 이상'\n  },\n  almostXYears: {\n    one: '거의 1년',\n    other: '거의 {{count}}년'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' 후';\n    } else {\n      return result + ' 전';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ko/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '1초 미만',\n    other: '{{count}}초 미만'\n  },\n  xSeconds: {\n    one: '1초',\n    other: '{{count}}초'\n  },\n  halfAMinute: '30초',\n  lessThanXMinutes: {\n    one: '1분 미만',\n    other: '{{count}}분 미만'\n  },\n  xMinutes: {\n    one: '1분',\n    other: '{{count}}분'\n  },\n  aboutXHours: {\n    one: '약 1시간',\n    other: '약 {{count}}시간'\n  },\n  xHours: {\n    one: '1시간',\n    other: '{{count}}시간'\n  },\n  xDays: {\n    one: '1일',\n    other: '{{count}}일'\n  },\n  aboutXWeeks: {\n    one: '약 1주',\n    other: '약 {{count}}주'\n  },\n  xWeeks: {\n    one: '1주',\n    other: '{{count}}주'\n  },\n  aboutXMonths: {\n    one: '약 1개월',\n    other: '약 {{count}}개월'\n  },\n  xMonths: {\n    one: '1개월',\n    other: '{{count}}개월'\n  },\n  aboutXYears: {\n    one: '약 1년',\n    other: '약 {{count}}년'\n  },\n  xYears: {\n    one: '1년',\n    other: '{{count}}년'\n  },\n  overXYears: {\n    one: '1년 이상',\n    other: '{{count}}년 이상'\n  },\n  almostXYears: {\n    one: '거의 1년',\n    other: '거의 {{count}}년'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' 후';\n    } else {\n      return result + ' 전';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,KAAK;EAClBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,IAAI;IACtB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}