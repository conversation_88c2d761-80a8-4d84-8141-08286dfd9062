{"ast": null, "code": "// ASN.1 JavaScript decoder\n// Copyright (c) 2008-2014 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\n/*global oids */\nimport { Int10 } from \"./int10\";\nvar ellipsis = \"\\u2026\";\nvar reTimeS = /^(\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nvar reTimeL = /^(\\d\\d\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nfunction stringCut(str, len) {\n  if (str.length > len) {\n    str = str.substring(0, len) + ellipsis;\n  }\n  return str;\n}\nvar Stream = /** @class */function () {\n  function Stream(enc, pos) {\n    this.hexDigits = \"0123456789ABCDEF\";\n    if (enc instanceof Stream) {\n      this.enc = enc.enc;\n      this.pos = enc.pos;\n    } else {\n      // enc should be an array or a binary string\n      this.enc = enc;\n      this.pos = pos;\n    }\n  }\n  Stream.prototype.get = function (pos) {\n    if (pos === undefined) {\n      pos = this.pos++;\n    }\n    if (pos >= this.enc.length) {\n      throw new Error(\"Requesting byte offset \".concat(pos, \" on a stream of length \").concat(this.enc.length));\n    }\n    return \"string\" === typeof this.enc ? this.enc.charCodeAt(pos) : this.enc[pos];\n  };\n  Stream.prototype.hexByte = function (b) {\n    return this.hexDigits.charAt(b >> 4 & 0xF) + this.hexDigits.charAt(b & 0xF);\n  };\n  Stream.prototype.hexDump = function (start, end, raw) {\n    var s = \"\";\n    for (var i = start; i < end; ++i) {\n      s += this.hexByte(this.get(i));\n      if (raw !== true) {\n        switch (i & 0xF) {\n          case 0x7:\n            s += \"  \";\n            break;\n          case 0xF:\n            s += \"\\n\";\n            break;\n          default:\n            s += \" \";\n        }\n      }\n    }\n    return s;\n  };\n  Stream.prototype.isASCII = function (start, end) {\n    for (var i = start; i < end; ++i) {\n      var c = this.get(i);\n      if (c < 32 || c > 176) {\n        return false;\n      }\n    }\n    return true;\n  };\n  Stream.prototype.parseStringISO = function (start, end) {\n    var s = \"\";\n    for (var i = start; i < end; ++i) {\n      s += String.fromCharCode(this.get(i));\n    }\n    return s;\n  };\n  Stream.prototype.parseStringUTF = function (start, end) {\n    var s = \"\";\n    for (var i = start; i < end;) {\n      var c = this.get(i++);\n      if (c < 128) {\n        s += String.fromCharCode(c);\n      } else if (c > 191 && c < 224) {\n        s += String.fromCharCode((c & 0x1F) << 6 | this.get(i++) & 0x3F);\n      } else {\n        s += String.fromCharCode((c & 0x0F) << 12 | (this.get(i++) & 0x3F) << 6 | this.get(i++) & 0x3F);\n      }\n    }\n    return s;\n  };\n  Stream.prototype.parseStringBMP = function (start, end) {\n    var str = \"\";\n    var hi;\n    var lo;\n    for (var i = start; i < end;) {\n      hi = this.get(i++);\n      lo = this.get(i++);\n      str += String.fromCharCode(hi << 8 | lo);\n    }\n    return str;\n  };\n  Stream.prototype.parseTime = function (start, end, shortYear) {\n    var s = this.parseStringISO(start, end);\n    var m = (shortYear ? reTimeS : reTimeL).exec(s);\n    if (!m) {\n      return \"Unrecognized time: \" + s;\n    }\n    if (shortYear) {\n      // to avoid querying the timer, use the fixed range [1970, 2069]\n      // it will conform with ITU X.400 [-10, +40] sliding window until 2030\n      m[1] = +m[1];\n      m[1] += +m[1] < 70 ? 2000 : 1900;\n    }\n    s = m[1] + \"-\" + m[2] + \"-\" + m[3] + \" \" + m[4];\n    if (m[5]) {\n      s += \":\" + m[5];\n      if (m[6]) {\n        s += \":\" + m[6];\n        if (m[7]) {\n          s += \".\" + m[7];\n        }\n      }\n    }\n    if (m[8]) {\n      s += \" UTC\";\n      if (m[8] != \"Z\") {\n        s += m[8];\n        if (m[9]) {\n          s += \":\" + m[9];\n        }\n      }\n    }\n    return s;\n  };\n  Stream.prototype.parseInteger = function (start, end) {\n    var v = this.get(start);\n    var neg = v > 127;\n    var pad = neg ? 255 : 0;\n    var len;\n    var s = \"\";\n    // skip unuseful bits (not allowed in DER)\n    while (v == pad && ++start < end) {\n      v = this.get(start);\n    }\n    len = end - start;\n    if (len === 0) {\n      return neg ? -1 : 0;\n    }\n    // show bit length of huge integers\n    if (len > 4) {\n      s = v;\n      len <<= 3;\n      while (((+s ^ pad) & 0x80) == 0) {\n        s = +s << 1;\n        --len;\n      }\n      s = \"(\" + len + \" bit)\\n\";\n    }\n    // decode the integer\n    if (neg) {\n      v = v - 256;\n    }\n    var n = new Int10(v);\n    for (var i = start + 1; i < end; ++i) {\n      n.mulAdd(256, this.get(i));\n    }\n    return s + n.toString();\n  };\n  Stream.prototype.parseBitString = function (start, end, maxLength) {\n    var unusedBit = this.get(start);\n    var lenBit = (end - start - 1 << 3) - unusedBit;\n    var intro = \"(\" + lenBit + \" bit)\\n\";\n    var s = \"\";\n    for (var i = start + 1; i < end; ++i) {\n      var b = this.get(i);\n      var skip = i == end - 1 ? unusedBit : 0;\n      for (var j = 7; j >= skip; --j) {\n        s += b >> j & 1 ? \"1\" : \"0\";\n      }\n      if (s.length > maxLength) {\n        return intro + stringCut(s, maxLength);\n      }\n    }\n    return intro + s;\n  };\n  Stream.prototype.parseOctetString = function (start, end, maxLength) {\n    if (this.isASCII(start, end)) {\n      return stringCut(this.parseStringISO(start, end), maxLength);\n    }\n    var len = end - start;\n    var s = \"(\" + len + \" byte)\\n\";\n    maxLength /= 2; // we work in bytes\n    if (len > maxLength) {\n      end = start + maxLength;\n    }\n    for (var i = start; i < end; ++i) {\n      s += this.hexByte(this.get(i));\n    }\n    if (len > maxLength) {\n      s += ellipsis;\n    }\n    return s;\n  };\n  Stream.prototype.parseOID = function (start, end, maxLength) {\n    var s = \"\";\n    var n = new Int10();\n    var bits = 0;\n    for (var i = start; i < end; ++i) {\n      var v = this.get(i);\n      n.mulAdd(128, v & 0x7F);\n      bits += 7;\n      if (!(v & 0x80)) {\n        // finished\n        if (s === \"\") {\n          n = n.simplify();\n          if (n instanceof Int10) {\n            n.sub(80);\n            s = \"2.\" + n.toString();\n          } else {\n            var m = n < 80 ? n < 40 ? 0 : 1 : 2;\n            s = m + \".\" + (n - m * 40);\n          }\n        } else {\n          s += \".\" + n.toString();\n        }\n        if (s.length > maxLength) {\n          return stringCut(s, maxLength);\n        }\n        n = new Int10();\n        bits = 0;\n      }\n    }\n    if (bits > 0) {\n      s += \".incomplete\";\n    }\n    return s;\n  };\n  return Stream;\n}();\nexport { Stream };\nvar ASN1 = /** @class */function () {\n  function ASN1(stream, header, length, tag, sub) {\n    if (!(tag instanceof ASN1Tag)) {\n      throw new Error(\"Invalid tag value.\");\n    }\n    this.stream = stream;\n    this.header = header;\n    this.length = length;\n    this.tag = tag;\n    this.sub = sub;\n  }\n  ASN1.prototype.typeName = function () {\n    switch (this.tag.tagClass) {\n      case 0:\n        // universal\n        switch (this.tag.tagNumber) {\n          case 0x00:\n            return \"EOC\";\n          case 0x01:\n            return \"BOOLEAN\";\n          case 0x02:\n            return \"INTEGER\";\n          case 0x03:\n            return \"BIT_STRING\";\n          case 0x04:\n            return \"OCTET_STRING\";\n          case 0x05:\n            return \"NULL\";\n          case 0x06:\n            return \"OBJECT_IDENTIFIER\";\n          case 0x07:\n            return \"ObjectDescriptor\";\n          case 0x08:\n            return \"EXTERNAL\";\n          case 0x09:\n            return \"REAL\";\n          case 0x0A:\n            return \"ENUMERATED\";\n          case 0x0B:\n            return \"EMBEDDED_PDV\";\n          case 0x0C:\n            return \"UTF8String\";\n          case 0x10:\n            return \"SEQUENCE\";\n          case 0x11:\n            return \"SET\";\n          case 0x12:\n            return \"NumericString\";\n          case 0x13:\n            return \"PrintableString\";\n          // ASCII subset\n          case 0x14:\n            return \"TeletexString\";\n          // aka T61String\n          case 0x15:\n            return \"VideotexString\";\n          case 0x16:\n            return \"IA5String\";\n          // ASCII\n          case 0x17:\n            return \"UTCTime\";\n          case 0x18:\n            return \"GeneralizedTime\";\n          case 0x19:\n            return \"GraphicString\";\n          case 0x1A:\n            return \"VisibleString\";\n          // ASCII subset\n          case 0x1B:\n            return \"GeneralString\";\n          case 0x1C:\n            return \"UniversalString\";\n          case 0x1E:\n            return \"BMPString\";\n        }\n        return \"Universal_\" + this.tag.tagNumber.toString();\n      case 1:\n        return \"Application_\" + this.tag.tagNumber.toString();\n      case 2:\n        return \"[\" + this.tag.tagNumber.toString() + \"]\";\n      // Context\n      case 3:\n        return \"Private_\" + this.tag.tagNumber.toString();\n    }\n  };\n  ASN1.prototype.content = function (maxLength) {\n    if (this.tag === undefined) {\n      return null;\n    }\n    if (maxLength === undefined) {\n      maxLength = Infinity;\n    }\n    var content = this.posContent();\n    var len = Math.abs(this.length);\n    if (!this.tag.isUniversal()) {\n      if (this.sub !== null) {\n        return \"(\" + this.sub.length + \" elem)\";\n      }\n      return this.stream.parseOctetString(content, content + len, maxLength);\n    }\n    switch (this.tag.tagNumber) {\n      case 0x01:\n        // BOOLEAN\n        return this.stream.get(content) === 0 ? \"false\" : \"true\";\n      case 0x02:\n        // INTEGER\n        return this.stream.parseInteger(content, content + len);\n      case 0x03:\n        // BIT_STRING\n        return this.sub ? \"(\" + this.sub.length + \" elem)\" : this.stream.parseBitString(content, content + len, maxLength);\n      case 0x04:\n        // OCTET_STRING\n        return this.sub ? \"(\" + this.sub.length + \" elem)\" : this.stream.parseOctetString(content, content + len, maxLength);\n      // case 0x05: // NULL\n      case 0x06:\n        // OBJECT_IDENTIFIER\n        return this.stream.parseOID(content, content + len, maxLength);\n      // case 0x07: // ObjectDescriptor\n      // case 0x08: // EXTERNAL\n      // case 0x09: // REAL\n      // case 0x0A: // ENUMERATED\n      // case 0x0B: // EMBEDDED_PDV\n      case 0x10: // SEQUENCE\n      case 0x11:\n        // SET\n        if (this.sub !== null) {\n          return \"(\" + this.sub.length + \" elem)\";\n        } else {\n          return \"(no elem)\";\n        }\n      case 0x0C:\n        // UTF8String\n        return stringCut(this.stream.parseStringUTF(content, content + len), maxLength);\n      case 0x12: // NumericString\n      case 0x13: // PrintableString\n      case 0x14: // TeletexString\n      case 0x15: // VideotexString\n      case 0x16: // IA5String\n      // case 0x19: // GraphicString\n      case 0x1A:\n        // VisibleString\n        // case 0x1B: // GeneralString\n        // case 0x1C: // UniversalString\n        return stringCut(this.stream.parseStringISO(content, content + len), maxLength);\n      case 0x1E:\n        // BMPString\n        return stringCut(this.stream.parseStringBMP(content, content + len), maxLength);\n      case 0x17: // UTCTime\n      case 0x18:\n        // GeneralizedTime\n        return this.stream.parseTime(content, content + len, this.tag.tagNumber == 0x17);\n    }\n    return null;\n  };\n  ASN1.prototype.toString = function () {\n    return this.typeName() + \"@\" + this.stream.pos + \"[header:\" + this.header + \",length:\" + this.length + \",sub:\" + (this.sub === null ? \"null\" : this.sub.length) + \"]\";\n  };\n  ASN1.prototype.toPrettyString = function (indent) {\n    if (indent === undefined) {\n      indent = \"\";\n    }\n    var s = indent + this.typeName() + \" @\" + this.stream.pos;\n    if (this.length >= 0) {\n      s += \"+\";\n    }\n    s += this.length;\n    if (this.tag.tagConstructed) {\n      s += \" (constructed)\";\n    } else if (this.tag.isUniversal() && (this.tag.tagNumber == 0x03 || this.tag.tagNumber == 0x04) && this.sub !== null) {\n      s += \" (encapsulates)\";\n    }\n    s += \"\\n\";\n    if (this.sub !== null) {\n      indent += \"  \";\n      for (var i = 0, max = this.sub.length; i < max; ++i) {\n        s += this.sub[i].toPrettyString(indent);\n      }\n    }\n    return s;\n  };\n  ASN1.prototype.posStart = function () {\n    return this.stream.pos;\n  };\n  ASN1.prototype.posContent = function () {\n    return this.stream.pos + this.header;\n  };\n  ASN1.prototype.posEnd = function () {\n    return this.stream.pos + this.header + Math.abs(this.length);\n  };\n  ASN1.prototype.toHexString = function () {\n    return this.stream.hexDump(this.posStart(), this.posEnd(), true);\n  };\n  ASN1.decodeLength = function (stream) {\n    var buf = stream.get();\n    var len = buf & 0x7F;\n    if (len == buf) {\n      return len;\n    }\n    // no reason to use Int10, as it would be a huge buffer anyways\n    if (len > 6) {\n      throw new Error(\"Length over 48 bits not supported at position \" + (stream.pos - 1));\n    }\n    if (len === 0) {\n      return null;\n    } // undefined\n    buf = 0;\n    for (var i = 0; i < len; ++i) {\n      buf = buf * 256 + stream.get();\n    }\n    return buf;\n  };\n  /**\n   * Retrieve the hexadecimal value (as a string) of the current ASN.1 element\n   * @returns {string}\n   * @public\n   */\n  ASN1.prototype.getHexStringValue = function () {\n    var hexString = this.toHexString();\n    var offset = this.header * 2;\n    var length = this.length * 2;\n    return hexString.substr(offset, length);\n  };\n  ASN1.decode = function (str) {\n    var stream;\n    if (!(str instanceof Stream)) {\n      stream = new Stream(str, 0);\n    } else {\n      stream = str;\n    }\n    var streamStart = new Stream(stream);\n    var tag = new ASN1Tag(stream);\n    var len = ASN1.decodeLength(stream);\n    var start = stream.pos;\n    var header = start - streamStart.pos;\n    var sub = null;\n    var getSub = function () {\n      var ret = [];\n      if (len !== null) {\n        // definite length\n        var end = start + len;\n        while (stream.pos < end) {\n          ret[ret.length] = ASN1.decode(stream);\n        }\n        if (stream.pos != end) {\n          throw new Error(\"Content size is not correct for container starting at offset \" + start);\n        }\n      } else {\n        // undefined length\n        try {\n          for (;;) {\n            var s = ASN1.decode(stream);\n            if (s.tag.isEOC()) {\n              break;\n            }\n            ret[ret.length] = s;\n          }\n          len = start - stream.pos; // undefined lengths are represented as negative values\n        } catch (e) {\n          throw new Error(\"Exception while decoding undefined length content: \" + e);\n        }\n      }\n      return ret;\n    };\n    if (tag.tagConstructed) {\n      // must have valid content\n      sub = getSub();\n    } else if (tag.isUniversal() && (tag.tagNumber == 0x03 || tag.tagNumber == 0x04)) {\n      // sometimes BitString and OctetString are used to encapsulate ASN.1\n      try {\n        if (tag.tagNumber == 0x03) {\n          if (stream.get() != 0) {\n            throw new Error(\"BIT STRINGs with unused bits cannot encapsulate.\");\n          }\n        }\n        sub = getSub();\n        for (var i = 0; i < sub.length; ++i) {\n          if (sub[i].tag.isEOC()) {\n            throw new Error(\"EOC is not supposed to be actual content.\");\n          }\n        }\n      } catch (e) {\n        // but silently ignore when they don't\n        sub = null;\n      }\n    }\n    if (sub === null) {\n      if (len === null) {\n        throw new Error(\"We can't skip over an invalid tag with undefined length at offset \" + start);\n      }\n      stream.pos = start + Math.abs(len);\n    }\n    return new ASN1(streamStart, header, len, tag, sub);\n  };\n  return ASN1;\n}();\nexport { ASN1 };\nvar ASN1Tag = /** @class */function () {\n  function ASN1Tag(stream) {\n    var buf = stream.get();\n    this.tagClass = buf >> 6;\n    this.tagConstructed = (buf & 0x20) !== 0;\n    this.tagNumber = buf & 0x1F;\n    if (this.tagNumber == 0x1F) {\n      // long tag\n      var n = new Int10();\n      do {\n        buf = stream.get();\n        n.mulAdd(128, buf & 0x7F);\n      } while (buf & 0x80);\n      this.tagNumber = n.simplify();\n    }\n  }\n  ASN1Tag.prototype.isUniversal = function () {\n    return this.tagClass === 0x00;\n  };\n  ASN1Tag.prototype.isEOC = function () {\n    return this.tagClass === 0x00 && this.tagNumber === 0x00;\n  };\n  return ASN1Tag;\n}();\nexport { ASN1Tag };", "map": {"version": 3, "names": ["Int10", "ellipsis", "reTimeS", "reTimeL", "stringCut", "str", "len", "length", "substring", "Stream", "enc", "pos", "hexDigits", "prototype", "get", "undefined", "Error", "concat", "charCodeAt", "hexByte", "b", "char<PERSON>t", "hexDump", "start", "end", "raw", "s", "i", "isASCII", "c", "parseStringISO", "String", "fromCharCode", "parseStringUTF", "parseStringBMP", "hi", "lo", "parseTime", "shortYear", "m", "exec", "parseInteger", "v", "neg", "pad", "n", "mulAdd", "toString", "parseBitString", "max<PERSON><PERSON><PERSON>", "unusedBit", "lenBit", "intro", "skip", "j", "parseOctetString", "parseOID", "bits", "simplify", "sub", "ASN1", "stream", "header", "tag", "ASN1Tag", "typeName", "tagClass", "tagNumber", "content", "Infinity", "pos<PERSON>onte<PERSON>", "Math", "abs", "isUniversal", "toPrettyString", "indent", "tagConstructed", "max", "posStart", "posEnd", "toHexString", "decodeLength", "buf", "getHexStringValue", "hexString", "offset", "substr", "decode", "streamStart", "getSub", "ret", "isEOC", "e"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/asn1js/asn1.js"], "sourcesContent": ["// ASN.1 JavaScript decoder\n// Copyright (c) 2008-2014 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\n/*global oids */\nimport { Int10 } from \"./int10\";\nvar ellipsis = \"\\u2026\";\nvar reTimeS = /^(\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nvar reTimeL = /^(\\d\\d\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nfunction stringCut(str, len) {\n    if (str.length > len) {\n        str = str.substring(0, len) + ellipsis;\n    }\n    return str;\n}\nvar Stream = /** @class */ (function () {\n    function Stream(enc, pos) {\n        this.hexDigits = \"0123456789ABCDEF\";\n        if (enc instanceof Stream) {\n            this.enc = enc.enc;\n            this.pos = enc.pos;\n        }\n        else {\n            // enc should be an array or a binary string\n            this.enc = enc;\n            this.pos = pos;\n        }\n    }\n    Stream.prototype.get = function (pos) {\n        if (pos === undefined) {\n            pos = this.pos++;\n        }\n        if (pos >= this.enc.length) {\n            throw new Error(\"Requesting byte offset \".concat(pos, \" on a stream of length \").concat(this.enc.length));\n        }\n        return (\"string\" === typeof this.enc) ? this.enc.charCodeAt(pos) : this.enc[pos];\n    };\n    Stream.prototype.hexByte = function (b) {\n        return this.hexDigits.charAt((b >> 4) & 0xF) + this.hexDigits.charAt(b & 0xF);\n    };\n    Stream.prototype.hexDump = function (start, end, raw) {\n        var s = \"\";\n        for (var i = start; i < end; ++i) {\n            s += this.hexByte(this.get(i));\n            if (raw !== true) {\n                switch (i & 0xF) {\n                    case 0x7:\n                        s += \"  \";\n                        break;\n                    case 0xF:\n                        s += \"\\n\";\n                        break;\n                    default:\n                        s += \" \";\n                }\n            }\n        }\n        return s;\n    };\n    Stream.prototype.isASCII = function (start, end) {\n        for (var i = start; i < end; ++i) {\n            var c = this.get(i);\n            if (c < 32 || c > 176) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Stream.prototype.parseStringISO = function (start, end) {\n        var s = \"\";\n        for (var i = start; i < end; ++i) {\n            s += String.fromCharCode(this.get(i));\n        }\n        return s;\n    };\n    Stream.prototype.parseStringUTF = function (start, end) {\n        var s = \"\";\n        for (var i = start; i < end;) {\n            var c = this.get(i++);\n            if (c < 128) {\n                s += String.fromCharCode(c);\n            }\n            else if ((c > 191) && (c < 224)) {\n                s += String.fromCharCode(((c & 0x1F) << 6) | (this.get(i++) & 0x3F));\n            }\n            else {\n                s += String.fromCharCode(((c & 0x0F) << 12) | ((this.get(i++) & 0x3F) << 6) | (this.get(i++) & 0x3F));\n            }\n        }\n        return s;\n    };\n    Stream.prototype.parseStringBMP = function (start, end) {\n        var str = \"\";\n        var hi;\n        var lo;\n        for (var i = start; i < end;) {\n            hi = this.get(i++);\n            lo = this.get(i++);\n            str += String.fromCharCode((hi << 8) | lo);\n        }\n        return str;\n    };\n    Stream.prototype.parseTime = function (start, end, shortYear) {\n        var s = this.parseStringISO(start, end);\n        var m = (shortYear ? reTimeS : reTimeL).exec(s);\n        if (!m) {\n            return \"Unrecognized time: \" + s;\n        }\n        if (shortYear) {\n            // to avoid querying the timer, use the fixed range [1970, 2069]\n            // it will conform with ITU X.400 [-10, +40] sliding window until 2030\n            m[1] = +m[1];\n            m[1] += (+m[1] < 70) ? 2000 : 1900;\n        }\n        s = m[1] + \"-\" + m[2] + \"-\" + m[3] + \" \" + m[4];\n        if (m[5]) {\n            s += \":\" + m[5];\n            if (m[6]) {\n                s += \":\" + m[6];\n                if (m[7]) {\n                    s += \".\" + m[7];\n                }\n            }\n        }\n        if (m[8]) {\n            s += \" UTC\";\n            if (m[8] != \"Z\") {\n                s += m[8];\n                if (m[9]) {\n                    s += \":\" + m[9];\n                }\n            }\n        }\n        return s;\n    };\n    Stream.prototype.parseInteger = function (start, end) {\n        var v = this.get(start);\n        var neg = (v > 127);\n        var pad = neg ? 255 : 0;\n        var len;\n        var s = \"\";\n        // skip unuseful bits (not allowed in DER)\n        while (v == pad && ++start < end) {\n            v = this.get(start);\n        }\n        len = end - start;\n        if (len === 0) {\n            return neg ? -1 : 0;\n        }\n        // show bit length of huge integers\n        if (len > 4) {\n            s = v;\n            len <<= 3;\n            while (((+s ^ pad) & 0x80) == 0) {\n                s = +s << 1;\n                --len;\n            }\n            s = \"(\" + len + \" bit)\\n\";\n        }\n        // decode the integer\n        if (neg) {\n            v = v - 256;\n        }\n        var n = new Int10(v);\n        for (var i = start + 1; i < end; ++i) {\n            n.mulAdd(256, this.get(i));\n        }\n        return s + n.toString();\n    };\n    Stream.prototype.parseBitString = function (start, end, maxLength) {\n        var unusedBit = this.get(start);\n        var lenBit = ((end - start - 1) << 3) - unusedBit;\n        var intro = \"(\" + lenBit + \" bit)\\n\";\n        var s = \"\";\n        for (var i = start + 1; i < end; ++i) {\n            var b = this.get(i);\n            var skip = (i == end - 1) ? unusedBit : 0;\n            for (var j = 7; j >= skip; --j) {\n                s += (b >> j) & 1 ? \"1\" : \"0\";\n            }\n            if (s.length > maxLength) {\n                return intro + stringCut(s, maxLength);\n            }\n        }\n        return intro + s;\n    };\n    Stream.prototype.parseOctetString = function (start, end, maxLength) {\n        if (this.isASCII(start, end)) {\n            return stringCut(this.parseStringISO(start, end), maxLength);\n        }\n        var len = end - start;\n        var s = \"(\" + len + \" byte)\\n\";\n        maxLength /= 2; // we work in bytes\n        if (len > maxLength) {\n            end = start + maxLength;\n        }\n        for (var i = start; i < end; ++i) {\n            s += this.hexByte(this.get(i));\n        }\n        if (len > maxLength) {\n            s += ellipsis;\n        }\n        return s;\n    };\n    Stream.prototype.parseOID = function (start, end, maxLength) {\n        var s = \"\";\n        var n = new Int10();\n        var bits = 0;\n        for (var i = start; i < end; ++i) {\n            var v = this.get(i);\n            n.mulAdd(128, v & 0x7F);\n            bits += 7;\n            if (!(v & 0x80)) { // finished\n                if (s === \"\") {\n                    n = n.simplify();\n                    if (n instanceof Int10) {\n                        n.sub(80);\n                        s = \"2.\" + n.toString();\n                    }\n                    else {\n                        var m = n < 80 ? n < 40 ? 0 : 1 : 2;\n                        s = m + \".\" + (n - m * 40);\n                    }\n                }\n                else {\n                    s += \".\" + n.toString();\n                }\n                if (s.length > maxLength) {\n                    return stringCut(s, maxLength);\n                }\n                n = new Int10();\n                bits = 0;\n            }\n        }\n        if (bits > 0) {\n            s += \".incomplete\";\n        }\n        return s;\n    };\n    return Stream;\n}());\nexport { Stream };\nvar ASN1 = /** @class */ (function () {\n    function ASN1(stream, header, length, tag, sub) {\n        if (!(tag instanceof ASN1Tag)) {\n            throw new Error(\"Invalid tag value.\");\n        }\n        this.stream = stream;\n        this.header = header;\n        this.length = length;\n        this.tag = tag;\n        this.sub = sub;\n    }\n    ASN1.prototype.typeName = function () {\n        switch (this.tag.tagClass) {\n            case 0: // universal\n                switch (this.tag.tagNumber) {\n                    case 0x00:\n                        return \"EOC\";\n                    case 0x01:\n                        return \"BOOLEAN\";\n                    case 0x02:\n                        return \"INTEGER\";\n                    case 0x03:\n                        return \"BIT_STRING\";\n                    case 0x04:\n                        return \"OCTET_STRING\";\n                    case 0x05:\n                        return \"NULL\";\n                    case 0x06:\n                        return \"OBJECT_IDENTIFIER\";\n                    case 0x07:\n                        return \"ObjectDescriptor\";\n                    case 0x08:\n                        return \"EXTERNAL\";\n                    case 0x09:\n                        return \"REAL\";\n                    case 0x0A:\n                        return \"ENUMERATED\";\n                    case 0x0B:\n                        return \"EMBEDDED_PDV\";\n                    case 0x0C:\n                        return \"UTF8String\";\n                    case 0x10:\n                        return \"SEQUENCE\";\n                    case 0x11:\n                        return \"SET\";\n                    case 0x12:\n                        return \"NumericString\";\n                    case 0x13:\n                        return \"PrintableString\"; // ASCII subset\n                    case 0x14:\n                        return \"TeletexString\"; // aka T61String\n                    case 0x15:\n                        return \"VideotexString\";\n                    case 0x16:\n                        return \"IA5String\"; // ASCII\n                    case 0x17:\n                        return \"UTCTime\";\n                    case 0x18:\n                        return \"GeneralizedTime\";\n                    case 0x19:\n                        return \"GraphicString\";\n                    case 0x1A:\n                        return \"VisibleString\"; // ASCII subset\n                    case 0x1B:\n                        return \"GeneralString\";\n                    case 0x1C:\n                        return \"UniversalString\";\n                    case 0x1E:\n                        return \"BMPString\";\n                }\n                return \"Universal_\" + this.tag.tagNumber.toString();\n            case 1:\n                return \"Application_\" + this.tag.tagNumber.toString();\n            case 2:\n                return \"[\" + this.tag.tagNumber.toString() + \"]\"; // Context\n            case 3:\n                return \"Private_\" + this.tag.tagNumber.toString();\n        }\n    };\n    ASN1.prototype.content = function (maxLength) {\n        if (this.tag === undefined) {\n            return null;\n        }\n        if (maxLength === undefined) {\n            maxLength = Infinity;\n        }\n        var content = this.posContent();\n        var len = Math.abs(this.length);\n        if (!this.tag.isUniversal()) {\n            if (this.sub !== null) {\n                return \"(\" + this.sub.length + \" elem)\";\n            }\n            return this.stream.parseOctetString(content, content + len, maxLength);\n        }\n        switch (this.tag.tagNumber) {\n            case 0x01: // BOOLEAN\n                return (this.stream.get(content) === 0) ? \"false\" : \"true\";\n            case 0x02: // INTEGER\n                return this.stream.parseInteger(content, content + len);\n            case 0x03: // BIT_STRING\n                return this.sub ? \"(\" + this.sub.length + \" elem)\" :\n                    this.stream.parseBitString(content, content + len, maxLength);\n            case 0x04: // OCTET_STRING\n                return this.sub ? \"(\" + this.sub.length + \" elem)\" :\n                    this.stream.parseOctetString(content, content + len, maxLength);\n            // case 0x05: // NULL\n            case 0x06: // OBJECT_IDENTIFIER\n                return this.stream.parseOID(content, content + len, maxLength);\n            // case 0x07: // ObjectDescriptor\n            // case 0x08: // EXTERNAL\n            // case 0x09: // REAL\n            // case 0x0A: // ENUMERATED\n            // case 0x0B: // EMBEDDED_PDV\n            case 0x10: // SEQUENCE\n            case 0x11: // SET\n                if (this.sub !== null) {\n                    return \"(\" + this.sub.length + \" elem)\";\n                }\n                else {\n                    return \"(no elem)\";\n                }\n            case 0x0C: // UTF8String\n                return stringCut(this.stream.parseStringUTF(content, content + len), maxLength);\n            case 0x12: // NumericString\n            case 0x13: // PrintableString\n            case 0x14: // TeletexString\n            case 0x15: // VideotexString\n            case 0x16: // IA5String\n            // case 0x19: // GraphicString\n            case 0x1A: // VisibleString\n                // case 0x1B: // GeneralString\n                // case 0x1C: // UniversalString\n                return stringCut(this.stream.parseStringISO(content, content + len), maxLength);\n            case 0x1E: // BMPString\n                return stringCut(this.stream.parseStringBMP(content, content + len), maxLength);\n            case 0x17: // UTCTime\n            case 0x18: // GeneralizedTime\n                return this.stream.parseTime(content, content + len, (this.tag.tagNumber == 0x17));\n        }\n        return null;\n    };\n    ASN1.prototype.toString = function () {\n        return this.typeName() + \"@\" + this.stream.pos + \"[header:\" + this.header + \",length:\" + this.length + \",sub:\" + ((this.sub === null) ? \"null\" : this.sub.length) + \"]\";\n    };\n    ASN1.prototype.toPrettyString = function (indent) {\n        if (indent === undefined) {\n            indent = \"\";\n        }\n        var s = indent + this.typeName() + \" @\" + this.stream.pos;\n        if (this.length >= 0) {\n            s += \"+\";\n        }\n        s += this.length;\n        if (this.tag.tagConstructed) {\n            s += \" (constructed)\";\n        }\n        else if ((this.tag.isUniversal() && ((this.tag.tagNumber == 0x03) || (this.tag.tagNumber == 0x04))) && (this.sub !== null)) {\n            s += \" (encapsulates)\";\n        }\n        s += \"\\n\";\n        if (this.sub !== null) {\n            indent += \"  \";\n            for (var i = 0, max = this.sub.length; i < max; ++i) {\n                s += this.sub[i].toPrettyString(indent);\n            }\n        }\n        return s;\n    };\n    ASN1.prototype.posStart = function () {\n        return this.stream.pos;\n    };\n    ASN1.prototype.posContent = function () {\n        return this.stream.pos + this.header;\n    };\n    ASN1.prototype.posEnd = function () {\n        return this.stream.pos + this.header + Math.abs(this.length);\n    };\n    ASN1.prototype.toHexString = function () {\n        return this.stream.hexDump(this.posStart(), this.posEnd(), true);\n    };\n    ASN1.decodeLength = function (stream) {\n        var buf = stream.get();\n        var len = buf & 0x7F;\n        if (len == buf) {\n            return len;\n        }\n        // no reason to use Int10, as it would be a huge buffer anyways\n        if (len > 6) {\n            throw new Error(\"Length over 48 bits not supported at position \" + (stream.pos - 1));\n        }\n        if (len === 0) {\n            return null;\n        } // undefined\n        buf = 0;\n        for (var i = 0; i < len; ++i) {\n            buf = (buf * 256) + stream.get();\n        }\n        return buf;\n    };\n    /**\n     * Retrieve the hexadecimal value (as a string) of the current ASN.1 element\n     * @returns {string}\n     * @public\n     */\n    ASN1.prototype.getHexStringValue = function () {\n        var hexString = this.toHexString();\n        var offset = this.header * 2;\n        var length = this.length * 2;\n        return hexString.substr(offset, length);\n    };\n    ASN1.decode = function (str) {\n        var stream;\n        if (!(str instanceof Stream)) {\n            stream = new Stream(str, 0);\n        }\n        else {\n            stream = str;\n        }\n        var streamStart = new Stream(stream);\n        var tag = new ASN1Tag(stream);\n        var len = ASN1.decodeLength(stream);\n        var start = stream.pos;\n        var header = start - streamStart.pos;\n        var sub = null;\n        var getSub = function () {\n            var ret = [];\n            if (len !== null) {\n                // definite length\n                var end = start + len;\n                while (stream.pos < end) {\n                    ret[ret.length] = ASN1.decode(stream);\n                }\n                if (stream.pos != end) {\n                    throw new Error(\"Content size is not correct for container starting at offset \" + start);\n                }\n            }\n            else {\n                // undefined length\n                try {\n                    for (;;) {\n                        var s = ASN1.decode(stream);\n                        if (s.tag.isEOC()) {\n                            break;\n                        }\n                        ret[ret.length] = s;\n                    }\n                    len = start - stream.pos; // undefined lengths are represented as negative values\n                }\n                catch (e) {\n                    throw new Error(\"Exception while decoding undefined length content: \" + e);\n                }\n            }\n            return ret;\n        };\n        if (tag.tagConstructed) {\n            // must have valid content\n            sub = getSub();\n        }\n        else if (tag.isUniversal() && ((tag.tagNumber == 0x03) || (tag.tagNumber == 0x04))) {\n            // sometimes BitString and OctetString are used to encapsulate ASN.1\n            try {\n                if (tag.tagNumber == 0x03) {\n                    if (stream.get() != 0) {\n                        throw new Error(\"BIT STRINGs with unused bits cannot encapsulate.\");\n                    }\n                }\n                sub = getSub();\n                for (var i = 0; i < sub.length; ++i) {\n                    if (sub[i].tag.isEOC()) {\n                        throw new Error(\"EOC is not supposed to be actual content.\");\n                    }\n                }\n            }\n            catch (e) {\n                // but silently ignore when they don't\n                sub = null;\n            }\n        }\n        if (sub === null) {\n            if (len === null) {\n                throw new Error(\"We can't skip over an invalid tag with undefined length at offset \" + start);\n            }\n            stream.pos = start + Math.abs(len);\n        }\n        return new ASN1(streamStart, header, len, tag, sub);\n    };\n    return ASN1;\n}());\nexport { ASN1 };\nvar ASN1Tag = /** @class */ (function () {\n    function ASN1Tag(stream) {\n        var buf = stream.get();\n        this.tagClass = buf >> 6;\n        this.tagConstructed = ((buf & 0x20) !== 0);\n        this.tagNumber = buf & 0x1F;\n        if (this.tagNumber == 0x1F) { // long tag\n            var n = new Int10();\n            do {\n                buf = stream.get();\n                n.mulAdd(128, buf & 0x7F);\n            } while (buf & 0x80);\n            this.tagNumber = n.simplify();\n        }\n    }\n    ASN1Tag.prototype.isUniversal = function () {\n        return this.tagClass === 0x00;\n    };\n    ASN1Tag.prototype.isEOC = function () {\n        return this.tagClass === 0x00 && this.tagNumber === 0x00;\n    };\n    return ASN1Tag;\n}());\nexport { ASN1Tag };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,SAAS;AAC/B,IAAIC,QAAQ,GAAG,QAAQ;AACvB,IAAIC,OAAO,GAAG,8IAA8I;AAC5J,IAAIC,OAAO,GAAG,kJAAkJ;AAChK,SAASC,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACzB,IAAID,GAAG,CAACE,MAAM,GAAGD,GAAG,EAAE;IAClBD,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAGL,QAAQ;EAC1C;EACA,OAAOI,GAAG;AACd;AACA,IAAII,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAI,CAACC,SAAS,GAAG,kBAAkB;IACnC,IAAIF,GAAG,YAAYD,MAAM,EAAE;MACvB,IAAI,CAACC,GAAG,GAAGA,GAAG,CAACA,GAAG;MAClB,IAAI,CAACC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACtB,CAAC,MACI;MACD;MACA,IAAI,CAACD,GAAG,GAAGA,GAAG;MACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IAClB;EACJ;EACAF,MAAM,CAACI,SAAS,CAACC,GAAG,GAAG,UAAUH,GAAG,EAAE;IAClC,IAAIA,GAAG,KAAKI,SAAS,EAAE;MACnBJ,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE;IACpB;IACA,IAAIA,GAAG,IAAI,IAAI,CAACD,GAAG,CAACH,MAAM,EAAE;MACxB,MAAM,IAAIS,KAAK,CAAC,yBAAyB,CAACC,MAAM,CAACN,GAAG,EAAE,yBAAyB,CAAC,CAACM,MAAM,CAAC,IAAI,CAACP,GAAG,CAACH,MAAM,CAAC,CAAC;IAC7G;IACA,OAAQ,QAAQ,KAAK,OAAO,IAAI,CAACG,GAAG,GAAI,IAAI,CAACA,GAAG,CAACQ,UAAU,CAACP,GAAG,CAAC,GAAG,IAAI,CAACD,GAAG,CAACC,GAAG,CAAC;EACpF,CAAC;EACDF,MAAM,CAACI,SAAS,CAACM,OAAO,GAAG,UAAUC,CAAC,EAAE;IACpC,OAAO,IAAI,CAACR,SAAS,CAACS,MAAM,CAAED,CAAC,IAAI,CAAC,GAAI,GAAG,CAAC,GAAG,IAAI,CAACR,SAAS,CAACS,MAAM,CAACD,CAAC,GAAG,GAAG,CAAC;EACjF,CAAC;EACDX,MAAM,CAACI,SAAS,CAACS,OAAO,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAClD,IAAIC,CAAC,GAAG,EAAE;IACV,KAAK,IAAIC,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAC9BD,CAAC,IAAI,IAAI,CAACP,OAAO,CAAC,IAAI,CAACL,GAAG,CAACa,CAAC,CAAC,CAAC;MAC9B,IAAIF,GAAG,KAAK,IAAI,EAAE;QACd,QAAQE,CAAC,GAAG,GAAG;UACX,KAAK,GAAG;YACJD,CAAC,IAAI,IAAI;YACT;UACJ,KAAK,GAAG;YACJA,CAAC,IAAI,IAAI;YACT;UACJ;YACIA,CAAC,IAAI,GAAG;QAChB;MACJ;IACJ;IACA,OAAOA,CAAC;EACZ,CAAC;EACDjB,MAAM,CAACI,SAAS,CAACe,OAAO,GAAG,UAAUL,KAAK,EAAEC,GAAG,EAAE;IAC7C,KAAK,IAAIG,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAC9B,IAAIE,CAAC,GAAG,IAAI,CAACf,GAAG,CAACa,CAAC,CAAC;MACnB,IAAIE,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,EAAE;QACnB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDpB,MAAM,CAACI,SAAS,CAACiB,cAAc,GAAG,UAAUP,KAAK,EAAEC,GAAG,EAAE;IACpD,IAAIE,CAAC,GAAG,EAAE;IACV,KAAK,IAAIC,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAC9BD,CAAC,IAAIK,MAAM,CAACC,YAAY,CAAC,IAAI,CAAClB,GAAG,CAACa,CAAC,CAAC,CAAC;IACzC;IACA,OAAOD,CAAC;EACZ,CAAC;EACDjB,MAAM,CAACI,SAAS,CAACoB,cAAc,GAAG,UAAUV,KAAK,EAAEC,GAAG,EAAE;IACpD,IAAIE,CAAC,GAAG,EAAE;IACV,KAAK,IAAIC,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,GAAG;MAC1B,IAAIK,CAAC,GAAG,IAAI,CAACf,GAAG,CAACa,CAAC,EAAE,CAAC;MACrB,IAAIE,CAAC,GAAG,GAAG,EAAE;QACTH,CAAC,IAAIK,MAAM,CAACC,YAAY,CAACH,CAAC,CAAC;MAC/B,CAAC,MACI,IAAKA,CAAC,GAAG,GAAG,IAAMA,CAAC,GAAG,GAAI,EAAE;QAC7BH,CAAC,IAAIK,MAAM,CAACC,YAAY,CAAE,CAACH,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,IAAI,CAACf,GAAG,CAACa,CAAC,EAAE,CAAC,GAAG,IAAK,CAAC;MACxE,CAAC,MACI;QACDD,CAAC,IAAIK,MAAM,CAACC,YAAY,CAAE,CAACH,CAAC,GAAG,IAAI,KAAK,EAAE,GAAK,CAAC,IAAI,CAACf,GAAG,CAACa,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAE,GAAI,IAAI,CAACb,GAAG,CAACa,CAAC,EAAE,CAAC,GAAG,IAAK,CAAC;MACzG;IACJ;IACA,OAAOD,CAAC;EACZ,CAAC;EACDjB,MAAM,CAACI,SAAS,CAACqB,cAAc,GAAG,UAAUX,KAAK,EAAEC,GAAG,EAAE;IACpD,IAAInB,GAAG,GAAG,EAAE;IACZ,IAAI8B,EAAE;IACN,IAAIC,EAAE;IACN,KAAK,IAAIT,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,GAAG;MAC1BW,EAAE,GAAG,IAAI,CAACrB,GAAG,CAACa,CAAC,EAAE,CAAC;MAClBS,EAAE,GAAG,IAAI,CAACtB,GAAG,CAACa,CAAC,EAAE,CAAC;MAClBtB,GAAG,IAAI0B,MAAM,CAACC,YAAY,CAAEG,EAAE,IAAI,CAAC,GAAIC,EAAE,CAAC;IAC9C;IACA,OAAO/B,GAAG;EACd,CAAC;EACDI,MAAM,CAACI,SAAS,CAACwB,SAAS,GAAG,UAAUd,KAAK,EAAEC,GAAG,EAAEc,SAAS,EAAE;IAC1D,IAAIZ,CAAC,GAAG,IAAI,CAACI,cAAc,CAACP,KAAK,EAAEC,GAAG,CAAC;IACvC,IAAIe,CAAC,GAAG,CAACD,SAAS,GAAGpC,OAAO,GAAGC,OAAO,EAAEqC,IAAI,CAACd,CAAC,CAAC;IAC/C,IAAI,CAACa,CAAC,EAAE;MACJ,OAAO,qBAAqB,GAAGb,CAAC;IACpC;IACA,IAAIY,SAAS,EAAE;MACX;MACA;MACAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC;MACZA,CAAC,CAAC,CAAC,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAI,IAAI,GAAG,IAAI;IACtC;IACAb,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;MACNb,CAAC,IAAI,GAAG,GAAGa,CAAC,CAAC,CAAC,CAAC;MACf,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;QACNb,CAAC,IAAI,GAAG,GAAGa,CAAC,CAAC,CAAC,CAAC;QACf,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;UACNb,CAAC,IAAI,GAAG,GAAGa,CAAC,CAAC,CAAC,CAAC;QACnB;MACJ;IACJ;IACA,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;MACNb,CAAC,IAAI,MAAM;MACX,IAAIa,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QACbb,CAAC,IAAIa,CAAC,CAAC,CAAC,CAAC;QACT,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;UACNb,CAAC,IAAI,GAAG,GAAGa,CAAC,CAAC,CAAC,CAAC;QACnB;MACJ;IACJ;IACA,OAAOb,CAAC;EACZ,CAAC;EACDjB,MAAM,CAACI,SAAS,CAAC4B,YAAY,GAAG,UAAUlB,KAAK,EAAEC,GAAG,EAAE;IAClD,IAAIkB,CAAC,GAAG,IAAI,CAAC5B,GAAG,CAACS,KAAK,CAAC;IACvB,IAAIoB,GAAG,GAAID,CAAC,GAAG,GAAI;IACnB,IAAIE,GAAG,GAAGD,GAAG,GAAG,GAAG,GAAG,CAAC;IACvB,IAAIrC,GAAG;IACP,IAAIoB,CAAC,GAAG,EAAE;IACV;IACA,OAAOgB,CAAC,IAAIE,GAAG,IAAI,EAAErB,KAAK,GAAGC,GAAG,EAAE;MAC9BkB,CAAC,GAAG,IAAI,CAAC5B,GAAG,CAACS,KAAK,CAAC;IACvB;IACAjB,GAAG,GAAGkB,GAAG,GAAGD,KAAK;IACjB,IAAIjB,GAAG,KAAK,CAAC,EAAE;MACX,OAAOqC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IACvB;IACA;IACA,IAAIrC,GAAG,GAAG,CAAC,EAAE;MACToB,CAAC,GAAGgB,CAAC;MACLpC,GAAG,KAAK,CAAC;MACT,OAAO,CAAC,CAAC,CAACoB,CAAC,GAAGkB,GAAG,IAAI,IAAI,KAAK,CAAC,EAAE;QAC7BlB,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC;QACX,EAAEpB,GAAG;MACT;MACAoB,CAAC,GAAG,GAAG,GAAGpB,GAAG,GAAG,SAAS;IAC7B;IACA;IACA,IAAIqC,GAAG,EAAE;MACLD,CAAC,GAAGA,CAAC,GAAG,GAAG;IACf;IACA,IAAIG,CAAC,GAAG,IAAI7C,KAAK,CAAC0C,CAAC,CAAC;IACpB,KAAK,IAAIf,CAAC,GAAGJ,KAAK,GAAG,CAAC,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAClCkB,CAAC,CAACC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAChC,GAAG,CAACa,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOD,CAAC,GAAGmB,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC3B,CAAC;EACDtC,MAAM,CAACI,SAAS,CAACmC,cAAc,GAAG,UAAUzB,KAAK,EAAEC,GAAG,EAAEyB,SAAS,EAAE;IAC/D,IAAIC,SAAS,GAAG,IAAI,CAACpC,GAAG,CAACS,KAAK,CAAC;IAC/B,IAAI4B,MAAM,GAAG,CAAE3B,GAAG,GAAGD,KAAK,GAAG,CAAC,IAAK,CAAC,IAAI2B,SAAS;IACjD,IAAIE,KAAK,GAAG,GAAG,GAAGD,MAAM,GAAG,SAAS;IACpC,IAAIzB,CAAC,GAAG,EAAE;IACV,KAAK,IAAIC,CAAC,GAAGJ,KAAK,GAAG,CAAC,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAClC,IAAIP,CAAC,GAAG,IAAI,CAACN,GAAG,CAACa,CAAC,CAAC;MACnB,IAAI0B,IAAI,GAAI1B,CAAC,IAAIH,GAAG,GAAG,CAAC,GAAI0B,SAAS,GAAG,CAAC;MACzC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,IAAI,EAAE,EAAEC,CAAC,EAAE;QAC5B5B,CAAC,IAAKN,CAAC,IAAIkC,CAAC,GAAI,CAAC,GAAG,GAAG,GAAG,GAAG;MACjC;MACA,IAAI5B,CAAC,CAACnB,MAAM,GAAG0C,SAAS,EAAE;QACtB,OAAOG,KAAK,GAAGhD,SAAS,CAACsB,CAAC,EAAEuB,SAAS,CAAC;MAC1C;IACJ;IACA,OAAOG,KAAK,GAAG1B,CAAC;EACpB,CAAC;EACDjB,MAAM,CAACI,SAAS,CAAC0C,gBAAgB,GAAG,UAAUhC,KAAK,EAAEC,GAAG,EAAEyB,SAAS,EAAE;IACjE,IAAI,IAAI,CAACrB,OAAO,CAACL,KAAK,EAAEC,GAAG,CAAC,EAAE;MAC1B,OAAOpB,SAAS,CAAC,IAAI,CAAC0B,cAAc,CAACP,KAAK,EAAEC,GAAG,CAAC,EAAEyB,SAAS,CAAC;IAChE;IACA,IAAI3C,GAAG,GAAGkB,GAAG,GAAGD,KAAK;IACrB,IAAIG,CAAC,GAAG,GAAG,GAAGpB,GAAG,GAAG,UAAU;IAC9B2C,SAAS,IAAI,CAAC,CAAC,CAAC;IAChB,IAAI3C,GAAG,GAAG2C,SAAS,EAAE;MACjBzB,GAAG,GAAGD,KAAK,GAAG0B,SAAS;IAC3B;IACA,KAAK,IAAItB,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAC9BD,CAAC,IAAI,IAAI,CAACP,OAAO,CAAC,IAAI,CAACL,GAAG,CAACa,CAAC,CAAC,CAAC;IAClC;IACA,IAAIrB,GAAG,GAAG2C,SAAS,EAAE;MACjBvB,CAAC,IAAIzB,QAAQ;IACjB;IACA,OAAOyB,CAAC;EACZ,CAAC;EACDjB,MAAM,CAACI,SAAS,CAAC2C,QAAQ,GAAG,UAAUjC,KAAK,EAAEC,GAAG,EAAEyB,SAAS,EAAE;IACzD,IAAIvB,CAAC,GAAG,EAAE;IACV,IAAImB,CAAC,GAAG,IAAI7C,KAAK,CAAC,CAAC;IACnB,IAAIyD,IAAI,GAAG,CAAC;IACZ,KAAK,IAAI9B,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAE,EAAEG,CAAC,EAAE;MAC9B,IAAIe,CAAC,GAAG,IAAI,CAAC5B,GAAG,CAACa,CAAC,CAAC;MACnBkB,CAAC,CAACC,MAAM,CAAC,GAAG,EAAEJ,CAAC,GAAG,IAAI,CAAC;MACvBe,IAAI,IAAI,CAAC;MACT,IAAI,EAAEf,CAAC,GAAG,IAAI,CAAC,EAAE;QAAE;QACf,IAAIhB,CAAC,KAAK,EAAE,EAAE;UACVmB,CAAC,GAAGA,CAAC,CAACa,QAAQ,CAAC,CAAC;UAChB,IAAIb,CAAC,YAAY7C,KAAK,EAAE;YACpB6C,CAAC,CAACc,GAAG,CAAC,EAAE,CAAC;YACTjC,CAAC,GAAG,IAAI,GAAGmB,CAAC,CAACE,QAAQ,CAAC,CAAC;UAC3B,CAAC,MACI;YACD,IAAIR,CAAC,GAAGM,CAAC,GAAG,EAAE,GAAGA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;YACnCnB,CAAC,GAAGa,CAAC,GAAG,GAAG,IAAIM,CAAC,GAAGN,CAAC,GAAG,EAAE,CAAC;UAC9B;QACJ,CAAC,MACI;UACDb,CAAC,IAAI,GAAG,GAAGmB,CAAC,CAACE,QAAQ,CAAC,CAAC;QAC3B;QACA,IAAIrB,CAAC,CAACnB,MAAM,GAAG0C,SAAS,EAAE;UACtB,OAAO7C,SAAS,CAACsB,CAAC,EAAEuB,SAAS,CAAC;QAClC;QACAJ,CAAC,GAAG,IAAI7C,KAAK,CAAC,CAAC;QACfyD,IAAI,GAAG,CAAC;MACZ;IACJ;IACA,IAAIA,IAAI,GAAG,CAAC,EAAE;MACV/B,CAAC,IAAI,aAAa;IACtB;IACA,OAAOA,CAAC;EACZ,CAAC;EACD,OAAOjB,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf,IAAImD,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAACC,MAAM,EAAEC,MAAM,EAAEvD,MAAM,EAAEwD,GAAG,EAAEJ,GAAG,EAAE;IAC5C,IAAI,EAAEI,GAAG,YAAYC,OAAO,CAAC,EAAE;MAC3B,MAAM,IAAIhD,KAAK,CAAC,oBAAoB,CAAC;IACzC;IACA,IAAI,CAAC6C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACvD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACwD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACJ,GAAG,GAAGA,GAAG;EAClB;EACAC,IAAI,CAAC/C,SAAS,CAACoD,QAAQ,GAAG,YAAY;IAClC,QAAQ,IAAI,CAACF,GAAG,CAACG,QAAQ;MACrB,KAAK,CAAC;QAAE;QACJ,QAAQ,IAAI,CAACH,GAAG,CAACI,SAAS;UACtB,KAAK,IAAI;YACL,OAAO,KAAK;UAChB,KAAK,IAAI;YACL,OAAO,SAAS;UACpB,KAAK,IAAI;YACL,OAAO,SAAS;UACpB,KAAK,IAAI;YACL,OAAO,YAAY;UACvB,KAAK,IAAI;YACL,OAAO,cAAc;UACzB,KAAK,IAAI;YACL,OAAO,MAAM;UACjB,KAAK,IAAI;YACL,OAAO,mBAAmB;UAC9B,KAAK,IAAI;YACL,OAAO,kBAAkB;UAC7B,KAAK,IAAI;YACL,OAAO,UAAU;UACrB,KAAK,IAAI;YACL,OAAO,MAAM;UACjB,KAAK,IAAI;YACL,OAAO,YAAY;UACvB,KAAK,IAAI;YACL,OAAO,cAAc;UACzB,KAAK,IAAI;YACL,OAAO,YAAY;UACvB,KAAK,IAAI;YACL,OAAO,UAAU;UACrB,KAAK,IAAI;YACL,OAAO,KAAK;UAChB,KAAK,IAAI;YACL,OAAO,eAAe;UAC1B,KAAK,IAAI;YACL,OAAO,iBAAiB;UAAE;UAC9B,KAAK,IAAI;YACL,OAAO,eAAe;UAAE;UAC5B,KAAK,IAAI;YACL,OAAO,gBAAgB;UAC3B,KAAK,IAAI;YACL,OAAO,WAAW;UAAE;UACxB,KAAK,IAAI;YACL,OAAO,SAAS;UACpB,KAAK,IAAI;YACL,OAAO,iBAAiB;UAC5B,KAAK,IAAI;YACL,OAAO,eAAe;UAC1B,KAAK,IAAI;YACL,OAAO,eAAe;UAAE;UAC5B,KAAK,IAAI;YACL,OAAO,eAAe;UAC1B,KAAK,IAAI;YACL,OAAO,iBAAiB;UAC5B,KAAK,IAAI;YACL,OAAO,WAAW;QAC1B;QACA,OAAO,YAAY,GAAG,IAAI,CAACJ,GAAG,CAACI,SAAS,CAACpB,QAAQ,CAAC,CAAC;MACvD,KAAK,CAAC;QACF,OAAO,cAAc,GAAG,IAAI,CAACgB,GAAG,CAACI,SAAS,CAACpB,QAAQ,CAAC,CAAC;MACzD,KAAK,CAAC;QACF,OAAO,GAAG,GAAG,IAAI,CAACgB,GAAG,CAACI,SAAS,CAACpB,QAAQ,CAAC,CAAC,GAAG,GAAG;MAAE;MACtD,KAAK,CAAC;QACF,OAAO,UAAU,GAAG,IAAI,CAACgB,GAAG,CAACI,SAAS,CAACpB,QAAQ,CAAC,CAAC;IACzD;EACJ,CAAC;EACDa,IAAI,CAAC/C,SAAS,CAACuD,OAAO,GAAG,UAAUnB,SAAS,EAAE;IAC1C,IAAI,IAAI,CAACc,GAAG,KAAKhD,SAAS,EAAE;MACxB,OAAO,IAAI;IACf;IACA,IAAIkC,SAAS,KAAKlC,SAAS,EAAE;MACzBkC,SAAS,GAAGoB,QAAQ;IACxB;IACA,IAAID,OAAO,GAAG,IAAI,CAACE,UAAU,CAAC,CAAC;IAC/B,IAAIhE,GAAG,GAAGiE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjE,MAAM,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACwD,GAAG,CAACU,WAAW,CAAC,CAAC,EAAE;MACzB,IAAI,IAAI,CAACd,GAAG,KAAK,IAAI,EAAE;QACnB,OAAO,GAAG,GAAG,IAAI,CAACA,GAAG,CAACpD,MAAM,GAAG,QAAQ;MAC3C;MACA,OAAO,IAAI,CAACsD,MAAM,CAACN,gBAAgB,CAACa,OAAO,EAAEA,OAAO,GAAG9D,GAAG,EAAE2C,SAAS,CAAC;IAC1E;IACA,QAAQ,IAAI,CAACc,GAAG,CAACI,SAAS;MACtB,KAAK,IAAI;QAAE;QACP,OAAQ,IAAI,CAACN,MAAM,CAAC/C,GAAG,CAACsD,OAAO,CAAC,KAAK,CAAC,GAAI,OAAO,GAAG,MAAM;MAC9D,KAAK,IAAI;QAAE;QACP,OAAO,IAAI,CAACP,MAAM,CAACpB,YAAY,CAAC2B,OAAO,EAAEA,OAAO,GAAG9D,GAAG,CAAC;MAC3D,KAAK,IAAI;QAAE;QACP,OAAO,IAAI,CAACqD,GAAG,GAAG,GAAG,GAAG,IAAI,CAACA,GAAG,CAACpD,MAAM,GAAG,QAAQ,GAC9C,IAAI,CAACsD,MAAM,CAACb,cAAc,CAACoB,OAAO,EAAEA,OAAO,GAAG9D,GAAG,EAAE2C,SAAS,CAAC;MACrE,KAAK,IAAI;QAAE;QACP,OAAO,IAAI,CAACU,GAAG,GAAG,GAAG,GAAG,IAAI,CAACA,GAAG,CAACpD,MAAM,GAAG,QAAQ,GAC9C,IAAI,CAACsD,MAAM,CAACN,gBAAgB,CAACa,OAAO,EAAEA,OAAO,GAAG9D,GAAG,EAAE2C,SAAS,CAAC;MACvE;MACA,KAAK,IAAI;QAAE;QACP,OAAO,IAAI,CAACY,MAAM,CAACL,QAAQ,CAACY,OAAO,EAAEA,OAAO,GAAG9D,GAAG,EAAE2C,SAAS,CAAC;MAClE;MACA;MACA;MACA;MACA;MACA,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI;QAAE;QACP,IAAI,IAAI,CAACU,GAAG,KAAK,IAAI,EAAE;UACnB,OAAO,GAAG,GAAG,IAAI,CAACA,GAAG,CAACpD,MAAM,GAAG,QAAQ;QAC3C,CAAC,MACI;UACD,OAAO,WAAW;QACtB;MACJ,KAAK,IAAI;QAAE;QACP,OAAOH,SAAS,CAAC,IAAI,CAACyD,MAAM,CAAC5B,cAAc,CAACmC,OAAO,EAAEA,OAAO,GAAG9D,GAAG,CAAC,EAAE2C,SAAS,CAAC;MACnF,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI,CAAC,CAAC;MACX;MACA,KAAK,IAAI;QAAE;QACP;QACA;QACA,OAAO7C,SAAS,CAAC,IAAI,CAACyD,MAAM,CAAC/B,cAAc,CAACsC,OAAO,EAAEA,OAAO,GAAG9D,GAAG,CAAC,EAAE2C,SAAS,CAAC;MACnF,KAAK,IAAI;QAAE;QACP,OAAO7C,SAAS,CAAC,IAAI,CAACyD,MAAM,CAAC3B,cAAc,CAACkC,OAAO,EAAEA,OAAO,GAAG9D,GAAG,CAAC,EAAE2C,SAAS,CAAC;MACnF,KAAK,IAAI,CAAC,CAAC;MACX,KAAK,IAAI;QAAE;QACP,OAAO,IAAI,CAACY,MAAM,CAACxB,SAAS,CAAC+B,OAAO,EAAEA,OAAO,GAAG9D,GAAG,EAAG,IAAI,CAACyD,GAAG,CAACI,SAAS,IAAI,IAAK,CAAC;IAC1F;IACA,OAAO,IAAI;EACf,CAAC;EACDP,IAAI,CAAC/C,SAAS,CAACkC,QAAQ,GAAG,YAAY;IAClC,OAAO,IAAI,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACJ,MAAM,CAAClD,GAAG,GAAG,UAAU,GAAG,IAAI,CAACmD,MAAM,GAAG,UAAU,GAAG,IAAI,CAACvD,MAAM,GAAG,OAAO,IAAK,IAAI,CAACoD,GAAG,KAAK,IAAI,GAAI,MAAM,GAAG,IAAI,CAACA,GAAG,CAACpD,MAAM,CAAC,GAAG,GAAG;EAC3K,CAAC;EACDqD,IAAI,CAAC/C,SAAS,CAAC6D,cAAc,GAAG,UAAUC,MAAM,EAAE;IAC9C,IAAIA,MAAM,KAAK5D,SAAS,EAAE;MACtB4D,MAAM,GAAG,EAAE;IACf;IACA,IAAIjD,CAAC,GAAGiD,MAAM,GAAG,IAAI,CAACV,QAAQ,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACJ,MAAM,CAAClD,GAAG;IACzD,IAAI,IAAI,CAACJ,MAAM,IAAI,CAAC,EAAE;MAClBmB,CAAC,IAAI,GAAG;IACZ;IACAA,CAAC,IAAI,IAAI,CAACnB,MAAM;IAChB,IAAI,IAAI,CAACwD,GAAG,CAACa,cAAc,EAAE;MACzBlD,CAAC,IAAI,gBAAgB;IACzB,CAAC,MACI,IAAK,IAAI,CAACqC,GAAG,CAACU,WAAW,CAAC,CAAC,KAAM,IAAI,CAACV,GAAG,CAACI,SAAS,IAAI,IAAI,IAAM,IAAI,CAACJ,GAAG,CAACI,SAAS,IAAI,IAAK,CAAC,IAAM,IAAI,CAACR,GAAG,KAAK,IAAK,EAAE;MACxHjC,CAAC,IAAI,iBAAiB;IAC1B;IACAA,CAAC,IAAI,IAAI;IACT,IAAI,IAAI,CAACiC,GAAG,KAAK,IAAI,EAAE;MACnBgB,MAAM,IAAI,IAAI;MACd,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEkD,GAAG,GAAG,IAAI,CAAClB,GAAG,CAACpD,MAAM,EAAEoB,CAAC,GAAGkD,GAAG,EAAE,EAAElD,CAAC,EAAE;QACjDD,CAAC,IAAI,IAAI,CAACiC,GAAG,CAAChC,CAAC,CAAC,CAAC+C,cAAc,CAACC,MAAM,CAAC;MAC3C;IACJ;IACA,OAAOjD,CAAC;EACZ,CAAC;EACDkC,IAAI,CAAC/C,SAAS,CAACiE,QAAQ,GAAG,YAAY;IAClC,OAAO,IAAI,CAACjB,MAAM,CAAClD,GAAG;EAC1B,CAAC;EACDiD,IAAI,CAAC/C,SAAS,CAACyD,UAAU,GAAG,YAAY;IACpC,OAAO,IAAI,CAACT,MAAM,CAAClD,GAAG,GAAG,IAAI,CAACmD,MAAM;EACxC,CAAC;EACDF,IAAI,CAAC/C,SAAS,CAACkE,MAAM,GAAG,YAAY;IAChC,OAAO,IAAI,CAAClB,MAAM,CAAClD,GAAG,GAAG,IAAI,CAACmD,MAAM,GAAGS,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjE,MAAM,CAAC;EAChE,CAAC;EACDqD,IAAI,CAAC/C,SAAS,CAACmE,WAAW,GAAG,YAAY;IACrC,OAAO,IAAI,CAACnB,MAAM,CAACvC,OAAO,CAAC,IAAI,CAACwD,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC;EACDnB,IAAI,CAACqB,YAAY,GAAG,UAAUpB,MAAM,EAAE;IAClC,IAAIqB,GAAG,GAAGrB,MAAM,CAAC/C,GAAG,CAAC,CAAC;IACtB,IAAIR,GAAG,GAAG4E,GAAG,GAAG,IAAI;IACpB,IAAI5E,GAAG,IAAI4E,GAAG,EAAE;MACZ,OAAO5E,GAAG;IACd;IACA;IACA,IAAIA,GAAG,GAAG,CAAC,EAAE;MACT,MAAM,IAAIU,KAAK,CAAC,gDAAgD,IAAI6C,MAAM,CAAClD,GAAG,GAAG,CAAC,CAAC,CAAC;IACxF;IACA,IAAIL,GAAG,KAAK,CAAC,EAAE;MACX,OAAO,IAAI;IACf,CAAC,CAAC;IACF4E,GAAG,GAAG,CAAC;IACP,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,GAAG,EAAE,EAAEqB,CAAC,EAAE;MAC1BuD,GAAG,GAAIA,GAAG,GAAG,GAAG,GAAIrB,MAAM,CAAC/C,GAAG,CAAC,CAAC;IACpC;IACA,OAAOoE,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;EACItB,IAAI,CAAC/C,SAAS,CAACsE,iBAAiB,GAAG,YAAY;IAC3C,IAAIC,SAAS,GAAG,IAAI,CAACJ,WAAW,CAAC,CAAC;IAClC,IAAIK,MAAM,GAAG,IAAI,CAACvB,MAAM,GAAG,CAAC;IAC5B,IAAIvD,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC;IAC5B,OAAO6E,SAAS,CAACE,MAAM,CAACD,MAAM,EAAE9E,MAAM,CAAC;EAC3C,CAAC;EACDqD,IAAI,CAAC2B,MAAM,GAAG,UAAUlF,GAAG,EAAE;IACzB,IAAIwD,MAAM;IACV,IAAI,EAAExD,GAAG,YAAYI,MAAM,CAAC,EAAE;MAC1BoD,MAAM,GAAG,IAAIpD,MAAM,CAACJ,GAAG,EAAE,CAAC,CAAC;IAC/B,CAAC,MACI;MACDwD,MAAM,GAAGxD,GAAG;IAChB;IACA,IAAImF,WAAW,GAAG,IAAI/E,MAAM,CAACoD,MAAM,CAAC;IACpC,IAAIE,GAAG,GAAG,IAAIC,OAAO,CAACH,MAAM,CAAC;IAC7B,IAAIvD,GAAG,GAAGsD,IAAI,CAACqB,YAAY,CAACpB,MAAM,CAAC;IACnC,IAAItC,KAAK,GAAGsC,MAAM,CAAClD,GAAG;IACtB,IAAImD,MAAM,GAAGvC,KAAK,GAAGiE,WAAW,CAAC7E,GAAG;IACpC,IAAIgD,GAAG,GAAG,IAAI;IACd,IAAI8B,MAAM,GAAG,SAAAA,CAAA,EAAY;MACrB,IAAIC,GAAG,GAAG,EAAE;MACZ,IAAIpF,GAAG,KAAK,IAAI,EAAE;QACd;QACA,IAAIkB,GAAG,GAAGD,KAAK,GAAGjB,GAAG;QACrB,OAAOuD,MAAM,CAAClD,GAAG,GAAGa,GAAG,EAAE;UACrBkE,GAAG,CAACA,GAAG,CAACnF,MAAM,CAAC,GAAGqD,IAAI,CAAC2B,MAAM,CAAC1B,MAAM,CAAC;QACzC;QACA,IAAIA,MAAM,CAAClD,GAAG,IAAIa,GAAG,EAAE;UACnB,MAAM,IAAIR,KAAK,CAAC,+DAA+D,GAAGO,KAAK,CAAC;QAC5F;MACJ,CAAC,MACI;QACD;QACA,IAAI;UACA,SAAS;YACL,IAAIG,CAAC,GAAGkC,IAAI,CAAC2B,MAAM,CAAC1B,MAAM,CAAC;YAC3B,IAAInC,CAAC,CAACqC,GAAG,CAAC4B,KAAK,CAAC,CAAC,EAAE;cACf;YACJ;YACAD,GAAG,CAACA,GAAG,CAACnF,MAAM,CAAC,GAAGmB,CAAC;UACvB;UACApB,GAAG,GAAGiB,KAAK,GAAGsC,MAAM,CAAClD,GAAG,CAAC,CAAC;QAC9B,CAAC,CACD,OAAOiF,CAAC,EAAE;UACN,MAAM,IAAI5E,KAAK,CAAC,qDAAqD,GAAG4E,CAAC,CAAC;QAC9E;MACJ;MACA,OAAOF,GAAG;IACd,CAAC;IACD,IAAI3B,GAAG,CAACa,cAAc,EAAE;MACpB;MACAjB,GAAG,GAAG8B,MAAM,CAAC,CAAC;IAClB,CAAC,MACI,IAAI1B,GAAG,CAACU,WAAW,CAAC,CAAC,KAAMV,GAAG,CAACI,SAAS,IAAI,IAAI,IAAMJ,GAAG,CAACI,SAAS,IAAI,IAAK,CAAC,EAAE;MAChF;MACA,IAAI;QACA,IAAIJ,GAAG,CAACI,SAAS,IAAI,IAAI,EAAE;UACvB,IAAIN,MAAM,CAAC/C,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE;YACnB,MAAM,IAAIE,KAAK,CAAC,kDAAkD,CAAC;UACvE;QACJ;QACA2C,GAAG,GAAG8B,MAAM,CAAC,CAAC;QACd,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,GAAG,CAACpD,MAAM,EAAE,EAAEoB,CAAC,EAAE;UACjC,IAAIgC,GAAG,CAAChC,CAAC,CAAC,CAACoC,GAAG,CAAC4B,KAAK,CAAC,CAAC,EAAE;YACpB,MAAM,IAAI3E,KAAK,CAAC,2CAA2C,CAAC;UAChE;QACJ;MACJ,CAAC,CACD,OAAO4E,CAAC,EAAE;QACN;QACAjC,GAAG,GAAG,IAAI;MACd;IACJ;IACA,IAAIA,GAAG,KAAK,IAAI,EAAE;MACd,IAAIrD,GAAG,KAAK,IAAI,EAAE;QACd,MAAM,IAAIU,KAAK,CAAC,oEAAoE,GAAGO,KAAK,CAAC;MACjG;MACAsC,MAAM,CAAClD,GAAG,GAAGY,KAAK,GAAGgD,IAAI,CAACC,GAAG,CAAClE,GAAG,CAAC;IACtC;IACA,OAAO,IAAIsD,IAAI,CAAC4B,WAAW,EAAE1B,MAAM,EAAExD,GAAG,EAAEyD,GAAG,EAAEJ,GAAG,CAAC;EACvD,CAAC;EACD,OAAOC,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,SAASA,IAAI;AACb,IAAII,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACH,MAAM,EAAE;IACrB,IAAIqB,GAAG,GAAGrB,MAAM,CAAC/C,GAAG,CAAC,CAAC;IACtB,IAAI,CAACoD,QAAQ,GAAGgB,GAAG,IAAI,CAAC;IACxB,IAAI,CAACN,cAAc,GAAI,CAACM,GAAG,GAAG,IAAI,MAAM,CAAE;IAC1C,IAAI,CAACf,SAAS,GAAGe,GAAG,GAAG,IAAI;IAC3B,IAAI,IAAI,CAACf,SAAS,IAAI,IAAI,EAAE;MAAE;MAC1B,IAAItB,CAAC,GAAG,IAAI7C,KAAK,CAAC,CAAC;MACnB,GAAG;QACCkF,GAAG,GAAGrB,MAAM,CAAC/C,GAAG,CAAC,CAAC;QAClB+B,CAAC,CAACC,MAAM,CAAC,GAAG,EAAEoC,GAAG,GAAG,IAAI,CAAC;MAC7B,CAAC,QAAQA,GAAG,GAAG,IAAI;MACnB,IAAI,CAACf,SAAS,GAAGtB,CAAC,CAACa,QAAQ,CAAC,CAAC;IACjC;EACJ;EACAM,OAAO,CAACnD,SAAS,CAAC4D,WAAW,GAAG,YAAY;IACxC,OAAO,IAAI,CAACP,QAAQ,KAAK,IAAI;EACjC,CAAC;EACDF,OAAO,CAACnD,SAAS,CAAC8E,KAAK,GAAG,YAAY;IAClC,OAAO,IAAI,CAACzB,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACC,SAAS,KAAK,IAAI;EAC5D,CAAC;EACD,OAAOH,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,SAASA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}