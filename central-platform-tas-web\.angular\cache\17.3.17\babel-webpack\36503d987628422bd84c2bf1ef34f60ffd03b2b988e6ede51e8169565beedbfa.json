{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'më pak se një sekondë',\n    other: 'më pak se {{count}} sekonda'\n  },\n  xSeconds: {\n    one: '1 sekondë',\n    other: '{{count}} sekonda'\n  },\n  halfAMinute: 'gjysëm minuti',\n  lessThanXMinutes: {\n    one: 'më pak se një minute',\n    other: 'më pak se {{count}} minuta'\n  },\n  xMinutes: {\n    one: '1 minutë',\n    other: '{{count}} minuta'\n  },\n  aboutXHours: {\n    one: 'rreth 1 orë',\n    other: 'rreth {{count}} orë'\n  },\n  xHours: {\n    one: '1 orë',\n    other: '{{count}} orë'\n  },\n  xDays: {\n    one: '1 ditë',\n    other: '{{count}} ditë'\n  },\n  aboutXWeeks: {\n    one: 'rreth 1 javë',\n    other: 'rreth {{count}} javë'\n  },\n  xWeeks: {\n    one: '1 javë',\n    other: '{{count}} javë'\n  },\n  aboutXMonths: {\n    one: 'rreth 1 muaj',\n    other: 'rreth {{count}} muaj'\n  },\n  xMonths: {\n    one: '1 muaj',\n    other: '{{count}} muaj'\n  },\n  aboutXYears: {\n    one: 'rreth 1 vit',\n    other: 'rreth {{count}} vite'\n  },\n  xYears: {\n    one: '1 vit',\n    other: '{{count}} vite'\n  },\n  overXYears: {\n    one: 'mbi 1 vit',\n    other: 'mbi {{count}} vite'\n  },\n  almostXYears: {\n    one: 'pothuajse 1 vit',\n    other: 'pothuajse {{count}} vite'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'në ' + result;\n    } else {\n      return result + ' më parë';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/sq/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'më pak se një sekondë',\n    other: 'më pak se {{count}} sekonda'\n  },\n  xSeconds: {\n    one: '1 sekondë',\n    other: '{{count}} sekonda'\n  },\n  halfAMinute: 'gjysëm minuti',\n  lessThanXMinutes: {\n    one: 'më pak se një minute',\n    other: 'më pak se {{count}} minuta'\n  },\n  xMinutes: {\n    one: '1 minutë',\n    other: '{{count}} minuta'\n  },\n  aboutXHours: {\n    one: 'rreth 1 orë',\n    other: 'rreth {{count}} orë'\n  },\n  xHours: {\n    one: '1 orë',\n    other: '{{count}} orë'\n  },\n  xDays: {\n    one: '1 ditë',\n    other: '{{count}} ditë'\n  },\n  aboutXWeeks: {\n    one: 'rreth 1 javë',\n    other: 'rreth {{count}} javë'\n  },\n  xWeeks: {\n    one: '1 javë',\n    other: '{{count}} javë'\n  },\n  aboutXMonths: {\n    one: 'rreth 1 muaj',\n    other: 'rreth {{count}} muaj'\n  },\n  xMonths: {\n    one: '1 muaj',\n    other: '{{count}} muaj'\n  },\n  aboutXYears: {\n    one: 'rreth 1 vit',\n    other: 'rreth {{count}} vite'\n  },\n  xYears: {\n    one: '1 vit',\n    other: '{{count}} vite'\n  },\n  overXYears: {\n    one: 'mbi 1 vit',\n    other: 'mbi {{count}} vite'\n  },\n  almostXYears: {\n    one: 'pothuajse 1 vit',\n    other: 'pothuajse {{count}} vite'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'në ' + result;\n    } else {\n      return result + ' më parë';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,eAAe;EAC5BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,UAAU;IAC5B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}