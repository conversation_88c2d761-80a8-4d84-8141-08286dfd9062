{"ast": null, "code": "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n  const color = new TinyColor(firstColor);\n  const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n  let secondHex8String = hex8String;\n  const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n  if (secondColor) {\n    const s = new TinyColor(secondColor);\n    secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n  }\n  return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}", "map": {"version": 3, "names": ["rgbaToArgbHex", "TinyColor", "to<PERSON><PERSON><PERSON><PERSON>", "firstColor", "secondColor", "color", "hex8String", "r", "g", "b", "a", "secondHex8String", "gradientType", "s"], "sources": ["G:/web/central-platform-tas-web/node_modules/@ctrl/tinycolor/dist/module/to-ms-filter.js"], "sourcesContent": ["import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    const color = new TinyColor(firstColor);\n    const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    let secondHex8String = hex8String;\n    const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        const s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,YAAY;AACtC;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,UAAU,EAAEC,WAAW,EAAE;EAChD,MAAMC,KAAK,GAAG,IAAIJ,SAAS,CAACE,UAAU,CAAC;EACvC,MAAMG,UAAU,GAAG,GAAG,GAAGN,aAAa,CAACK,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACI,CAAC,EAAEJ,KAAK,CAACK,CAAC,CAAC;EAC1E,IAAIC,gBAAgB,GAAGL,UAAU;EACjC,MAAMM,YAAY,GAAGP,KAAK,CAACO,YAAY,GAAG,oBAAoB,GAAG,EAAE;EACnE,IAAIR,WAAW,EAAE;IACb,MAAMS,CAAC,GAAG,IAAIZ,SAAS,CAACG,WAAW,CAAC;IACpCO,gBAAgB,GAAG,GAAG,GAAGX,aAAa,CAACa,CAAC,CAACN,CAAC,EAAEM,CAAC,CAACL,CAAC,EAAEK,CAAC,CAACJ,CAAC,EAAEI,CAAC,CAACH,CAAC,CAAC;EAC9D;EACA,OAAO,8CAA8CE,YAAY,iBAAiBN,UAAU,gBAAgBK,gBAAgB,GAAG;AACnI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}