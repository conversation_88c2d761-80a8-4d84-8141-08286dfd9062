{"ast": null, "code": "// 消息扩展\nconst W = new Uint32Array(68);\nconst M = new Uint32Array(64); // W'\n\n/**\n * 循环左移\n */\nfunction rotl(x, n) {\n  const s = n & 31;\n  return x << s | x >>> 32 - s;\n}\n\n/**\n * 二进制异或运算\n */\nfunction xor(x, y) {\n  const result = [];\n  for (let i = x.length - 1; i >= 0; i--) result[i] = (x[i] ^ y[i]) & 0xff;\n  return result;\n}\n\n/**\n * 压缩函数中的置换函数 P0(X) = X xor (X <<< 9) xor (X <<< 17)\n */\nfunction P0(X) {\n  return X ^ rotl(X, 9) ^ rotl(X, 17);\n}\n\n/**\n * 消息扩展中的置换函数 P1(X) = X xor (X <<< 15) xor (X <<< 23)\n */\nfunction P1(X) {\n  return X ^ rotl(X, 15) ^ rotl(X, 23);\n}\n\n/**\n * sm3 本体\n */\nfunction sm3(array) {\n  let len = array.length * 8;\n\n  // k 是满足 len + 1 + k = 448mod512 的最小的非负整数\n  let k = len % 512;\n  // 如果 448 <= (512 % len) < 512，需要多补充 (len % 448) 比特'0'以满足总比特长度为512的倍数\n  k = k >= 448 ? 512 - k % 448 - 1 : 448 - k - 1;\n\n  // 填充\n  const kArr = new Array((k - 7) / 8);\n  const lenArr = new Array(8);\n  for (let i = 0, len = kArr.length; i < len; i++) kArr[i] = 0;\n  for (let i = 0, len = lenArr.length; i < len; i++) lenArr[i] = 0;\n  len = len.toString(2);\n  for (let i = 7; i >= 0; i--) {\n    if (len.length > 8) {\n      const start = len.length - 8;\n      lenArr[i] = parseInt(len.substr(start), 2);\n      len = len.substr(0, start);\n    } else if (len.length > 0) {\n      lenArr[i] = parseInt(len, 2);\n      len = '';\n    }\n  }\n  const m = new Uint8Array([...array, 0x80, ...kArr, ...lenArr]);\n  const dataView = new DataView(m.buffer, 0);\n\n  // 迭代压缩\n  const n = m.length / 64;\n  const V = new Uint32Array([0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e]);\n  for (let i = 0; i < n; i++) {\n    W.fill(0);\n    M.fill(0);\n\n    // 将消息分组B划分为 16 个字 W0， W1，……，W15\n    const start = 16 * i;\n    for (let j = 0; j < 16; j++) {\n      W[j] = dataView.getUint32((start + j) * 4, false);\n    }\n\n    // W16 ～ W67：W[j] <- P1(W[j−16] xor W[j−9] xor (W[j−3] <<< 15)) xor (W[j−13] <<< 7) xor W[j−6]\n    for (let j = 16; j < 68; j++) {\n      W[j] = P1(W[j - 16] ^ W[j - 9] ^ rotl(W[j - 3], 15)) ^ rotl(W[j - 13], 7) ^ W[j - 6];\n    }\n\n    // W′0 ～ W′63：W′[j] = W[j] xor W[j+4]\n    for (let j = 0; j < 64; j++) {\n      M[j] = W[j] ^ W[j + 4];\n    }\n\n    // 压缩\n    const T1 = 0x79cc4519;\n    const T2 = 0x7a879d8a;\n    // 字寄存器\n    let A = V[0];\n    let B = V[1];\n    let C = V[2];\n    let D = V[3];\n    let E = V[4];\n    let F = V[5];\n    let G = V[6];\n    let H = V[7];\n    // 中间变量\n    let SS1;\n    let SS2;\n    let TT1;\n    let TT2;\n    let T;\n    for (let j = 0; j < 64; j++) {\n      T = j >= 0 && j <= 15 ? T1 : T2;\n      SS1 = rotl(rotl(A, 12) + E + rotl(T, j), 7);\n      SS2 = SS1 ^ rotl(A, 12);\n      TT1 = (j >= 0 && j <= 15 ? A ^ B ^ C : A & B | A & C | B & C) + D + SS2 + M[j];\n      TT2 = (j >= 0 && j <= 15 ? E ^ F ^ G : E & F | ~E & G) + H + SS1 + W[j];\n      D = C;\n      C = rotl(B, 9);\n      B = A;\n      A = TT1;\n      H = G;\n      G = rotl(F, 19);\n      F = E;\n      E = P0(TT2);\n    }\n    V[0] ^= A;\n    V[1] ^= B;\n    V[2] ^= C;\n    V[3] ^= D;\n    V[4] ^= E;\n    V[5] ^= F;\n    V[6] ^= G;\n    V[7] ^= H;\n  }\n\n  // 转回 uint8\n  const result = [];\n  for (let i = 0, len = V.length; i < len; i++) {\n    const word = V[i];\n    result.push((word & 0xff000000) >>> 24, (word & 0xff0000) >>> 16, (word & 0xff00) >>> 8, word & 0xff);\n  }\n  return result;\n}\n\n/**\n * hmac 实现\n */\nconst blockLen = 64;\nconst iPad = new Uint8Array(blockLen);\nconst oPad = new Uint8Array(blockLen);\nfor (let i = 0; i < blockLen; i++) {\n  iPad[i] = 0x36;\n  oPad[i] = 0x5c;\n}\nfunction hmac(input, key) {\n  // 密钥填充\n  if (key.length > blockLen) key = sm3(key);\n  while (key.length < blockLen) key.push(0);\n  const iPadKey = xor(key, iPad);\n  const oPadKey = xor(key, oPad);\n  const hash = sm3([...iPadKey, ...input]);\n  return sm3([...oPadKey, ...hash]);\n}\nmodule.exports = {\n  sm3,\n  hmac\n};", "map": {"version": 3, "names": ["W", "Uint32Array", "M", "rotl", "x", "n", "s", "xor", "y", "result", "i", "length", "P0", "X", "P1", "sm3", "array", "len", "k", "kArr", "Array", "lenArr", "toString", "start", "parseInt", "substr", "m", "Uint8Array", "dataView", "DataView", "buffer", "V", "fill", "j", "getUint32", "T1", "T2", "A", "B", "C", "D", "E", "F", "G", "H", "SS1", "SS2", "TT1", "TT2", "T", "word", "push", "blockLen", "iPad", "oPad", "hmac", "input", "key", "iPadKey", "oPadKey", "hash", "module", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/sm-crypto/src/sm2/sm3.js"], "sourcesContent": ["// 消息扩展\nconst W = new Uint32Array(68)\nconst M = new Uint32Array(64) // W'\n\n/**\n * 循环左移\n */\nfunction rotl(x, n) {\n  const s = n & 31\n  return (x << s) | (x >>> (32 - s))\n}\n\n/**\n * 二进制异或运算\n */\nfunction xor(x, y) {\n  const result = []\n  for (let i = x.length - 1; i >= 0; i--) result[i] = (x[i] ^ y[i]) & 0xff\n  return result\n}\n\n/**\n * 压缩函数中的置换函数 P0(X) = X xor (X <<< 9) xor (X <<< 17)\n */\nfunction P0(X) {\n  return (X ^ rotl(X, 9)) ^ rotl(X, 17)\n}\n\n/**\n * 消息扩展中的置换函数 P1(X) = X xor (X <<< 15) xor (X <<< 23)\n */\nfunction P1(X) {\n  return (X ^ rotl(X, 15)) ^ rotl(X, 23)\n}\n\n/**\n * sm3 本体\n */\nfunction sm3(array) {\n  let len = array.length * 8\n\n  // k 是满足 len + 1 + k = 448mod512 的最小的非负整数\n  let k = len % 512\n  // 如果 448 <= (512 % len) < 512，需要多补充 (len % 448) 比特'0'以满足总比特长度为512的倍数\n  k = k >= 448 ? 512 - (k % 448) - 1 : 448 - k - 1\n\n  // 填充\n  const kArr = new Array((k - 7) / 8)\n  const lenArr = new Array(8)\n  for (let i = 0, len = kArr.length; i < len; i++) kArr[i] = 0\n  for (let i = 0, len = lenArr.length; i < len; i++) lenArr[i] = 0\n  len = len.toString(2)\n  for (let i = 7; i >= 0; i--) {\n    if (len.length > 8) {\n      const start = len.length - 8\n      lenArr[i] = parseInt(len.substr(start), 2)\n      len = len.substr(0, start)\n    } else if (len.length > 0) {\n      lenArr[i] = parseInt(len, 2)\n      len = ''\n    }\n  }\n  const m = new Uint8Array([...array, 0x80, ...kArr, ...lenArr])\n  const dataView = new DataView(m.buffer, 0)\n\n  // 迭代压缩\n  const n = m.length / 64\n  const V = new Uint32Array([0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e])\n  for (let i = 0; i < n; i++) {\n    W.fill(0)\n    M.fill(0)\n\n    // 将消息分组B划分为 16 个字 W0， W1，……，W15\n    const start = 16 * i\n    for (let j = 0; j < 16; j++) {\n      W[j] = dataView.getUint32((start + j) * 4, false)\n    }\n\n    // W16 ～ W67：W[j] <- P1(W[j−16] xor W[j−9] xor (W[j−3] <<< 15)) xor (W[j−13] <<< 7) xor W[j−6]\n    for (let j = 16; j < 68; j++) {\n      W[j] = (P1((W[j - 16] ^ W[j - 9]) ^ rotl(W[j - 3], 15)) ^ rotl(W[j - 13], 7)) ^ W[j - 6]\n    }\n\n    // W′0 ～ W′63：W′[j] = W[j] xor W[j+4]\n    for (let j = 0; j < 64; j++) {\n      M[j] = W[j] ^ W[j + 4]\n    }\n\n    // 压缩\n    const T1 = 0x79cc4519\n    const T2 = 0x7a879d8a\n    // 字寄存器\n    let A = V[0]\n    let B = V[1]\n    let C = V[2]\n    let D = V[3]\n    let E = V[4]\n    let F = V[5]\n    let G = V[6]\n    let H = V[7]\n    // 中间变量\n    let SS1\n    let SS2\n    let TT1\n    let TT2\n    let T\n    for (let j = 0; j < 64; j++) {\n      T = j >= 0 && j <= 15 ? T1 : T2\n      SS1 = rotl(rotl(A, 12) + E + rotl(T, j), 7)\n      SS2 = SS1 ^ rotl(A, 12)\n\n      TT1 = (j >= 0 && j <= 15 ? ((A ^ B) ^ C) : (((A & B) | (A & C)) | (B & C))) + D + SS2 + M[j]\n      TT2 = (j >= 0 && j <= 15 ? ((E ^ F) ^ G) : ((E & F) | ((~E) & G))) + H + SS1 + W[j]\n\n      D = C\n      C = rotl(B, 9)\n      B = A\n      A = TT1\n      H = G\n      G = rotl(F, 19)\n      F = E\n      E = P0(TT2)\n    }\n\n    V[0] ^= A\n    V[1] ^= B\n    V[2] ^= C\n    V[3] ^= D\n    V[4] ^= E\n    V[5] ^= F\n    V[6] ^= G\n    V[7] ^= H\n  }\n\n  // 转回 uint8\n  const result = []\n  for (let i = 0, len = V.length; i < len; i++) {\n    const word = V[i]\n    result.push((word & 0xff000000) >>> 24, (word & 0xff0000) >>> 16, (word & 0xff00) >>> 8, word & 0xff)\n  }\n\n  return result\n}\n\n/**\n * hmac 实现\n */\nconst blockLen = 64\nconst iPad = new Uint8Array(blockLen)\nconst oPad = new Uint8Array(blockLen)\nfor (let i = 0; i < blockLen; i++) {\n  iPad[i] = 0x36\n  oPad[i] = 0x5c\n}\nfunction hmac(input, key) {\n  // 密钥填充\n  if (key.length > blockLen) key = sm3(key)\n  while (key.length < blockLen) key.push(0)\n\n  const iPadKey = xor(key, iPad)\n  const oPadKey = xor(key, oPad)\n\n  const hash = sm3([...iPadKey, ...input])\n  return sm3([...oPadKey, ...hash])\n}\n\nmodule.exports = {\n  sm3,\n  hmac,\n}\n"], "mappings": "AAAA;AACA,MAAMA,CAAC,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;AAC7B,MAAMC,CAAC,GAAG,IAAID,WAAW,CAAC,EAAE,CAAC,EAAC;;AAE9B;AACA;AACA;AACA,SAASE,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,MAAMC,CAAC,GAAGD,CAAC,GAAG,EAAE;EAChB,OAAQD,CAAC,IAAIE,CAAC,GAAKF,CAAC,KAAM,EAAE,GAAGE,CAAG;AACpC;;AAEA;AACA;AACA;AACA,SAASC,GAAGA,CAACH,CAAC,EAAEI,CAAC,EAAE;EACjB,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAGN,CAAC,CAACO,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAED,MAAM,CAACC,CAAC,CAAC,GAAG,CAACN,CAAC,CAACM,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,IAAI,IAAI;EACxE,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA,SAASG,EAAEA,CAACC,CAAC,EAAE;EACb,OAAQA,CAAC,GAAGV,IAAI,CAACU,CAAC,EAAE,CAAC,CAAC,GAAIV,IAAI,CAACU,CAAC,EAAE,EAAE,CAAC;AACvC;;AAEA;AACA;AACA;AACA,SAASC,EAAEA,CAACD,CAAC,EAAE;EACb,OAAQA,CAAC,GAAGV,IAAI,CAACU,CAAC,EAAE,EAAE,CAAC,GAAIV,IAAI,CAACU,CAAC,EAAE,EAAE,CAAC;AACxC;;AAEA;AACA;AACA;AACA,SAASE,GAAGA,CAACC,KAAK,EAAE;EAClB,IAAIC,GAAG,GAAGD,KAAK,CAACL,MAAM,GAAG,CAAC;;EAE1B;EACA,IAAIO,CAAC,GAAGD,GAAG,GAAG,GAAG;EACjB;EACAC,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,GAAG,GAAIA,CAAC,GAAG,GAAI,GAAG,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,CAAC;;EAEhD;EACA,MAAMC,IAAI,GAAG,IAAIC,KAAK,CAAC,CAACF,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EACnC,MAAMG,MAAM,GAAG,IAAID,KAAK,CAAC,CAAC,CAAC;EAC3B,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGE,IAAI,CAACR,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAES,IAAI,CAACT,CAAC,CAAC,GAAG,CAAC;EAC5D,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGI,MAAM,CAACV,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAEW,MAAM,CAACX,CAAC,CAAC,GAAG,CAAC;EAChEO,GAAG,GAAGA,GAAG,CAACK,QAAQ,CAAC,CAAC,CAAC;EACrB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3B,IAAIO,GAAG,CAACN,MAAM,GAAG,CAAC,EAAE;MAClB,MAAMY,KAAK,GAAGN,GAAG,CAACN,MAAM,GAAG,CAAC;MAC5BU,MAAM,CAACX,CAAC,CAAC,GAAGc,QAAQ,CAACP,GAAG,CAACQ,MAAM,CAACF,KAAK,CAAC,EAAE,CAAC,CAAC;MAC1CN,GAAG,GAAGA,GAAG,CAACQ,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIN,GAAG,CAACN,MAAM,GAAG,CAAC,EAAE;MACzBU,MAAM,CAACX,CAAC,CAAC,GAAGc,QAAQ,CAACP,GAAG,EAAE,CAAC,CAAC;MAC5BA,GAAG,GAAG,EAAE;IACV;EACF;EACA,MAAMS,CAAC,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAGX,KAAK,EAAE,IAAI,EAAE,GAAGG,IAAI,EAAE,GAAGE,MAAM,CAAC,CAAC;EAC9D,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAACH,CAAC,CAACI,MAAM,EAAE,CAAC,CAAC;;EAE1C;EACA,MAAMzB,CAAC,GAAGqB,CAAC,CAACf,MAAM,GAAG,EAAE;EACvB,MAAMoB,CAAC,GAAG,IAAI9B,WAAW,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;EAC3H,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;IAC1BV,CAAC,CAACgC,IAAI,CAAC,CAAC,CAAC;IACT9B,CAAC,CAAC8B,IAAI,CAAC,CAAC,CAAC;;IAET;IACA,MAAMT,KAAK,GAAG,EAAE,GAAGb,CAAC;IACpB,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BjC,CAAC,CAACiC,CAAC,CAAC,GAAGL,QAAQ,CAACM,SAAS,CAAC,CAACX,KAAK,GAAGU,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC;IACnD;;IAEA;IACA,KAAK,IAAIA,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5BjC,CAAC,CAACiC,CAAC,CAAC,GAAInB,EAAE,CAAEd,CAAC,CAACiC,CAAC,GAAG,EAAE,CAAC,GAAGjC,CAAC,CAACiC,CAAC,GAAG,CAAC,CAAC,GAAI9B,IAAI,CAACH,CAAC,CAACiC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG9B,IAAI,CAACH,CAAC,CAACiC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAIjC,CAAC,CAACiC,CAAC,GAAG,CAAC,CAAC;IAC1F;;IAEA;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B/B,CAAC,CAAC+B,CAAC,CAAC,GAAGjC,CAAC,CAACiC,CAAC,CAAC,GAAGjC,CAAC,CAACiC,CAAC,GAAG,CAAC,CAAC;IACxB;;IAEA;IACA,MAAME,EAAE,GAAG,UAAU;IACrB,MAAMC,EAAE,GAAG,UAAU;IACrB;IACA,IAAIC,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIO,CAAC,GAAGP,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIQ,CAAC,GAAGR,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIS,CAAC,GAAGT,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIU,CAAC,GAAGV,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIW,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIY,CAAC,GAAGZ,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIa,CAAC,GAAGb,CAAC,CAAC,CAAC,CAAC;IACZ;IACA,IAAIc,GAAG;IACP,IAAIC,GAAG;IACP,IAAIC,GAAG;IACP,IAAIC,GAAG;IACP,IAAIC,CAAC;IACL,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BgB,CAAC,GAAGhB,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAGE,EAAE,GAAGC,EAAE;MAC/BS,GAAG,GAAG1C,IAAI,CAACA,IAAI,CAACkC,CAAC,EAAE,EAAE,CAAC,GAAGI,CAAC,GAAGtC,IAAI,CAAC8C,CAAC,EAAEhB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3Ca,GAAG,GAAGD,GAAG,GAAG1C,IAAI,CAACkC,CAAC,EAAE,EAAE,CAAC;MAEvBU,GAAG,GAAG,CAACd,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAKI,CAAC,GAAGC,CAAC,GAAIC,CAAC,GAAOF,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAKD,CAAC,GAAGC,CAAG,IAAIC,CAAC,GAAGM,GAAG,GAAG5C,CAAC,CAAC+B,CAAC,CAAC;MAC5Fe,GAAG,GAAG,CAACf,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAKQ,CAAC,GAAGC,CAAC,GAAIC,CAAC,GAAMF,CAAC,GAAGC,CAAC,GAAM,CAACD,CAAC,GAAIE,CAAG,IAAIC,CAAC,GAAGC,GAAG,GAAG7C,CAAC,CAACiC,CAAC,CAAC;MAEnFO,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGpC,IAAI,CAACmC,CAAC,EAAE,CAAC,CAAC;MACdA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGU,GAAG;MACPH,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGxC,IAAI,CAACuC,CAAC,EAAE,EAAE,CAAC;MACfA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAG7B,EAAE,CAACoC,GAAG,CAAC;IACb;IAEAjB,CAAC,CAAC,CAAC,CAAC,IAAIM,CAAC;IACTN,CAAC,CAAC,CAAC,CAAC,IAAIO,CAAC;IACTP,CAAC,CAAC,CAAC,CAAC,IAAIQ,CAAC;IACTR,CAAC,CAAC,CAAC,CAAC,IAAIS,CAAC;IACTT,CAAC,CAAC,CAAC,CAAC,IAAIU,CAAC;IACTV,CAAC,CAAC,CAAC,CAAC,IAAIW,CAAC;IACTX,CAAC,CAAC,CAAC,CAAC,IAAIY,CAAC;IACTZ,CAAC,CAAC,CAAC,CAAC,IAAIa,CAAC;EACX;;EAEA;EACA,MAAMnC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEO,GAAG,GAAGc,CAAC,CAACpB,MAAM,EAAED,CAAC,GAAGO,GAAG,EAAEP,CAAC,EAAE,EAAE;IAC5C,MAAMwC,IAAI,GAAGnB,CAAC,CAACrB,CAAC,CAAC;IACjBD,MAAM,CAAC0C,IAAI,CAAC,CAACD,IAAI,GAAG,UAAU,MAAM,EAAE,EAAE,CAACA,IAAI,GAAG,QAAQ,MAAM,EAAE,EAAE,CAACA,IAAI,GAAG,MAAM,MAAM,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC;EACvG;EAEA,OAAOzC,MAAM;AACf;;AAEA;AACA;AACA;AACA,MAAM2C,QAAQ,GAAG,EAAE;AACnB,MAAMC,IAAI,GAAG,IAAI1B,UAAU,CAACyB,QAAQ,CAAC;AACrC,MAAME,IAAI,GAAG,IAAI3B,UAAU,CAACyB,QAAQ,CAAC;AACrC,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,QAAQ,EAAE1C,CAAC,EAAE,EAAE;EACjC2C,IAAI,CAAC3C,CAAC,CAAC,GAAG,IAAI;EACd4C,IAAI,CAAC5C,CAAC,CAAC,GAAG,IAAI;AAChB;AACA,SAAS6C,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxB;EACA,IAAIA,GAAG,CAAC9C,MAAM,GAAGyC,QAAQ,EAAEK,GAAG,GAAG1C,GAAG,CAAC0C,GAAG,CAAC;EACzC,OAAOA,GAAG,CAAC9C,MAAM,GAAGyC,QAAQ,EAAEK,GAAG,CAACN,IAAI,CAAC,CAAC,CAAC;EAEzC,MAAMO,OAAO,GAAGnD,GAAG,CAACkD,GAAG,EAAEJ,IAAI,CAAC;EAC9B,MAAMM,OAAO,GAAGpD,GAAG,CAACkD,GAAG,EAAEH,IAAI,CAAC;EAE9B,MAAMM,IAAI,GAAG7C,GAAG,CAAC,CAAC,GAAG2C,OAAO,EAAE,GAAGF,KAAK,CAAC,CAAC;EACxC,OAAOzC,GAAG,CAAC,CAAC,GAAG4C,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC;AACnC;AAEAC,MAAM,CAACC,OAAO,GAAG;EACf/C,GAAG;EACHwC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}