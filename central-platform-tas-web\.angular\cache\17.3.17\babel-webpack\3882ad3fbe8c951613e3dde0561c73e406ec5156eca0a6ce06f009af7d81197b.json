{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { OthertallyComponent } from './othertally.component';\nimport { OthertallyRoutingModule } from './othertally-routing.module';\nimport { OthertallyEditComponent } from '@business/tas/othertally/othertally-edit/othertally-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [OthertallyComponent, OthertallyEditComponent];\nexport class OthertallyModule {\n  static {\n    this.ɵfac = function OthertallyModule_Factory(t) {\n      return new (t || OthertallyModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OthertallyModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, OthertallyRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OthertallyModule, {\n    declarations: [OthertallyComponent, OthertallyEditComponent],\n    imports: [SharedModule, OthertallyRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "OthertallyComponent", "OthertallyRoutingModule", "OthertallyEditComponent", "COMPONENTS", "OthertallyModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\othertally\\othertally.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { OthertallyComponent } from './othertally.component';\r\nimport { OthertallyRoutingModule } from './othertally-routing.module';\r\nimport {OthertallyEditComponent} from '@business/tas/othertally/othertally-edit/othertally-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  OthertallyComponent,\r\n  OthertallyEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, OthertallyRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class OthertallyModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAAQC,uBAAuB,QAAO,oEAAoE;;AAE1G,MAAMC,UAAU,GAAG,CACjBH,mBAAmB,EACnBE,uBAAuB,CACxB;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBN,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;IAAA;EAAA;;;2EAGlDK,gBAAgB;IAAAC,YAAA,GAR3BL,mBAAmB,EACnBE,uBAAuB;IAAAI,OAAA,GAIbR,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}