{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'λιγότερο από ένα δευτερόλεπτο',\n    other: 'λιγότερο από {{count}} δευτερόλεπτα'\n  },\n  xSeconds: {\n    one: '1 δευτερόλεπτο',\n    other: '{{count}} δευτερόλεπτα'\n  },\n  halfAMinute: 'μισό λεπτό',\n  lessThanXMinutes: {\n    one: 'λιγότερο από ένα λεπτό',\n    other: 'λιγότερο από {{count}} λεπτά'\n  },\n  xMinutes: {\n    one: '1 λεπτό',\n    other: '{{count}} λεπτά'\n  },\n  aboutXHours: {\n    one: 'περίπου 1 ώρα',\n    other: 'περίπου {{count}} ώρες'\n  },\n  xHours: {\n    one: '1 ώρα',\n    other: '{{count}} ώρες'\n  },\n  xDays: {\n    one: '1 ημέρα',\n    other: '{{count}} ημέρες'\n  },\n  aboutXWeeks: {\n    one: 'περίπου 1 εβδομάδα',\n    other: 'περίπου {{count}} εβδομάδες'\n  },\n  xWeeks: {\n    one: '1 εβδομάδα',\n    other: '{{count}} εβδομάδες'\n  },\n  aboutXMonths: {\n    one: 'περίπου 1 μήνας',\n    other: 'περίπου {{count}} μήνες'\n  },\n  xMonths: {\n    one: '1 μήνας',\n    other: '{{count}} μήνες'\n  },\n  aboutXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  },\n  xYears: {\n    one: '1 χρόνο',\n    other: '{{count}} χρόνια'\n  },\n  overXYears: {\n    one: 'πάνω από 1 χρόνο',\n    other: 'πάνω από {{count}} χρόνια'\n  },\n  almostXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'σε ' + result;\n    } else {\n      return result + ' πριν';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/el/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'λιγότερο από ένα δευτερόλεπτο',\n    other: 'λιγότερο από {{count}} δευτερόλεπτα'\n  },\n  xSeconds: {\n    one: '1 δευτερόλεπτο',\n    other: '{{count}} δευτερόλεπτα'\n  },\n  halfAMinute: 'μισό λεπτό',\n  lessThanXMinutes: {\n    one: 'λιγότερο από ένα λεπτό',\n    other: 'λιγότερο από {{count}} λεπτά'\n  },\n  xMinutes: {\n    one: '1 λεπτό',\n    other: '{{count}} λεπτά'\n  },\n  aboutXHours: {\n    one: 'περίπου 1 ώρα',\n    other: 'περίπου {{count}} ώρες'\n  },\n  xHours: {\n    one: '1 ώρα',\n    other: '{{count}} ώρες'\n  },\n  xDays: {\n    one: '1 ημέρα',\n    other: '{{count}} ημέρες'\n  },\n  aboutXWeeks: {\n    one: 'περίπου 1 εβδομάδα',\n    other: 'περίπου {{count}} εβδομάδες'\n  },\n  xWeeks: {\n    one: '1 εβδομάδα',\n    other: '{{count}} εβδομάδες'\n  },\n  aboutXMonths: {\n    one: 'περίπου 1 μήνας',\n    other: 'περίπου {{count}} μήνες'\n  },\n  xMonths: {\n    one: '1 μήνας',\n    other: '{{count}} μήνες'\n  },\n  aboutXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  },\n  xYears: {\n    one: '1 χρόνο',\n    other: '{{count}} χρόνια'\n  },\n  overXYears: {\n    one: 'πάνω από 1 χρόνο',\n    other: 'πάνω από {{count}} χρόνια'\n  },\n  almostXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'σε ' + result;\n    } else {\n      return result + ' πριν';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,+BAA+B;IACpCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,YAAY;EACzBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}