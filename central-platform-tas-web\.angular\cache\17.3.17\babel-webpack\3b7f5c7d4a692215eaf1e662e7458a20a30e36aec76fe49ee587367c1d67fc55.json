{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'trecută la' p\",\n  yesterday: \"'ieri la' p\",\n  today: \"'astăzi la' p\",\n  tomorrow: \"'mâine la' p\",\n  nextWeek: \"eeee 'viitoare la' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ro/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'trecută la' p\",\n  yesterday: \"'ieri la' p\",\n  today: \"'astăzi la' p\",\n  tomorrow: \"'mâine la' p\",\n  nextWeek: \"eeee 'viitoare la' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,sBAAsB;EAChCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}