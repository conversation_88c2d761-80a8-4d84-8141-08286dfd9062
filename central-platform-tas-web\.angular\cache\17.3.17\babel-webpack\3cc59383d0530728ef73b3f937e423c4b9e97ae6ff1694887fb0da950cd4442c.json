{"ast": null, "code": "// Big integer base-10 printing library\n// Copyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar max = 10000000000000; // biggest integer that can still fit 2^53 when multiplied by 256\nvar Int10 = /** @class */function () {\n  function Int10(value) {\n    this.buf = [+value || 0];\n  }\n  Int10.prototype.mulAdd = function (m, c) {\n    // assert(m <= 256)\n    var b = this.buf;\n    var l = b.length;\n    var i;\n    var t;\n    for (i = 0; i < l; ++i) {\n      t = b[i] * m + c;\n      if (t < max) {\n        c = 0;\n      } else {\n        c = 0 | t / max;\n        t -= c * max;\n      }\n      b[i] = t;\n    }\n    if (c > 0) {\n      b[i] = c;\n    }\n  };\n  Int10.prototype.sub = function (c) {\n    // assert(m <= 256)\n    var b = this.buf;\n    var l = b.length;\n    var i;\n    var t;\n    for (i = 0; i < l; ++i) {\n      t = b[i] - c;\n      if (t < 0) {\n        t += max;\n        c = 1;\n      } else {\n        c = 0;\n      }\n      b[i] = t;\n    }\n    while (b[b.length - 1] === 0) {\n      b.pop();\n    }\n  };\n  Int10.prototype.toString = function (base) {\n    if ((base || 10) != 10) {\n      throw new Error(\"only base 10 is supported\");\n    }\n    var b = this.buf;\n    var s = b[b.length - 1].toString();\n    for (var i = b.length - 2; i >= 0; --i) {\n      s += (max + b[i]).toString().substring(1);\n    }\n    return s;\n  };\n  Int10.prototype.valueOf = function () {\n    var b = this.buf;\n    var v = 0;\n    for (var i = b.length - 1; i >= 0; --i) {\n      v = v * max + b[i];\n    }\n    return v;\n  };\n  Int10.prototype.simplify = function () {\n    var b = this.buf;\n    return b.length == 1 ? b[0] : this;\n  };\n  return Int10;\n}();\nexport { Int10 };", "map": {"version": 3, "names": ["max", "Int10", "value", "buf", "prototype", "mulAdd", "m", "c", "b", "l", "length", "i", "t", "sub", "pop", "toString", "base", "Error", "s", "substring", "valueOf", "v", "simplify"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/asn1js/int10.js"], "sourcesContent": ["// Big integer base-10 printing library\n// Copyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar max = 10000000000000; // biggest integer that can still fit 2^53 when multiplied by 256\nvar Int10 = /** @class */ (function () {\n    function Int10(value) {\n        this.buf = [+value || 0];\n    }\n    Int10.prototype.mulAdd = function (m, c) {\n        // assert(m <= 256)\n        var b = this.buf;\n        var l = b.length;\n        var i;\n        var t;\n        for (i = 0; i < l; ++i) {\n            t = b[i] * m + c;\n            if (t < max) {\n                c = 0;\n            }\n            else {\n                c = 0 | (t / max);\n                t -= c * max;\n            }\n            b[i] = t;\n        }\n        if (c > 0) {\n            b[i] = c;\n        }\n    };\n    Int10.prototype.sub = function (c) {\n        // assert(m <= 256)\n        var b = this.buf;\n        var l = b.length;\n        var i;\n        var t;\n        for (i = 0; i < l; ++i) {\n            t = b[i] - c;\n            if (t < 0) {\n                t += max;\n                c = 1;\n            }\n            else {\n                c = 0;\n            }\n            b[i] = t;\n        }\n        while (b[b.length - 1] === 0) {\n            b.pop();\n        }\n    };\n    Int10.prototype.toString = function (base) {\n        if ((base || 10) != 10) {\n            throw new Error(\"only base 10 is supported\");\n        }\n        var b = this.buf;\n        var s = b[b.length - 1].toString();\n        for (var i = b.length - 2; i >= 0; --i) {\n            s += (max + b[i]).toString().substring(1);\n        }\n        return s;\n    };\n    Int10.prototype.valueOf = function () {\n        var b = this.buf;\n        var v = 0;\n        for (var i = b.length - 1; i >= 0; --i) {\n            v = v * max + b[i];\n        }\n        return v;\n    };\n    Int10.prototype.simplify = function () {\n        var b = this.buf;\n        return (b.length == 1) ? b[0] : this;\n    };\n    return Int10;\n}());\nexport { Int10 };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,GAAG,GAAG,cAAc,CAAC,CAAC;AAC1B,IAAIC,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAACC,KAAK,EAAE;IAClB,IAAI,CAACC,GAAG,GAAG,CAAC,CAACD,KAAK,IAAI,CAAC,CAAC;EAC5B;EACAD,KAAK,CAACG,SAAS,CAACC,MAAM,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACrC;IACA,IAAIC,CAAC,GAAG,IAAI,CAACL,GAAG;IAChB,IAAIM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChB,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACpBC,CAAC,GAAGJ,CAAC,CAACG,CAAC,CAAC,GAAGL,CAAC,GAAGC,CAAC;MAChB,IAAIK,CAAC,GAAGZ,GAAG,EAAE;QACTO,CAAC,GAAG,CAAC;MACT,CAAC,MACI;QACDA,CAAC,GAAG,CAAC,GAAIK,CAAC,GAAGZ,GAAI;QACjBY,CAAC,IAAIL,CAAC,GAAGP,GAAG;MAChB;MACAQ,CAAC,CAACG,CAAC,CAAC,GAAGC,CAAC;IACZ;IACA,IAAIL,CAAC,GAAG,CAAC,EAAE;MACPC,CAAC,CAACG,CAAC,CAAC,GAAGJ,CAAC;IACZ;EACJ,CAAC;EACDN,KAAK,CAACG,SAAS,CAACS,GAAG,GAAG,UAAUN,CAAC,EAAE;IAC/B;IACA,IAAIC,CAAC,GAAG,IAAI,CAACL,GAAG;IAChB,IAAIM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChB,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACpBC,CAAC,GAAGJ,CAAC,CAACG,CAAC,CAAC,GAAGJ,CAAC;MACZ,IAAIK,CAAC,GAAG,CAAC,EAAE;QACPA,CAAC,IAAIZ,GAAG;QACRO,CAAC,GAAG,CAAC;MACT,CAAC,MACI;QACDA,CAAC,GAAG,CAAC;MACT;MACAC,CAAC,CAACG,CAAC,CAAC,GAAGC,CAAC;IACZ;IACA,OAAOJ,CAAC,CAACA,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MAC1BF,CAAC,CAACM,GAAG,CAAC,CAAC;IACX;EACJ,CAAC;EACDb,KAAK,CAACG,SAAS,CAACW,QAAQ,GAAG,UAAUC,IAAI,EAAE;IACvC,IAAI,CAACA,IAAI,IAAI,EAAE,KAAK,EAAE,EAAE;MACpB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;IAChD;IACA,IAAIT,CAAC,GAAG,IAAI,CAACL,GAAG;IAChB,IAAIe,CAAC,GAAGV,CAAC,CAACA,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC;IAClC,KAAK,IAAIJ,CAAC,GAAGH,CAAC,CAACE,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACpCO,CAAC,IAAI,CAAClB,GAAG,GAAGQ,CAAC,CAACG,CAAC,CAAC,EAAEI,QAAQ,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC;IAC7C;IACA,OAAOD,CAAC;EACZ,CAAC;EACDjB,KAAK,CAACG,SAAS,CAACgB,OAAO,GAAG,YAAY;IAClC,IAAIZ,CAAC,GAAG,IAAI,CAACL,GAAG;IAChB,IAAIkB,CAAC,GAAG,CAAC;IACT,KAAK,IAAIV,CAAC,GAAGH,CAAC,CAACE,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACpCU,CAAC,GAAGA,CAAC,GAAGrB,GAAG,GAAGQ,CAAC,CAACG,CAAC,CAAC;IACtB;IACA,OAAOU,CAAC;EACZ,CAAC;EACDpB,KAAK,CAACG,SAAS,CAACkB,QAAQ,GAAG,YAAY;IACnC,IAAId,CAAC,GAAG,IAAI,CAACL,GAAG;IAChB,OAAQK,CAAC,CAACE,MAAM,IAAI,CAAC,GAAIF,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACxC,CAAC;EACD,OAAOP,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}