{"ast": null, "code": "import { toDate } from \"../../../../index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['неділю', 'понеділок', 'вівторок', 'середу', 'четвер', 'п’ятницю', 'суботу'];\nfunction lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\nvar lastWeekFormat = function lastWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormat = function nextWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["toDate", "isSameUTCWeek", "accusativeWeekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "lastWeekFormat", "dirtyDate", "baseDate", "options", "date", "getUTCDay", "nextWeekFormat", "formatRelativeLocale", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/uk/_lib/formatRelative/index.js"], "sourcesContent": ["import { toDate } from \"../../../../index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['неділю', 'понеділок', 'вівторок', 'середу', 'четвер', 'п’ятницю', 'суботу'];\nfunction lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\nvar lastWeekFormat = function lastWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormat = function nextWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,aAAa,MAAM,yCAAyC;AACnE,IAAIC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;AACtG,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,OAAO;IACzC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;EAC5C;AACF;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,OAAO,KAAK,GAAGC,OAAO,GAAG,OAAO;AAClC;AACA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,cAAc,GAAGC,OAAO,GAAG,OAAO;IAC3C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGA,OAAO,GAAG,OAAO;EAC9C;AACF;AACA,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACzE,IAAIC,IAAI,GAAGZ,MAAM,CAACS,SAAS,CAAC;EAC5B,IAAIL,GAAG,GAAGQ,IAAI,CAACC,SAAS,CAAC,CAAC;EAC1B,IAAIZ,aAAa,CAACW,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1C,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;EACtB;AACF,CAAC;AACD,IAAIU,cAAc,GAAG,SAASA,cAAcA,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACzE,IAAIC,IAAI,GAAGZ,MAAM,CAACS,SAAS,CAAC;EAC5B,IAAIL,GAAG,GAAGQ,IAAI,CAACC,SAAS,CAAC,CAAC;EAC1B,IAAIZ,aAAa,CAACW,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1C,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;EACtB;AACF,CAAC;AACD,IAAIW,oBAAoB,GAAG;EACzBZ,QAAQ,EAAEK,cAAc;EACxBQ,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,cAAc;EACxBX,QAAQ,EAAEO,cAAc;EACxBK,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAET,IAAI,EAAEF,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIW,MAAM,GAAGP,oBAAoB,CAACM,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOW,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}