{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_SA_BUSINESS extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"业务数据表主键\",\n      \"business_type_cd\": \"业务类型代码\",\n      \"business_type_nm\": \"业务类型名称\",\n      \"report_org_cd\": \"上报公司代码\",\n      \"report_org_nm\": \"上报公司名称\",\n      \"report_ym\": \"上报年月\",\n      \"report_dt\": \"上报年月:日期\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_SA_BUSINESS'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_SA_BUSINESS", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\TAS_T_SA_BUSINESS.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_SA_BUSINESS extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_SA_BUSINESS'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"业务数据表主键\",\r\n      \"business_type_cd\":\"业务类型代码\",\r\n      \"business_type_nm\":\"业务类型名称\",\r\n      \"report_org_cd\":\"上报公司代码\",\r\n      \"report_org_nm\":\"上报公司名称\",\r\n      \"report_ym\":\"上报年月\",\r\n      \"report_dt\":\"上报年月:日期\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,iBAAkB,SAAQD,QAAQ;EAQ7CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,kBAAkB,EAAC,QAAQ;MAC3B,kBAAkB,EAAC,QAAQ;MAC3B,eAAe,EAAC,QAAQ;MACxB,eAAe,EAAC,QAAQ;MACxB,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC,SAAS;MACrB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IAvBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,mBAAmB,CAAC,CAAC;IACjC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAoBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}