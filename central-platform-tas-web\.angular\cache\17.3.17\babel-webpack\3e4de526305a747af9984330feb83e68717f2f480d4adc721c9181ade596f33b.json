{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['воскресенье', 'понедельник', 'вторник', 'среду', 'четверг', 'пятницу', 'субботу'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в прошлое \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в прошлый \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в прошлую \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'во \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в следующее \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в следующий \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в следующую \" + weekday + \" в' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера в' p\",\n  today: \"'сегодня в' p\",\n  tomorrow: \"'завтра в' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "accusativeWeekdays", "_lastWeek", "day", "weekday", "thisWeek", "_nextWeek", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ru/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['воскресенье', 'понедельник', 'вторник', 'среду', 'четверг', 'пятницу', 'субботу'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в прошлое \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в прошлый \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в прошлую \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'во \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в следующее \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в следующий \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в следующую \" + weekday + \" в' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера в' p\",\n  today: \"'сегодня в' p\",\n  tomorrow: \"'завтра в' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,IAAIC,kBAAkB,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAC5G,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGC,OAAO,GAAG,OAAO;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;EAC5C;AACF;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,IAAIA,GAAG,KAAK,CAAC,CAAC,WAAW;IACvB,OAAO,MAAM,GAAGC,OAAO,GAAG,OAAO;EACnC,CAAC,MAAM;IACL,OAAO,KAAK,GAAGA,OAAO,GAAG,OAAO;EAClC;AACF;AACA,SAASE,SAASA,CAACH,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGC,OAAO,GAAG,OAAO;IAC5C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGA,OAAO,GAAG,OAAO;IAC5C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGA,OAAO,GAAG,OAAO;EAC9C;AACF;AACA,IAAIG,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,SAAS,CAACC,GAAG,CAAC;IACvB;EACF,CAAC;EACDU,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,SAASA,QAAQA,CAACP,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,SAAS,CAACH,GAAG,CAAC;IACvB;EACF,CAAC;EACDc,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIS,MAAM,GAAGb,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOS,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}