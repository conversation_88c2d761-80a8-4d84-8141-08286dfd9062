{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_EMAILTEMPLATE extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"邮件模板表主键\",\n      \"template_nm\": \"模板名称\",\n      \"title\": \"邮件标题\",\n      \"text\": \"邮件正文\",\n      \"bs_cd\": \"业务场景代码\",\n      \"bs_nm\": \"业务场景名称\",\n      \"bs_nm_en\": \"业务场景英文名称\",\n      \"org_id\": \"所属组织主键\",\n      \"org_nm\": \"所属组织机构名称\",\n      \"org_level_no\": \"所属组织机构代码\",\n      \"owner_sco_no\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_EMAILTEMPLATE'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_EMAILTEMPLATE", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_EMAILTEMPLATE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_EMAILTEMPLATE extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_EMAILTEMPLATE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"邮件模板表主键\",\r\n      \"template_nm\":\"模板名称\",\r\n      \"title\":\"邮件标题\",\r\n      \"text\":\"邮件正文\",\r\n      \"bs_cd\":\"业务场景代码\",\r\n      \"bs_nm\":\"业务场景名称\",\r\n      \"bs_nm_en\":\"业务场景英文名称\",\r\n      \"org_id\":\"所属组织主键\",\r\n      \"org_nm\":\"所属组织机构名称\",\r\n      \"org_level_no\":\"所属组织机构代码\",\r\n      \"owner_sco_no\":\"所属公司代码\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,oBAAqB,SAAQD,QAAQ;EAQhDE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,aAAa,EAAC,MAAM;MACpB,OAAO,EAAC,MAAM;MACd,MAAM,EAAC,MAAM;MACb,OAAO,EAAC,QAAQ;MAChB,OAAO,EAAC,QAAQ;MAChB,UAAU,EAAC,UAAU;MACrB,QAAQ,EAAC,QAAQ;MACjB,QAAQ,EAAC,UAAU;MACnB,cAAc,EAAC,UAAU;MACzB,cAAc,EAAC,QAAQ;MACvB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IA3BJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,sBAAsB,CAAC,CAAC;IACpC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAwBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}