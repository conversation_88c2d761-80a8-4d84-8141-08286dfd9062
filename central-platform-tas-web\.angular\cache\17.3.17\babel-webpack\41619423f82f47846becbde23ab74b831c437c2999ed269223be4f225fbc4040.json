{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: 'せんしゅうのeeeeのp',\n  yesterday: 'きのうのp',\n  today: 'きょうのp',\n  tomorrow: 'あしたのp',\n  nextWeek: 'よくしゅうのeeeeのp',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ja-Hira/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: 'せんしゅうのeeeeのp',\n  yesterday: 'きのうのp',\n  today: 'きょうのp',\n  tomorrow: 'あしたのp',\n  nextWeek: 'よくしゅうのeeeeのp',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,cAAc;EACxBC,SAAS,EAAE,OAAO;EAClBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}