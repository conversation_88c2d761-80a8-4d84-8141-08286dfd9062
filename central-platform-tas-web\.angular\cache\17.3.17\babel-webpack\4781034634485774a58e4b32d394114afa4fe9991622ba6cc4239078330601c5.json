{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function setUTCDay(dirtyDate, dirtyDay, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var weekStartsOn = toInteger((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "toInteger", "getDefaultOptions", "setUTCDay", "dirtyDate", "dirtyDay", "options", "_ref", "_ref2", "_ref3", "_options$weekStartsOn", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "defaultOptions", "weekStartsOn", "locale", "RangeError", "date", "day", "currentDay", "getUTCDay", "remainder", "dayIndex", "diff", "setUTCDate", "getUTCDate"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/_lib/setUTCDay/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function setUTCDay(dirtyDate, dirtyDay, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var weekStartsOn = toInteger((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,eAAe,SAASC,SAASA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC9D,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpId,YAAY,CAAC,CAAC,EAAEe,SAAS,CAAC;EAC1B,IAAIC,cAAc,GAAGd,iBAAiB,CAAC,CAAC;EACxC,IAAIe,YAAY,GAAGhB,SAAS,CAAC,CAACM,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,YAAY,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIP,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,YAAY,MAAM,IAAI,IAAIR,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,cAAc,CAACC,YAAY,MAAM,IAAI,IAAIT,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGG,cAAc,CAACE,MAAM,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACG,YAAY,MAAM,IAAI,IAAIV,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;;EAEr4B;EACA,IAAI,EAAEU,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIE,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EACA,IAAIC,IAAI,GAAGrB,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAIiB,GAAG,GAAGpB,SAAS,CAACI,QAAQ,CAAC;EAC7B,IAAIiB,UAAU,GAAGF,IAAI,CAACG,SAAS,CAAC,CAAC;EACjC,IAAIC,SAAS,GAAGH,GAAG,GAAG,CAAC;EACvB,IAAII,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAI,CAAC;EAClC,IAAIE,IAAI,GAAG,CAACD,QAAQ,GAAGR,YAAY,GAAG,CAAC,GAAG,CAAC,IAAII,GAAG,GAAGC,UAAU;EAC/DF,IAAI,CAACO,UAAU,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzC,OAAON,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}