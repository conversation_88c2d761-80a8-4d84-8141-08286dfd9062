{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { ContainerComponent } from './container.component';\nimport { ContainerRoutingModule } from './container-routing.module';\nimport { ContainerEditComponent } from '@business/tas/container/container-edit/container-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [ContainerComponent, ContainerEditComponent];\nexport class ContainerModule {\n  static {\n    this.ɵfac = function ContainerModule_Factory(t) {\n      return new (t || ContainerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContainerModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, ContainerRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContainerModule, {\n    declarations: [ContainerComponent, ContainerEditComponent],\n    imports: [SharedModule, ContainerRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "ContainerComponent", "ContainerRoutingModule", "ContainerEditComponent", "COMPONENTS", "ContainerModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\container\\container.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { ContainerComponent } from './container.component';\r\nimport { ContainerRoutingModule } from './container-routing.module';\r\nimport {ContainerEditComponent} from '@business/tas/container/container-edit/container-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  ContainerComponent,\r\n  ContainerEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, ContainerRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class ContainerModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}