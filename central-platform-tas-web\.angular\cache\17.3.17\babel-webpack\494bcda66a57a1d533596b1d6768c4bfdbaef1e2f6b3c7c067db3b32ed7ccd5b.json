{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'по-малко от секунда',\n    other: 'по-малко от {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половин минута',\n  lessThanXMinutes: {\n    one: 'по-малко от минута',\n    other: 'по-малко от {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'около час',\n    other: 'около {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дни'\n  },\n  aboutXWeeks: {\n    one: 'около седмица',\n    other: 'около {{count}} седмици'\n  },\n  xWeeks: {\n    one: '1 седмица',\n    other: '{{count}} седмици'\n  },\n  aboutXMonths: {\n    one: 'около месец',\n    other: 'около {{count}} месеца'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеца'\n  },\n  aboutXYears: {\n    one: 'около година',\n    other: 'около {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'над година',\n    other: 'над {{count}} години'\n  },\n  almostXYears: {\n    one: 'почти година',\n    other: 'почти {{count}} години'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'след ' + result;\n    } else {\n      return 'преди ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/bg/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'по-малко от секунда',\n    other: 'по-малко от {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половин минута',\n  lessThanXMinutes: {\n    one: 'по-малко от минута',\n    other: 'по-малко от {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'около час',\n    other: 'около {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дни'\n  },\n  aboutXWeeks: {\n    one: 'около седмица',\n    other: 'около {{count}} седмици'\n  },\n  xWeeks: {\n    one: '1 седмица',\n    other: '{{count}} седмици'\n  },\n  aboutXMonths: {\n    one: 'около месец',\n    other: 'около {{count}} месеца'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеца'\n  },\n  aboutXYears: {\n    one: 'около година',\n    other: 'около {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'над година',\n    other: 'над {{count}} години'\n  },\n  almostXYears: {\n    one: 'почти година',\n    other: 'почти {{count}} години'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'след ' + result;\n    } else {\n      return 'преди ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,gBAAgB;EAC7BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}