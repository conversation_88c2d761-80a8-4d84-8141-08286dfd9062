{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { SpaEnterpriseComponent } from './spaenterprise.component';\nimport { SpaEnterpriseEditComponent } from '@business/tas/spaenterprise/spaenterprise-edit/spaenterprise-edit.component';\nimport { SpaEnterpriseRoutingModule } from './spaenterprise-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [SpaEnterpriseComponent, SpaEnterpriseEditComponent];\nexport class SpaEnterpriseModule {\n  static {\n    this.ɵfac = function SpaEnterpriseModule_Factory(t) {\n      return new (t || SpaEnterpriseModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SpaEnterpriseModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, SpaEnterpriseRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SpaEnterpriseModule, {\n    declarations: [SpaEnterpriseComponent, SpaEnterpriseEditComponent],\n    imports: [SharedModule, SpaEnterpriseRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "SpaEnterpriseComponent", "SpaEnterpriseEditComponent", "SpaEnterpriseRoutingModule", "COMPONENTS", "SpaEnterpriseModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\spaenterprise\\spaenterprise.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { SpaEnterpriseComponent } from './spaenterprise.component';\r\nimport { SpaEnterpriseEditComponent } from '@business/tas/spaenterprise/spaenterprise-edit/spaenterprise-edit.component';\r\nimport { SpaEnterpriseRoutingModule } from './spaenterprise-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  SpaEnterpriseComponent,\r\n  SpaEnterpriseEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, SpaEnterpriseRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class SpaEnterpriseModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,0BAA0B,QAAQ,6EAA6E;AACxH,SAASC,0BAA0B,QAAQ,gCAAgC;;AAE3E,MAAMC,UAAU,GAAG,CACjBH,sBAAsB,EACtBC,0BAA0B,CAC3B;AAMD,OAAM,MAAOG,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBN,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;IAAA;EAAA;;;2EAGrDK,mBAAmB;IAAAC,YAAA,GAR9BL,sBAAsB,EACtBC,0BAA0B;IAAAK,OAAA,GAIhBR,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}