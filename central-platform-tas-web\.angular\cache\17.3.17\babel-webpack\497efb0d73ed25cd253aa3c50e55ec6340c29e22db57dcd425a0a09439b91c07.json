{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FileTypeComponent } from './filetype.component';\nimport { FileTypeEditComponent } from '@business/tas/filetype/filetype-edit/filetype-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: FileTypeComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: FileTypeEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class FileTypeRoutingModule {\n  static {\n    this.ɵfac = function FileTypeRoutingModule_Factory(t) {\n      return new (t || FileTypeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: FileTypeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FileTypeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FileTypeComponent", "FileTypeEditComponent", "routes", "path", "component", "data", "cache", "FileTypeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\filetype\\filetype-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { FileTypeComponent } from './filetype.component';\r\nimport { FileTypeEditComponent } from '@business/tas/filetype/filetype-edit/filetype-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: FileTypeComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: FileTypeEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class FileTypeRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,8DAA8D;;;AACpG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,iBAAiB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACrE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,qBAAqB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAC/E;AAMD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFtBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}