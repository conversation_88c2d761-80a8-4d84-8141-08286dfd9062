{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { PackandunComponent } from './packandun.component';\nimport { PackandunRoutingModule } from './packandun-routing.module';\nimport { PackandunEditComponent } from '@business/tas/packandun/packandun-edit/packandun-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [PackandunComponent, PackandunEditComponent];\nexport class PackandunModule {\n  static {\n    this.ɵfac = function PackandunModule_Factory(t) {\n      return new (t || PackandunModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PackandunModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, PackandunRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PackandunModule, {\n    declarations: [PackandunComponent, PackandunEditComponent],\n    imports: [SharedModule, PackandunRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "PackandunComponent", "PackandunRoutingModule", "PackandunEditComponent", "COMPONENTS", "PackandunModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\packandun\\packandun.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { PackandunComponent } from './packandun.component';\r\nimport { PackandunRoutingModule } from './packandun-routing.module';\r\nimport {PackandunEditComponent} from '@business/tas/packandun/packandun-edit/packandun-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  PackandunComponent,\r\n  PackandunEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, PackandunRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class PackandunModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}