{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { FileTypeComponent } from './filetype.component';\nimport { FileTypeEditComponent } from '@business/tas/filetype/filetype-edit/filetype-edit.component';\nimport { FileTypeRoutingModule } from './filetype-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [FileTypeComponent, FileTypeEditComponent];\nexport class FileTypeModule {\n  static {\n    this.ɵfac = function FileTypeModule_Factory(t) {\n      return new (t || FileTypeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: FileTypeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, FileTypeRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FileTypeModule, {\n    declarations: [FileTypeComponent, FileTypeEditComponent],\n    imports: [SharedModule, FileTypeRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "FileTypeComponent", "FileTypeEditComponent", "FileTypeRoutingModule", "COMPONENTS", "FileTypeModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\filetype\\filetype.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { FileTypeComponent } from './filetype.component';\r\nimport { FileTypeEditComponent } from '@business/tas/filetype/filetype-edit/filetype-edit.component';\r\nimport { FileTypeRoutingModule } from './filetype-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  FileTypeComponent,\r\n  FileTypeEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, FileTypeRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class FileTypeModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,qBAAqB,QAAQ,2BAA2B;;AAEjE,MAAMC,UAAU,GAAG,CACjBH,iBAAiB,EACjBC,qBAAqB,CACtB;AAMD,OAAM,MAAOG,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAHfN,YAAY,EAAEI,qBAAqB,EAAEH,YAAY;IAAA;EAAA;;;2EAGhDK,cAAc;IAAAC,YAAA,GARzBL,iBAAiB,EACjBC,qBAAqB;IAAAK,OAAA,GAIXR,YAAY,EAAEI,qBAAqB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}