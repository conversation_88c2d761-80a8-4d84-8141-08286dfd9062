{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { BulkcargoComponent } from './bulkcargo.component';\nimport { BulkcargoRoutingModule } from './bulkcargo-routing.module';\nimport { BulkcargoEditComponent } from '@business/tas/bulkcargo/bulkcargo-edit/bulkcargo-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [BulkcargoComponent, BulkcargoEditComponent];\nexport class BulkcargoModule {\n  static {\n    this.ɵfac = function BulkcargoModule_Factory(t) {\n      return new (t || BulkcargoModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BulkcargoModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, BulkcargoRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BulkcargoModule, {\n    declarations: [BulkcargoComponent, BulkcargoEditComponent],\n    imports: [SharedModule, BulkcargoRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "BulkcargoComponent", "BulkcargoRoutingModule", "BulkcargoEditComponent", "COMPONENTS", "BulkcargoModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\bulkcargo\\bulkcargo.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { BulkcargoComponent } from './bulkcargo.component';\r\nimport { BulkcargoRoutingModule } from './bulkcargo-routing.module';\r\nimport {BulkcargoEditComponent} from '@business/tas/bulkcargo/bulkcargo-edit/bulkcargo-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  BulkcargoComponent,\r\n  BulkcargoEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, BulkcargoRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class BulkcargoModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}