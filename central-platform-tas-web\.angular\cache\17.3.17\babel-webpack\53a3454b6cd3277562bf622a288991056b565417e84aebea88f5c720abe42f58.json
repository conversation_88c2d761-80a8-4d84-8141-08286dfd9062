{"ast": null, "code": "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n  const c1 = new TinyColor(color1);\n  const c2 = new TinyColor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2 = {\n  level: 'AA',\n  size: 'small'\n}) {\n  const readabilityLevel = readability(color1, color2);\n  switch ((wcag2.level ?? 'AA') + (wcag2.size ?? 'small')) {\n    case 'AAsmall':\n    case 'AAAlarge':\n      return readabilityLevel >= 4.5;\n    case 'AAlarge':\n      return readabilityLevel >= 3;\n    case 'AAAsmall':\n      return readabilityLevel >= 7;\n    default:\n      return false;\n  }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args = {\n  includeFallbackColors: false,\n  level: 'AA',\n  size: 'small'\n}) {\n  let bestColor = null;\n  let bestScore = 0;\n  const {\n    includeFallbackColors,\n    level,\n    size\n  } = args;\n  for (const color of colorList) {\n    const score = readability(baseColor, color);\n    if (score > bestScore) {\n      bestScore = score;\n      bestColor = new TinyColor(color);\n    }\n  }\n  if (isReadable(baseColor, bestColor, {\n    level,\n    size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  }\n  args.includeFallbackColors = false;\n  return mostReadable(baseColor, ['#fff', '#000'], args);\n}", "map": {"version": 3, "names": ["TinyColor", "readability", "color1", "color2", "c1", "c2", "Math", "max", "getLuminance", "min", "isReadable", "wcag2", "level", "size", "readabilityLevel", "mostReadable", "baseColor", "colorList", "args", "includeFallbackColors", "bestColor", "bestScore", "color", "score"], "sources": ["G:/web/central-platform-tas-web/node_modules/@ctrl/tinycolor/dist/module/readability.js"], "sourcesContent": ["import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    const c1 = new TinyColor(color1);\n    const c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2 = { level: 'AA', size: 'small' }) {\n    const readabilityLevel = readability(color1, color2);\n    switch ((wcag2.level ?? 'AA') + (wcag2.size ?? 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args = { includeFallbackColors: false, level: 'AA', size: 'small' }) {\n    let bestColor = null;\n    let bestScore = 0;\n    const { includeFallbackColors, level, size } = args;\n    for (const color of colorList) {\n        const score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level, size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,MAAMC,EAAE,GAAG,IAAIJ,SAAS,CAACE,MAAM,CAAC;EAChC,MAAMG,EAAE,GAAG,IAAIL,SAAS,CAACG,MAAM,CAAC;EAChC,OAAQ,CAACG,IAAI,CAACC,GAAG,CAACH,EAAE,CAACI,YAAY,CAAC,CAAC,EAAEH,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,KACzDF,IAAI,CAACG,GAAG,CAACL,EAAE,CAACI,YAAY,CAAC,CAAC,EAAEH,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,UAAUA,CAACR,MAAM,EAAEC,MAAM,EAAEQ,KAAK,GAAG;EAAEC,KAAK,EAAE,IAAI;EAAEC,IAAI,EAAE;AAAQ,CAAC,EAAE;EAC/E,MAAMC,gBAAgB,GAAGb,WAAW,CAACC,MAAM,EAAEC,MAAM,CAAC;EACpD,QAAQ,CAACQ,KAAK,CAACC,KAAK,IAAI,IAAI,KAAKD,KAAK,CAACE,IAAI,IAAI,OAAO,CAAC;IACnD,KAAK,SAAS;IACd,KAAK,UAAU;MACX,OAAOC,gBAAgB,IAAI,GAAG;IAClC,KAAK,SAAS;MACV,OAAOA,gBAAgB,IAAI,CAAC;IAChC,KAAK,UAAU;MACX,OAAOA,gBAAgB,IAAI,CAAC;IAChC;MACI,OAAO,KAAK;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,SAAS,EAAEC,IAAI,GAAG;EAAEC,qBAAqB,EAAE,KAAK;EAAEP,KAAK,EAAE,IAAI;EAAEC,IAAI,EAAE;AAAQ,CAAC,EAAE;EACpH,IAAIO,SAAS,GAAG,IAAI;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,MAAM;IAAEF,qBAAqB;IAAEP,KAAK;IAAEC;EAAK,CAAC,GAAGK,IAAI;EACnD,KAAK,MAAMI,KAAK,IAAIL,SAAS,EAAE;IAC3B,MAAMM,KAAK,GAAGtB,WAAW,CAACe,SAAS,EAAEM,KAAK,CAAC;IAC3C,IAAIC,KAAK,GAAGF,SAAS,EAAE;MACnBA,SAAS,GAAGE,KAAK;MACjBH,SAAS,GAAG,IAAIpB,SAAS,CAACsB,KAAK,CAAC;IACpC;EACJ;EACA,IAAIZ,UAAU,CAACM,SAAS,EAAEI,SAAS,EAAE;IAAER,KAAK;IAAEC;EAAK,CAAC,CAAC,IAAI,CAACM,qBAAqB,EAAE;IAC7E,OAAOC,SAAS;EACpB;EACAF,IAAI,CAACC,qBAAqB,GAAG,KAAK;EAClC,OAAOJ,YAAY,CAACC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEE,IAAI,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}