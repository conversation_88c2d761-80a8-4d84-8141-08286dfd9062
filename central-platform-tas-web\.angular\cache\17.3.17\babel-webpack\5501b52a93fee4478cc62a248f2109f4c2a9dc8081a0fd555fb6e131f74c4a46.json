{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { InterconfigComponent } from './interconfig.component';\nimport { InterconfigRoutingModule } from './interconfig-routing.module';\nimport { InterconfigEditComponent } from '@business/tas/interconfig/interconfig-edit/interconfig-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [InterconfigComponent, InterconfigEditComponent];\nexport class InterConfigModule {\n  static {\n    this.ɵfac = function InterConfigModule_Factory(t) {\n      return new (t || InterConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: InterConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, InterconfigRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InterConfigModule, {\n    declarations: [InterconfigComponent, InterconfigEditComponent],\n    imports: [SharedModule, InterconfigRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "InterconfigComponent", "InterconfigRoutingModule", "InterconfigEditComponent", "COMPONENTS", "InterConfigModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\interconfig\\interconfig.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { InterconfigComponent } from './interconfig.component';\r\nimport { InterconfigRoutingModule } from './interconfig-routing.module';\r\nimport {InterconfigEditComponent} from '@business/tas/interconfig/interconfig-edit/interconfig-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  InterconfigComponent,\r\n  InterconfigEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, InterconfigRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class InterConfigModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}