{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VslvoyComponent } from './vslvoy.component';\nimport { VslvoyEditComponent } from '@business/tas/vslvoy/vslvoy-edit/vslvoy-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: VslvoyComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: VslvoyEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class VslvoyRoutingModule {\n  static {\n    this.ɵfac = function VslvoyRoutingModule_Factory(t) {\n      return new (t || VslvoyRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VslvoyRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VslvoyRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VslvoyComponent", "VslvoyEditComponent", "routes", "path", "component", "data", "cache", "VslvoyRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vslvoy\\vslvoy-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VslvoyComponent } from './vslvoy.component';\r\nimport { VslvoyEditComponent } from '@business/tas/vslvoy/vslvoy-edit/vslvoy-edit.component';\r\n\r\nconst routes: Routes = [\r\n  { path: 'list', component: VslvoyComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: VslvoyEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class VslvoyRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,QAAQ,wDAAwD;;;AAE5F,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,eAAe;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACnE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,mBAAmB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAC7E;AAMD,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFpBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}