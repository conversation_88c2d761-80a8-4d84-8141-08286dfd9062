{"ast": null, "code": "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /\\d+/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(нтө|нт)/i,\n  abbreviated: /^(нтө|нт)/i,\n  wide: /^(нийтийн тооллын өмнө|нийтийн тооллын)/i\n};\nvar parseEraPatterns = {\n  any: [/^(нтө|нийтийн тооллын өмнө)/i, /^(нт|нийтийн тооллын)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^(iv|iii|ii|i)/i,\n  abbreviated: /^(iv|iii|ii|i) улирал/i,\n  wide: /^[1-4]-р улирал/i\n};\nvar parseQuarterPatterns = {\n  any: [/^(i(\\s|$)|1)/i, /^(ii(\\s|$)|2)/i, /^(iii(\\s|$)|3)/i, /^(iv(\\s|$)|4)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,\n  abbreviated: /^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,\n  wide: /^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^i$/i, /^ii$/i, /^iii$/i, /^iv$/i, /^v$/i, /^vi$/i, /^vii$/i, /^viii$/i, /^ix$/i, /^x$/i, /^xi$/i, /^xii$/i],\n  any: [/^(1|нэгдүгээр)/i, /^(2|хоёрдугаар)/i, /^(3|гуравдугаар)/i, /^(4|дөрөвдүгээр)/i, /^(5|тавдугаар)/i, /^(6|зургаадугаар)/i, /^(7|долоодугаар)/i, /^(8|наймдугаар)/i, /^(9|есдүгээр)/i, /^(10|аравдугаар)/i, /^(11|арван нэгдүгээр)/i, /^(12|арван хоёрдугаар)/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[ндмлпбб]/i,\n  short: /^(ня|да|мя|лх|пү|ба|бя)/i,\n  abbreviated: /^(ням|дав|мяг|лха|пүр|баа|бям)/i,\n  wide: /^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^д/i, /^м/i, /^л/i, /^п/i, /^б/i, /^б/i],\n  any: [/^ня/i, /^да/i, /^мя/i, /^лх/i, /^пү/i, /^ба/i, /^бя/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,\n  any: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ү\\.ө\\./i,\n    pm: /^ү\\.х\\./i,\n    midnight: /^шөнө дунд/i,\n    noon: /^үд дунд/i,\n    morning: /өглөө/i,\n    afternoon: /өдөр/i,\n    evening: /орой/i,\n    night: /шөнө/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/mn/_lib/match/index.js"], "sourcesContent": ["import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /\\d+/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(нтө|нт)/i,\n  abbreviated: /^(нтө|нт)/i,\n  wide: /^(нийтийн тооллын өмнө|нийтийн тооллын)/i\n};\nvar parseEraPatterns = {\n  any: [/^(нтө|нийтийн тооллын өмнө)/i, /^(нт|нийтийн тооллын)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^(iv|iii|ii|i)/i,\n  abbreviated: /^(iv|iii|ii|i) улирал/i,\n  wide: /^[1-4]-р улирал/i\n};\nvar parseQuarterPatterns = {\n  any: [/^(i(\\s|$)|1)/i, /^(ii(\\s|$)|2)/i, /^(iii(\\s|$)|3)/i, /^(iv(\\s|$)|4)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,\n  abbreviated: /^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,\n  wide: /^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^i$/i, /^ii$/i, /^iii$/i, /^iv$/i, /^v$/i, /^vi$/i, /^vii$/i, /^viii$/i, /^ix$/i, /^x$/i, /^xi$/i, /^xii$/i],\n  any: [/^(1|нэгдүгээр)/i, /^(2|хоёрдугаар)/i, /^(3|гуравдугаар)/i, /^(4|дөрөвдүгээр)/i, /^(5|тавдугаар)/i, /^(6|зургаадугаар)/i, /^(7|долоодугаар)/i, /^(8|наймдугаар)/i, /^(9|есдүгээр)/i, /^(10|аравдугаар)/i, /^(11|арван нэгдүгээр)/i, /^(12|арван хоёрдугаар)/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[ндмлпбб]/i,\n  short: /^(ня|да|мя|лх|пү|ба|бя)/i,\n  abbreviated: /^(ням|дав|мяг|лха|пүр|баа|бям)/i,\n  wide: /^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^д/i, /^м/i, /^л/i, /^п/i, /^б/i, /^б/i],\n  any: [/^ня/i, /^да/i, /^мя/i, /^лх/i, /^пү/i, /^ба/i, /^бя/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,\n  any: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ү\\.ө\\./i,\n    pm: /^ү\\.х\\./i,\n    midnight: /^шөнө дунд/i,\n    noon: /^үд дунд/i,\n    morning: /өглөө/i,\n    afternoon: /өдөр/i,\n    evening: /орой/i,\n    night: /шөнө/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,IAAIC,yBAAyB,GAAG,MAAM;AACtC,IAAIC,yBAAyB,GAAG,MAAM;AACtC,IAAIC,gBAAgB,GAAG;EACrBC,MAAM,EAAE,YAAY;EACpBC,WAAW,EAAE,YAAY;EACzBC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,gBAAgB,GAAG;EACrBC,GAAG,EAAE,CAAC,8BAA8B,EAAE,wBAAwB;AAChE,CAAC;AACD,IAAIC,oBAAoB,GAAG;EACzBL,MAAM,EAAE,iBAAiB;EACzBC,WAAW,EAAE,wBAAwB;EACrCC,IAAI,EAAE;AACR,CAAC;AACD,IAAII,oBAAoB,GAAG;EACzBF,GAAG,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB;AAC9E,CAAC;AACD,IAAIG,kBAAkB,GAAG;EACvBP,MAAM,EAAE,2CAA2C;EACnDC,WAAW,EAAE,wGAAwG;EACrHC,IAAI,EAAE;AACR,CAAC;AACD,IAAIM,kBAAkB,GAAG;EACvBR,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;EACtHI,GAAG,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,yBAAyB;AACrQ,CAAC;AACD,IAAIK,gBAAgB,GAAG;EACrBT,MAAM,EAAE,aAAa;EACrBU,KAAK,EAAE,0BAA0B;EACjCT,WAAW,EAAE,iCAAiC;EAC9CC,IAAI,EAAE;AACR,CAAC;AACD,IAAIS,gBAAgB,GAAG;EACrBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAC9D,CAAC;AACD,IAAIQ,sBAAsB,GAAG;EAC3BZ,MAAM,EAAE,0DAA0D;EAClEI,GAAG,EAAE;AACP,CAAC;AACD,IAAIS,sBAAsB,GAAG;EAC3BT,GAAG,EAAE;IACHU,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,KAAK,GAAG;EACVC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAE,SAASA,aAAaA,CAACC,KAAK,EAAE;MAC3C,OAAOC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;IAC5B;EACF,CAAC,CAAC;EACFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAE,SAASA,aAAaA,CAACS,KAAK,EAAE;MAC3C,OAAOA,KAAK,GAAG,CAAC;IAClB;EACF,CAAC,CAAC;EACFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC;AACD,eAAeX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}