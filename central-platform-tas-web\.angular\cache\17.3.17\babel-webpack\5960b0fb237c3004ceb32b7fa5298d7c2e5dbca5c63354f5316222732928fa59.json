{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\nvar eraValues = {\n  narrow: ['ಕ್ರಿ.ಪೂ', 'ಕ್ರಿ.ಶ'],\n  abbreviated: ['ಕ್ರಿ.ಪೂ', 'ಕ್ರಿ.ಶ'],\n  // CLDR #1618, #1620\n  wide: ['ಕ್ರಿಸ್ತ ಪೂರ್ವ', 'ಕ್ರಿಸ್ತ ಶಕ'] // CLDR #1614, #1616\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ತ್ರೈ 1', 'ತ್ರೈ 2', 'ತ್ರೈ 3', 'ತ್ರೈ 4'],\n  // CLDR #1630 - #1638\n  wide: ['1ನೇ ತ್ರೈಮಾಸಿಕ', '2ನೇ ತ್ರೈಮಾಸಿಕ', '3ನೇ ತ್ರೈಮಾಸಿಕ', '4ನೇ ತ್ರೈಮಾಸಿಕ'] // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nvar monthValues = {\n  narrow: ['ಜ', 'ಫೆ', 'ಮಾ', 'ಏ', 'ಮೇ', 'ಜೂ', 'ಜು', 'ಆ', 'ಸೆ', 'ಅ', 'ನ', 'ಡಿ'],\n  abbreviated: ['ಜನ', 'ಫೆಬ್ರ', 'ಮಾರ್ಚ್', 'ಏಪ್ರಿ', 'ಮೇ', 'ಜೂನ್', 'ಜುಲೈ', 'ಆಗ', 'ಸೆಪ್ಟೆಂ', 'ಅಕ್ಟೋ', 'ನವೆಂ', 'ಡಿಸೆಂ'],\n  wide: ['ಜನವರಿ', 'ಫೆಬ್ರವರಿ', 'ಮಾರ್ಚ್', 'ಏಪ್ರಿಲ್', 'ಮೇ', 'ಜೂನ್', 'ಜುಲೈ', 'ಆಗಸ್ಟ್', 'ಸೆಪ್ಟೆಂಬರ್', 'ಅಕ್ಟೋಬರ್', 'ನವೆಂಬರ್', 'ಡಿಸೆಂಬರ್']\n};\n\n// CLDR #1718 - #1773\nvar dayValues = {\n  narrow: ['ಭಾ', 'ಸೋ', 'ಮಂ', 'ಬು', 'ಗು', 'ಶು', 'ಶ'],\n  short: ['ಭಾನು', 'ಸೋಮ', 'ಮಂಗಳ', 'ಬುಧ', 'ಗುರು', 'ಶುಕ್ರ', 'ಶನಿ'],\n  abbreviated: ['ಭಾನು', 'ಸೋಮ', 'ಮಂಗಳ', 'ಬುಧ', 'ಗುರು', 'ಶುಕ್ರ', 'ಶನಿ'],\n  wide: ['ಭಾನುವಾರ', 'ಸೋಮವಾರ', 'ಮಂಗಳವಾರ', 'ಬುಧವಾರ', 'ಗುರುವಾರ', 'ಶುಕ್ರವಾರ', 'ಶನಿವಾರ']\n};\n\n// CLDR #1774 - #1815\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾಹ್ನ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾಹ್ನ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  abbreviated: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  wide: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ಪೂ',\n    pm: 'ಅ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  abbreviated: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯ ರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  wide: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯ ರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'ನೇ';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/kn/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\nvar eraValues = {\n  narrow: ['ಕ್ರಿ.ಪೂ', 'ಕ್ರಿ.ಶ'],\n  abbreviated: ['ಕ್ರಿ.ಪೂ', 'ಕ್ರಿ.ಶ'],\n  // CLDR #1618, #1620\n  wide: ['ಕ್ರಿಸ್ತ ಪೂರ್ವ', 'ಕ್ರಿಸ್ತ ಶಕ'] // CLDR #1614, #1616\n};\n\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ತ್ರೈ 1', 'ತ್ರೈ 2', 'ತ್ರೈ 3', 'ತ್ರೈ 4'],\n  // CLDR #1630 - #1638\n  wide: ['1ನೇ ತ್ರೈಮಾಸಿಕ', '2ನೇ ತ್ರೈಮಾಸಿಕ', '3ನೇ ತ್ರೈಮಾಸಿಕ', '4ನೇ ತ್ರೈಮಾಸಿಕ'] // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nvar monthValues = {\n  narrow: ['ಜ', 'ಫೆ', 'ಮಾ', 'ಏ', 'ಮೇ', 'ಜೂ', 'ಜು', 'ಆ', 'ಸೆ', 'ಅ', 'ನ', 'ಡಿ'],\n  abbreviated: ['ಜನ', 'ಫೆಬ್ರ', 'ಮಾರ್ಚ್', 'ಏಪ್ರಿ', 'ಮೇ', 'ಜೂನ್', 'ಜುಲೈ', 'ಆಗ', 'ಸೆಪ್ಟೆಂ', 'ಅಕ್ಟೋ', 'ನವೆಂ', 'ಡಿಸೆಂ'],\n  wide: ['ಜನವರಿ', 'ಫೆಬ್ರವರಿ', 'ಮಾರ್ಚ್', 'ಏಪ್ರಿಲ್', 'ಮೇ', 'ಜೂನ್', 'ಜುಲೈ', 'ಆಗಸ್ಟ್', 'ಸೆಪ್ಟೆಂಬರ್', 'ಅಕ್ಟೋಬರ್', 'ನವೆಂಬರ್', 'ಡಿಸೆಂಬರ್']\n};\n\n// CLDR #1718 - #1773\nvar dayValues = {\n  narrow: ['ಭಾ', 'ಸೋ', 'ಮಂ', 'ಬು', 'ಗು', 'ಶು', 'ಶ'],\n  short: ['ಭಾನು', 'ಸೋಮ', 'ಮಂಗಳ', 'ಬುಧ', 'ಗುರು', 'ಶುಕ್ರ', 'ಶನಿ'],\n  abbreviated: ['ಭಾನು', 'ಸೋಮ', 'ಮಂಗಳ', 'ಬುಧ', 'ಗುರು', 'ಶುಕ್ರ', 'ಶನಿ'],\n  wide: ['ಭಾನುವಾರ', 'ಸೋಮವಾರ', 'ಮಂಗಳವಾರ', 'ಬುಧವಾರ', 'ಗುರುವಾರ', 'ಶುಕ್ರವಾರ', 'ಶನಿವಾರ']\n};\n\n// CLDR #1774 - #1815\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾಹ್ನ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾಹ್ನ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  abbreviated: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  wide: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ಪೂ',\n    pm: 'ಅ',\n    midnight: 'ಮಧ್ಯರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  abbreviated: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯ ರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  },\n  wide: {\n    am: 'ಪೂರ್ವಾಹ್ನ',\n    pm: 'ಅಪರಾಹ್ನ',\n    midnight: 'ಮಧ್ಯ ರಾತ್ರಿ',\n    noon: 'ಮಧ್ಯಾನ್ಹ',\n    morning: 'ಬೆಳಗ್ಗೆ',\n    afternoon: 'ಮಧ್ಯಾನ್ಹ',\n    evening: 'ಸಂಜೆ',\n    night: 'ರಾತ್ರಿ'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'ನೇ';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;EAC7BC,WAAW,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;EAClC;EACAC,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;AACxC,CAAC;AAED,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD;EACAC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;AAC7E,CAAC;;AAED;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC3EC,WAAW,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAChHC,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU;AAClI,CAAC;;AAED;AACA,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACjDM,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAC7DL,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EACnEC,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ;AAClF,CAAC;;AAED;AACA,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,IAAI;AACtB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}