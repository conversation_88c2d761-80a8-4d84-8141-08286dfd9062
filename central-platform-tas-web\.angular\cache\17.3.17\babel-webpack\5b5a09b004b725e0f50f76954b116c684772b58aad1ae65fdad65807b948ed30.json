{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.э.', 'н.э.'],\n  abbreviated: ['до н. э.', 'н. э.'],\n  wide: ['до нашей эры', 'нашей эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'март', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']\n};\nvar formattingMonthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'мар.', 'апр.', 'мая', 'июн.', 'июл.', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря']\n};\nvar dayValues = {\n  narrow: ['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['вск', 'пнд', 'втр', 'срд', 'чтв', 'птн', 'суб'],\n  wide: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'вечер',\n    night: 'ночь'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'вечера',\n    night: 'ночи'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n  if (unit === 'date') {\n    suffix = '-е';\n  } else if (unit === 'week' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ru/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.э.', 'н.э.'],\n  abbreviated: ['до н. э.', 'н. э.'],\n  wide: ['до нашей эры', 'нашей эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'март', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']\n};\nvar formattingMonthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'мар.', 'апр.', 'мая', 'июн.', 'июл.', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря']\n};\nvar dayValues = {\n  narrow: ['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['вск', 'пнд', 'втр', 'срд', 'чтв', 'птн', 'суб'],\n  wide: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'вечер',\n    night: 'ночь'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'вечера',\n    night: 'ночи'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n  if (unit === 'date') {\n    suffix = '-е';\n  } else if (unit === 'week' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAC9GC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS;AAC3H,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAC9GC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS;AAC7H,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AAC1F,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,IAAI,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;EACzE,IAAIC,MAAM;EACV,IAAID,IAAI,KAAK,MAAM,EAAE;IACnBC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM,IAAID,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACpEC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GAAG,IAAI;EACf;EACA,OAAOH,MAAM,GAAGG,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbP,aAAa,EAAEA,aAAa;EAC5BQ,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEjC,eAAe,CAAC;IACrB6B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE3B,qBAAqB;IACvC4B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAEpC,eAAe,CAAC;IACnB6B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAErC,eAAe,CAAC;IACzB6B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}