{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar weekdays = ['svētdienā', 'pirmdienā', 'otrdienā', 'trešdienā', 'ceturtdienā', 'piektdienā', 'sestdienā'];\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'Pagā<PERSON><PERSON><PERSON> \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON>r plkst.' p\",\n  today: \"'<PERSON><PERSON><PERSON> plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'<PERSON><PERSON><PERSON><PERSON>j<PERSON> \" + weekday + \" plkst.' p\";\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "weekdays", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "weekday", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/lv/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar weekdays = ['svētdienā', 'pirmdienā', 'otrdienā', 'trešdienā', 'ceturtdienā', 'piektdienā', 'sestdienā'];\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'Pagā<PERSON><PERSON><PERSON> \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON>r plkst.' p\",\n  today: \"'<PERSON><PERSON><PERSON> plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    var weekday = weekdays[date.getUTCDay()];\n    return \"'<PERSON><PERSON><PERSON><PERSON>j<PERSON> \" + weekday + \" plkst.' p\";\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,IAAIC,QAAQ,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5G,IAAIC,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIN,aAAa,CAACI,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAO,iBAAiB;IAC1B;IACA,IAAIC,OAAO,GAAGN,QAAQ,CAACG,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;IACxC,OAAO,YAAY,GAAGD,OAAO,GAAG,YAAY;EAC9C,CAAC;EACDE,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,SAASA,QAAQA,CAACR,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIN,aAAa,CAACI,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAO,iBAAiB;IAC1B;IACA,IAAIC,OAAO,GAAGN,QAAQ,CAACG,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC;IACxC,OAAO,YAAY,GAAGD,OAAO,GAAG,YAAY;EAC9C,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIU,MAAM,GAAGd,oBAAoB,CAACa,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOU,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}