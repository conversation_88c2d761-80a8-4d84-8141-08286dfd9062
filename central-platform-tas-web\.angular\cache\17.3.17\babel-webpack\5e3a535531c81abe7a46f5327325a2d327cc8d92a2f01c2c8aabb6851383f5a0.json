{"ast": null, "code": "// String encode/decode helpers\n'use strict';\n\nvar utils = require('./common');\n\n// Quick check if we can use fast array to bin string conversion\n//\n// - apply(Array) can fail on Android 2.2\n// - apply(Uint8Array) can fail on iOS 5.1 Safari\n//\nvar STR_APPLY_OK = true;\nvar STR_APPLY_UIA_OK = true;\ntry {\n  String.fromCharCode.apply(null, [0]);\n} catch (__) {\n  STR_APPLY_OK = false;\n}\ntry {\n  String.fromCharCode.apply(null, new Uint8Array(1));\n} catch (__) {\n  STR_APPLY_UIA_OK = false;\n}\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new utils.Buf8(256);\nfor (var q = 0; q < 256; q++) {\n  _utf8len[q] = q >= 252 ? 6 : q >= 248 ? 5 : q >= 240 ? 4 : q >= 224 ? 3 : q >= 192 ? 2 : 1;\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nexports.string2buf = function (str) {\n  var buf,\n    c,\n    c2,\n    m_pos,\n    i,\n    str_len = str.length,\n    buf_len = 0;\n\n  // count binary size\n  for (m_pos = 0; m_pos < str_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && m_pos + 1 < str_len) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  buf = new utils.Buf8(buf_len);\n\n  // convert\n  for (i = 0, m_pos = 0; i < buf_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && m_pos + 1 < str_len) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xC0 | c >>> 6;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xE0 | c >>> 12;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | c >>> 18;\n      buf[i++] = 0x80 | c >>> 12 & 0x3f;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    }\n  }\n  return buf;\n};\n\n// Helper (used in 2 places)\nfunction buf2binstring(buf, len) {\n  // On Chrome, the arguments in a function call that are allowed is `65534`.\n  // If the length of the buffer is smaller than that, we can use this optimization,\n  // otherwise we will take a slower path.\n  if (len < 65534) {\n    if (buf.subarray && STR_APPLY_UIA_OK || !buf.subarray && STR_APPLY_OK) {\n      return String.fromCharCode.apply(null, utils.shrinkBuf(buf, len));\n    }\n  }\n  var result = '';\n  for (var i = 0; i < len; i++) {\n    result += String.fromCharCode(buf[i]);\n  }\n  return result;\n}\n\n// Convert byte array to binary string\nexports.buf2binstring = function (buf) {\n  return buf2binstring(buf, buf.length);\n};\n\n// Convert binary string (typed, when possible)\nexports.binstring2buf = function (str) {\n  var buf = new utils.Buf8(str.length);\n  for (var i = 0, len = buf.length; i < len; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n};\n\n// convert array to string\nexports.buf2string = function (buf, max) {\n  var i, out, c, c_len;\n  var len = max || buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len * 2);\n  for (out = 0, i = 0; i < len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) {\n      utf16buf[out++] = c;\n      continue;\n    }\n    c_len = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (c_len > 4) {\n      utf16buf[out++] = 0xfffd;\n      i += c_len - 1;\n      continue;\n    }\n\n    // apply mask on first byte\n    c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (c_len > 1 && i < len) {\n      c = c << 6 | buf[i++] & 0x3f;\n      c_len--;\n    }\n\n    // terminated by end of string?\n    if (c_len > 1) {\n      utf16buf[out++] = 0xfffd;\n      continue;\n    }\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | c >> 10 & 0x3ff;\n      utf16buf[out++] = 0xdc00 | c & 0x3ff;\n    }\n  }\n  return buf2binstring(utf16buf, out);\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nexports.utf8border = function (buf, max) {\n  var pos;\n  max = max || buf.length;\n  if (max > buf.length) {\n    max = buf.length;\n  }\n\n  // go back from last position, until start of sequence found\n  pos = max - 1;\n  while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) {\n    pos--;\n  }\n\n  // Very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) {\n    return max;\n  }\n\n  // If we came to start of buffer - that means buffer is too small,\n  // return max too.\n  if (pos === 0) {\n    return max;\n  }\n  return pos + _utf8len[buf[pos]] > max ? pos : max;\n};", "map": {"version": 3, "names": ["utils", "require", "STR_APPLY_OK", "STR_APPLY_UIA_OK", "String", "fromCharCode", "apply", "__", "Uint8Array", "_utf8len", "Buf8", "q", "exports", "string2buf", "str", "buf", "c", "c2", "m_pos", "i", "str_len", "length", "buf_len", "charCodeAt", "buf2binstring", "len", "subarray", "shrinkBuf", "result", "binstring2buf", "buf2string", "max", "out", "c_len", "utf16buf", "Array", "utf8border", "pos"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/utils/strings.js"], "sourcesContent": ["// String encode/decode helpers\n'use strict';\n\n\nvar utils = require('./common');\n\n\n// Quick check if we can use fast array to bin string conversion\n//\n// - apply(Array) can fail on Android 2.2\n// - apply(Uint8Array) can fail on iOS 5.1 Safari\n//\nvar STR_APPLY_OK = true;\nvar STR_APPLY_UIA_OK = true;\n\ntry { String.fromCharCode.apply(null, [ 0 ]); } catch (__) { STR_APPLY_OK = false; }\ntry { String.fromCharCode.apply(null, new Uint8Array(1)); } catch (__) { STR_APPLY_UIA_OK = false; }\n\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new utils.Buf8(256);\nfor (var q = 0; q < 256; q++) {\n  _utf8len[q] = (q >= 252 ? 6 : q >= 248 ? 5 : q >= 240 ? 4 : q >= 224 ? 3 : q >= 192 ? 2 : 1);\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n\n\n// convert string to array (typed, when possible)\nexports.string2buf = function (str) {\n  var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n  // count binary size\n  for (m_pos = 0; m_pos < str_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos + 1 < str_len)) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  buf = new utils.Buf8(buf_len);\n\n  // convert\n  for (i = 0, m_pos = 0; i < buf_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos + 1 < str_len)) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xC0 | (c >>> 6);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xE0 | (c >>> 12);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | (c >>> 18);\n      buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    }\n  }\n\n  return buf;\n};\n\n// Helper (used in 2 places)\nfunction buf2binstring(buf, len) {\n  // On Chrome, the arguments in a function call that are allowed is `65534`.\n  // If the length of the buffer is smaller than that, we can use this optimization,\n  // otherwise we will take a slower path.\n  if (len < 65534) {\n    if ((buf.subarray && STR_APPLY_UIA_OK) || (!buf.subarray && STR_APPLY_OK)) {\n      return String.fromCharCode.apply(null, utils.shrinkBuf(buf, len));\n    }\n  }\n\n  var result = '';\n  for (var i = 0; i < len; i++) {\n    result += String.fromCharCode(buf[i]);\n  }\n  return result;\n}\n\n\n// Convert byte array to binary string\nexports.buf2binstring = function (buf) {\n  return buf2binstring(buf, buf.length);\n};\n\n\n// Convert binary string (typed, when possible)\nexports.binstring2buf = function (str) {\n  var buf = new utils.Buf8(str.length);\n  for (var i = 0, len = buf.length; i < len; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n};\n\n\n// convert array to string\nexports.buf2string = function (buf, max) {\n  var i, out, c, c_len;\n  var len = max || buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len * 2);\n\n  for (out = 0, i = 0; i < len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n    c_len = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len - 1; continue; }\n\n    // apply mask on first byte\n    c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (c_len > 1 && i < len) {\n      c = (c << 6) | (buf[i++] & 0x3f);\n      c_len--;\n    }\n\n    // terminated by end of string?\n    if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n      utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n    }\n  }\n\n  return buf2binstring(utf16buf, out);\n};\n\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nexports.utf8border = function (buf, max) {\n  var pos;\n\n  max = max || buf.length;\n  if (max > buf.length) { max = buf.length; }\n\n  // go back from last position, until start of sequence found\n  pos = max - 1;\n  while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n  // Very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) { return max; }\n\n  // If we came to start of buffer - that means buffer is too small,\n  // return max too.\n  if (pos === 0) { return max; }\n\n  return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n"], "mappings": "AAAA;AACA,YAAY;;AAGZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAG/B;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,gBAAgB,GAAG,IAAI;AAE3B,IAAI;EAAEC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAE,CAAE,CAAC,CAAE,CAAC;AAAE,CAAC,CAAC,OAAOC,EAAE,EAAE;EAAEL,YAAY,GAAG,KAAK;AAAE;AACnF,IAAI;EAAEE,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAE,IAAIE,UAAU,CAAC,CAAC,CAAC,CAAC;AAAE,CAAC,CAAC,OAAOD,EAAE,EAAE;EAAEJ,gBAAgB,GAAG,KAAK;AAAE;;AAGnG;AACA;AACA;AACA,IAAIM,QAAQ,GAAG,IAAIT,KAAK,CAACU,IAAI,CAAC,GAAG,CAAC;AAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;EAC5BF,QAAQ,CAACE,CAAC,CAAC,GAAIA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAE;AAC9F;AACAF,QAAQ,CAAC,GAAG,CAAC,GAAGA,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;AAGnC;AACAG,OAAO,CAACC,UAAU,GAAG,UAAUC,GAAG,EAAE;EAClC,IAAIC,GAAG;IAAEC,CAAC;IAAEC,EAAE;IAAEC,KAAK;IAAEC,CAAC;IAAEC,OAAO,GAAGN,GAAG,CAACO,MAAM;IAAEC,OAAO,GAAG,CAAC;;EAE3D;EACA,KAAKJ,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGE,OAAO,EAAEF,KAAK,EAAE,EAAE;IACxCF,CAAC,GAAGF,GAAG,CAACS,UAAU,CAACL,KAAK,CAAC;IACzB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAKE,KAAK,GAAG,CAAC,GAAGE,OAAQ,EAAE;MACpDH,EAAE,GAAGH,GAAG,CAACS,UAAU,CAACL,KAAK,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAKA,CAAC,GAAG,MAAM,IAAK,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAClDC,KAAK,EAAE;MACT;IACF;IACAI,OAAO,IAAIN,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC;EAC/D;;EAEA;EACAD,GAAG,GAAG,IAAIf,KAAK,CAACU,IAAI,CAACY,OAAO,CAAC;;EAE7B;EACA,KAAKH,CAAC,GAAG,CAAC,EAAED,KAAK,GAAG,CAAC,EAAEC,CAAC,GAAGG,OAAO,EAAEJ,KAAK,EAAE,EAAE;IAC3CF,CAAC,GAAGF,GAAG,CAACS,UAAU,CAACL,KAAK,CAAC;IACzB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAKE,KAAK,GAAG,CAAC,GAAGE,OAAQ,EAAE;MACpDH,EAAE,GAAGH,GAAG,CAACS,UAAU,CAACL,KAAK,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAKA,CAAC,GAAG,MAAM,IAAK,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAClDC,KAAK,EAAE;MACT;IACF;IACA,IAAIF,CAAC,GAAG,IAAI,EAAE;MACZ;MACAD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGH,CAAC;IACd,CAAC,MAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MACpB;MACAD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,CAAE;MAC3BD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,GAAG,IAAK;IAC9B,CAAC,MAAM,IAAIA,CAAC,GAAG,OAAO,EAAE;MACtB;MACAD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,EAAG;MAC5BD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,CAAC,GAAG,IAAK;MAClCD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,GAAG,IAAK;IAC9B,CAAC,MAAM;MACL;MACAD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,EAAG;MAC5BD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,EAAE,GAAG,IAAK;MACnCD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,KAAK,CAAC,GAAG,IAAK;MAClCD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAIH,CAAC,GAAG,IAAK;IAC9B;EACF;EAEA,OAAOD,GAAG;AACZ,CAAC;;AAED;AACA,SAASS,aAAaA,CAACT,GAAG,EAAEU,GAAG,EAAE;EAC/B;EACA;EACA;EACA,IAAIA,GAAG,GAAG,KAAK,EAAE;IACf,IAAKV,GAAG,CAACW,QAAQ,IAAIvB,gBAAgB,IAAM,CAACY,GAAG,CAACW,QAAQ,IAAIxB,YAAa,EAAE;MACzE,OAAOE,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEN,KAAK,CAAC2B,SAAS,CAACZ,GAAG,EAAEU,GAAG,CAAC,CAAC;IACnE;EACF;EAEA,IAAIG,MAAM,GAAG,EAAE;EACf,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;IAC5BS,MAAM,IAAIxB,MAAM,CAACC,YAAY,CAACU,GAAG,CAACI,CAAC,CAAC,CAAC;EACvC;EACA,OAAOS,MAAM;AACf;;AAGA;AACAhB,OAAO,CAACY,aAAa,GAAG,UAAUT,GAAG,EAAE;EACrC,OAAOS,aAAa,CAACT,GAAG,EAAEA,GAAG,CAACM,MAAM,CAAC;AACvC,CAAC;;AAGD;AACAT,OAAO,CAACiB,aAAa,GAAG,UAAUf,GAAG,EAAE;EACrC,IAAIC,GAAG,GAAG,IAAIf,KAAK,CAACU,IAAI,CAACI,GAAG,CAACO,MAAM,CAAC;EACpC,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGV,GAAG,CAACM,MAAM,EAAEF,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;IAC9CJ,GAAG,CAACI,CAAC,CAAC,GAAGL,GAAG,CAACS,UAAU,CAACJ,CAAC,CAAC;EAC5B;EACA,OAAOJ,GAAG;AACZ,CAAC;;AAGD;AACAH,OAAO,CAACkB,UAAU,GAAG,UAAUf,GAAG,EAAEgB,GAAG,EAAE;EACvC,IAAIZ,CAAC,EAAEa,GAAG,EAAEhB,CAAC,EAAEiB,KAAK;EACpB,IAAIR,GAAG,GAAGM,GAAG,IAAIhB,GAAG,CAACM,MAAM;;EAE3B;EACA;EACA;EACA,IAAIa,QAAQ,GAAG,IAAIC,KAAK,CAACV,GAAG,GAAG,CAAC,CAAC;EAEjC,KAAKO,GAAG,GAAG,CAAC,EAAEb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,GAAG;IAC7BT,CAAC,GAAGD,GAAG,CAACI,CAAC,EAAE,CAAC;IACZ;IACA,IAAIH,CAAC,GAAG,IAAI,EAAE;MAAEkB,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAGhB,CAAC;MAAE;IAAU;IAE/CiB,KAAK,GAAGxB,QAAQ,CAACO,CAAC,CAAC;IACnB;IACA,IAAIiB,KAAK,GAAG,CAAC,EAAE;MAAEC,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAG,MAAM;MAAEb,CAAC,IAAIc,KAAK,GAAG,CAAC;MAAE;IAAU;;IAErE;IACAjB,CAAC,IAAIiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;IACnD;IACA,OAAOA,KAAK,GAAG,CAAC,IAAId,CAAC,GAAGM,GAAG,EAAE;MAC3BT,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAKD,GAAG,CAACI,CAAC,EAAE,CAAC,GAAG,IAAK;MAChCc,KAAK,EAAE;IACT;;IAEA;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MAAEC,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAG,MAAM;MAAE;IAAU;IAErD,IAAIhB,CAAC,GAAG,OAAO,EAAE;MACfkB,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAGhB,CAAC;IACrB,CAAC,MAAM;MACLA,CAAC,IAAI,OAAO;MACZkB,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAG,MAAM,GAAKhB,CAAC,IAAI,EAAE,GAAI,KAAM;MAC9CkB,QAAQ,CAACF,GAAG,EAAE,CAAC,GAAG,MAAM,GAAIhB,CAAC,GAAG,KAAM;IACxC;EACF;EAEA,OAAOQ,aAAa,CAACU,QAAQ,EAAEF,GAAG,CAAC;AACrC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACApB,OAAO,CAACwB,UAAU,GAAG,UAAUrB,GAAG,EAAEgB,GAAG,EAAE;EACvC,IAAIM,GAAG;EAEPN,GAAG,GAAGA,GAAG,IAAIhB,GAAG,CAACM,MAAM;EACvB,IAAIU,GAAG,GAAGhB,GAAG,CAACM,MAAM,EAAE;IAAEU,GAAG,GAAGhB,GAAG,CAACM,MAAM;EAAE;;EAE1C;EACAgB,GAAG,GAAGN,GAAG,GAAG,CAAC;EACb,OAAOM,GAAG,IAAI,CAAC,IAAI,CAACtB,GAAG,CAACsB,GAAG,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;IAAEA,GAAG,EAAE;EAAE;;EAExD;EACA;EACA,IAAIA,GAAG,GAAG,CAAC,EAAE;IAAE,OAAON,GAAG;EAAE;;EAE3B;EACA;EACA,IAAIM,GAAG,KAAK,CAAC,EAAE;IAAE,OAAON,GAAG;EAAE;EAE7B,OAAQM,GAAG,GAAG5B,QAAQ,CAACM,GAAG,CAACsB,GAAG,CAAC,CAAC,GAAGN,GAAG,GAAIM,GAAG,GAAGN,GAAG;AACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}