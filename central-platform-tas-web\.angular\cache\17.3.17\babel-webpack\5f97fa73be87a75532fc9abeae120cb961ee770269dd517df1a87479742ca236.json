{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre end ét sekund',\n    other: 'mindre end {{count}} sekunder'\n  },\n  xSeconds: {\n    one: '1 sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'ét halvt minut',\n  lessThanXMinutes: {\n    one: 'mindre end ét minut',\n    other: 'mindre end {{count}} minutter'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minutter'\n  },\n  aboutXHours: {\n    one: 'cirka 1 time',\n    other: 'cirka {{count}} timer'\n  },\n  xHours: {\n    one: '1 time',\n    other: '{{count}} timer'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dage'\n  },\n  aboutXWeeks: {\n    one: 'cirka 1 uge',\n    other: 'cirka {{count}} uger'\n  },\n  xWeeks: {\n    one: '1 uge',\n    other: '{{count}} uger'\n  },\n  aboutXMonths: {\n    one: 'cirka 1 måned',\n    other: 'cirka {{count}} måneder'\n  },\n  xMonths: {\n    one: '1 måned',\n    other: '{{count}} måneder'\n  },\n  aboutXYears: {\n    one: 'cirka 1 år',\n    other: 'cirka {{count}} år'\n  },\n  xYears: {\n    one: '1 år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over 1 år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'næsten 1 år',\n    other: 'næsten {{count}} år'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' siden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/da/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre end ét sekund',\n    other: 'mindre end {{count}} sekunder'\n  },\n  xSeconds: {\n    one: '1 sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'ét halvt minut',\n  lessThanXMinutes: {\n    one: 'mindre end ét minut',\n    other: 'mindre end {{count}} minutter'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minutter'\n  },\n  aboutXHours: {\n    one: 'cirka 1 time',\n    other: 'cirka {{count}} timer'\n  },\n  xHours: {\n    one: '1 time',\n    other: '{{count}} timer'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dage'\n  },\n  aboutXWeeks: {\n    one: 'cirka 1 uge',\n    other: 'cirka {{count}} uger'\n  },\n  xWeeks: {\n    one: '1 uge',\n    other: '{{count}} uger'\n  },\n  aboutXMonths: {\n    one: 'cirka 1 måned',\n    other: 'cirka {{count}} måneder'\n  },\n  xMonths: {\n    one: '1 måned',\n    other: '{{count}} måneder'\n  },\n  aboutXYears: {\n    one: 'cirka 1 år',\n    other: 'cirka {{count}} år'\n  },\n  xYears: {\n    one: '1 år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over 1 år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'næsten 1 år',\n    other: 'næsten {{count}} år'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' siden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,gBAAgB;EAC7BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}