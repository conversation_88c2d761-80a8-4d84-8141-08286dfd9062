{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { CustomsCodeComponent } from './customscode.component';\nimport { CustomsCodeEditComponent } from '@business/tas/customscode/customscode-edit/customscode-edit.component';\nimport { CustomsCodeRoutingModule } from './customscode-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [CustomsCodeComponent, CustomsCodeEditComponent];\nexport class CustomsCodeModule {\n  static {\n    this.ɵfac = function CustomsCodeModule_Factory(t) {\n      return new (t || CustomsCodeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomsCodeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CustomsCodeRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomsCodeModule, {\n    declarations: [CustomsCodeComponent, CustomsCodeEditComponent],\n    imports: [SharedModule, CustomsCodeRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "CustomsCodeComponent", "CustomsCodeEditComponent", "CustomsCodeRoutingModule", "COMPONENTS", "CustomsCodeModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\customscode\\customscode.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { CustomsCodeComponent } from './customscode.component';\r\nimport { CustomsCodeEditComponent } from '@business/tas/customscode/customscode-edit/customscode-edit.component';\r\nimport { CustomsCodeRoutingModule } from './customscode-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  CustomsCodeComponent,\r\n  CustomsCodeEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, CustomsCodeRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class CustomsCodeModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,uEAAuE;AAChH,SAASC,wBAAwB,QAAQ,8BAA8B;;AAEvE,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBC,wBAAwB,CACzB;AAMD,OAAM,MAAOG,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEI,wBAAwB,EAAEH,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBC,wBAAwB;IAAAK,OAAA,GAIdR,YAAY,EAAEI,wBAAwB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}