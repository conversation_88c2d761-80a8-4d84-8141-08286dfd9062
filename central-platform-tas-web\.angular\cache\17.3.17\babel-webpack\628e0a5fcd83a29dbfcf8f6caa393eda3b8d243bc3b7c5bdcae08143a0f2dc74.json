{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nvar accusativeWeekdays = ['nedeľu', 'pondelok', 'utorok', 'stredu', 'štvrtok', 'piatok', 'sobotu'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 3: /* Wed */\n    case 6 /* Sat */:\n      return \"'minulú \" + weekday + \" o' p\";\n    default:\n      return \"'minulý' eeee 'o' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 4 /* Thu */) {\n    return \"'vo' eeee 'o' p\";\n  } else {\n    return \"'v \" + weekday + \" o' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 4: /* Wed */\n    case 6 /* Sat */:\n      return \"'budú<PERSON> \" + weekday + \" o' p\";\n    default:\n      return \"'budúci' eeee 'o' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'včera o' p\",\n  today: \"'dnes o' p\",\n  tomorrow: \"'zajtra o' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "accusativeWeekdays", "_lastWeek", "day", "weekday", "thisWeek", "_nextWeek", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/sk/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nvar accusativeWeekdays = ['nedeľu', 'pondelok', 'utorok', 'stredu', 'štvrtok', 'piatok', 'sobotu'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 3: /* Wed */\n    case 6 /* Sat */:\n      return \"'minulú \" + weekday + \" o' p\";\n    default:\n      return \"'minulý' eeee 'o' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 4 /* Thu */) {\n    return \"'vo' eeee 'o' p\";\n  } else {\n    return \"'v \" + weekday + \" o' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 4: /* Wed */\n    case 6 /* Sat */:\n      return \"'budú<PERSON> \" + weekday + \" o' p\";\n    default:\n      return \"'budúci' eeee 'o' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'včera o' p\",\n  today: \"'dnes o' p\",\n  tomorrow: \"'zajtra o' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE;AACA,IAAIC,kBAAkB,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAClG,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC;MACL,OAAO,UAAU,GAAGC,OAAO,GAAG,OAAO;IACvC;MACE,OAAO,qBAAqB;EAChC;AACF;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,IAAIA,GAAG,KAAK,CAAC,CAAC,WAAW;IACvB,OAAO,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAO,KAAK,GAAGC,OAAO,GAAG,OAAO;EAClC;AACF;AACA,SAASE,SAASA,CAACH,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,QAAQA,GAAG;IACT,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC;MACL,OAAO,UAAU,GAAGC,OAAO,GAAG,OAAO;IACvC;MACE,OAAO,qBAAqB;EAChC;AACF;AACA,IAAIG,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,SAAS,CAACC,GAAG,CAAC;IACvB;EACF,CAAC;EACDU,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,SAASA,QAAQA,CAACP,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,SAAS,CAACH,GAAG,CAAC;IACvB;EACF,CAAC;EACDc,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIS,MAAM,GAAGb,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOS,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}