{"ast": null, "code": "'use strict';\n\n// Note: we can't get significant speed boost here.\n// So write code to minimize size - no pregenerated tables\n// and array tools dependencies.\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n  var c,\n    table = [];\n  for (var n = 0; n < 256; n++) {\n    c = n;\n    for (var k = 0; k < 8; k++) {\n      c = c & 1 ? 0xEDB88320 ^ c >>> 1 : c >>> 1;\n    }\n    table[n] = c;\n  }\n  return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\nfunction crc32(crc, buf, len, pos) {\n  var t = crcTable,\n    end = pos + len;\n  crc ^= -1;\n  for (var i = pos; i < end; i++) {\n    crc = crc >>> 8 ^ t[(crc ^ buf[i]) & 0xFF];\n  }\n  return crc ^ -1; // >>> 0;\n}\nmodule.exports = crc32;", "map": {"version": 3, "names": ["makeTable", "c", "table", "n", "k", "crcTable", "crc32", "crc", "buf", "len", "pos", "t", "end", "i", "module", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/zlib/crc32.js"], "sourcesContent": ["'use strict';\n\n// Note: we can't get significant speed boost here.\n// So write code to minimize size - no pregenerated tables\n// and array tools dependencies.\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n  var c, table = [];\n\n  for (var n = 0; n < 256; n++) {\n    c = n;\n    for (var k = 0; k < 8; k++) {\n      c = ((c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n    }\n    table[n] = c;\n  }\n\n  return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n  var t = crcTable,\n      end = pos + len;\n\n  crc ^= -1;\n\n  for (var i = pos; i < end; i++) {\n    crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n  }\n\n  return (crc ^ (-1)); // >>> 0;\n}\n\n\nmodule.exports = crc32;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,SAASA,CAAA,EAAG;EACnB,IAAIC,CAAC;IAAEC,KAAK,GAAG,EAAE;EAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5BF,CAAC,GAAGE,CAAC;IACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BH,CAAC,GAAKA,CAAC,GAAG,CAAC,GAAK,UAAU,GAAIA,CAAC,KAAK,CAAE,GAAKA,CAAC,KAAK,CAAG;IACtD;IACAC,KAAK,CAACC,CAAC,CAAC,GAAGF,CAAC;EACd;EAEA,OAAOC,KAAK;AACd;;AAEA;AACA,IAAIG,QAAQ,GAAGL,SAAS,CAAC,CAAC;AAG1B,SAASM,KAAKA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACjC,IAAIC,CAAC,GAAGN,QAAQ;IACZO,GAAG,GAAGF,GAAG,GAAGD,GAAG;EAEnBF,GAAG,IAAI,CAAC,CAAC;EAET,KAAK,IAAIM,CAAC,GAAGH,GAAG,EAAEG,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;IAC9BN,GAAG,GAAIA,GAAG,KAAK,CAAC,GAAII,CAAC,CAAC,CAACJ,GAAG,GAAGC,GAAG,CAACK,CAAC,CAAC,IAAI,IAAI,CAAC;EAC9C;EAEA,OAAQN,GAAG,GAAI,CAAC,CAAE,CAAE,CAAC;AACvB;AAGAO,MAAM,CAACC,OAAO,GAAGT,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}