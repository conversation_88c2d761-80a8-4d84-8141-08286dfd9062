{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_FEPCONFIG extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"前置机配置表主键\",\n      \"wharf_id\": \"码头主键\",\n      \"wharf_cd\": \"码头代码\",\n      \"wharf_nm\": \"码头名称\",\n      \"wharf_nm_en\": \"码头英文名称\",\n      \"port_id\": \"港口主键\",\n      \"port_cd\": \"港口代码\",\n      \"port_nm\": \"港口名称\",\n      \"port_nm_en\": \"港口英文名称\",\n      \"fep_ip\": \"前置机IP\",\n      \"fep_port\": \"端口\",\n      \"user_name\": \"用户名\",\n      \"user_password\": \"密码\",\n      \"file_path\": \"文件位置\",\n      \"interface\": \"接口地址\",\n      \"org_id\": \"所属组织主键\",\n      \"org_nm\": \"所属组织机构名称\",\n      \"org_level_no\": \"所属组织机构代码\",\n      \"ent_level_no\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_FEP_CONFIG'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_FEPCONFIG", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_FEPCONFIG.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_FEPCONFIG extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_FEP_CONFIG'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"前置机配置表主键\",\r\n      \"wharf_id\":\"码头主键\",\r\n      \"wharf_cd\":\"码头代码\",\r\n      \"wharf_nm\":\"码头名称\",\r\n      \"wharf_nm_en\":\"码头英文名称\",\r\n      \"port_id\":\"港口主键\",\r\n      \"port_cd\":\"港口代码\",\r\n      \"port_nm\":\"港口名称\",\r\n      \"port_nm_en\":\"港口英文名称\",\r\n      \"fep_ip\":\"前置机IP\",\r\n      \"fep_port\":\"端口\",\r\n      \"user_name\":\"用户名\",\r\n      \"user_password\":\"密码\",\r\n      \"file_path\":\"文件位置\",\r\n      \"interface\":\"接口地址\",\r\n      \"org_id\":\"所属组织主键\",\r\n      \"org_nm\":\"所属组织机构名称\",\r\n      \"org_level_no\":\"所属组织机构代码\",\r\n      \"ent_level_no\":\"所属公司代码\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,gBAAiB,SAAQD,QAAQ;EAQ5CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,UAAU;MACf,UAAU,EAAC,MAAM;MACjB,UAAU,EAAC,MAAM;MACjB,UAAU,EAAC,MAAM;MACjB,aAAa,EAAC,QAAQ;MACtB,SAAS,EAAC,MAAM;MAChB,SAAS,EAAC,MAAM;MAChB,SAAS,EAAC,MAAM;MAChB,YAAY,EAAC,QAAQ;MACrB,QAAQ,EAAC,OAAO;MAChB,UAAU,EAAC,IAAI;MACf,WAAW,EAAC,KAAK;MACjB,eAAe,EAAC,IAAI;MACpB,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC,MAAM;MAClB,QAAQ,EAAC,QAAQ;MACjB,QAAQ,EAAC,UAAU;MACnB,cAAc,EAAC,UAAU;MACzB,cAAc,EAAC,QAAQ;MACvB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IAnCJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,mBAAmB,CAAC,CAAC;IACjC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAgCnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}