{"ast": null, "code": "/* eslint-disable no-case-declarations, max-len */\nconst {\n  BigInteger\n} = require('jsbn');\n\n/**\r\n * thanks for <PERSON> : http://www-cs-students.stanford.edu/~tjw/jsbn/\r\n *\r\n * Basic Javascript Elliptic Curve implementation\r\n * Ported loosely from BouncyCastle's Java EC code\r\n * Only Fp curves implemented for now\r\n */\n\nconst TWO = new BigInteger('2');\nconst THREE = new BigInteger('3');\n\n/**\r\n * 椭圆曲线域元素\r\n */\nclass ECFieldElementFp {\n  constructor(q, x) {\n    this.x = x;\n    this.q = q;\n    // TODO if (x.compareTo(q) >= 0) error\n  }\n\n  /**\r\n   * 判断相等\r\n   */\n  equals(other) {\n    if (other === this) return true;\n    return this.q.equals(other.q) && this.x.equals(other.x);\n  }\n\n  /**\r\n   * 返回具体数值\r\n   */\n  toBigInteger() {\n    return this.x;\n  }\n\n  /**\r\n   * 取反\r\n   */\n  negate() {\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q));\n  }\n\n  /**\r\n   * 相加\r\n   */\n  add(b) {\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q));\n  }\n\n  /**\r\n   * 相减\r\n   */\n  subtract(b) {\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q));\n  }\n\n  /**\r\n   * 相乘\r\n   */\n  multiply(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q));\n  }\n\n  /**\r\n   * 相除\r\n   */\n  divide(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q));\n  }\n\n  /**\r\n   * 平方\r\n   */\n  square() {\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q));\n  }\n}\nclass ECPointFp {\n  constructor(curve, x, y, z) {\n    this.curve = curve;\n    this.x = x;\n    this.y = y;\n    // 标准射影坐标系：zinv == null 或 z * zinv == 1\n    this.z = z == null ? BigInteger.ONE : z;\n    this.zinv = null;\n    // TODO: compression flag\n  }\n  getX() {\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q);\n    return this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q));\n  }\n  getY() {\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q);\n    return this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q));\n  }\n\n  /**\r\n   * 判断相等\r\n   */\n  equals(other) {\n    if (other === this) return true;\n    if (this.isInfinity()) return other.isInfinity();\n    if (other.isInfinity()) return this.isInfinity();\n\n    // u = y2 * z1 - y1 * z2\n    const u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    if (!u.equals(BigInteger.ZERO)) return false;\n\n    // v = x2 * z1 - x1 * z2\n    const v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    return v.equals(BigInteger.ZERO);\n  }\n\n  /**\r\n   * 是否是无穷远点\r\n   */\n  isInfinity() {\n    if (this.x === null && this.y === null) return true;\n    return this.z.equals(BigInteger.ZERO) && !this.y.toBigInteger().equals(BigInteger.ZERO);\n  }\n\n  /**\r\n   * 取反，x 轴对称点\r\n   */\n  negate() {\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z);\n  }\n\n  /**\r\n   * 相加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = x1 * z2\r\n   * λ2 = x2 * z1\r\n   * λ3 = λ1 − λ2\r\n   * λ4 = y1 * z2\r\n   * λ5 = y2 * z1\r\n   * λ6 = λ4 − λ5\r\n   * λ7 = λ1 + λ2\r\n   * λ8 = z1 * z2\r\n   * λ9 = λ3^2\r\n   * λ10 = λ3 * λ9\r\n   * λ11 = λ8 * λ6^2 − λ7 * λ9\r\n   * x3 = λ3 * λ11\r\n   * y3 = λ6 * (λ9 * λ1 − λ11) − λ4 * λ10\r\n   * z3 = λ10 * λ8\r\n   */\n  add(b) {\n    if (this.isInfinity()) return b;\n    if (b.isInfinity()) return this;\n    const x1 = this.x.toBigInteger();\n    const y1 = this.y.toBigInteger();\n    const z1 = this.z;\n    const x2 = b.x.toBigInteger();\n    const y2 = b.y.toBigInteger();\n    const z2 = b.z;\n    const q = this.curve.q;\n    const w1 = x1.multiply(z2).mod(q);\n    const w2 = x2.multiply(z1).mod(q);\n    const w3 = w1.subtract(w2);\n    const w4 = y1.multiply(z2).mod(q);\n    const w5 = y2.multiply(z1).mod(q);\n    const w6 = w4.subtract(w5);\n    if (BigInteger.ZERO.equals(w3)) {\n      if (BigInteger.ZERO.equals(w6)) {\n        return this.twice(); // this == b，计算自加\n      }\n      return this.curve.infinity; // this == -b，则返回无穷远点\n    }\n    const w7 = w1.add(w2);\n    const w8 = z1.multiply(z2).mod(q);\n    const w9 = w3.square().mod(q);\n    const w10 = w3.multiply(w9).mod(q);\n    const w11 = w8.multiply(w6.square()).subtract(w7.multiply(w9)).mod(q);\n    const x3 = w3.multiply(w11).mod(q);\n    const y3 = w6.multiply(w9.multiply(w1).subtract(w11)).subtract(w4.multiply(w10)).mod(q);\n    const z3 = w10.multiply(w8).mod(q);\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n  }\n\n  /**\r\n   * 自加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = 3 * x1^2 + a * z1^2\r\n   * λ2 = 2 * y1 * z1\r\n   * λ3 = y1^2\r\n   * λ4 = λ3 * x1 * z1\r\n   * λ5 = λ2^2\r\n   * λ6 = λ1^2 − 8 * λ4\r\n   * x3 = λ2 * λ6\r\n   * y3 = λ1 * (4 * λ4 − λ6) − 2 * λ5 * λ3\r\n   * z3 = λ2 * λ5\r\n   */\n  twice() {\n    if (this.isInfinity()) return this;\n    if (!this.y.toBigInteger().signum()) return this.curve.infinity;\n    const x1 = this.x.toBigInteger();\n    const y1 = this.y.toBigInteger();\n    const z1 = this.z;\n    const q = this.curve.q;\n    const a = this.curve.a.toBigInteger();\n    const w1 = x1.square().multiply(THREE).add(a.multiply(z1.square())).mod(q);\n    const w2 = y1.shiftLeft(1).multiply(z1).mod(q);\n    const w3 = y1.square().mod(q);\n    const w4 = w3.multiply(x1).multiply(z1).mod(q);\n    const w5 = w2.square().mod(q);\n    const w6 = w1.square().subtract(w4.shiftLeft(3)).mod(q);\n    const x3 = w2.multiply(w6).mod(q);\n    const y3 = w1.multiply(w4.shiftLeft(2).subtract(w6)).subtract(w5.shiftLeft(1).multiply(w3)).mod(q);\n    const z3 = w2.multiply(w5).mod(q);\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n  }\n\n  /**\r\n   * 倍点计算\r\n   */\n  multiply(k) {\n    if (this.isInfinity()) return this;\n    if (!k.signum()) return this.curve.infinity;\n\n    // 使用加减法\n    const k3 = k.multiply(THREE);\n    const neg = this.negate();\n    let Q = this;\n    for (let i = k3.bitLength() - 2; i > 0; i--) {\n      Q = Q.twice();\n      const k3Bit = k3.testBit(i);\n      const kBit = k.testBit(i);\n      if (k3Bit !== kBit) {\n        Q = Q.add(k3Bit ? this : neg);\n      }\n    }\n    return Q;\n  }\n}\n\n/**\r\n * 椭圆曲线 y^2 = x^3 + ax + b\r\n */\nclass ECCurveFp {\n  constructor(q, a, b) {\n    this.q = q;\n    this.a = this.fromBigInteger(a);\n    this.b = this.fromBigInteger(b);\n    this.infinity = new ECPointFp(this, null, null); // 无穷远点\n  }\n\n  /**\r\n   * 判断两个椭圆曲线是否相等\r\n   */\n  equals(other) {\n    if (other === this) return true;\n    return this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b);\n  }\n\n  /**\r\n   * 生成椭圆曲线域元素\r\n   */\n  fromBigInteger(x) {\n    return new ECFieldElementFp(this.q, x);\n  }\n\n  /**\r\n   * 解析 16 进制串为椭圆曲线点\r\n   */\n  decodePointHex(s) {\n    switch (parseInt(s.substr(0, 2), 16)) {\n      // 第一个字节\n      case 0:\n        return this.infinity;\n      case 2:\n      case 3:\n        // 压缩\n        const x = this.fromBigInteger(new BigInteger(s.substr(2), 16));\n        // 对 p ≡ 3 (mod4)，即存在正整数 u，使得 p = 4u + 3\n        // 计算 y = (√ (x^3 + ax + b) % p)^(u + 1) modp\n        let y = this.fromBigInteger(x.multiply(x.square()).add(x.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new BigInteger('4')).add(BigInteger.ONE), this.q));\n        // 算出结果 2 进制最后 1 位不等于第 1 个字节减 2 则取反\n        if (!y.toBigInteger().mod(TWO).equals(new BigInteger(s.substr(0, 2), 16).subtract(TWO))) {\n          y = y.negate();\n        }\n        return new ECPointFp(this, x, y);\n      case 4:\n      case 6:\n      case 7:\n        const len = (s.length - 2) / 2;\n        const xHex = s.substr(2, len);\n        const yHex = s.substr(len + 2, len);\n        return new ECPointFp(this, this.fromBigInteger(new BigInteger(xHex, 16)), this.fromBigInteger(new BigInteger(yHex, 16)));\n      default:\n        // 不支持\n        return null;\n    }\n  }\n}\nmodule.exports = {\n  ECPointFp,\n  ECCurveFp\n};", "map": {"version": 3, "names": ["BigInteger", "require", "TWO", "THREE", "ECFieldElementFp", "constructor", "q", "x", "equals", "other", "toBigInteger", "negate", "mod", "add", "b", "subtract", "multiply", "divide", "modInverse", "square", "ECPointFp", "curve", "y", "z", "ONE", "zinv", "getX", "fromBigInteger", "getY", "isInfinity", "u", "ZERO", "v", "x1", "y1", "z1", "x2", "y2", "z2", "w1", "w2", "w3", "w4", "w5", "w6", "twice", "infinity", "w7", "w8", "w9", "w10", "w11", "x3", "y3", "z3", "signum", "a", "shiftLeft", "k", "k3", "neg", "Q", "i", "bitLength", "k3Bit", "testBit", "kBit", "ECCurveFp", "decodePointHex", "s", "parseInt", "substr", "modPow", "len", "length", "xHex", "yHex", "module", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/sm-crypto/src/sm2/ec.js"], "sourcesContent": ["/* eslint-disable no-case-declarations, max-len */\r\nconst {BigInteger} = require('jsbn')\r\n\r\n/**\r\n * thanks for <PERSON> : http://www-cs-students.stanford.edu/~tjw/jsbn/\r\n *\r\n * Basic Javascript Elliptic Curve implementation\r\n * Ported loosely from BouncyCastle's Java EC code\r\n * Only Fp curves implemented for now\r\n */\r\n\r\nconst TWO = new BigInteger('2')\r\nconst THREE = new BigInteger('3')\r\n\r\n/**\r\n * 椭圆曲线域元素\r\n */\r\nclass ECFieldElementFp {\r\n  constructor(q, x) {\r\n    this.x = x\r\n    this.q = q\r\n    // TODO if (x.compareTo(q) >= 0) error\r\n  }\r\n\r\n  /**\r\n   * 判断相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    return (this.q.equals(other.q) && this.x.equals(other.x))\r\n  }\r\n\r\n  /**\r\n   * 返回具体数值\r\n   */\r\n  toBigInteger() {\r\n    return this.x\r\n  }\r\n\r\n  /**\r\n   * 取反\r\n   */\r\n  negate() {\r\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相加\r\n   */\r\n  add(b) {\r\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相减\r\n   */\r\n  subtract(b) {\r\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相乘\r\n   */\r\n  multiply(b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 相除\r\n   */\r\n  divide(b) {\r\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q))\r\n  }\r\n\r\n  /**\r\n   * 平方\r\n   */\r\n  square() {\r\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q))\r\n  }\r\n}\r\n\r\nclass ECPointFp {\r\n  constructor(curve, x, y, z) {\r\n    this.curve = curve\r\n    this.x = x\r\n    this.y = y\r\n    // 标准射影坐标系：zinv == null 或 z * zinv == 1\r\n    this.z = z == null ? BigInteger.ONE : z\r\n    this.zinv = null\r\n    // TODO: compression flag\r\n  }\r\n\r\n  getX() {\r\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  getY() {\r\n    if (this.zinv === null) this.zinv = this.z.modInverse(this.curve.q)\r\n\r\n    return this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))\r\n  }\r\n\r\n  /**\r\n   * 判断相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    if (this.isInfinity()) return other.isInfinity()\r\n    if (other.isInfinity()) return this.isInfinity()\r\n\r\n    // u = y2 * z1 - y1 * z2\r\n    const u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    if (!u.equals(BigInteger.ZERO)) return false\r\n\r\n    // v = x2 * z1 - x1 * z2\r\n    const v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q)\r\n    return v.equals(BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n   * 是否是无穷远点\r\n   */\r\n  isInfinity() {\r\n    if ((this.x === null) && (this.y === null)) return true\r\n    return this.z.equals(BigInteger.ZERO) && !this.y.toBigInteger().equals(BigInteger.ZERO)\r\n  }\r\n\r\n  /**\r\n   * 取反，x 轴对称点\r\n   */\r\n  negate() {\r\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z)\r\n  }\r\n\r\n  /**\r\n   * 相加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = x1 * z2\r\n   * λ2 = x2 * z1\r\n   * λ3 = λ1 − λ2\r\n   * λ4 = y1 * z2\r\n   * λ5 = y2 * z1\r\n   * λ6 = λ4 − λ5\r\n   * λ7 = λ1 + λ2\r\n   * λ8 = z1 * z2\r\n   * λ9 = λ3^2\r\n   * λ10 = λ3 * λ9\r\n   * λ11 = λ8 * λ6^2 − λ7 * λ9\r\n   * x3 = λ3 * λ11\r\n   * y3 = λ6 * (λ9 * λ1 − λ11) − λ4 * λ10\r\n   * z3 = λ10 * λ8\r\n   */\r\n  add(b) {\r\n    if (this.isInfinity()) return b\r\n    if (b.isInfinity()) return this\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const x2 = b.x.toBigInteger()\r\n    const y2 = b.y.toBigInteger()\r\n    const z2 = b.z\r\n    const q = this.curve.q\r\n\r\n    const w1 = x1.multiply(z2).mod(q)\r\n    const w2 = x2.multiply(z1).mod(q)\r\n    const w3 = w1.subtract(w2)\r\n    const w4 = y1.multiply(z2).mod(q)\r\n    const w5 = y2.multiply(z1).mod(q)\r\n    const w6 = w4.subtract(w5)\r\n\r\n    if (BigInteger.ZERO.equals(w3)) {\r\n      if (BigInteger.ZERO.equals(w6)) {\r\n        return this.twice() // this == b，计算自加\r\n      }\r\n      return this.curve.infinity // this == -b，则返回无穷远点\r\n    }\r\n\r\n    const w7 = w1.add(w2)\r\n    const w8 = z1.multiply(z2).mod(q)\r\n    const w9 = w3.square().mod(q)\r\n    const w10 = w3.multiply(w9).mod(q)\r\n    const w11 = w8.multiply(w6.square()).subtract(w7.multiply(w9)).mod(q)\r\n\r\n    const x3 = w3.multiply(w11).mod(q)\r\n    const y3 = w6.multiply(w9.multiply(w1).subtract(w11)).subtract(w4.multiply(w10)).mod(q)\r\n    const z3 = w10.multiply(w8).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n   * 自加\r\n   *\r\n   * 标准射影坐标系：\r\n   *\r\n   * λ1 = 3 * x1^2 + a * z1^2\r\n   * λ2 = 2 * y1 * z1\r\n   * λ3 = y1^2\r\n   * λ4 = λ3 * x1 * z1\r\n   * λ5 = λ2^2\r\n   * λ6 = λ1^2 − 8 * λ4\r\n   * x3 = λ2 * λ6\r\n   * y3 = λ1 * (4 * λ4 − λ6) − 2 * λ5 * λ3\r\n   * z3 = λ2 * λ5\r\n   */\r\n  twice() {\r\n    if (this.isInfinity()) return this\r\n    if (!this.y.toBigInteger().signum()) return this.curve.infinity\r\n\r\n    const x1 = this.x.toBigInteger()\r\n    const y1 = this.y.toBigInteger()\r\n    const z1 = this.z\r\n    const q = this.curve.q\r\n    const a = this.curve.a.toBigInteger()\r\n\r\n    const w1 = x1.square().multiply(THREE).add(a.multiply(z1.square())).mod(q)\r\n    const w2 = y1.shiftLeft(1).multiply(z1).mod(q)\r\n    const w3 = y1.square().mod(q)\r\n    const w4 = w3.multiply(x1).multiply(z1).mod(q)\r\n    const w5 = w2.square().mod(q)\r\n    const w6 = w1.square().subtract(w4.shiftLeft(3)).mod(q)\r\n\r\n    const x3 = w2.multiply(w6).mod(q)\r\n    const y3 = w1.multiply(w4.shiftLeft(2).subtract(w6)).subtract(w5.shiftLeft(1).multiply(w3)).mod(q)\r\n    const z3 = w2.multiply(w5).mod(q)\r\n\r\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3)\r\n  }\r\n\r\n  /**\r\n   * 倍点计算\r\n   */\r\n  multiply(k) {\r\n    if (this.isInfinity()) return this\r\n    if (!k.signum()) return this.curve.infinity\r\n\r\n    // 使用加减法\r\n    const k3 = k.multiply(THREE)\r\n    const neg = this.negate()\r\n    let Q = this\r\n\r\n    for (let i = k3.bitLength() - 2; i > 0; i--) {\r\n      Q = Q.twice()\r\n\r\n      const k3Bit = k3.testBit(i)\r\n      const kBit = k.testBit(i)\r\n\r\n      if (k3Bit !== kBit) {\r\n        Q = Q.add(k3Bit ? this : neg)\r\n      }\r\n    }\r\n\r\n    return Q\r\n  }\r\n}\r\n\r\n/**\r\n * 椭圆曲线 y^2 = x^3 + ax + b\r\n */\r\nclass ECCurveFp {\r\n  constructor(q, a, b) {\r\n    this.q = q\r\n    this.a = this.fromBigInteger(a)\r\n    this.b = this.fromBigInteger(b)\r\n    this.infinity = new ECPointFp(this, null, null) // 无穷远点\r\n  }\r\n\r\n  /**\r\n   * 判断两个椭圆曲线是否相等\r\n   */\r\n  equals(other) {\r\n    if (other === this) return true\r\n    return (this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b))\r\n  }\r\n\r\n  /**\r\n   * 生成椭圆曲线域元素\r\n   */\r\n  fromBigInteger(x) {\r\n    return new ECFieldElementFp(this.q, x)\r\n  }\r\n\r\n  /**\r\n   * 解析 16 进制串为椭圆曲线点\r\n   */\r\n  decodePointHex(s) {\r\n    switch (parseInt(s.substr(0, 2), 16)) {\r\n      // 第一个字节\r\n      case 0:\r\n        return this.infinity\r\n      case 2:\r\n      case 3:\r\n        // 压缩\r\n        const x = this.fromBigInteger(new BigInteger(s.substr(2), 16))\r\n        // 对 p ≡ 3 (mod4)，即存在正整数 u，使得 p = 4u + 3\r\n        // 计算 y = (√ (x^3 + ax + b) % p)^(u + 1) modp\r\n        let y = this.fromBigInteger(x.multiply(x.square()).add(\r\n          x.multiply(this.a)\r\n        ).add(this.b).toBigInteger()\r\n          .modPow(\r\n            this.q.divide(new BigInteger('4')).add(BigInteger.ONE), this.q\r\n          ))\r\n        // 算出结果 2 进制最后 1 位不等于第 1 个字节减 2 则取反\r\n        if (!y.toBigInteger().mod(TWO).equals(new BigInteger(s.substr(0, 2), 16).subtract(TWO))) {\r\n          y = y.negate()\r\n        }\r\n        return new ECPointFp(this, x, y)\r\n      case 4:\r\n      case 6:\r\n      case 7:\r\n        const len = (s.length - 2) / 2\r\n        const xHex = s.substr(2, len)\r\n        const yHex = s.substr(len + 2, len)\r\n\r\n        return new ECPointFp(this, this.fromBigInteger(new BigInteger(xHex, 16)), this.fromBigInteger(new BigInteger(yHex, 16)))\r\n      default:\r\n        // 不支持\r\n        return null\r\n    }\r\n  }\r\n}\r\n\r\nmodule.exports = {\r\n  ECPointFp,\r\n  ECCurveFp,\r\n}\r\n"], "mappings": "AAAA;AACA,MAAM;EAACA;AAAU,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,GAAG,GAAG,IAAIF,UAAU,CAAC,GAAG,CAAC;AAC/B,MAAMG,KAAK,GAAG,IAAIH,UAAU,CAAC,GAAG,CAAC;;AAEjC;AACA;AACA;AACA,MAAMI,gBAAgB,CAAC;EACrBC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAChB,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV;EACF;;EAEA;AACF;AACA;EACEE,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;IAC/B,OAAQ,IAAI,CAACH,CAAC,CAACE,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAI,IAAI,CAACC,CAAC,CAACC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC;EAC1D;;EAEA;AACF;AACA;EACEG,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACH,CAAC;EACf;;EAEA;AACF;AACA;EACEI,MAAMA,CAAA,EAAG;IACP,OAAO,IAAIP,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACI,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EAClE;;EAEA;AACF;AACA;EACEO,GAAGA,CAACC,CAAC,EAAE;IACL,OAAO,IAAIV,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACM,GAAG,CAACC,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EAC/E;;EAEA;AACF;AACA;EACES,QAAQA,CAACD,CAAC,EAAE;IACV,OAAO,IAAIV,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACQ,QAAQ,CAACD,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EACpF;;EAEA;AACF;AACA;EACEU,QAAQA,CAACF,CAAC,EAAE;IACV,OAAO,IAAIV,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACS,QAAQ,CAACF,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EACpF;;EAEA;AACF;AACA;EACEW,MAAMA,CAACH,CAAC,EAAE;IACR,OAAO,IAAIV,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACS,QAAQ,CAACF,CAAC,CAACJ,YAAY,CAAC,CAAC,CAACQ,UAAU,CAAC,IAAI,CAACZ,CAAC,CAAC,CAAC,CAACM,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EACvG;;EAEA;AACF;AACA;EACEa,MAAMA,CAAA,EAAG;IACP,OAAO,IAAIf,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAE,IAAI,CAACC,CAAC,CAACY,MAAM,CAAC,CAAC,CAACP,GAAG,CAAC,IAAI,CAACN,CAAC,CAAC,CAAC;EAClE;AACF;AAEA,MAAMc,SAAS,CAAC;EACdf,WAAWA,CAACgB,KAAK,EAAEd,CAAC,EAAEe,CAAC,EAAEC,CAAC,EAAE;IAC1B,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACd,CAAC,GAAGA,CAAC;IACV,IAAI,CAACe,CAAC,GAAGA,CAAC;IACV;IACA,IAAI,CAACC,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGvB,UAAU,CAACwB,GAAG,GAAGD,CAAC;IACvC,IAAI,CAACE,IAAI,GAAG,IAAI;IAChB;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACD,IAAI,KAAK,IAAI,EAAE,IAAI,CAACA,IAAI,GAAG,IAAI,CAACF,CAAC,CAACL,UAAU,CAAC,IAAI,CAACG,KAAK,CAACf,CAAC,CAAC;IAEnE,OAAO,IAAI,CAACe,KAAK,CAACM,cAAc,CAAC,IAAI,CAACpB,CAAC,CAACG,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,IAAI,CAACS,IAAI,CAAC,CAACb,GAAG,CAAC,IAAI,CAACS,KAAK,CAACf,CAAC,CAAC,CAAC;EAC/F;EAEAsB,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACH,IAAI,KAAK,IAAI,EAAE,IAAI,CAACA,IAAI,GAAG,IAAI,CAACF,CAAC,CAACL,UAAU,CAAC,IAAI,CAACG,KAAK,CAACf,CAAC,CAAC;IAEnE,OAAO,IAAI,CAACe,KAAK,CAACM,cAAc,CAAC,IAAI,CAACL,CAAC,CAACZ,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,IAAI,CAACS,IAAI,CAAC,CAACb,GAAG,CAAC,IAAI,CAACS,KAAK,CAACf,CAAC,CAAC,CAAC;EAC/F;;EAEA;AACF;AACA;EACEE,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;IAC/B,IAAI,IAAI,CAACoB,UAAU,CAAC,CAAC,EAAE,OAAOpB,KAAK,CAACoB,UAAU,CAAC,CAAC;IAChD,IAAIpB,KAAK,CAACoB,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI,CAACA,UAAU,CAAC,CAAC;;IAEhD;IACA,MAAMC,CAAC,GAAGrB,KAAK,CAACa,CAAC,CAACZ,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,IAAI,CAACO,CAAC,CAAC,CAACR,QAAQ,CAAC,IAAI,CAACO,CAAC,CAACZ,YAAY,CAAC,CAAC,CAACM,QAAQ,CAACP,KAAK,CAACc,CAAC,CAAC,CAAC,CAACX,GAAG,CAAC,IAAI,CAACS,KAAK,CAACf,CAAC,CAAC;IACrH,IAAI,CAACwB,CAAC,CAACtB,MAAM,CAACR,UAAU,CAAC+B,IAAI,CAAC,EAAE,OAAO,KAAK;;IAE5C;IACA,MAAMC,CAAC,GAAGvB,KAAK,CAACF,CAAC,CAACG,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,IAAI,CAACO,CAAC,CAAC,CAACR,QAAQ,CAAC,IAAI,CAACR,CAAC,CAACG,YAAY,CAAC,CAAC,CAACM,QAAQ,CAACP,KAAK,CAACc,CAAC,CAAC,CAAC,CAACX,GAAG,CAAC,IAAI,CAACS,KAAK,CAACf,CAAC,CAAC;IACrH,OAAO0B,CAAC,CAACxB,MAAM,CAACR,UAAU,CAAC+B,IAAI,CAAC;EAClC;;EAEA;AACF;AACA;EACEF,UAAUA,CAAA,EAAG;IACX,IAAK,IAAI,CAACtB,CAAC,KAAK,IAAI,IAAM,IAAI,CAACe,CAAC,KAAK,IAAK,EAAE,OAAO,IAAI;IACvD,OAAO,IAAI,CAACC,CAAC,CAACf,MAAM,CAACR,UAAU,CAAC+B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACT,CAAC,CAACZ,YAAY,CAAC,CAAC,CAACF,MAAM,CAACR,UAAU,CAAC+B,IAAI,CAAC;EACzF;;EAEA;AACF;AACA;EACEpB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAIS,SAAS,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACd,CAAC,EAAE,IAAI,CAACe,CAAC,CAACX,MAAM,CAAC,CAAC,EAAE,IAAI,CAACY,CAAC,CAAC;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEV,GAAGA,CAACC,CAAC,EAAE;IACL,IAAI,IAAI,CAACe,UAAU,CAAC,CAAC,EAAE,OAAOf,CAAC;IAC/B,IAAIA,CAAC,CAACe,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI;IAE/B,MAAMI,EAAE,GAAG,IAAI,CAAC1B,CAAC,CAACG,YAAY,CAAC,CAAC;IAChC,MAAMwB,EAAE,GAAG,IAAI,CAACZ,CAAC,CAACZ,YAAY,CAAC,CAAC;IAChC,MAAMyB,EAAE,GAAG,IAAI,CAACZ,CAAC;IACjB,MAAMa,EAAE,GAAGtB,CAAC,CAACP,CAAC,CAACG,YAAY,CAAC,CAAC;IAC7B,MAAM2B,EAAE,GAAGvB,CAAC,CAACQ,CAAC,CAACZ,YAAY,CAAC,CAAC;IAC7B,MAAM4B,EAAE,GAAGxB,CAAC,CAACS,CAAC;IACd,MAAMjB,CAAC,GAAG,IAAI,CAACe,KAAK,CAACf,CAAC;IAEtB,MAAMiC,EAAE,GAAGN,EAAE,CAACjB,QAAQ,CAACsB,EAAE,CAAC,CAAC1B,GAAG,CAACN,CAAC,CAAC;IACjC,MAAMkC,EAAE,GAAGJ,EAAE,CAACpB,QAAQ,CAACmB,EAAE,CAAC,CAACvB,GAAG,CAACN,CAAC,CAAC;IACjC,MAAMmC,EAAE,GAAGF,EAAE,CAACxB,QAAQ,CAACyB,EAAE,CAAC;IAC1B,MAAME,EAAE,GAAGR,EAAE,CAAClB,QAAQ,CAACsB,EAAE,CAAC,CAAC1B,GAAG,CAACN,CAAC,CAAC;IACjC,MAAMqC,EAAE,GAAGN,EAAE,CAACrB,QAAQ,CAACmB,EAAE,CAAC,CAACvB,GAAG,CAACN,CAAC,CAAC;IACjC,MAAMsC,EAAE,GAAGF,EAAE,CAAC3B,QAAQ,CAAC4B,EAAE,CAAC;IAE1B,IAAI3C,UAAU,CAAC+B,IAAI,CAACvB,MAAM,CAACiC,EAAE,CAAC,EAAE;MAC9B,IAAIzC,UAAU,CAAC+B,IAAI,CAACvB,MAAM,CAACoC,EAAE,CAAC,EAAE;QAC9B,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAC;MACtB;MACA,OAAO,IAAI,CAACxB,KAAK,CAACyB,QAAQ,EAAC;IAC7B;IAEA,MAAMC,EAAE,GAAGR,EAAE,CAAC1B,GAAG,CAAC2B,EAAE,CAAC;IACrB,MAAMQ,EAAE,GAAGb,EAAE,CAACnB,QAAQ,CAACsB,EAAE,CAAC,CAAC1B,GAAG,CAACN,CAAC,CAAC;IACjC,MAAM2C,EAAE,GAAGR,EAAE,CAACtB,MAAM,CAAC,CAAC,CAACP,GAAG,CAACN,CAAC,CAAC;IAC7B,MAAM4C,GAAG,GAAGT,EAAE,CAACzB,QAAQ,CAACiC,EAAE,CAAC,CAACrC,GAAG,CAACN,CAAC,CAAC;IAClC,MAAM6C,GAAG,GAAGH,EAAE,CAAChC,QAAQ,CAAC4B,EAAE,CAACzB,MAAM,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAACgC,EAAE,CAAC/B,QAAQ,CAACiC,EAAE,CAAC,CAAC,CAACrC,GAAG,CAACN,CAAC,CAAC;IAErE,MAAM8C,EAAE,GAAGX,EAAE,CAACzB,QAAQ,CAACmC,GAAG,CAAC,CAACvC,GAAG,CAACN,CAAC,CAAC;IAClC,MAAM+C,EAAE,GAAGT,EAAE,CAAC5B,QAAQ,CAACiC,EAAE,CAACjC,QAAQ,CAACuB,EAAE,CAAC,CAACxB,QAAQ,CAACoC,GAAG,CAAC,CAAC,CAACpC,QAAQ,CAAC2B,EAAE,CAAC1B,QAAQ,CAACkC,GAAG,CAAC,CAAC,CAACtC,GAAG,CAACN,CAAC,CAAC;IACvF,MAAMgD,EAAE,GAAGJ,GAAG,CAAClC,QAAQ,CAACgC,EAAE,CAAC,CAACpC,GAAG,CAACN,CAAC,CAAC;IAElC,OAAO,IAAIc,SAAS,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACA,KAAK,CAACM,cAAc,CAACyB,EAAE,CAAC,EAAE,IAAI,CAAC/B,KAAK,CAACM,cAAc,CAAC0B,EAAE,CAAC,EAAEC,EAAE,CAAC;EACpG;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACET,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAAChB,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI;IAClC,IAAI,CAAC,IAAI,CAACP,CAAC,CAACZ,YAAY,CAAC,CAAC,CAAC6C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAClC,KAAK,CAACyB,QAAQ;IAE/D,MAAMb,EAAE,GAAG,IAAI,CAAC1B,CAAC,CAACG,YAAY,CAAC,CAAC;IAChC,MAAMwB,EAAE,GAAG,IAAI,CAACZ,CAAC,CAACZ,YAAY,CAAC,CAAC;IAChC,MAAMyB,EAAE,GAAG,IAAI,CAACZ,CAAC;IACjB,MAAMjB,CAAC,GAAG,IAAI,CAACe,KAAK,CAACf,CAAC;IACtB,MAAMkD,CAAC,GAAG,IAAI,CAACnC,KAAK,CAACmC,CAAC,CAAC9C,YAAY,CAAC,CAAC;IAErC,MAAM6B,EAAE,GAAGN,EAAE,CAACd,MAAM,CAAC,CAAC,CAACH,QAAQ,CAACb,KAAK,CAAC,CAACU,GAAG,CAAC2C,CAAC,CAACxC,QAAQ,CAACmB,EAAE,CAAChB,MAAM,CAAC,CAAC,CAAC,CAAC,CAACP,GAAG,CAACN,CAAC,CAAC;IAC1E,MAAMkC,EAAE,GAAGN,EAAE,CAACuB,SAAS,CAAC,CAAC,CAAC,CAACzC,QAAQ,CAACmB,EAAE,CAAC,CAACvB,GAAG,CAACN,CAAC,CAAC;IAC9C,MAAMmC,EAAE,GAAGP,EAAE,CAACf,MAAM,CAAC,CAAC,CAACP,GAAG,CAACN,CAAC,CAAC;IAC7B,MAAMoC,EAAE,GAAGD,EAAE,CAACzB,QAAQ,CAACiB,EAAE,CAAC,CAACjB,QAAQ,CAACmB,EAAE,CAAC,CAACvB,GAAG,CAACN,CAAC,CAAC;IAC9C,MAAMqC,EAAE,GAAGH,EAAE,CAACrB,MAAM,CAAC,CAAC,CAACP,GAAG,CAACN,CAAC,CAAC;IAC7B,MAAMsC,EAAE,GAAGL,EAAE,CAACpB,MAAM,CAAC,CAAC,CAACJ,QAAQ,CAAC2B,EAAE,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC7C,GAAG,CAACN,CAAC,CAAC;IAEvD,MAAM8C,EAAE,GAAGZ,EAAE,CAACxB,QAAQ,CAAC4B,EAAE,CAAC,CAAChC,GAAG,CAACN,CAAC,CAAC;IACjC,MAAM+C,EAAE,GAAGd,EAAE,CAACvB,QAAQ,CAAC0B,EAAE,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC1C,QAAQ,CAAC6B,EAAE,CAAC,CAAC,CAAC7B,QAAQ,CAAC4B,EAAE,CAACc,SAAS,CAAC,CAAC,CAAC,CAACzC,QAAQ,CAACyB,EAAE,CAAC,CAAC,CAAC7B,GAAG,CAACN,CAAC,CAAC;IAClG,MAAMgD,EAAE,GAAGd,EAAE,CAACxB,QAAQ,CAAC2B,EAAE,CAAC,CAAC/B,GAAG,CAACN,CAAC,CAAC;IAEjC,OAAO,IAAIc,SAAS,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACA,KAAK,CAACM,cAAc,CAACyB,EAAE,CAAC,EAAE,IAAI,CAAC/B,KAAK,CAACM,cAAc,CAAC0B,EAAE,CAAC,EAAEC,EAAE,CAAC;EACpG;;EAEA;AACF;AACA;EACEtC,QAAQA,CAAC0C,CAAC,EAAE;IACV,IAAI,IAAI,CAAC7B,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI;IAClC,IAAI,CAAC6B,CAAC,CAACH,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAClC,KAAK,CAACyB,QAAQ;;IAE3C;IACA,MAAMa,EAAE,GAAGD,CAAC,CAAC1C,QAAQ,CAACb,KAAK,CAAC;IAC5B,MAAMyD,GAAG,GAAG,IAAI,CAACjD,MAAM,CAAC,CAAC;IACzB,IAAIkD,CAAC,GAAG,IAAI;IAEZ,KAAK,IAAIC,CAAC,GAAGH,EAAE,CAACI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3CD,CAAC,GAAGA,CAAC,CAAChB,KAAK,CAAC,CAAC;MAEb,MAAMmB,KAAK,GAAGL,EAAE,CAACM,OAAO,CAACH,CAAC,CAAC;MAC3B,MAAMI,IAAI,GAAGR,CAAC,CAACO,OAAO,CAACH,CAAC,CAAC;MAEzB,IAAIE,KAAK,KAAKE,IAAI,EAAE;QAClBL,CAAC,GAAGA,CAAC,CAAChD,GAAG,CAACmD,KAAK,GAAG,IAAI,GAAGJ,GAAG,CAAC;MAC/B;IACF;IAEA,OAAOC,CAAC;EACV;AACF;;AAEA;AACA;AACA;AACA,MAAMM,SAAS,CAAC;EACd9D,WAAWA,CAACC,CAAC,EAAEkD,CAAC,EAAE1C,CAAC,EAAE;IACnB,IAAI,CAACR,CAAC,GAAGA,CAAC;IACV,IAAI,CAACkD,CAAC,GAAG,IAAI,CAAC7B,cAAc,CAAC6B,CAAC,CAAC;IAC/B,IAAI,CAAC1C,CAAC,GAAG,IAAI,CAACa,cAAc,CAACb,CAAC,CAAC;IAC/B,IAAI,CAACgC,QAAQ,GAAG,IAAI1B,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAC;EAClD;;EAEA;AACF;AACA;EACEZ,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;IAC/B,OAAQ,IAAI,CAACH,CAAC,CAACE,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAI,IAAI,CAACkD,CAAC,CAAChD,MAAM,CAACC,KAAK,CAAC+C,CAAC,CAAC,IAAI,IAAI,CAAC1C,CAAC,CAACN,MAAM,CAACC,KAAK,CAACK,CAAC,CAAC;EACpF;;EAEA;AACF;AACA;EACEa,cAAcA,CAACpB,CAAC,EAAE;IAChB,OAAO,IAAIH,gBAAgB,CAAC,IAAI,CAACE,CAAC,EAAEC,CAAC,CAAC;EACxC;;EAEA;AACF;AACA;EACE6D,cAAcA,CAACC,CAAC,EAAE;IAChB,QAAQC,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MAClC;MACA,KAAK,CAAC;QACJ,OAAO,IAAI,CAACzB,QAAQ;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ;QACA,MAAMvC,CAAC,GAAG,IAAI,CAACoB,cAAc,CAAC,IAAI3B,UAAU,CAACqE,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9D;QACA;QACA,IAAIjD,CAAC,GAAG,IAAI,CAACK,cAAc,CAACpB,CAAC,CAACS,QAAQ,CAACT,CAAC,CAACY,MAAM,CAAC,CAAC,CAAC,CAACN,GAAG,CACpDN,CAAC,CAACS,QAAQ,CAAC,IAAI,CAACwC,CAAC,CACnB,CAAC,CAAC3C,GAAG,CAAC,IAAI,CAACC,CAAC,CAAC,CAACJ,YAAY,CAAC,CAAC,CACzB8D,MAAM,CACL,IAAI,CAAClE,CAAC,CAACW,MAAM,CAAC,IAAIjB,UAAU,CAAC,GAAG,CAAC,CAAC,CAACa,GAAG,CAACb,UAAU,CAACwB,GAAG,CAAC,EAAE,IAAI,CAAClB,CAC/D,CAAC,CAAC;QACJ;QACA,IAAI,CAACgB,CAAC,CAACZ,YAAY,CAAC,CAAC,CAACE,GAAG,CAACV,GAAG,CAAC,CAACM,MAAM,CAAC,IAAIR,UAAU,CAACqE,CAAC,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAACxD,QAAQ,CAACb,GAAG,CAAC,CAAC,EAAE;UACvFoB,CAAC,GAAGA,CAAC,CAACX,MAAM,CAAC,CAAC;QAChB;QACA,OAAO,IAAIS,SAAS,CAAC,IAAI,EAAEb,CAAC,EAAEe,CAAC,CAAC;MAClC,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,MAAMmD,GAAG,GAAG,CAACJ,CAAC,CAACK,MAAM,GAAG,CAAC,IAAI,CAAC;QAC9B,MAAMC,IAAI,GAAGN,CAAC,CAACE,MAAM,CAAC,CAAC,EAAEE,GAAG,CAAC;QAC7B,MAAMG,IAAI,GAAGP,CAAC,CAACE,MAAM,CAACE,GAAG,GAAG,CAAC,EAAEA,GAAG,CAAC;QAEnC,OAAO,IAAIrD,SAAS,CAAC,IAAI,EAAE,IAAI,CAACO,cAAc,CAAC,IAAI3B,UAAU,CAAC2E,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAChD,cAAc,CAAC,IAAI3B,UAAU,CAAC4E,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;MAC1H;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG;EACf1D,SAAS;EACT+C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}