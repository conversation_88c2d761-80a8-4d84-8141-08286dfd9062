{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_CRANE extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"桥吊表主键\",\n      \"crane_cd\": \"桥吊代码\",\n      \"crane_nm\": \"桥吊名称\",\n      \"pd_crane_cd\": \"港口/码头桥吊代码\",\n      \"org_id\": \"所属组织主键\",\n      \"org_nm\": \"所属组织机构名称\",\n      \"org_level_no\": \"所属组织机构代码\",\n      \"owner_sco_no\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_CRANE'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_CRANE", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_CRANE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_CRANE extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_CRANE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"桥吊表主键\",\r\n      \"crane_cd\":\"桥吊代码\",\r\n      \"crane_nm\":\"桥吊名称\",\r\n      \"pd_crane_cd\":\"港口/码头桥吊代码\",\r\n      \"org_id\":\"所属组织主键\",\r\n      \"org_nm\":\"所属组织机构名称\",\r\n      \"org_level_no\":\"所属组织机构代码\",\r\n      \"owner_sco_no\":\"所属公司代码\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,YAAa,SAAQD,QAAQ;EAQxCE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,OAAO;MACZ,UAAU,EAAC,MAAM;MACjB,UAAU,EAAC,MAAM;MACjB,aAAa,EAAC,WAAW;MACzB,QAAQ,EAAC,QAAQ;MACjB,QAAQ,EAAC,UAAU;MACnB,cAAc,EAAC,UAAU;MACzB,cAAc,EAAC,QAAQ;MACvB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IAxBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,cAAc,CAAC,CAAC;IAC5B,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAqBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}