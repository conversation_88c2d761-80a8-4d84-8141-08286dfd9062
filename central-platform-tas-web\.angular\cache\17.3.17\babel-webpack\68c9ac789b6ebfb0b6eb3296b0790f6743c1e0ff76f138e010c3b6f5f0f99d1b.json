{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DamageAreaComponent } from './damagearea.component';\nimport { DamageAreaEditComponent } from '@business/tas/damagearea/damagearea-edit/damagearea-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: DamageAreaComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: DamageAreaEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class DamageAreaRoutingModule {\n  static {\n    this.ɵfac = function DamageAreaRoutingModule_Factory(t) {\n      return new (t || DamageAreaRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DamageAreaRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DamageAreaRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DamageAreaComponent", "DamageAreaEditComponent", "routes", "path", "component", "data", "cache", "DamageAreaRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\damagearea\\damagearea-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { DamageAreaComponent } from './damagearea.component';\r\nimport {DamageAreaEditComponent} from '@business/tas/damagearea/damagearea-edit/damagearea-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: DamageAreaComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: DamageAreaEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class DamageAreaRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAAQC,uBAAuB,QAAO,oEAAoE;;;AAC1G,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,mBAAmB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACvE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,uBAAuB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACjF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFxBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}