{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { SuperviseComponent } from './supervise.component';\nimport { SuperviseRoutingModule } from './supervise-routing.module';\nimport { SuperviseEditComponent } from '@business/tas/supervise/supervise-edit/supervise-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [SuperviseComponent, SuperviseEditComponent];\nexport class SuperviseModule {\n  static {\n    this.ɵfac = function SuperviseModule_Factory(t) {\n      return new (t || SuperviseModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SuperviseModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, SuperviseRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SuperviseModule, {\n    declarations: [SuperviseComponent, SuperviseEditComponent],\n    imports: [SharedModule, SuperviseRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "SuperviseComponent", "SuperviseRoutingModule", "SuperviseEditComponent", "COMPONENTS", "SuperviseModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { SuperviseComponent } from './supervise.component';\r\nimport { SuperviseRoutingModule } from './supervise-routing.module';\r\nimport {SuperviseEditComponent} from '@business/tas/supervise/supervise-edit/supervise-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  SuperviseComponent,\r\n  SuperviseEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, SuperviseRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class SuperviseModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}