{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mwens pase yon segond',\n    other: 'mwens pase {{count}} segond'\n  },\n  xSeconds: {\n    one: '1 segond',\n    other: '{{count}} segond'\n  },\n  halfAMinute: '30 segond',\n  lessThanXMinutes: {\n    one: 'mwens pase yon minit',\n    other: 'mwens pase {{count}} minit'\n  },\n  xMinutes: {\n    one: '1 minit',\n    other: '{{count}} minit'\n  },\n  aboutXHours: {\n    one: 'anviwon inè',\n    other: 'anviwon {{count}} è'\n  },\n  xHours: {\n    one: '1 lè',\n    other: '{{count}} lè'\n  },\n  xDays: {\n    one: '1 jou',\n    other: '{{count}} jou'\n  },\n  aboutXWeeks: {\n    one: 'anviwon 1 semèn',\n    other: 'anviwon {{count}} semèn'\n  },\n  xWeeks: {\n    one: '1 semèn',\n    other: '{{count}} semèn'\n  },\n  aboutXMonths: {\n    one: 'anviwon 1 mwa',\n    other: 'anviwon {{count}} mwa'\n  },\n  xMonths: {\n    one: '1 mwa',\n    other: '{{count}} mwa'\n  },\n  aboutXYears: {\n    one: 'anviwon 1 an',\n    other: 'anviwon {{count}} an'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} an'\n  },\n  overXYears: {\n    one: 'plis pase 1 an',\n    other: 'plis pase {{count}} an'\n  },\n  almostXYears: {\n    one: 'prèske 1 an',\n    other: 'prèske {{count}} an'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'nan ' + result;\n    } else {\n      return 'sa fè ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ht/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mwens pase yon segond',\n    other: 'mwens pase {{count}} segond'\n  },\n  xSeconds: {\n    one: '1 segond',\n    other: '{{count}} segond'\n  },\n  halfAMinute: '30 segond',\n  lessThanXMinutes: {\n    one: 'mwens pase yon minit',\n    other: 'mwens pase {{count}} minit'\n  },\n  xMinutes: {\n    one: '1 minit',\n    other: '{{count}} minit'\n  },\n  aboutXHours: {\n    one: 'anviwon inè',\n    other: 'anviwon {{count}} è'\n  },\n  xHours: {\n    one: '1 lè',\n    other: '{{count}} lè'\n  },\n  xDays: {\n    one: '1 jou',\n    other: '{{count}} jou'\n  },\n  aboutXWeeks: {\n    one: 'anviwon 1 semèn',\n    other: 'anviwon {{count}} semèn'\n  },\n  xWeeks: {\n    one: '1 semèn',\n    other: '{{count}} semèn'\n  },\n  aboutXMonths: {\n    one: 'anviwon 1 mwa',\n    other: 'anviwon {{count}} mwa'\n  },\n  xMonths: {\n    one: '1 mwa',\n    other: '{{count}} mwa'\n  },\n  aboutXYears: {\n    one: 'anviwon 1 an',\n    other: 'anviwon {{count}} an'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} an'\n  },\n  overXYears: {\n    one: 'plis pase 1 an',\n    other: 'plis pase {{count}} an'\n  },\n  almostXYears: {\n    one: 'prèske 1 an',\n    other: 'prèske {{count}} an'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'nan ' + result;\n    } else {\n      return 'sa fè ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,WAAW;EACxBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGL,MAAM;IACxB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}