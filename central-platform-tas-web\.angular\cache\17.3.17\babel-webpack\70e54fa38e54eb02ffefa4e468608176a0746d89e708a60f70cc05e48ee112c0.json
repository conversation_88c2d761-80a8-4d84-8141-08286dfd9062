{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { CwfBaseCrud, DialogResultEnum, ModalTypeEnum } from 'cwf-ng-library';\nimport { TAS_T_VESSEL_KAY } from '@store/TAS/TAS_T_VESSEL_KAY';\nimport { TAS_T_VESSEL_KAY_DEDUCT } from '@store/TAS/TAS_T_VESSEL_KAY_DEDUCT';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@service/cwfRestful.service\";\nimport * as i4 from \"ng-zorro-antd/modal\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-zorro-antd/grid\";\nimport * as i8 from \"ng-zorro-antd/form\";\nimport * as i9 from \"ng-zorro-antd/button\";\nimport * as i10 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i11 from \"ng-zorro-antd/core/wave\";\nimport * as i12 from \"ng-zorro-antd/input\";\nimport * as i13 from \"ng-zorro-antd/input-number\";\nimport * as i14 from \"ng-zorro-antd/alert\";\nimport * as i15 from \"ng-zorro-antd/select\";\nimport * as i16 from \"ng-zorro-antd/card\";\nimport * as i17 from \"ng-zorro-antd/icon\";\nimport * as i18 from \"ng-zorro-antd/divider\";\nimport * as i19 from \"ng-zorro-antd/empty\";\nimport * as i20 from \"ng-zorro-antd/tag\";\nconst _c0 = () => ({\n  \"padding\": \"16px 20px\"\n});\nconst _c1 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c2 = () => ({\n  \"padding\": \"12px 20px\"\n});\nconst _c3 = () => ({\n  \"padding\": \"0\"\n});\nconst _c4 = () => ({\n  \"height\": \"450px\",\n  \"overflow\": \"hidden\"\n});\nconst _c5 = () => [24, 0];\nconst _c6 = () => [16, 8];\nconst _c7 = () => ({\n  minRows: 2,\n  maxRows: 4\n});\nfunction VesselKayComponent_nz_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 21);\n  }\n  if (rf & 2) {\n    const kay_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", kay_r1.kayNo)(\"nzLabel\", kay_r1.kayNo);\n  }\n}\nfunction VesselKayComponent_div_35_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const colLabel_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatNumber(colLabel_r2), \" \");\n  }\n}\nfunction VesselKayComponent_div_35_div_8_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(position_r5.position);\n  }\n}\nfunction VesselKayComponent_div_35_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function VesselKayComponent_div_35_div_8_Template_div_click_0_listener($event) {\n      const position_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPositionClick(position_r5, $event));\n    })(\"mouseenter\", function VesselKayComponent_div_35_div_8_Template_div_mouseenter_0_listener($event) {\n      const position_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPositionMouseEnter(position_r5, $event));\n    });\n    i0.ɵɵtemplate(1, VesselKayComponent_div_35_div_8_span_1_Template, 2, 1, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r5 = ctx.$implicit;\n    i0.ɵɵclassMap(\"position-cell\" + (position_r5.selected ? \" selected\" : \"\") + (position_r5.isDeduct ? \" invalid\" : \"\") + (position_r5.dragHighlight ? \" drag-highlight\" : \"\") + (position_r5.deleted ? \" deleted\" : \"\") + (position_r5.deleted && position_r5.wasDeleted ? \" truly-deleted\" : \"\"));\n    i0.ɵɵstyleProp(\"grid-row\", position_r5.gridRow)(\"grid-column\", position_r5.gridColumn);\n    i0.ɵɵattribute(\"data-position-id\", position_r5.position);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !position_r5.deleted);\n  }\n}\nfunction VesselKayComponent_div_35_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowLabel_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatNumber(rowLabel_r6), \" \");\n  }\n}\nfunction VesselKayComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵtemplate(4, VesselKayComponent_div_35_div_4_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"div\", 29);\n    i0.ɵɵtemplate(8, VesselKayComponent_div_35_div_8_Template, 2, 8, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 31);\n    i0.ɵɵtemplate(10, VesselKayComponent_div_35_div_10_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getGridStyle());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getGridStyle());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getColumnLabels());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getGridStyle());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.kayPositions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getRowLabels());\n  }\n}\nfunction VesselKayComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"nz-empty\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselKayComponent_ng_container_38_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r7 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"grid-row\", position_r7.gridRow)(\"grid-column\", position_r7.gridColumn);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r7.position, \" \");\n  }\n}\nfunction VesselKayComponent_ng_container_38_div_57_nz_tag_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-tag\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r8.position, \" \");\n  }\n}\nfunction VesselKayComponent_ng_container_38_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵelement(2, \"nz-statistic\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵtemplate(4, VesselKayComponent_ng_container_38_div_57_div_4_Template, 2, 5, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 72);\n    i0.ɵɵelement(6, \"nz-divider\", 73);\n    i0.ɵɵelementStart(7, \"div\", 74);\n    i0.ɵɵtemplate(8, VesselKayComponent_ng_container_38_div_57_nz_tag_8_Template, 2, 1, \"nz-tag\", 75);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzValue\", ctx_r2.previewKayData.totalPositions);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"grid-template-columns\", \"repeat(\" + ctx_r2.previewKayData.columnNum + \", 1fr)\")(\"grid-template-rows\", \"repeat(\" + ctx_r2.previewKayData.rowNum + \", 1fr)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.previewKayData.positions)(\"ngForTrackBy\", ctx_r2.trackByPositionId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.previewKayData.positions);\n  }\n}\nfunction VesselKayComponent_ng_container_38_nz_empty_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty\", 78);\n  }\n}\nfunction VesselKayComponent_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"nz-card\", 41)(4, \"form\", 42)(5, \"div\", 39)(6, \"div\", 43)(7, \"nz-form-item\")(8, \"nz-form-label\", 44);\n    i0.ɵɵtext(9, \"\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nz-form-control\", 45);\n    i0.ɵɵelement(11, \"input\", 46);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 47)(13, \"nz-form-item\")(14, \"nz-form-label\", 44);\n    i0.ɵɵtext(15, \"\\u884C\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nz-form-control\", 48);\n    i0.ɵɵelement(17, \"nz-input-number\", 49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 47)(19, \"nz-form-item\")(20, \"nz-form-label\", 44);\n    i0.ɵɵtext(21, \"\\u5217\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"nz-form-control\", 50);\n    i0.ɵɵelement(23, \"nz-input-number\", 51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 47)(25, \"nz-form-item\")(26, \"nz-form-label\", 44);\n    i0.ɵɵtext(27, \"\\u884C\\u6B65\\u957F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"nz-form-control\", 52);\n    i0.ɵɵelement(29, \"nz-input-number\", 53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 47)(31, \"nz-form-item\")(32, \"nz-form-label\", 44);\n    i0.ɵɵtext(33, \"\\u5217\\u6B65\\u957F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"nz-form-control\", 54);\n    i0.ɵɵelement(35, \"nz-input-number\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 47)(37, \"nz-form-item\")(38, \"nz-form-label\", 44);\n    i0.ɵɵtext(39, \"\\u884C\\u8D77\\u59CB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nz-form-control\", 56);\n    i0.ɵɵelement(41, \"nz-input-number\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 47)(43, \"nz-form-item\")(44, \"nz-form-label\", 44);\n    i0.ɵɵtext(45, \"\\u5217\\u8D77\\u59CB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"nz-form-control\", 58);\n    i0.ɵɵelement(47, \"nz-input-number\", 59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 43)(49, \"nz-form-item\")(50, \"nz-form-label\", 60);\n    i0.ɵɵtext(51, \"\\u5907\\u6CE8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"nz-form-control\");\n    i0.ɵɵelement(53, \"textarea\", 61);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(54, \"div\", 62)(55, \"nz-card\", 63)(56, \"div\", 64);\n    i0.ɵɵtemplate(57, VesselKayComponent_ng_container_38_div_57_Template, 9, 8, \"div\", 65)(58, VesselKayComponent_ng_container_38_nz_empty_58_Template, 1, 0, \"nz-empty\", 66);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(14, _c5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzSize\", \"small\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.newKayForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(15, _c6));\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"nzMin\", 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzAutosize\", i0.ɵɵpureFunction0(16, _c7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzSize\", \"small\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.previewKayData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.previewKayData || ctx_r2.previewKayData.totalPositions === 0);\n  }\n}\nfunction VesselKayComponent_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 42)(2, \"nz-form-item\")(3, \"nz-form-label\");\n    i0.ɵɵtext(4, \"\\u539F\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nz-form-control\");\n    i0.ɵɵelement(6, \"input\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nz-form-item\")(8, \"nz-form-label\", 80);\n    i0.ɵɵtext(9, \"\\u65B0\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nz-form-control\", 81);\n    i0.ɵɵelement(11, \"input\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"nz-alert\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.copyKayForm);\n  }\n}\nfunction VesselKayComponent_ng_container_42_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function VesselKayComponent_ng_container_42_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectAllRestorePositions());\n    });\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" \\u5168\\u9009 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselKayComponent_ng_container_42_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function VesselKayComponent_ng_container_42_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deselectAllRestorePositions());\n    });\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵtext(2, \" \\u53CD\\u9009 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselKayComponent_ng_container_42_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function VesselKayComponent_ng_container_42_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearRestoreSelection());\n    });\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselKayComponent_ng_container_42_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 97);\n    i0.ɵɵtext(1, \" \\u5DF2\\u9009\\u62E9: \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedDeletedPositions.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" / \", ctx_r2.deletedPositionOptions.length, \" \");\n  }\n}\nfunction VesselKayComponent_ng_container_42_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function VesselKayComponent_ng_container_42_div_9_div_1_Template_div_click_0_listener() {\n      const position_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.togglePositionSelection(position_r13.value));\n    });\n    i0.ɵɵelementStart(1, \"div\", 101)(2, \"div\", 102);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 103);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"i\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const position_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.isPositionSelected(position_r13.value));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(position_r13.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getPositionArea(position_r13.value));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", ctx_r2.isPositionSelected(position_r13.value) ? \"check-circle\" : \"plus-circle\")(\"nzTheme\", ctx_r2.isPositionSelected(position_r13.value) ? \"fill\" : \"outline\");\n  }\n}\nfunction VesselKayComponent_ng_container_42_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, VesselKayComponent_ng_container_42_div_9_div_1_Template, 7, 6, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.deletedPositionOptions)(\"ngForTrackBy\", ctx_r2.trackByPositionId);\n  }\n}\nfunction VesselKayComponent_ng_container_42_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵelement(1, \"nz-empty\", 106);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselKayComponent_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 84)(2, \"div\", 85)(3, \"nz-space\");\n    i0.ɵɵtemplate(4, VesselKayComponent_ng_container_42_button_4_Template, 3, 0, \"button\", 86)(5, VesselKayComponent_ng_container_42_button_5_Template, 3, 0, \"button\", 86)(6, VesselKayComponent_ng_container_42_button_6_Template, 3, 0, \"button\", 86)(7, VesselKayComponent_ng_container_42_span_7_Template, 5, 2, \"span\", 87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 88);\n    i0.ɵɵtemplate(9, VesselKayComponent_ng_container_42_div_9_Template, 2, 2, \"div\", 89)(10, VesselKayComponent_ng_container_42_div_10_Template, 2, 0, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 91);\n    i0.ɵɵelement(12, \"nz-alert\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.deletedPositionOptions.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.deletedPositionOptions.length === 0);\n  }\n}\nexport class VesselKayComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, cwfRestfulService, modal) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.cwfRestfulService = cwfRestfulService;\n    this.modal = modal;\n    this.mainStore = new TAS_T_VESSEL_KAY();\n    this.deductStore = new TAS_T_VESSEL_KAY_DEDUCT();\n    this.vesselId = '';\n    this.vesselNm = '';\n    this.selectedKayNo = '';\n    this.selectedKayData = null;\n    this.kayList = [];\n    this.kayPositions = [];\n    this.selectedPositions = [];\n    this.deductPositions = [];\n    // 模态框相关\n    this.isNewKayModalVisible = false;\n    this.isCopyKayModalVisible = false;\n    this.isRestorePositionModalVisible = false;\n    // 恢复贝位功能相关属性\n    this.selectedDeletedPositions = [];\n    this.deletedPositionOptions = [];\n    this.deletedPositions = new Set();\n    // 恢复贝位弹出框优化相关属性（已简化，移除筛选功能）\n    // 预览功能相关属性\n    this.previewKayData = null;\n    // 防重复操作标志\n    this.isDeleting = false;\n    this.isRestoring = false;\n    // 数据加载状态标志 - 修复首次操作API调用失败问题\n    this.isDataLoading = false;\n    this.isDataLoaded = false;\n    // 多选拖拽相关属性\n    this.isDragging = false;\n    this.dragStartTime = 0;\n    this.dragStartX = 0;\n    this.dragStartY = 0;\n    this.hasMoved = false;\n    this.dragThreshold = 150; // 拖拽时间阈值（毫秒）\n    this.moveThreshold = 5; // 鼠标移动阈值（像素）\n    // 详细的数据初始化步骤跟踪 - 解决时序问题和竞态条件\n    this.dataInitializationSteps = {\n      kayListLoaded: false,\n      // 贝位列表加载完成\n      kayDataSelected: false,\n      // 贝位数据选择完成\n      positionsGenerated: false,\n      // 贝位图生成完成\n      statusSyncCompleted: false // 状态同步完成\n    };\n  }\n  /**\n   * 页面加载后处理\n   */\n  onShow() {\n    this.vesselId = this.openParam['vesselId'] || '';\n    this.vesselNm = this.openParam['vesselNm'] || '';\n    console.log('客混船贝图 - 接收到的参数:', {\n      vesselId: this.vesselId,\n      vesselNm: this.vesselNm,\n      allParams: this.openParam\n    });\n    if (!this.vesselId) {\n      this.showState(ModalTypeEnum.error, '船舶ID参数缺失！');\n      this.openMainPage();\n      return;\n    }\n    this.initForms();\n    this.loadKayList();\n  }\n  /**\n   * 初始化表单\n   */\n  initForms() {\n    this.newKayForm = new FormGroup({\n      kayNo: new FormControl('', [Validators.required, Validators.maxLength(12)]),\n      rowNum: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      rowSep: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      rowFrom: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      columnNum: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      columnSep: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      columnFrom: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\n      remark: new FormControl('', [Validators.maxLength(255)])\n    });\n    this.copyKayForm = new FormGroup({\n      originalKayNo: new FormControl({\n        value: '',\n        disabled: true\n      }),\n      newKayNo: new FormControl('', [Validators.required, Validators.maxLength(12)])\n    });\n    // 监听新建贝表单变化，实时生成预览\n    this.newKayForm.valueChanges.subscribe(() => {\n      this.generatePreviewKayData();\n    });\n  }\n  /**\n   * 加载客混船贝位列表 - 完全修复版本，解决时序问题和竞态条件\n   */\n  loadKayList() {\n    // 重置所有状态\n    this.isDataLoading = true;\n    this.resetDataInitializationSteps();\n    // 清除之前的数据，确保不会显示缓存的数据\n    this.kayList = [];\n    this.selectedKayNo = '';\n    this.selectedKayData = null;\n    this.kayPositions = [];\n    this.selectedPositions = [];\n    this.deductPositions = [];\n    this.deletedPositions.clear();\n    // 按照正确的参数格式构建请求参数\n    const requestData = {\n      data: {\n        vesselId: this.vesselId\n      }\n    };\n    console.log('客混船贝图 - 请求参数:', requestData);\n    this.cwfRestfulService.post('/vessel-kay/list', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      console.log('客混船贝图 - API响应:', rps);\n      if (rps.ok === true) {\n        this.kayList = rps.data || [];\n        console.log('客混船贝图 - 加载贝位列表成功:', {\n          vesselId: this.vesselId,\n          kayCount: this.kayList.length,\n          kayList: this.kayList\n        });\n        // 标记贝位列表加载完成\n        this.dataInitializationSteps.kayListLoaded = true;\n        if (this.kayList.length > 0) {\n          this.selectedKayNo = this.kayList[0].kayNo;\n          this.onKaySelect();\n          // 注意：不再使用固定延迟，而是依赖各个步骤的完成状态\n        } else {\n          console.log('客混船贝图 - 该船舶没有客混船贝位数据');\n          this.selectedKayNo = '';\n          this.selectedKayData = null;\n          // 没有数据时，直接完成初始化\n          this.dataInitializationSteps.kayDataSelected = true;\n          this.dataInitializationSteps.positionsGenerated = true;\n          this.dataInitializationSteps.statusSyncCompleted = true;\n          this.completeDataInitialization();\n        }\n      } else {\n        console.log('客混船贝图 - API错误:', rps.msg);\n        this.showState(ModalTypeEnum.error, rps.msg);\n        this.isDataLoading = false;\n      }\n    }).catch(error => {\n      console.error('客混船贝图 - API调用失败:', error);\n      this.showState(ModalTypeEnum.error, '加载客混船贝位列表失败');\n      this.isDataLoading = false;\n    });\n  }\n  /**\n   * 加载特殊位置数据\n   */\n  loadDeductPositions() {\n    if (!this.selectedKayData) return;\n    const requestData = {\n      kayId: this.selectedKayData.id,\n      vesselId: this.vesselId\n    };\n    this.cwfRestfulService.post('/vessel-kay-deduct/list', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok === true) {\n        this.deductPositions = rps.data || [];\n        this.updatePositionStyles();\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  /**\n   * 贝号选择事件 - 修复版本，集成状态管理\n   */\n  onKaySelect() {\n    this.selectedKayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\n    if (this.selectedKayData) {\n      console.log('客混船贝图 - 贝位数据选择完成:', {\n        kayNo: this.selectedKayData.kayNo,\n        kayId: this.selectedKayData.id\n      });\n      // 标记贝位数据选择完成\n      this.dataInitializationSteps.kayDataSelected = true;\n      this.generateKayPositions();\n      this.loadDeductPositions();\n      // 检查是否可以完成初始化\n      this.completeDataInitialization();\n    }\n  }\n  /**\n   * 生成客混船贝位图 - 从右上角开始，向左向下排列\n   * 右上角第一个贝位编号为0202，向左排列（02列方向），向下排列（行方向）\n   */\n  generateKayPositions() {\n    if (!this.selectedKayData) return;\n    const positions = [];\n    const data = this.selectedKayData;\n    console.log('客混船贝图 - 生成贝位数据调试信息:', {\n      selectedKayNo: this.selectedKayNo,\n      selectedKayData: data,\n      kayDataId: data.id,\n      kayDataRowtier: data.rowtier,\n      rowNum: data.rowNum,\n      columnNum: data.columnNum\n    });\n    // 生成行列贝位 - 从右上角开始布局\n    for (let row = 0; row < data.rowNum; row++) {\n      for (let col = 0; col < data.columnNum; col++) {\n        const actualRow = data.rowFrom + row * data.rowSep;\n        // 从右上角开始，列数从右到左递减\n        // 确保右上角第一个贝位编号为0202\n        const actualCol = data.columnFrom + (data.columnNum - 1 - col) * data.columnSep;\n        const position = {\n          row: actualRow,\n          column: actualCol,\n          position: `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`,\n          selected: false,\n          isDeduct: false,\n          rowIndex: row,\n          // 行索引，用于布局\n          colIndex: col,\n          // 列索引，用于布局\n          displayRow: actualRow,\n          // 显示用的行号\n          displayCol: actualCol,\n          // 显示用的列号\n          gridRow: row + 1,\n          // CSS Grid行位置（1-based）\n          gridColumn: col + 1 // CSS Grid列位置（1-based）\n        };\n        positions.push(position);\n      }\n    }\n    // 重新设计贝位状态管理逻辑 - 修复删除状态显示与恢复功能不一致问题\n    // 关键修复：正确初始化和管理deletedPositions集合\n    if (data.rowtier) {\n      const validPositions = data.rowtier.split(',').filter(pos => pos.trim() !== '');\n      console.log('客混船贝图 - 开始贝位状态分析:', {\n        selectedKayNo: this.selectedKayNo,\n        kayDataId: data.id,\n        rowtierOriginal: data.rowtier,\n        validPositionsFromRowtier: validPositions,\n        allGeneratedPositionsCount: positions.length,\n        allGeneratedPositions: positions.map(p => p.position),\n        currentDeletedPositionsSetSize: this.deletedPositions.size,\n        currentDeletedPositionsArray: Array.from(this.deletedPositions)\n      });\n      // 关键修复：识别真正被删除的贝位\n      // 如果deletedPositions集合为空（首次加载），需要根据rowtier和生成的贝位来推断删除状态\n      if (this.deletedPositions.size === 0) {\n        // 首次加载：将不在rowtier中但在生成贝位中的贝位标记为已删除\n        positions.forEach(position => {\n          if (!validPositions.includes(position.position)) {\n            // 这些贝位在配置范围内但不在rowtier中，应该被认为是已删除的\n            this.deletedPositions.add(position.position);\n          }\n        });\n        console.log('客混船贝图 - 首次加载，初始化deletedPositions集合:', {\n          initializedDeletedPositionsCount: this.deletedPositions.size,\n          initializedDeletedPositions: Array.from(this.deletedPositions)\n        });\n      }\n      // 标记每个贝位的状态\n      positions.forEach(position => {\n        if (validPositions.includes(position.position)) {\n          // 当前存在的贝位\n          position.deleted = false;\n          position.exists = true;\n          position.wasDeleted = false;\n          // 如果这个贝位之前被标记为已删除，现在恢复了，从已删除集合中移除\n          if (this.deletedPositions.has(position.position)) {\n            this.deletedPositions.delete(position.position);\n            console.log('客混船贝图 - 贝位已恢复，从deletedPositions中移除:', position.position);\n          }\n        } else if (this.deletedPositions.has(position.position)) {\n          // 真正被删除的贝位（在已删除集合中）\n          position.deleted = true;\n          position.exists = false;\n          position.wasDeleted = true; // 标记为真正被删除\n        } else {\n          // 从未存在的贝位（不在rowtier中，也不在deletedPositions中）\n          position.deleted = true;\n          position.exists = false;\n          position.wasDeleted = false; // 标记为从未存在\n        }\n      });\n      console.log('客混船贝图 - 贝位状态标记结果:', {\n        originalPositionsCount: positions.length,\n        validPositionsFromRowtier: validPositions,\n        existingPositionsCount: positions.filter(p => !p.deleted && p.exists).length,\n        trulyDeletedPositionsCount: positions.filter(p => p.deleted && p.wasDeleted).length,\n        neverExistedPositionsCount: positions.filter(p => p.deleted && !p.wasDeleted).length,\n        deletedPositionsSetSize: this.deletedPositions.size,\n        deletedPositionsArray: Array.from(this.deletedPositions),\n        trulyDeletedPositionsList: positions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\n        neverExistedPositionsList: positions.filter(p => p.deleted && !p.wasDeleted).map(p => p.position)\n      });\n    } else {\n      // 如果没有rowtier数据，需要特殊处理\n      console.log('客混船贝图 - 警告：没有rowtier数据，将所有生成的贝位标记为已删除');\n      // 如果deletedPositions集合为空，将所有生成的贝位添加到已删除集合\n      if (this.deletedPositions.size === 0) {\n        positions.forEach(position => {\n          this.deletedPositions.add(position.position);\n        });\n        console.log('客混船贝图 - 无rowtier数据时初始化deletedPositions集合:', {\n          addedPositionsCount: positions.length,\n          addedPositions: positions.map(p => p.position)\n        });\n      }\n      positions.forEach(position => {\n        if (this.deletedPositions.has(position.position)) {\n          // 真正被删除的贝位\n          position.deleted = true;\n          position.exists = false;\n          position.wasDeleted = true;\n        } else {\n          // 从未存在的贝位\n          position.deleted = true;\n          position.exists = false;\n          position.wasDeleted = false;\n        }\n      });\n      console.log('客混船贝图 - 没有rowtier数据，根据已删除集合标记状态:', {\n        allPositionsCount: positions.length,\n        deletedPositionsSetSize: this.deletedPositions.size,\n        deletedPositionsArray: Array.from(this.deletedPositions)\n      });\n    }\n    // 保留所有贝位用于显示（包括已删除的）\n    this.kayPositions = positions;\n    this.selectedPositions = [];\n    console.log('客混船贝图 - 最终贝位数据和状态同步验证:', {\n      totalPositionsCount: this.kayPositions.length,\n      validPositionsCount: this.kayPositions.filter(p => !p.deleted).length,\n      deletedPositionsCount: this.kayPositions.filter(p => p.deleted).length,\n      trulyDeletedPositionsCount: this.kayPositions.filter(p => p.deleted && p.wasDeleted).length,\n      neverExistedPositionsCount: this.kayPositions.filter(p => p.deleted && !p.wasDeleted).length,\n      deletedPositionsSetSize: this.deletedPositions.size,\n      deletedPositionsArray: Array.from(this.deletedPositions),\n      // 关键验证：UI显示的删除状态贝位是否与deletedPositions集合同步\n      uiDeletedPositions: this.kayPositions.filter(p => p.deleted).map(p => p.position),\n      uiTrulyDeletedPositions: this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\n      statusSyncCheck: {\n        deletedPositionsInSet: Array.from(this.deletedPositions),\n        trulyDeletedInUI: this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\n        isSync: JSON.stringify(Array.from(this.deletedPositions).sort()) === JSON.stringify(this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position).sort())\n      }\n    });\n    // 标记贝位图生成完成\n    this.dataInitializationSteps.positionsGenerated = true;\n    console.log('客混船贝图 - 贝位图生成完成');\n    // 关键修复：在贝位生成完成后进行状态同步验证\n    // 延迟执行，确保所有状态标记完成\n    setTimeout(() => {\n      this.validateAndFixStatusSync();\n    }, 100);\n  }\n  /**\n   * 更新位置样式（标记特殊位置）\n   */\n  updatePositionStyles() {\n    console.log('客混船贝图 - 开始更新位置样式:', {\n      kayPositionsCount: this.kayPositions.length,\n      deductPositionsCount: this.deductPositions.length,\n      deductPositions: this.deductPositions\n    });\n    let invalidCount = 0;\n    this.kayPositions.forEach(pos => {\n      const wasInvalid = pos.isDeduct;\n      pos.isDeduct = this.deductPositions.some(deduct => deduct.rowtier && deduct.rowtier.includes(pos.position));\n      if (pos.isDeduct) {\n        invalidCount++;\n        if (!wasInvalid) {\n          console.log('客混船贝图 - 贝位标记为无效:', pos.position);\n        }\n      } else if (wasInvalid) {\n        console.log('客混船贝图 - 贝位取消无效标记:', pos.position);\n      }\n    });\n    console.log('客混船贝图 - 位置样式更新完成:', {\n      totalInvalidPositions: invalidCount\n    });\n  }\n  /**\n   * 贝位点击事件 - 参考vessel-bay的多选模式\n   */\n  onPositionClick(position, event) {\n    console.log('客混船贝图 - 贝位点击事件触发:', {\n      positionId: position.position,\n      currentSelected: position.selected,\n      isDragging: this.isDragging,\n      dragStartTime: this.dragStartTime,\n      hasEvent: !!event\n    });\n    // 无效位置、已删除的贝位或不存在的贝位不能被选中\n    if (position.isDeduct || position.deleted || !position.exists) {\n      console.log('客混船贝图 - 无效位置、已删除或不存在的贝位，忽略点击:', {\n        positionId: position.position,\n        isDeduct: position.isDeduct,\n        deleted: position.deleted,\n        exists: position.exists\n      });\n      return;\n    }\n    // 阻止事件冒泡和默认行为，避免触发容器的mousedown事件\n    if (event) {\n      event.stopPropagation();\n      event.preventDefault();\n    }\n    // 检查是否是真正的拖拽操作（鼠标移动了）\n    const timeSinceMouseDown = Date.now() - this.dragStartTime;\n    // 如果刚刚结束拖拽操作且鼠标确实移动了，忽略点击事件\n    if (this.dragStartTime > 0 && this.hasMoved && timeSinceMouseDown < 300) {\n      console.log('客混船贝图 - 刚结束拖拽操作，忽略点击事件，时间差:', timeSinceMouseDown);\n      return;\n    }\n    // 如果正在进行真正的拖拽（鼠标移动了），忽略点击事件\n    if (this.isDragging && this.hasMoved) {\n      console.log('客混船贝图 - 正在进行真正的拖拽，忽略点击事件');\n      return;\n    }\n    // 重置拖拽状态，确保单击操作能正常处理\n    if (this.dragStartTime > 0 && !this.hasMoved) {\n      console.log('客混船贝图 - 检测到单击操作，重置拖拽状态以正常处理点击');\n      this.isDragging = false;\n      this.dragStartTime = 0;\n    }\n    // 记录原始状态\n    const wasSelected = position.selected;\n    // 默认多选模式：普通点击也支持多选\n    position.selected = !position.selected;\n    if (position.selected) {\n      // 添加到选中列表\n      if (!this.selectedPositions.find(p => p.position === position.position)) {\n        this.selectedPositions.push(position);\n      }\n    } else {\n      // 从选中列表移除\n      this.selectedPositions = this.selectedPositions.filter(p => p.position !== position.position);\n    }\n    console.log('客混船贝图 - 贝位选择状态更新:', {\n      positionId: position.position,\n      wasSelected: wasSelected,\n      nowSelected: position.selected,\n      totalSelected: this.selectedPositions.length\n    });\n  }\n  /**\n   * 鼠标按下事件（在贝位图容器上）\n   */\n  onMouseDown(event) {\n    // 只处理左键\n    if (event.button !== 0) return;\n    const target = event.target;\n    // 修复点击区域敏感性问题：使用closest方法向上查找贝位容器\n    // 这样无论点击贝位的哪个区域（包括数字文本），都能正确识别\n    const positionCell = target.closest('.position-cell');\n    const isPositionCell = !!positionCell;\n    const positionId = positionCell?.getAttribute('data-position-id');\n    console.log('客混船贝图 - 鼠标按下事件（修复点击区域敏感性）:', {\n      originalTarget: target.className || target.tagName,\n      originalTargetText: target.textContent?.trim(),\n      positionCell: positionCell?.className,\n      isPositionCell: isPositionCell,\n      positionId: positionId,\n      currentTime: Date.now(),\n      clientX: event.clientX,\n      clientY: event.clientY\n    });\n    // 记录拖拽开始时间和位置\n    this.dragStartTime = Date.now();\n    this.dragStartX = event.clientX;\n    this.dragStartY = event.clientY;\n    this.hasMoved = false;\n    // 检查是否点击在贝位上\n    if (isPositionCell && positionId) {\n      // 点击在贝位上，立即选中该贝位并启动拖拽准备模式\n      const startPosition = this.kayPositions.find(p => p.position === positionId);\n      if (startPosition) {\n        // 检查是否可以选中（不是无效位置、已删除或不存在的贝位）\n        if (!startPosition.isDeduct && !startPosition.deleted && startPosition.exists) {\n          console.log('客混船贝图 - 选中拖拽起始贝位（修复后）:', {\n            positionId: positionId,\n            wasSelected: startPosition.selected,\n            currentSelectedCount: this.selectedPositions.length\n          });\n          // 不在mousedown中立即选中贝位，避免与click事件冲突\n          // 只记录起始贝位，等待鼠标移动时再处理拖拽选择\n          console.log('客混船贝图 - 记录起始贝位，等待确定是单击还是拖拽操作:', {\n            positionId: positionId,\n            currentSelected: startPosition.selected\n          });\n        } else {\n          console.log('客混船贝图 - 起始贝位不可选中:', {\n            positionId: positionId,\n            isDeduct: startPosition.isDeduct,\n            deleted: startPosition.deleted,\n            exists: startPosition.exists\n          });\n        }\n      } else {\n        console.log('客混船贝图 - 未找到对应的贝位数据:', positionId);\n      }\n      console.log('客混船贝图 - 点击在贝位上，拖拽选择模式已启动:', {\n        positionId: positionId,\n        isDragging: this.isDragging,\n        selectedCount: this.selectedPositions.length\n      });\n      // 阻止默认行为，避免文本选择等\n      event.preventDefault();\n    } else {\n      // 点击在空白区域\n      console.log('客混船贝图 - 点击在空白区域');\n    }\n  }\n  /**\n   * 鼠标移动事件\n   */\n  onMouseMove(event) {\n    // 如果没有按下鼠标，不处理\n    if (this.dragStartTime === 0) return;\n    // 计算移动距离\n    const deltaX = Math.abs(event.clientX - this.dragStartX);\n    const deltaY = Math.abs(event.clientY - this.dragStartY);\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    // 如果移动距离超过阈值，标记为已移动并启动拖拽模式\n    if (distance > this.moveThreshold) {\n      this.hasMoved = true;\n      // 如果还没有启动拖拽模式，现在启动并选中起始贝位\n      if (!this.isDragging) {\n        this.isDragging = true;\n        // 在拖拽开始时选中起始贝位\n        this.selectStartPositionForDrag();\n        console.log('客混船贝图 - 鼠标移动超过阈值，启动拖拽选择模式:', {\n          distance: distance,\n          threshold: this.moveThreshold,\n          isDragging: this.isDragging\n        });\n      }\n    }\n    // 如果已经开始拖拽，处理拖拽逻辑\n    if (this.isDragging) {\n      console.log('客混船贝图 - 拖拽中...', {\n        currentX: event.clientX,\n        currentY: event.clientY,\n        distance: distance\n      });\n    }\n  }\n  /**\n   * 鼠标释放事件\n   */\n  onMouseUp(event) {\n    console.log('客混船贝图 - 鼠标释放事件:', {\n      isDragging: this.isDragging,\n      hasMoved: this.hasMoved,\n      dragStartTime: this.dragStartTime,\n      totalSelected: this.selectedPositions.length\n    });\n    // 如果正在拖拽，完成拖拽选择操作\n    if (this.isDragging) {\n      console.log('客混船贝图 - 拖拽选择完成，选中贝位数量:', this.selectedPositions.length);\n    }\n    // 清理拖拽状态\n    this.isDragging = false;\n    this.hasMoved = false;\n    this.dragStartTime = 0;\n  }\n  /**\n   * 鼠标离开事件\n   */\n  onMouseLeave(event) {\n    if (this.isDragging) {\n      this.onMouseUp(event);\n    }\n  }\n  /**\n   * 在拖拽开始时选中起始贝位\n   */\n  selectStartPositionForDrag() {\n    // 找到鼠标按下时的目标元素\n    const target = document.elementFromPoint(this.dragStartX, this.dragStartY);\n    if (!target) return;\n    // 使用closest方法向上查找贝位容器\n    const positionCell = target.closest('.position-cell');\n    if (!positionCell) return;\n    const positionId = positionCell.getAttribute('data-position-id');\n    if (!positionId) return;\n    // 找到对应的贝位数据\n    const startPosition = this.kayPositions.find(p => p.position === positionId);\n    if (!startPosition) return;\n    // 检查是否可以选中（不是无效位置、已删除或不存在的贝位）\n    if (startPosition.isDeduct || startPosition.deleted || !startPosition.exists) {\n      console.log('客混船贝图 - 拖拽起始贝位不可选中:', {\n        positionId: positionId,\n        isDeduct: startPosition.isDeduct,\n        deleted: startPosition.deleted,\n        exists: startPosition.exists\n      });\n      return;\n    }\n    // 选中起始贝位\n    if (!startPosition.selected) {\n      startPosition.selected = true;\n      if (!this.selectedPositions.find(p => p.position === startPosition.position)) {\n        this.selectedPositions.push(startPosition);\n        console.log('客混船贝图 - 拖拽开始时选中起始贝位:', {\n          positionId: positionId,\n          newSelectedCount: this.selectedPositions.length\n        });\n      }\n    }\n  }\n  /**\n   * 贝位鼠标进入事件 - 拖拽连续选择\n   */\n  onPositionMouseEnter(position, event) {\n    console.log('客混船贝图 - 鼠标进入贝位事件触发:', {\n      positionId: position.position,\n      isDragging: this.isDragging,\n      currentSelected: position.selected,\n      exists: position.exists,\n      deleted: position.deleted,\n      isDeduct: position.isDeduct\n    });\n    // 只在拖拽状态下处理\n    if (!this.isDragging) {\n      console.log('客混船贝图 - 非拖拽状态，忽略鼠标进入事件');\n      return;\n    }\n    // 无效位置、已删除的贝位或不存在的贝位不能被选中\n    if (position.isDeduct || position.deleted || !position.exists) {\n      console.log('客混船贝图 - 无效位置、已删除或不存在的贝位，跳过拖拽选择:', {\n        positionId: position.position,\n        isDeduct: position.isDeduct,\n        deleted: position.deleted,\n        exists: position.exists\n      });\n      return;\n    }\n    // 如果贝位还未被选中，则选中它\n    if (!position.selected) {\n      position.selected = true;\n      // 添加到选中列表（避免重复添加）\n      if (!this.selectedPositions.find(p => p.position === position.position)) {\n        this.selectedPositions.push(position);\n        console.log('客混船贝图 - 拖拽选中贝位成功:', {\n          positionId: position.position,\n          totalSelected: this.selectedPositions.length,\n          selectedPositions: this.selectedPositions.map(p => p.position)\n        });\n      } else {\n        console.log('客混船贝图 - 贝位已在选中列表中，跳过重复添加:', position.position);\n      }\n    } else {\n      console.log('客混船贝图 - 贝位已被选中，跳过重复选择:', position.position);\n    }\n    // 阻止事件冒泡\n    event.stopPropagation();\n  }\n  /**\n   * 容器双击事件 - 双击空白区域取消所有选中\n   */\n  onContainerDoubleClick(event) {\n    const target = event.target;\n    const isPositionCell = target.classList.contains('position-cell');\n    console.log('客混船贝图 - 容器双击事件:', {\n      target: target.className,\n      isPositionCell: isPositionCell,\n      currentSelectedCount: this.selectedPositions.length\n    });\n    // 只有点击在空白区域（非贝位元素）时才清除选中状态\n    if (!isPositionCell) {\n      console.log('客混船贝图 - 双击空白区域，清除所有选中状态');\n      this.clearAllSelections();\n    } else {\n      console.log('客混船贝图 - 双击在贝位上，不清除选中状态');\n    }\n  }\n  /**\n   * 清除所有选择\n   */\n  clearAllSelections() {\n    console.log('客混船贝图 - 清除所有选中状态，当前选中数量:', this.selectedPositions.length);\n    this.kayPositions.forEach(p => p.selected = false);\n    this.selectedPositions = [];\n    console.log('客混船贝图 - 选中状态已清除');\n  }\n  /**\n   * 获取网格样式 - 支持按行换行显示\n   */\n  getGridStyle() {\n    if (!this.selectedKayData) {\n      return {};\n    }\n    return {\n      'display': 'grid',\n      'grid-template-columns': `repeat(${this.selectedKayData.columnNum}, 50px)`,\n      'gap': '0',\n      'justify-content': 'start',\n      '--column-count': this.selectedKayData.columnNum.toString()\n    };\n  }\n  /**\n   * 获取列标识数组 - 与贝位图列顺序一致（从右到左排列）\n   */\n  getColumnLabels() {\n    if (!this.selectedKayData) return [];\n    const labels = [];\n    const data = this.selectedKayData;\n    // 按照贝位生成的顺序：从右上角开始，向左排列\n    for (let col = 0; col < data.columnNum; col++) {\n      // 与贝位生成逻辑保持一致：从右上角开始，列数从右到左递减\n      const actualCol = data.columnFrom + (data.columnNum - 1 - col) * data.columnSep;\n      labels.push(actualCol);\n    }\n    return labels;\n  }\n  /**\n   * 获取行标识数组 - 与贝位图行顺序一致（从上到下排列）\n   */\n  getRowLabels() {\n    if (!this.selectedKayData) return [];\n    const labels = [];\n    const data = this.selectedKayData;\n    // 按照贝位生成的顺序：从上到下排列\n    for (let row = 0; row < data.rowNum; row++) {\n      // 与贝位生成逻辑保持一致：从上到下递增\n      const actualRow = data.rowFrom + row * data.rowSep;\n      labels.push(actualRow);\n    }\n    return labels;\n  }\n  /**\n   * 格式化数字为两位字符串（补零）\n   * 用于解决模板中String()函数不可用的问题\n   */\n  formatNumber(num) {\n    return num.toString().padStart(2, '0');\n  }\n  /**\n   * 新建贝\n   */\n  showNewKayModal() {\n    this.newKayForm.reset();\n    // 设置默认值\n    this.newKayForm.patchValue({\n      rowNum: 2,\n      rowSep: 1,\n      rowFrom: 3,\n      columnNum: 3,\n      columnSep: 1,\n      columnFrom: 7\n    });\n    this.isNewKayModalVisible = true;\n    // 生成初始预览\n    this.generatePreviewKayData();\n  }\n  /**\n   * 取消新建贝\n   */\n  cancelNewKay() {\n    this.isNewKayModalVisible = false;\n    this.previewKayData = null;\n  }\n  /**\n   * 生成预览贝图数据\n   */\n  generatePreviewKayData() {\n    const formValue = this.newKayForm.getRawValue();\n    if (!formValue.rowNum || !formValue.columnNum) {\n      this.previewKayData = null;\n      return;\n    }\n    const positions = [];\n    // 基于行列参数生成所有贝位编号\n    for (let row = 0; row < formValue.rowNum; row++) {\n      for (let col = 0; col < formValue.columnNum; col++) {\n        const actualRow = formValue.rowFrom + row * formValue.rowSep;\n        // 从右上角开始，列数从右到左递减\n        const actualCol = formValue.columnFrom + (formValue.columnNum - 1 - col) * formValue.columnSep;\n        const position = {\n          position: `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`,\n          gridRow: row + 1,\n          // CSS Grid行位置（1-based）\n          gridColumn: col + 1 // CSS Grid列位置（1-based）\n        };\n        positions.push(position);\n      }\n    }\n    this.previewKayData = {\n      rowNum: formValue.rowNum,\n      columnNum: formValue.columnNum,\n      totalPositions: positions.length,\n      positions: positions\n    };\n    console.log('客混船贝图 - 预览数据生成:', {\n      formValue: formValue,\n      previewKayData: this.previewKayData\n    });\n  }\n  /**\n   * 保存新建贝\n   */\n  saveNewKay() {\n    for (const i in this.newKayForm.controls) {\n      this.newKayForm.controls[i].markAsDirty();\n      this.newKayForm.controls[i].updateValueAndValidity();\n    }\n    if (this.newKayForm.invalid) {\n      return;\n    }\n    // 检查贝号唯一性\n    const kayNo = this.newKayForm.get('kayNo')?.value;\n    if (this.kayList.some(kay => kay.kayNo === kayNo)) {\n      this.showState(ModalTypeEnum.error, '贝号已存在，请重新输入！');\n      return;\n    }\n    // 生成贝位列表（rowtier字段）\n    const formValue = this.newKayForm.getRawValue();\n    const generatedPositions = this.generateKayPositionsList(formValue);\n    console.log('客混船贝图 - 新建贝位生成的贝位列表:', {\n      formValue: formValue,\n      generatedPositions: generatedPositions,\n      positionsCount: generatedPositions.length\n    });\n    const requestData = {\n      vesselId: this.vesselId,\n      kayNo: formValue.kayNo,\n      rowtier: generatedPositions.join(','),\n      // 自动生成的贝位列表\n      rowNum: formValue.rowNum,\n      rowSep: formValue.rowSep,\n      rowFrom: formValue.rowFrom,\n      columnNum: formValue.columnNum,\n      columnSep: formValue.columnSep,\n      columnFrom: formValue.columnFrom,\n      remark: formValue.remark || ''\n    };\n    console.log('客混船贝图 - 新建贝位请求数据:', requestData);\n    this.cwfRestfulService.post('/vessel-kay', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '保存成功！');\n        this.isNewKayModalVisible = false;\n        this.loadKayList();\n        this.selectedKayNo = kayNo;\n        setTimeout(() => this.onKaySelect(), 100);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  /**\n   * 删除贝\n   */\n  deleteKay() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.selectedKayNo) {\n        _this.showState(ModalTypeEnum.error, '请选择要删除的贝！');\n        return;\n      }\n      const state = yield _this.showConfirm('确认', '是否确认删除？');\n      if (state !== DialogResultEnum.yes) {\n        return;\n      }\n      const kayData = _this.kayList.find(kay => kay.kayNo === _this.selectedKayNo);\n      if (!kayData) return;\n      _this.cwfRestfulService.delete(`/vessel-kay/${kayData.id}`, _this.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          _this.showState(ModalTypeEnum.success, '删除成功！');\n          _this.loadKayList();\n        } else {\n          _this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 生成客混船贝位列表（用于rowtier字段）\n   * 基于表单参数计算所有可能的贝位编号\n   */\n  generateKayPositionsList(formValue) {\n    const positions = [];\n    // 基于行列参数生成所有贝位编号\n    for (let row = 0; row < formValue.rowNum; row++) {\n      for (let col = 0; col < formValue.columnNum; col++) {\n        const actualRow = formValue.rowFrom + row * formValue.rowSep;\n        // 从右上角开始，列数从右到左递减\n        const actualCol = formValue.columnFrom + (formValue.columnNum - 1 - col) * formValue.columnSep;\n        // 生成贝位编号：行号+列号，都补齐为2位数\n        const positionId = `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`;\n        positions.push(positionId);\n      }\n    }\n    return positions;\n  }\n  /**\n   * 删除贝位 - 修复版本，解决首次操作API调用失败问题\n   */\n  deletePositions() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // 防止重复调用\n      if (_this2.isDeleting) {\n        console.log('客混船贝图 - 删除操作正在进行中，忽略重复调用');\n        return;\n      }\n      // 关键修复：使用统一的数据验证方法\n      const validationResult = _this2.validateDataLoadingStatus();\n      if (!validationResult.isValid) {\n        _this2.showState(ModalTypeEnum.error, validationResult.errorMessage);\n        return;\n      }\n      if (_this2.selectedPositions.length === 0) {\n        _this2.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\n        return;\n      }\n      const kayData = _this2.selectedKayData; // 验证通过后可以安全使用\n      // 获取要删除的贝位编号列表\n      const deletePositions = _this2.selectedPositions.map(p => p.position);\n      // 关键优化：只删除rowtier中实际存在的贝位\n      const currentRowtier = kayData.rowtier || '';\n      const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\n      const validDeletePositions = deletePositions.filter(pos => existingPositions.includes(pos));\n      const invalidDeletePositions = deletePositions.filter(pos => !existingPositions.includes(pos));\n      console.log('客混船贝图 - 删除贝位验证结果:', {\n        selectedKayNo: _this2.selectedKayNo,\n        totalSelectedPositions: deletePositions.length,\n        existingPositionsInRowtier: existingPositions,\n        validDeletePositions: validDeletePositions,\n        invalidDeletePositions: invalidDeletePositions,\n        kayDataId: kayData.id,\n        kayDataRowtier: kayData.rowtier\n      });\n      // 如果没有有效的删除贝位，提示用户\n      if (validDeletePositions.length === 0) {\n        _this2.showState(ModalTypeEnum.info, '选中的贝位都不存在于当前贝中，无法删除！');\n        return;\n      }\n      // 直接删除，不再显示确认弹出框\n      console.log('客混船贝图 - 直接删除贝位:', {\n        totalSelectedPositions: deletePositions.length,\n        validDeletePositions: validDeletePositions,\n        invalidDeletePositions: invalidDeletePositions\n      });\n      // 设置删除标志，防止重复调用\n      _this2.isDeleting = true;\n      // 根据接口文档规范构建请求数据，只传递有效的删除贝位\n      const requestData = {\n        kayId: kayData.id,\n        // 客混船舶贝的ID\n        kayNos: validDeletePositions // 只传递有效的删除贝位编号列表\n      };\n      console.log('客混船贝图 - 删除贝位请求数据:', {\n        requestData: requestData,\n        originalDeletePositions: deletePositions,\n        validDeletePositions: validDeletePositions,\n        invalidDeletePositions: invalidDeletePositions\n      });\n      // 使用正确的接口路径和HTTP方法\n      _this2.cwfRestfulService.delete('/vessel-kay/vessel/kay/batch-delete-positions', _this2.gol.serviceName['tas'].en, {\n        body: requestData\n      }).then(rps => {\n        if (rps.ok) {\n          _this2.showState(ModalTypeEnum.success, `成功删除 ${validDeletePositions.length} 个贝位！`);\n          // 只将有效删除的贝位添加到已删除集合\n          validDeletePositions.forEach(positionId => {\n            _this2.deletedPositions.add(positionId);\n          });\n          // 关键修复：同步更新selectedKayData的rowtier字段，移除已删除的贝位\n          if (_this2.selectedKayData.rowtier) {\n            const currentRowtier = _this2.selectedKayData.rowtier;\n            const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\n            const updatedPositions = existingPositions.filter(pos => !validDeletePositions.includes(pos));\n            _this2.selectedKayData.rowtier = updatedPositions.join(',');\n            console.log('客混船贝图 - 同步更新rowtier字段:', {\n              originalRowtier: currentRowtier,\n              deletedPositions: validDeletePositions,\n              updatedRowtier: _this2.selectedKayData.rowtier,\n              existingPositionsCount: existingPositions.length,\n              updatedPositionsCount: updatedPositions.length\n            });\n          }\n          // 更新UI状态：标记被删除的贝位\n          _this2.kayPositions.forEach(position => {\n            if (validDeletePositions.includes(position.position)) {\n              position.deleted = true;\n              position.selected = false; // 取消选中状态\n              position.exists = false; // 标记为不存在（已被删除）\n              position.wasDeleted = true; // 标记为真正被删除\n            }\n          });\n          // 更新已删除贝位的下拉选项\n          _this2.updateDeletedPositionOptions();\n          // 清除选择状态\n          _this2.clearAllSelections();\n          // 仅更新本地数据状态，不重新加载页面\n          // 从selectedPositions中移除已删除的贝位\n          _this2.selectedPositions = _this2.selectedPositions.filter(pos => !validDeletePositions.includes(pos.position));\n          // 触发视图更新\n          _this2.updatePositionStyles();\n          console.log('客混船贝图 - 删除操作完成:', {\n            deletedPositions: validDeletePositions,\n            deletedPositionsSet: Array.from(_this2.deletedPositions),\n            deletedPositionsSetSize: _this2.deletedPositions.size,\n            updatedRowtier: _this2.selectedKayData.rowtier\n          });\n        } else {\n          _this2.showState(ModalTypeEnum.error, rps.msg || '删除失败');\n        }\n      }).catch(error => {\n        console.error('客混船贝图 - 删除贝位失败:', error);\n        _this2.showState(ModalTypeEnum.error, '删除贝位失败，请重试');\n      }).finally(() => {\n        // 清除删除标志\n        _this2.isDeleting = false;\n      });\n    })();\n  }\n  /**\n   * 显示恢复贝位模态框 - 修复版本，解决删除状态显示与恢复功能不一致问题\n   */\n  showRestorePositionModal() {\n    if (!this.selectedKayData) {\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\n      return;\n    }\n    console.log('客混船贝图 - 显示恢复贝位模态框，开始状态验证...');\n    // 关键修复：先进行状态同步验证和修复\n    this.validateAndFixStatusSync();\n    // 然后更新已删除贝位选项，使用新的数据比较机制\n    this.updateDeletedPositionOptions();\n    // 检查是否有真正可以恢复的贝位\n    if (this.deletedPositionOptions.length === 0) {\n      // 提供更详细的提示信息\n      const kayData = this.selectedKayData;\n      const currentRowtier = kayData.rowtier || '';\n      const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\n      const allPossiblePositions = this.generateAllPossiblePositions(kayData);\n      console.log('客混船贝图 - 无可恢复贝位详细信息:', {\n        selectedKayNo: this.selectedKayNo,\n        deletedPositionsSetSize: this.deletedPositions.size,\n        deletedPositionsArray: Array.from(this.deletedPositions),\n        existingPositionsCount: existingPositions.length,\n        existingPositions: existingPositions,\n        allPossiblePositionsCount: allPossiblePositions.length,\n        allPossiblePositions: allPossiblePositions,\n        deletedPositionOptionsCount: this.deletedPositionOptions.length\n      });\n      if (this.deletedPositions.size === 0) {\n        this.showState(ModalTypeEnum.info, '当前没有已删除的贝位记录！');\n      } else {\n        this.showState(ModalTypeEnum.info, '当前没有可以恢复的贝位！\\n已删除的贝位可能不属于当前贝的配置范围。');\n      }\n      return;\n    }\n    // 清空之前的选择\n    this.selectedDeletedPositions = [];\n    // 显示模态框\n    this.isRestorePositionModalVisible = true;\n    console.log('客混船贝图 - 显示恢复贝位模态框:', {\n      selectedKayNo: this.selectedKayNo,\n      availableRestorePositionsCount: this.deletedPositionOptions.length,\n      availableRestorePositions: this.deletedPositionOptions.map(opt => opt.value)\n    });\n  }\n  /**\n   * 取消恢复贝位操作\n   */\n  cancelRestorePosition() {\n    this.isRestorePositionModalVisible = false;\n    this.selectedDeletedPositions = [];\n  }\n  /**\n   * 更新已删除贝位的选项列表 - 优化版本，基于数据比较机制\n   */\n  updateDeletedPositionOptions() {\n    if (!this.selectedKayData) {\n      this.deletedPositionOptions = [];\n      return;\n    }\n    // 1. 获取当前贝页面中已存在的贝位列表（从rowtier字段解析）\n    const currentRowtier = this.selectedKayData.rowtier || '';\n    const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\n    // 2. 获取根据配置参数计算出来的全部可能贝位列表\n    const allPossiblePositions = this.generateAllPossiblePositions(this.selectedKayData);\n    // 3. 通过数据比较确定真正被删除的贝位\n    const trulyDeletedPositions = this.identifyTrulyDeletedPositions(allPossiblePositions, existingPositions, this.deletedPositions);\n    // 4. 生成恢复列表选项\n    this.deletedPositionOptions = trulyDeletedPositions.map(positionId => ({\n      label: positionId,\n      value: positionId,\n      area: this.getPositionArea(positionId)\n    }));\n    console.log('客混船贝图 - 恢复贝位数据比较结果:', {\n      selectedKayNo: this.selectedKayNo,\n      kayDataId: this.selectedKayData.id,\n      currentRowtier: currentRowtier,\n      existingPositionsCount: existingPositions.length,\n      existingPositions: existingPositions,\n      allPossiblePositionsCount: allPossiblePositions.length,\n      allPossiblePositions: allPossiblePositions,\n      deletedPositionsSetSize: this.deletedPositions.size,\n      deletedPositionsArray: Array.from(this.deletedPositions),\n      trulyDeletedPositionsCount: trulyDeletedPositions.length,\n      trulyDeletedPositions: trulyDeletedPositions,\n      deletedPositionOptionsCount: this.deletedPositionOptions.length\n    });\n  }\n  /**\n   * 根据配置参数生成所有可能的贝位编号列表\n   */\n  generateAllPossiblePositions(kayData) {\n    const allPositions = [];\n    // 使用与generateKayPositions相同的算法生成所有可能的贝位\n    for (let row = 0; row < kayData.rowNum; row++) {\n      for (let col = 0; col < kayData.columnNum; col++) {\n        const actualRow = kayData.rowFrom + row * kayData.rowSep;\n        // 从右上角开始，列数从右到左递减\n        const actualCol = kayData.columnFrom + (kayData.columnNum - 1 - col) * kayData.columnSep;\n        const positionId = `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`;\n        allPositions.push(positionId);\n      }\n    }\n    return allPositions;\n  }\n  /**\n   * 通过数据比较确定真正被删除的贝位\n   */\n  identifyTrulyDeletedPositions(allPossiblePositions, existingPositions, deletedPositionsSet) {\n    const trulyDeletedPositions = [];\n    // 遍历所有可能的贝位\n    for (const positionId of allPossiblePositions) {\n      // 判断条件：\n      // 1. 不在当前已存在的贝位中（说明当前不存在）\n      // 2. 在deletedPositions集合中（说明曾经被删除过）\n      if (!existingPositions.includes(positionId) && deletedPositionsSet.has(positionId)) {\n        trulyDeletedPositions.push(positionId);\n      }\n    }\n    return trulyDeletedPositions.sort(); // 排序便于查看\n  }\n  /**\n   * 检查数据初始化是否完成 - 解决时序问题和竞态条件\n   */\n  checkDataInitializationComplete() {\n    const steps = this.dataInitializationSteps;\n    const isComplete = steps.kayListLoaded && steps.kayDataSelected && steps.positionsGenerated && steps.statusSyncCompleted;\n    console.log('客混船贝图 - 数据初始化步骤检查:', {\n      kayListLoaded: steps.kayListLoaded,\n      kayDataSelected: steps.kayDataSelected,\n      positionsGenerated: steps.positionsGenerated,\n      statusSyncCompleted: steps.statusSyncCompleted,\n      isComplete: isComplete\n    });\n    return isComplete;\n  }\n  /**\n   * 完成数据初始化 - 统一的初始化完成管理\n   */\n  completeDataInitialization() {\n    if (this.checkDataInitializationComplete()) {\n      this.isDataLoaded = true;\n      this.isDataLoading = false;\n      console.log('客混船贝图 - 所有数据初始化完成，可以进行操作');\n    } else {\n      console.log('客混船贝图 - 数据初始化尚未完成，等待其他步骤');\n    }\n  }\n  /**\n   * 重置数据初始化状态 - 用于数据重新加载\n   */\n  resetDataInitializationSteps() {\n    this.dataInitializationSteps = {\n      kayListLoaded: false,\n      kayDataSelected: false,\n      positionsGenerated: false,\n      statusSyncCompleted: false\n    };\n    this.isDataLoaded = false;\n    console.log('客混船贝图 - 数据初始化状态已重置');\n  }\n  /**\n   * 验证数据加载状态 - 修复首次操作API调用失败问题（增强版）\n   */\n  validateDataLoadingStatus() {\n    console.log('客混船贝图 - 验证数据加载状态（增强版）:', {\n      isDataLoading: this.isDataLoading,\n      isDataLoaded: this.isDataLoaded,\n      selectedKayNo: this.selectedKayNo,\n      selectedKayData: this.selectedKayData,\n      kayListLength: this.kayList.length,\n      kayPositionsLength: this.kayPositions.length,\n      selectedKayDataId: this.selectedKayData?.id,\n      dataInitializationSteps: this.dataInitializationSteps\n    });\n    // 0. 检查数据是否正在加载\n    if (this.isDataLoading) {\n      return {\n        isValid: false,\n        errorMessage: '数据正在加载中，请稍后重试！'\n      };\n    }\n    // 1. 检查详细的数据初始化步骤\n    if (!this.checkDataInitializationComplete()) {\n      const steps = this.dataInitializationSteps;\n      let missingSteps = [];\n      if (!steps.kayListLoaded) missingSteps.push('贝位列表加载');\n      if (!steps.kayDataSelected) missingSteps.push('贝位数据选择');\n      if (!steps.positionsGenerated) missingSteps.push('贝位图生成');\n      if (!steps.statusSyncCompleted) missingSteps.push('状态同步');\n      return {\n        isValid: false,\n        errorMessage: `数据初始化未完成，缺少步骤：${missingSteps.join('、')}，请稍后重试！`\n      };\n    }\n    // 2. 检查基础数据是否加载\n    if (this.kayList.length === 0) {\n      return {\n        isValid: false,\n        errorMessage: '贝位列表数据未加载完成，请稍后重试！'\n      };\n    }\n    // 2. 检查是否选中了贝号\n    if (!this.selectedKayNo) {\n      return {\n        isValid: false,\n        errorMessage: '请先选择贝号！'\n      };\n    }\n    // 3. 检查selectedKayData是否有效\n    if (!this.selectedKayData) {\n      // 尝试重新获取\n      this.selectedKayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\n      if (!this.selectedKayData) {\n        return {\n          isValid: false,\n          errorMessage: '未找到对应的贝位数据！请刷新页面后重试。'\n        };\n      }\n    }\n    // 4. 检查kayData.id是否有效\n    if (!this.selectedKayData.id) {\n      return {\n        isValid: false,\n        errorMessage: '贝位数据ID无效！请刷新页面后重试。'\n      };\n    }\n    // 5. 检查贝位图数据是否生成\n    if (this.kayPositions.length === 0) {\n      return {\n        isValid: false,\n        errorMessage: '贝位图数据未生成！请稍后重试或刷新页面。'\n      };\n    }\n    // 6. 检查deletedPositions集合是否已初始化（通过状态同步完成标志）\n    if (!this.dataInitializationSteps.statusSyncCompleted) {\n      return {\n        isValid: false,\n        errorMessage: '贝位状态同步未完成！请稍后重试。'\n      };\n    }\n    console.log('客混船贝图 - 数据加载状态验证通过（增强版）:', {\n      kayDataId: this.selectedKayData.id,\n      kayNo: this.selectedKayData.kayNo,\n      kayPositionsLength: this.kayPositions.length,\n      deletedPositionsSize: this.deletedPositions.size,\n      allStepsCompleted: this.checkDataInitializationComplete()\n    });\n    return {\n      isValid: true\n    };\n  }\n  /**\n   * 验证和修复删除状态与恢复功能的同步问题\n   */\n  validateAndFixStatusSync() {\n    if (!this.selectedKayData) {\n      console.log('客混船贝图 - 状态同步验证：没有选中的贝数据');\n      return;\n    }\n    console.log('客混船贝图 - 开始状态同步验证和修复...');\n    // 1. 获取当前UI中显示为删除状态的贝位\n    const uiDeletedPositions = this.kayPositions.filter(p => p.deleted).map(p => p.position);\n    const uiTrulyDeletedPositions = this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position);\n    // 2. 获取deletedPositions集合中的贝位\n    const setDeletedPositions = Array.from(this.deletedPositions);\n    // 3. 检查同步状态\n    const syncStatus = {\n      uiDeletedCount: uiDeletedPositions.length,\n      uiTrulyDeletedCount: uiTrulyDeletedPositions.length,\n      setDeletedCount: setDeletedPositions.length,\n      isSync: JSON.stringify(uiTrulyDeletedPositions.sort()) === JSON.stringify(setDeletedPositions.sort())\n    };\n    console.log('客混船贝图 - 状态同步验证结果:', {\n      uiDeletedPositions: uiDeletedPositions,\n      uiTrulyDeletedPositions: uiTrulyDeletedPositions,\n      setDeletedPositions: setDeletedPositions,\n      syncStatus: syncStatus\n    });\n    // 4. 如果不同步，进行修复\n    if (!syncStatus.isSync) {\n      console.log('客混船贝图 - 检测到状态不同步，开始修复...');\n      // 修复策略：以UI显示的真正删除状态为准，更新deletedPositions集合\n      this.deletedPositions.clear();\n      uiTrulyDeletedPositions.forEach(positionId => {\n        this.deletedPositions.add(positionId);\n      });\n      console.log('客混船贝图 - 状态同步修复完成:', {\n        fixedDeletedPositionsCount: this.deletedPositions.size,\n        fixedDeletedPositions: Array.from(this.deletedPositions)\n      });\n      // 重新更新恢复选项\n      this.updateDeletedPositionOptions();\n    } else {\n      console.log('客混船贝图 - 状态同步验证通过，无需修复');\n    }\n    // 标记状态同步完成\n    this.dataInitializationSteps.statusSyncCompleted = true;\n    console.log('客混船贝图 - 状态同步完成');\n    // 检查是否可以完成整体初始化\n    this.completeDataInitialization();\n  }\n  /**\n   * 获取贝位所属区域\n   */\n  getPositionArea(positionId) {\n    // 客混船贝图中所有贝位都在同一区域，这里返回默认值\n    return 'kay';\n  }\n  /**\n   * 检查贝位是否被选中\n   */\n  isPositionSelected(positionId) {\n    return this.selectedDeletedPositions.includes(positionId);\n  }\n  /**\n   * 切换贝位选中状态\n   */\n  togglePositionSelection(positionId) {\n    const index = this.selectedDeletedPositions.indexOf(positionId);\n    if (index > -1) {\n      this.selectedDeletedPositions.splice(index, 1);\n    } else {\n      this.selectedDeletedPositions.push(positionId);\n    }\n  }\n  /**\n   * 全选已删除贝位\n   */\n  selectAllRestorePositions() {\n    this.selectedDeletedPositions = [...this.deletedPositionOptions.map(item => item.value)];\n  }\n  /**\n   * 反选已删除贝位\n   */\n  deselectAllRestorePositions() {\n    const currentSelected = new Set(this.selectedDeletedPositions);\n    this.selectedDeletedPositions = this.deletedPositionOptions.map(item => item.value).filter(id => !currentSelected.has(id));\n  }\n  /**\n   * 清空选择\n   */\n  clearRestoreSelection() {\n    this.selectedDeletedPositions = [];\n  }\n  /**\n   * 跟踪函数，用于优化列表渲染性能\n   */\n  trackByPositionId(index, item) {\n    return item.value;\n  }\n  /**\n   * 恢复选中的已删除贝位 - 修复版本，解决首次操作API调用失败问题\n   */\n  restoreSelectedPositions() {\n    // 防止重复调用\n    if (this.isRestoring) {\n      console.log('客混船贝图 - 恢复操作正在进行中，忽略重复调用');\n      return;\n    }\n    // 关键修复：使用统一的数据验证方法\n    const validationResult = this.validateDataLoadingStatus();\n    if (!validationResult.isValid) {\n      this.showState(ModalTypeEnum.error, validationResult.errorMessage);\n      return;\n    }\n    if (this.selectedDeletedPositions.length === 0) {\n      this.showState(ModalTypeEnum.error, '请选择要恢复的贝位！');\n      return;\n    }\n    const kayData = this.selectedKayData; // 验证通过后可以安全使用\n    // 关键优化：验证要恢复的贝位是否真正在已删除集合中\n    const validRestorePositions = this.selectedDeletedPositions.filter(pos => this.deletedPositions.has(pos));\n    const invalidRestorePositions = this.selectedDeletedPositions.filter(pos => !this.deletedPositions.has(pos));\n    console.log('客混船贝图 - 恢复贝位验证结果:', {\n      selectedKayNo: this.selectedKayNo,\n      totalSelectedPositions: this.selectedDeletedPositions.length,\n      deletedPositionsSet: Array.from(this.deletedPositions),\n      validRestorePositions: validRestorePositions,\n      invalidRestorePositions: invalidRestorePositions,\n      kayDataId: kayData.id,\n      kayDataRowtier: kayData.rowtier\n    });\n    // 如果没有有效的恢复贝位，提示用户\n    if (validRestorePositions.length === 0) {\n      this.showState(ModalTypeEnum.info, '选中的贝位都不在已删除列表中，无法恢复！');\n      return;\n    }\n    // 如果有无效的恢复贝位，提示用户\n    if (invalidRestorePositions.length > 0) {\n      console.warn('客混船贝图 - 警告：以下贝位不在已删除集合中:', invalidRestorePositions);\n    }\n    // 设置恢复标志，防止重复调用\n    this.isRestoring = true;\n    // 根据接口文档规范构建请求数据，只传递有效的恢复贝位\n    const requestData = {\n      kayId: kayData.id,\n      // 客混船贝的ID\n      kayNos: validRestorePositions // 只传递有效的恢复贝位编号列表\n    };\n    console.log('客混船贝图 - 恢复贝位请求数据:', {\n      requestData: requestData,\n      originalRestorePositions: this.selectedDeletedPositions,\n      validRestorePositions: validRestorePositions,\n      invalidRestorePositions: invalidRestorePositions\n    });\n    // 调用批量插入贝位接口\n    this.cwfRestfulService.post('/vessel-kay/vessel/kay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en).then(response => {\n      console.log('客混船贝图 - 恢复贝位响应:', response);\n      if (response && response.ok) {\n        this.showState(ModalTypeEnum.success, `成功恢复 ${validRestorePositions.length} 个贝位！`);\n        // 只将有效恢复的贝位从已删除集合中移除\n        validRestorePositions.forEach(positionId => {\n          this.deletedPositions.delete(positionId);\n        });\n        // 更新UI状态：标记恢复的贝位\n        this.kayPositions.forEach(position => {\n          if (validRestorePositions.includes(position.position)) {\n            position.deleted = false;\n            position.exists = true; // 标记为存在（已恢复）\n            position.wasDeleted = false; // 标记为不再是已删除状态\n          }\n        });\n        // 清空选中的已删除贝位\n        this.selectedDeletedPositions = [];\n        // 更新已删除贝位的下拉选项\n        this.updateDeletedPositionOptions();\n        // 重新加载贝位列表，确保数据同步\n        this.loadKayList();\n        // 延迟重新选择当前贝位，确保数据加载完成\n        setTimeout(() => {\n          this.onKaySelect();\n        }, 200);\n        // 关闭模态框\n        this.isRestorePositionModalVisible = false;\n        console.log('客混船贝图 - 恢复操作完成:', {\n          restoredPositions: validRestorePositions,\n          deletedPositionsSet: Array.from(this.deletedPositions),\n          deletedPositionsSetSize: this.deletedPositions.size\n        });\n      } else {\n        this.showState(ModalTypeEnum.error, response?.msg || '恢复失败！');\n      }\n    }).catch(error => {\n      console.error('客混船贝图 - 恢复贝位失败:', error);\n      this.showState(ModalTypeEnum.error, '恢复贝位失败！');\n    }).finally(() => {\n      // 清除恢复标志\n      this.isRestoring = false;\n    });\n  }\n  /**\n   * 复制贝\n   */\n  showCopyKayModal() {\n    if (!this.selectedKayNo) {\n      this.showState(ModalTypeEnum.error, '请选择要复制的贝位信息！');\n      return;\n    }\n    this.copyKayForm.reset();\n    this.copyKayForm.patchValue({\n      originalKayNo: this.selectedKayNo\n    });\n    this.isCopyKayModalVisible = true;\n  }\n  /**\n   * 保存复制贝\n   */\n  saveCopyKay() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      for (const i in _this3.copyKayForm.controls) {\n        _this3.copyKayForm.controls[i].markAsDirty();\n        _this3.copyKayForm.controls[i].updateValueAndValidity();\n      }\n      if (_this3.copyKayForm.invalid) {\n        return;\n      }\n      const newKayNo = _this3.copyKayForm.get('newKayNo')?.value;\n      // 验证格式 - 允许数字和字母\n      const regex = /^[a-zA-Z0-9]+$/;\n      if (!regex.test(newKayNo.replace(/\\s/g, ''))) {\n        _this3.showState(ModalTypeEnum.error, '新贝号格式不正确，只能包含数字和字母，请检查后重新输入！');\n        return;\n      }\n      // 检查唯一性\n      if (_this3.kayList.some(kay => kay.kayNo === newKayNo)) {\n        const state = yield _this3.showConfirm('确认', '新贝号已存在，是否覆盖？');\n        if (state !== DialogResultEnum.yes) {\n          return;\n        }\n      }\n      const originalKayData = _this3.kayList.find(kay => kay.kayNo === _this3.selectedKayNo);\n      if (!originalKayData) return;\n      // 使用正确的接口路径，根据错误信息使用targetKayNo作为查询参数\n      const apiPath = `/vessel-kay/vessel/${_this3.vesselId}/kay/${_this3.selectedKayNo}/copy?targetKayNo=${encodeURIComponent(newKayNo)}`;\n      _this3.cwfRestfulService.post(apiPath, {}, _this3.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          _this3.showState(ModalTypeEnum.success, '复制成功！');\n          _this3.isCopyKayModalVisible = false;\n          _this3.loadKayList();\n          _this3.selectedKayNo = newKayNo;\n          setTimeout(() => _this3.onKaySelect(), 100);\n        } else {\n          _this3.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 标记无效位置 - 优化版本，去掉确认弹出框\n   */\n  markInvalidPositions() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.selectedKayNo) {\n        _this4.showState(ModalTypeEnum.error, '请选择要标记无效的贝！');\n        return;\n      }\n      if (_this4.selectedPositions.length === 0) {\n        _this4.showState(ModalTypeEnum.error, '请选择要标记无效的贝位！');\n        return;\n      }\n      // 检查是否已经标记过无效\n      const alreadyDeduct = _this4.selectedPositions.some(pos => pos.isDeduct);\n      if (alreadyDeduct) {\n        _this4.showState(ModalTypeEnum.error, '所选的贝位中包含已经标记过无效的位置！');\n        return;\n      }\n      // 直接执行标记操作，不再显示确认弹出框\n      console.log('客混船贝图 - 直接标记无效位置:', {\n        selectedPositions: _this4.selectedPositions.map(p => p.position),\n        selectedKayNo: _this4.selectedKayNo\n      });\n      const kayData = _this4.kayList.find(kay => kay.kayNo === _this4.selectedKayNo);\n      if (!kayData) return;\n      const invalidPositions = _this4.selectedPositions.map(p => p.position).join(',');\n      const requestData = {\n        kayId: kayData.id,\n        vesselId: _this4.vesselId,\n        rowtier: invalidPositions\n      };\n      _this4.cwfRestfulService.post('/vessel-kay-deduct', requestData, _this4.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          console.log('客混船贝图 - 标记无效位置API调用成功');\n          // 立即更新本地状态，提供即时视觉反馈\n          _this4.selectedPositions.forEach(pos => {\n            pos.isDeduct = true;\n            console.log('客混船贝图 - 本地标记贝位为无效:', pos.position);\n          });\n          _this4.showState(ModalTypeEnum.success, '标记成功！');\n          // 重新加载无效位置数据以确保数据一致性\n          _this4.loadDeductPositions();\n          // 清除选中状态\n          _this4.clearAllSelections();\n        } else {\n          _this4.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 取消无效标记 - 修复版本，自动识别已标记为无效的贝位\n   */\n  clearInvalidPositions() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.selectedKayNo) {\n        _this5.showState(ModalTypeEnum.error, '请选择要操作的贝！');\n        return;\n      }\n      // 修复：自动识别当前贝中所有已标记为无效的贝位\n      const invalidPositions = _this5.kayPositions.filter(pos => pos.isDeduct);\n      console.log('客混船贝图 - 检查无效标记贝位:', {\n        selectedKayNo: _this5.selectedKayNo,\n        totalPositions: _this5.kayPositions.length,\n        invalidPositions: invalidPositions.map(p => ({\n          position: p.position,\n          isDeduct: p.isDeduct\n        })),\n        deductPositions: _this5.deductPositions\n      });\n      if (invalidPositions.length === 0) {\n        _this5.showState(ModalTypeEnum.info, '当前贝中没有已标记为无效的贝位！');\n        return;\n      }\n      // 直接执行取消标记操作，不再显示确认弹出框\n      console.log('客混船贝图 - 直接取消无效标记:', {\n        invalidPositionsCount: invalidPositions.length,\n        invalidPositionsList: invalidPositions.map(p => p.position)\n      });\n      const kayData = _this5.kayList.find(kay => kay.kayNo === _this5.selectedKayNo);\n      if (!kayData) return;\n      // 修复：使用自动识别的无效贝位列表\n      const positionsToUnmark = invalidPositions.map(p => p.position).join(',');\n      console.log('客混船贝图 - 取消无效标记请求数据:', {\n        kayId: kayData.id,\n        vesselId: _this5.vesselId,\n        positionsToUnmark: positionsToUnmark,\n        invalidPositionsCount: invalidPositions.length,\n        correctApiPath: `/vessel-kay-deduct/clear/${kayData.id}`\n      });\n      // 修复：使用正确的接口路径和HTTP方法\n      // 接口：DELETE /vessel-kay-deduct/clear/{kayId}\n      // 该接口会清除指定贝位下的所有无效位置标记\n      _this5.cwfRestfulService.delete(`/vessel-kay-deduct/clear/${kayData.id}`, _this5.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          _this5.showState(ModalTypeEnum.success, '取消无效标记成功！');\n          _this5.loadDeductPositions();\n          _this5.clearAllSelections();\n          console.log('客混船贝图 - 取消无效标记成功:', {\n            kayId: kayData.id,\n            clearedPositionsCount: invalidPositions.length,\n            response: rps\n          });\n        } else {\n          _this5.showState(ModalTypeEnum.error, rps.msg);\n          console.error('客混船贝图 - 取消无效标记失败:', rps);\n        }\n      }).catch(error => {\n        console.error('客混船贝图 - 取消无效标记API调用失败:', error);\n        _this5.showState(ModalTypeEnum.error, '取消无效标记失败，请重试');\n      });\n    })();\n  }\n  /**\n   * 返回主页面\n   */\n  goBack() {\n    this.openPage('/tas/vessel/list');\n  }\n  static {\n    this.ɵfac = function VesselKayComponent_Factory(t) {\n      return new (t || VesselKayComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.CwfRestfulService), i0.ɵɵdirectiveInject(i4.NzModalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VesselKayComponent,\n      selectors: [[\"tas-vessel-kay-app\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 43,\n      vars: 30,\n      consts: [[1, \"title-card\", 3, \"nzBodyStyle\"], [1, \"page-title\"], [1, \"operations-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"24\"], [1, \"kay-operations\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\"], [\"nz-button\", \"\", 1, \"return-button\", 3, \"click\", \"nzType\"], [1, \"selector-card\", 3, \"nzBodyStyle\"], [1, \"kay-selector-container\"], [1, \"kay-selector\"], [1, \"kay-selector-label\"], [1, \"kay-selector-dropdown\", 3, \"ngModelChange\", \"ngModel\"], [3, \"nzValue\", \"nzLabel\", 4, \"ngFor\", \"ngForOf\"], [1, \"diagram-card\", 3, \"nzBodyStyle\"], [1, \"kay-diagram-container\", 3, \"mousedown\", \"mousemove\", \"mouseup\", \"mouseleave\", \"dblclick\"], [\"class\", \"kay-diagram\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"no-positions\", 4, \"ngIf\"], [\"nzTitle\", \"\\u65B0\\u5EFA\\u5BA2\\u6DF7\\u8239\\u8D1D\", \"nzOkText\", \"\\u4FDD\\u5B58\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\"], [4, \"nzModalContent\"], [\"nzTitle\", \"\\u590D\\u5236\\u8D1D\", \"nzOkText\", \"\\u4FDD\\u5B58\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\"], [\"nzTitle\", \"\\u6062\\u590D\\u8D1D\\u4F4D\", \"nzOkText\", \"\\u6062\\u590D\\u9009\\u4E2D\\u8D1D\\u4F4D\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\", \"nzBodyStyle\"], [3, \"nzValue\", \"nzLabel\"], [1, \"kay-diagram\", 3, \"ngStyle\"], [1, \"diagram-with-row-labels\"], [1, \"column-labels-container\"], [1, \"column-labels-grid\", 3, \"ngStyle\"], [\"class\", \"column-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"column-labels-spacer\"], [1, \"position-row-container\"], [1, \"position-grid\", 3, \"ngStyle\"], [3, \"class\", \"grid-row\", \"grid-column\", \"click\", \"mouseenter\", 4, \"ngFor\", \"ngForOf\"], [1, \"row-labels\"], [\"class\", \"row-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"column-label\"], [3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [1, \"row-label\"], [1, \"no-positions\"], [\"nzNotFoundContent\", \"\\u6682\\u65E0\\u8D1D\\u4F4D\\u6570\\u636E\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"10\"], [\"nzTitle\", \"\\u53C2\\u6570\\u914D\\u7F6E\", 3, \"nzSize\"], [\"nz-form\", \"\", 3, \"formGroup\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"nzRequired\", \"\", \"nzLabelAlign\", \"right\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8D1D\\u53F7\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8D1D\\u53F7\", \"formControlName\", \"kayNo\"], [\"nz-col\", \"\", \"nzSpan\", \"12\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u884C\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u884C\\u6570\", \"formControlName\", \"rowNum\", 3, \"nzMin\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u5217\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5217\\u6570\", \"formControlName\", \"columnNum\", 3, \"nzMin\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u884C\\u6B65\\u957F\"], [\"nz-input\", \"\", \"placeholder\", \"\\u884C\\u6B65\\u957F\", \"formControlName\", \"rowSep\", 3, \"nzMin\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u5217\\u6B65\\u957F\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5217\\u6B65\\u957F\", \"formControlName\", \"columnSep\", 3, \"nzMin\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u884C\\u8D77\\u59CB\"], [\"nz-input\", \"\", \"placeholder\", \"\\u884C\\u8D77\\u59CB\", \"formControlName\", \"rowFrom\", 3, \"nzMin\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u5217\\u8D77\\u59CB\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5217\\u8D77\\u59CB\", \"formControlName\", \"columnFrom\", 3, \"nzMin\"], [\"nzLabelAlign\", \"right\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5907\\u6CE8\", \"formControlName\", \"remark\", 3, \"nzAutosize\"], [\"nz-col\", \"\", \"nzSpan\", \"14\"], [\"nzTitle\", \"\\u8D1D\\u56FE\\u9884\\u89C8\", 3, \"nzSize\"], [1, \"kay-diagram-container\"], [\"class\", \"kay-diagram\", 4, \"ngIf\"], [\"nzNotFoundImage\", \"simple\", \"nzNotFoundContent\", \"\\u8BF7\\u914D\\u7F6E\\u53C2\\u6570\\u4EE5\\u751F\\u6210\\u8D1D\\u56FE\\u9884\\u89C8\", 4, \"ngIf\"], [1, \"kay-diagram\"], [1, \"preview-stats\"], [\"nzTitle\", \"\\u8D1D\\u4F4D\\u603B\\u6570\", \"nzSuffix\", \"\\u4E2A\", 3, \"nzValue\"], [1, \"kay-grid\"], [\"class\", \"position-cell kay-position\", 3, \"grid-row\", \"grid-column\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"position-list\"], [\"nzText\", \"\\u8D1D\\u4F4D\\u7F16\\u53F7\\u5217\\u8868\", \"nzOrientation\", \"left\"], [1, \"position-tags\"], [\"nzColor\", \"blue\", 4, \"ngFor\", \"ngForOf\"], [1, \"position-cell\", \"kay-position\"], [\"nzColor\", \"blue\"], [\"nzNotFoundImage\", \"simple\", \"nzNotFoundContent\", \"\\u8BF7\\u914D\\u7F6E\\u53C2\\u6570\\u4EE5\\u751F\\u6210\\u8D1D\\u56FE\\u9884\\u89C8\"], [\"nz-input\", \"\", \"formControlName\", \"originalKayNo\"], [\"nzRequired\", \"\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u65B0\\u8D1D\\u53F7\"], [\"nz-input\", \"\", \"placeholder\", \"\\u65B0\\u8D1D\\u53F7\", \"formControlName\", \"newKayNo\"], [\"nzType\", \"info\", \"nzMessage\", \"\\u683C\\u5F0F\\u8BF4\\u660E\\uFF1A\\u53EF\\u4EE5\\u5305\\u542B\\u6570\\u5B57\\u548C\\u5B57\\u6BCD\\uFF0C\\u7CFB\\u7EDF\\u4F1A\\u81EA\\u52A8\\u5254\\u9664\\u7A7A\\u683C\", \"nzShowIcon\", \"\"], [1, \"restore-position-container\"], [1, \"restore-batch-operations\"], [\"nz-button\", \"\", \"nzSize\", \"small\", 3, \"click\", 4, \"nzSpaceItem\"], [\"class\", \"restore-selection-count\", 4, \"nzSpaceItem\"], [1, \"restore-positions-grid-container\"], [\"class\", \"restore-positions-grid\", 4, \"ngIf\"], [\"class\", \"restore-empty-state\", 4, \"ngIf\"], [1, \"restore-info-section\"], [\"nzType\", \"info\", \"nzShowIcon\", \"\", \"nzMessage\", \"\\u9009\\u62E9\\u8981\\u6062\\u590D\\u7684\\u5DF2\\u5220\\u9664\\u8D1D\\u4F4D\\uFF0C\\u70B9\\u51FB'\\u6062\\u590D\\u9009\\u4E2D\\u8D1D\\u4F4D'\\u6309\\u94AE\\u5373\\u53EF\\u5C06\\u8FD9\\u4E9B\\u8D1D\\u4F4D\\u91CD\\u65B0\\u6DFB\\u52A0\\u5230\\u5F53\\u524D\\u8D1D\\u4E2D\\u3002\\u6062\\u590D\\u540E\\u7684\\u8D1D\\u4F4D\\u5C06\\u91CD\\u65B0\\u663E\\u793A\\u5728\\u8D1D\\u4F4D\\u56FE\\u4E2D\\u3002\"], [\"nz-button\", \"\", \"nzSize\", \"small\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"check-square\", \"nzTheme\", \"outline\"], [\"nz-icon\", \"\", \"nzType\", \"border\", \"nzTheme\", \"outline\"], [\"nz-icon\", \"\", \"nzType\", \"close-square\", \"nzTheme\", \"outline\"], [1, \"restore-selection-count\"], [1, \"restore-positions-grid\"], [\"class\", \"restore-position-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"restore-position-card\", 3, \"click\"], [1, \"position-card-content\"], [1, \"position-id\"], [1, \"position-area\"], [\"nz-icon\", \"\", 1, \"position-check-icon\", 3, \"nzType\", \"nzTheme\"], [1, \"restore-empty-state\"], [\"nzNotFoundImage\", \"simple\", \"nzNotFoundContent\", \"\\u6CA1\\u6709\\u627E\\u5230\\u5DF2\\u5220\\u9664\\u7684\\u8D1D\\u4F4D\"]],\n      template: function VesselKayComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nz-card\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"\\u5BA2\\u6DF7\\u8239\\u8D1D\\u4F4D\\u56FE\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(4, \"nz-card\", 2)(5, \"nz-row\")(6, \"nz-col\", 3)(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_8_listener() {\n            return ctx.showNewKayModal();\n          });\n          i0.ɵɵtext(9, \" \\u65B0\\u5EFA\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_10_listener() {\n            return ctx.showCopyKayModal();\n          });\n          i0.ɵɵtext(11, \" \\u590D\\u5236\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_12_listener() {\n            return ctx.deleteKay();\n          });\n          i0.ɵɵtext(13, \" \\u5220\\u9664\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_14_listener() {\n            return ctx.showRestorePositionModal();\n          });\n          i0.ɵɵtext(15, \" \\u6062\\u590D\\u8D1D\\u4F4D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_16_listener() {\n            return ctx.deletePositions();\n          });\n          i0.ɵɵtext(17, \" \\u5220\\u9664\\u8D1D\\u4F4D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_18_listener() {\n            return ctx.markInvalidPositions();\n          });\n          i0.ɵɵtext(19, \" \\u6807\\u8BB0\\u65E0\\u6548\\u4F4D\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_20_listener() {\n            return ctx.clearInvalidPositions();\n          });\n          i0.ɵɵtext(21, \" \\u53D6\\u6D88\\u65E0\\u6548\\u6807\\u8BB0 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function VesselKayComponent_Template_button_click_22_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(23, \" \\u8FD4\\u56DE \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(24, \"nz-card\", 7)(25, \"nz-row\")(26, \"nz-col\", 3)(27, \"div\", 8)(28, \"div\", 9)(29, \"span\", 10);\n          i0.ɵɵtext(30, \"\\u8D1D\\u53F7\\u9009\\u62E9:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nz-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function VesselKayComponent_Template_nz_select_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedKayNo, $event) || (ctx.selectedKayNo = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function VesselKayComponent_Template_nz_select_ngModelChange_31_listener() {\n            return ctx.onKaySelect();\n          });\n          i0.ɵɵtemplate(32, VesselKayComponent_nz_option_32_Template, 1, 2, \"nz-option\", 12);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(33, \"nz-card\", 13)(34, \"div\", 14);\n          i0.ɵɵlistener(\"mousedown\", function VesselKayComponent_Template_div_mousedown_34_listener($event) {\n            return ctx.onMouseDown($event);\n          })(\"mousemove\", function VesselKayComponent_Template_div_mousemove_34_listener($event) {\n            return ctx.onMouseMove($event);\n          })(\"mouseup\", function VesselKayComponent_Template_div_mouseup_34_listener($event) {\n            return ctx.onMouseUp($event);\n          })(\"mouseleave\", function VesselKayComponent_Template_div_mouseleave_34_listener($event) {\n            return ctx.onMouseLeave($event);\n          })(\"dblclick\", function VesselKayComponent_Template_div_dblclick_34_listener($event) {\n            return ctx.onContainerDoubleClick($event);\n          });\n          i0.ɵɵtemplate(35, VesselKayComponent_div_35_Template, 11, 6, \"div\", 15)(36, VesselKayComponent_div_36_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"nz-modal\", 17);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselKayComponent_Template_nz_modal_nzVisibleChange_37_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isNewKayModalVisible, $event) || (ctx.isNewKayModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselKayComponent_Template_nz_modal_nzOnCancel_37_listener() {\n            return ctx.cancelNewKay();\n          })(\"nzOnOk\", function VesselKayComponent_Template_nz_modal_nzOnOk_37_listener() {\n            return ctx.saveNewKay();\n          });\n          i0.ɵɵtemplate(38, VesselKayComponent_ng_container_38_Template, 59, 17, \"ng-container\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nz-modal\", 19);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselKayComponent_Template_nz_modal_nzVisibleChange_39_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isCopyKayModalVisible, $event) || (ctx.isCopyKayModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselKayComponent_Template_nz_modal_nzOnCancel_39_listener() {\n            return ctx.isCopyKayModalVisible = false;\n          })(\"nzOnOk\", function VesselKayComponent_Template_nz_modal_nzOnOk_39_listener() {\n            return ctx.saveCopyKay();\n          });\n          i0.ɵɵtemplate(40, VesselKayComponent_ng_container_40_Template, 13, 1, \"ng-container\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"nz-modal\", 20);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselKayComponent_Template_nz_modal_nzVisibleChange_41_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isRestorePositionModalVisible, $event) || (ctx.isRestorePositionModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselKayComponent_Template_nz_modal_nzOnCancel_41_listener() {\n            return ctx.cancelRestorePosition();\n          })(\"nzOnOk\", function VesselKayComponent_Template_nz_modal_nzOnOk_41_listener() {\n            return ctx.restoreSelectedPositions();\n          });\n          i0.ɵɵtemplate(42, VesselKayComponent_ng_container_42_Template, 13, 2, \"ng-container\", 18);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(25, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(26, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzType\", \"primary\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(27, _c2));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedKayNo);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.kayList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(28, _c3));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"dragging\", ctx.isDragging);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.kayPositions.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.kayPositions.length === 0 && ctx.selectedKayData);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isNewKayModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 1200);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isCopyKayModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 500);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isRestorePositionModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 800)(\"nzBodyStyle\", i0.ɵɵpureFunction0(29, _c4));\n        }\n      },\n      dependencies: [i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.NgModel, i6.NgForOf, i6.NgIf, i6.NgStyle, i5.FormGroupDirective, i5.FormControlName, i7.NzColDirective, i7.NzRowDirective, i8.NzFormDirective, i8.NzFormItemComponent, i8.NzFormLabelComponent, i8.NzFormControlComponent, i9.NzButtonComponent, i10.ɵNzTransitionPatchDirective, i11.NzWaveDirective, i12.NzInputDirective, i12.NzAutosizeDirective, i13.NzInputNumberComponent, i14.NzAlertComponent, i15.NzOptionComponent, i15.NzSelectComponent, i16.NzCardComponent, i4.NzModalComponent, i4.NzModalContentDirective, i17.NzIconDirective, i18.NzDividerComponent, i19.NzEmptyComponent, i20.NzTagComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n[_nghost-%COMP%]   .title-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .operations-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .selector-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .diagram-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n[_nghost-%COMP%]   .title-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .operations-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .selector-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .diagram-card[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.title-card[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #262626;\\n  line-height: 1.4;\\n}\\n.operations-card[_ngcontent-%COMP%]   .kay-operations[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 0;\\n  flex-wrap: wrap;\\n}\\n.operations-card[_ngcontent-%COMP%]   .kay-operations[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 32px;\\n  padding: 0 16px;\\n  font-size: 14px;\\n  border-radius: 4px;\\n}\\n.operations-card[_ngcontent-%COMP%]   .kay-operations[_ngcontent-%COMP%]   button[nz-button][nzType=\\\"primary\\\"][_ngcontent-%COMP%] {\\n  background: #1890ff;\\n  border-color: #1890ff;\\n}\\n.operations-card[_ngcontent-%COMP%]   .kay-operations[_ngcontent-%COMP%]   button[nz-button][nzType=\\\"primary\\\"][_ngcontent-%COMP%]:hover {\\n  background: #40a9ff;\\n  border-color: #40a9ff;\\n}\\n.operations-card[_ngcontent-%COMP%]   .kay-operations[_ngcontent-%COMP%]   .return-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n.selector-card[_ngcontent-%COMP%]   .kay-selector-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n}\\n.selector-card[_ngcontent-%COMP%]   .kay-selector-container[_ngcontent-%COMP%]   .kay-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.selector-card[_ngcontent-%COMP%]   .kay-selector-container[_ngcontent-%COMP%]   .kay-selector[_ngcontent-%COMP%]   .kay-selector-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #595959;\\n  font-weight: 500;\\n  white-space: nowrap;\\n}\\n.selector-card[_ngcontent-%COMP%]   .kay-selector-container[_ngcontent-%COMP%]   .kay-selector[_ngcontent-%COMP%]   .kay-selector-dropdown[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n.diagram-card[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #fafafa;\\n  border-radius: 6px;\\n  min-height: 500px;\\n  max-height: 700px;\\n  overflow: auto;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  position: relative;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 12px;\\n  background-color: #fff;\\n  border-radius: 4px;\\n  border-left: 4px solid #722ed1;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #722ed1;\\n  font-size: 16px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 4px;\\n  border: 1px solid #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .column-labels-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .column-labels-container[_ngcontent-%COMP%]   .column-labels-spacer[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 24px;\\n  flex-shrink: 0;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .column-labels-container[_ngcontent-%COMP%]   .column-labels-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 0;\\n  padding: 0 16px;\\n  justify-content: start;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .column-labels-container[_ngcontent-%COMP%]   .column-labels-grid[_ngcontent-%COMP%]   .column-label[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #1890ff;\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n  line-height: 1;\\n  text-align: center;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .position-row-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .position-row-container[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0px;\\n  padding-top: 0;\\n  margin-top: 0;\\n  align-items: center;\\n  justify-content: flex-start;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .diagram-with-row-labels[_ngcontent-%COMP%]   .position-row-container[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%]   .row-label[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #1890ff;\\n  box-sizing: border-box;\\n  line-height: 1;\\n  margin: 0;\\n  padding: 0;\\n  flex-shrink: 0;\\n  text-align: center;\\n  vertical-align: middle;\\n  position: relative;\\n  opacity: 1;\\n  visibility: visible;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .performance-info[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  padding: 8px 12px;\\n  background-color: #f0f9ff;\\n  border-radius: 4px;\\n  border-left: 4px solid #1890ff;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .performance-info[_ngcontent-%COMP%]   nz-tag[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #d9d9d9;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  background-color: #fff;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  background-color: #fafafa;\\n  border-bottom: 2px solid #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .corner-cell[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 30px;\\n  border-right: 1px solid #d9d9d9;\\n  background-color: #f5f5f5;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #1890ff;\\n  border-right: 1px solid #d9d9d9;\\n  background-color: #fafafa;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .diagram-body[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .diagram-body[_ngcontent-%COMP%]   .row-headers[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #fafafa;\\n  border-right: 2px solid #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .diagram-body[_ngcontent-%COMP%]   .row-headers[_ngcontent-%COMP%]   .row-header[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #1890ff;\\n  border-bottom: 1px solid #d9d9d9;\\n  background-color: #fafafa;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-diagram-table[_ngcontent-%COMP%]   .diagram-body[_ngcontent-%COMP%]   .row-headers[_ngcontent-%COMP%]   .row-header[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background-color: #fff;\\n  border-radius: 4px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[style*=\\\"overflow-y: auto\\\"][_ngcontent-%COMP%] {\\n  border: 1px solid #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[style*=\\\"overflow-y: auto\\\"][_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[style*=\\\"overflow-y: auto\\\"][_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[style*=\\\"overflow-y: auto\\\"][_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid-container[style*=\\\"overflow-y: auto\\\"][_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .virtual-scroll-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 0;\\n  row-gap: 8px;\\n  padding: 0 0 16px 16px;\\n  position: relative;\\n  justify-content: start;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid.grid-layout[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 0;\\n  row-gap: 8px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid.grid-layout.show-grid-lines[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to right, #e8e8e8 1px, transparent 1px), linear-gradient(to bottom, #e8e8e8 1px, transparent 1px);\\n  background-size: 54px 34px;\\n  background-position: 15px 15px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid.grid-layout.show-grid-lines[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(to right, #d0d0d0 1px, transparent 1px), linear-gradient(to bottom, #d0d0d0 1px, transparent 1px);\\n  background-size: 54px 34px;\\n  background-position: 15px 15px;\\n  pointer-events: none;\\n  opacity: 0.3;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]:not(.grid-layout).show-grid-lines {\\n  background-image: repeating-linear-gradient(0deg, transparent, transparent 33px, #e8e8e8 33px, #e8e8e8 34px), repeating-linear-gradient(90deg, transparent, transparent 53px, #e8e8e8 53px, #e8e8e8 54px);\\n  background-position: 16px 16px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 30px;\\n  border: 2px solid #d9d9d9;\\n  border-radius: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-user-select: none;\\n          user-select: none;\\n  background-color: #ffffff;\\n  color: #000000;\\n  border-color: #d9d9d9;\\n  box-sizing: border-box;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #1890ff;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.selected[_ngcontent-%COMP%] {\\n  background-color: #1890ff;\\n  color: #ffffff;\\n  border-color: #1890ff;\\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.invalid[_ngcontent-%COMP%] {\\n  background-color: #f5e6a3;\\n  color: #8b6914;\\n  border-color: #daa520;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.invalid[_ngcontent-%COMP%]:hover {\\n  background-color: #f0d878;\\n  border-color: #b8860b;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.invalid.selected[_ngcontent-%COMP%] {\\n  background-color: #daa520;\\n  color: #ffffff;\\n  border-color: #b8860b;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border: 2px solid #d9d9d9;\\n  color: transparent;\\n  cursor: not-allowed;\\n  position: relative;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n  border-color: #1890ff;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted.selected[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-color: #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted.truly-deleted[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border: 2px solid #d9d9d9;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted.truly-deleted[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n  border-color: #1890ff;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell.deleted.truly-deleted[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .selection-box[_ngcontent-%COMP%] {\\n  position: fixed;\\n  border: 2px dashed #722ed1;\\n  background-color: rgba(114, 46, 209, 0.1);\\n  pointer-events: none;\\n  z-index: 1000;\\n  border-radius: 2px;\\n  animation: _ngcontent-%COMP%_selection-pulse 1s infinite alternate;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .no-positions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  background-color: #fff;\\n  border-radius: 4px;\\n}\\n@keyframes _ngcontent-%COMP%_selection-pulse {\\n  from {\\n    border-color: #722ed1;\\n    background-color: rgba(114, 46, 209, 0.1);\\n  }\\n  to {\\n    border-color: #9254de;\\n    background-color: rgba(114, 46, 209, 0.2);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_selected-pulse {\\n  0% {\\n    transform: translateY(-2px) scale(1);\\n  }\\n  50% {\\n    transform: translateY(-2px) scale(1.05);\\n  }\\n  100% {\\n    transform: translateY(-2px) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_glow-pulse {\\n  from {\\n    opacity: 0.4;\\n    transform: scale(1);\\n  }\\n  to {\\n    opacity: 0.8;\\n    transform: scale(1.02);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .kay-diagram-container[_ngcontent-%COMP%]   .position-grid[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 25px;\\n    font-size: 10px;\\n  }\\n}\\n.ant-modal-body[_ngcontent-%COMP%]   .ant-form-item[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.ant-modal-body[_ngcontent-%COMP%]   .ant-alert[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n.position-cell.drag-highlight[_ngcontent-%COMP%] {\\n  background-color: #1890ff !important;\\n  color: white !important;\\n  border-color: #1890ff !important;\\n  opacity: 0.7;\\n}\\n.drag-selection-box[_ngcontent-%COMP%] {\\n  position: fixed;\\n  border: 2px dashed #1890ff;\\n  background: rgba(24, 144, 255, 0.1);\\n  pointer-events: none;\\n  z-index: 1000;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%] {\\n  -webkit-user-select: none;\\n          user-select: none;\\n  position: relative;\\n}\\n.restore-position-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  gap: 16px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-search-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding-bottom: 12px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: 8px 0;\\n  background: #fafafa;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%]   .restore-selection-count[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  min-height: 300px;\\n  max-height: 350px;\\n  padding: 8px 0;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));\\n  gap: 8px;\\n  padding: 4px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%] {\\n  border: 2px solid #d9d9d9;\\n  border-radius: 6px;\\n  padding: 8px 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  background: #fff;\\n  min-height: 50px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]:hover {\\n  border-color: #722ed1;\\n  box-shadow: 0 2px 4px rgba(114, 46, 209, 0.1);\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card.selected[_ngcontent-%COMP%] {\\n  border-color: #722ed1;\\n  background: #f9f0ff;\\n  box-shadow: 0 2px 8px rgba(114, 46, 209, 0.2);\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card.selected[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n  color: #722ed1;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 100%;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-id[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 12px;\\n  color: #333;\\n  margin-bottom: 2px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-area[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #999;\\n  margin-bottom: 4px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #d9d9d9;\\n  transition: color 0.2s ease;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-info-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-top: 8px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  text-align: center;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 4px;\\n  margin: 16px 0;\\n  justify-content: center;\\n  max-width: 100%;\\n  overflow-x: auto;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-grid[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 40px;\\n  border: 1px solid #d9d9d9;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 500;\\n  background-color: #fff;\\n  color: #1890ff;\\n  transition: all 0.2s ease;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-grid[_ngcontent-%COMP%]   .position-cell.kay-position[_ngcontent-%COMP%] {\\n  background-color: #e6f7ff;\\n  border-color: #91d5ff;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .kay-grid[_ngcontent-%COMP%]   .position-cell.kay-position[_ngcontent-%COMP%]:hover {\\n  background-color: #bae7ff;\\n  border-color: #40a9ff;\\n  transform: scale(1.05);\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-list[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-list[_ngcontent-%COMP%]   .position-tags[_ngcontent-%COMP%] {\\n  max-height: 120px;\\n  overflow-y: auto;\\n  padding: 8px;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   .kay-diagram[_ngcontent-%COMP%]   .position-list[_ngcontent-%COMP%]   .position-tags[_ngcontent-%COMP%]   nz-tag[_ngcontent-%COMP%] {\\n  margin: 2px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%]   nz-empty[_ngcontent-%COMP%] {\\n  padding: 40px 20px;\\n}\\n.kay-diagram-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n.kay-diagram-container.dragging[_ngcontent-%COMP%] {\\n  cursor: crosshair;\\n}\\n.kay-diagram-container.dragging[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%] {\\n  cursor: crosshair;\\n}\\n.position-cell.selected[_ngcontent-%COMP%] {\\n  background-color: #1890ff !important;\\n  color: white !important;\\n  border-color: #1890ff !important;\\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\\n}\\n.position-cell.drag-highlight[_ngcontent-%COMP%] {\\n  background-color: rgba(24, 144, 255, 0.2) !important;\\n  border-color: #1890ff !important;\\n  transform: scale(1.02);\\n  transition: all 0.2s ease;\\n}\\n.position-cell[_ngcontent-%COMP%]:hover:not(.deleted):not(.invalid) {\\n  background-color: #f0f8ff;\\n  border-color: #40a9ff;\\n  cursor: pointer;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\\n}\\n.position-cell.selected[_ngcontent-%COMP%]:hover {\\n  background-color: #40a9ff !important;\\n  border-color: #40a9ff !important;\\n}\\n.position-cell.invalid[_ngcontent-%COMP%] {\\n  background-color: #daa520 !important;\\n  color: white !important;\\n  border-color: #b8860b !important;\\n  box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);\\n  cursor: not-allowed;\\n}\\n.position-cell.invalid[_ngcontent-%COMP%]:hover {\\n  background-color: #b8860b !important;\\n  border-color: #daa520 !important;\\n}\\n.position-cell.deleted[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #bfbfbf !important;\\n  border-color: #d9d9d9 !important;\\n  cursor: not-allowed;\\n  text-decoration: line-through;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "CwfBaseCrud", "DialogResultEnum", "ModalTypeEnum", "TAS_T_VESSEL_KAY", "TAS_T_VESSEL_KAY_DEDUCT", "i0", "ɵɵelement", "ɵɵproperty", "kay_r1", "kayNo", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "formatNumber", "colLabel_r2", "ɵɵtextInterpolate", "position_r5", "position", "ɵɵlistener", "VesselKayComponent_div_35_div_8_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onPositionClick", "VesselKayComponent_div_35_div_8_Template_div_mouseenter_0_listener", "onPositionMouseEnter", "ɵɵtemplate", "VesselKayComponent_div_35_div_8_span_1_Template", "ɵɵclassMap", "selected", "isDeduct", "dragHighlight", "deleted", "wasDeleted", "ɵɵstyleProp", "gridRow", "gridColumn", "rowLabel_r6", "VesselKayComponent_div_35_div_4_Template", "VesselKayComponent_div_35_div_8_Template", "VesselKayComponent_div_35_div_10_Template", "getGridStyle", "getColumnLabels", "kayPositions", "getRowLabels", "position_r7", "position_r8", "VesselKayComponent_ng_container_38_div_57_div_4_Template", "VesselKayComponent_ng_container_38_div_57_nz_tag_8_Template", "previewKayData", "totalPositions", "columnNum", "row<PERSON>um", "positions", "trackByPositionId", "ɵɵelementContainerStart", "VesselKayComponent_ng_container_38_div_57_Template", "VesselKayComponent_ng_container_38_nz_empty_58_Template", "ɵɵpureFunction0", "_c5", "newKayForm", "_c6", "_c7", "copyKayForm", "VesselKayComponent_ng_container_42_button_4_Template_button_click_0_listener", "_r9", "selectAllRestorePositions", "VesselKayComponent_ng_container_42_button_5_Template_button_click_0_listener", "_r10", "deselectAllRestorePositions", "VesselKayComponent_ng_container_42_button_6_Template_button_click_0_listener", "_r11", "clearRestoreSelection", "selectedDeletedPositions", "length", "deletedPositionOptions", "VesselKayComponent_ng_container_42_div_9_div_1_Template_div_click_0_listener", "position_r13", "_r12", "togglePositionSelection", "value", "ɵɵclassProp", "isPositionSelected", "label", "getPositionArea", "VesselKayComponent_ng_container_42_div_9_div_1_Template", "VesselKayComponent_ng_container_42_button_4_Template", "VesselKayComponent_ng_container_42_button_5_Template", "VesselKayComponent_ng_container_42_button_6_Template", "VesselKayComponent_ng_container_42_span_7_Template", "VesselKayComponent_ng_container_42_div_9_Template", "VesselKayComponent_ng_container_42_div_10_Template", "VesselKayComponent", "constructor", "cwfBusContextService", "gol", "cwfRestfulService", "modal", "mainStore", "deductStore", "vesselId", "vesselNm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON><PERSON>", "kayList", "selectedPositions", "deductPositions", "isNewKayModalVisible", "isCopyKayModalVisible", "isRestorePositionModalVisible", "deletedPositions", "Set", "isDeleting", "isRestoring", "isDataLoading", "isDataLoaded", "isDragging", "dragStartTime", "dragStartX", "dragStartY", "hasMoved", "drag<PERSON><PERSON><PERSON><PERSON>", "moveT<PERSON><PERSON>old", "dataInitializationSteps", "kayListLoaded", "kayDataSelected", "positionsGenerated", "statusSyncCompleted", "onShow", "openParam", "console", "log", "allParams", "showState", "error", "openMainPage", "initForms", "loadKayList", "required", "max<PERSON><PERSON><PERSON>", "pattern", "rowSep", "rowFrom", "columnSep", "columnFrom", "remark", "originalKayNo", "disabled", "newKayNo", "valueChanges", "subscribe", "generatePreviewKayData", "resetDataInitializationSteps", "clear", "requestData", "data", "post", "serviceName", "en", "then", "rps", "ok", "kay<PERSON>ount", "onKaySelect", "completeDataInitialization", "msg", "catch", "loadDeductPositions", "kayId", "id", "updatePositionStyles", "find", "kay", "generateKayPositions", "kayDataId", "kayDataRowtier", "rowtier", "row", "col", "actualRow", "actualCol", "column", "String", "padStart", "rowIndex", "colIndex", "displayRow", "displayCol", "push", "validPositions", "split", "filter", "pos", "trim", "rowtierOriginal", "validPositionsFromRowtier", "allGeneratedPositionsCount", "allGeneratedPositions", "map", "p", "currentDeletedPositionsSetSize", "size", "currentDeletedPositionsArray", "Array", "from", "for<PERSON>ach", "includes", "add", "initializedDeletedPositionsCount", "initializedDeletedPositions", "exists", "has", "delete", "originalPositionsCount", "existingPositionsCount", "trulyDeletedPositionsCount", "neverExistedPositionsCount", "deletedPositionsSetSize", "deletedPositionsArray", "trulyDeletedPositionsList", "neverExistedPositionsList", "addedPositionsCount", "addedPositions", "allPositionsCount", "totalPositionsCount", "validPositionsCount", "deletedPositionsCount", "uiDeletedPositions", "uiTrulyDeletedPositions", "statusSyncCheck", "deletedPositionsInSet", "trulyDeletedInUI", "isSync", "JSON", "stringify", "sort", "setTimeout", "validateAndFixStatusSync", "kayPositionsCount", "deductPositionsCount", "invalidCount", "wasInvalid", "some", "deduct", "totalInvalidPositions", "event", "positionId", "currentSelected", "hasEvent", "stopPropagation", "preventDefault", "timeSinceMouseDown", "Date", "now", "wasSelected", "nowSelected", "totalSelected", "onMouseDown", "button", "target", "positionCell", "closest", "isPositionCell", "getAttribute", "originalTarget", "className", "tagName", "originalTargetText", "textContent", "currentTime", "clientX", "clientY", "startPosition", "currentSelectedCount", "selectedCount", "onMouseMove", "deltaX", "Math", "abs", "deltaY", "distance", "sqrt", "selectStartPositionForDrag", "threshold", "currentX", "currentY", "onMouseUp", "onMouseLeave", "document", "elementFromPoint", "newSelectedCount", "onContainerDoubleClick", "classList", "contains", "clearAllSelections", "toString", "labels", "num", "showNewKayModal", "reset", "patchValue", "cancelNewKay", "formValue", "getRawValue", "saveNewKay", "i", "controls", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "get", "generatedPositions", "generateKayPositionsList", "positionsCount", "join", "success", "deleteKay", "_this", "_asyncToGenerator", "state", "showConfirm", "yes", "kayData", "deletePositions", "_this2", "validationResult", "validateDataLoadingStatus", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "currentRowtier", "existingPositions", "validDeletePositions", "invalidDeletePositions", "totalSelectedPositions", "existingPositionsInRowtier", "info", "kayNos", "originalDeletePositions", "body", "updatedPositions", "originalRowtier", "updatedRowtier", "updatedPositionsCount", "updateDeletedPositionOptions", "deletedPositionsSet", "finally", "showRestorePositionModal", "allPossiblePositions", "generateAllPossiblePositions", "allPossiblePositionsCount", "deletedPositionOptionsCount", "availableRestorePositionsCount", "availableRestorePositions", "opt", "cancelRestorePosition", "trulyDeletedPositions", "identifyTrulyDeletedPositions", "area", "allPositions", "checkDataInitializationComplete", "steps", "isComplete", "kay<PERSON><PERSON><PERSON><PERSON><PERSON>", "kayPositionsLength", "selectedKayDataId", "missingSteps", "deletedPositionsSize", "allStepsCompleted", "setDeletedPositions", "syncStatus", "uiDeletedCount", "uiTrulyDeletedCount", "setDeletedCount", "fixedDeletedPositionsCount", "fixedDeletedPositions", "index", "indexOf", "splice", "item", "restoreSelectedPositions", "validRestorePositions", "invalidRestorePositions", "warn", "originalRestorePositions", "response", "restoredPositions", "showCopyKayModal", "saveCopyKay", "_this3", "regex", "test", "replace", "originalKayData", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "markInvalidPositions", "_this4", "alreadyDeduct", "invalidPositions", "clearInvalidPositions", "_this5", "invalidPositionsCount", "invalidPositionsList", "positionsToUnmark", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearedPositionsCount", "goBack", "openPage", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "CwfRestfulService", "i4", "NzModalService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "VesselKayComponent_Template", "rf", "ctx", "VesselKayComponent_Template_button_click_8_listener", "VesselKayComponent_Template_button_click_10_listener", "Vessel<PERSON>ayComponent_Template_button_click_12_listener", "Vessel<PERSON>ayComponent_Template_button_click_14_listener", "VesselKayComponent_Template_button_click_16_listener", "Vessel<PERSON>ayComponent_Template_button_click_18_listener", "VesselKayComponent_Template_button_click_20_listener", "Vessel<PERSON>ayComponent_Template_button_click_22_listener", "ɵɵtwoWayListener", "VesselKayComponent_Template_nz_select_ngModelChange_31_listener", "ɵɵtwoWayBindingSet", "VesselKayComponent_nz_option_32_Template", "Vessel<PERSON>ayComponent_Template_div_mousedown_34_listener", "Vessel<PERSON>ayComponent_Template_div_mousemove_34_listener", "Vessel<PERSON>ayComponent_Template_div_mouseup_34_listener", "Vessel<PERSON>ayComponent_Template_div_mouseleave_34_listener", "Vessel<PERSON>ayComponent_Template_div_dblclick_34_listener", "VesselKayComponent_div_35_Template", "VesselKayComponent_div_36_Template", "VesselKayComponent_Template_nz_modal_nzVisibleChange_37_listener", "VesselKayComponent_Template_nz_modal_nzOnCancel_37_listener", "VesselKayComponent_Template_nz_modal_nzOnOk_37_listener", "VesselKayComponent_ng_container_38_Template", "VesselKayComponent_Template_nz_modal_nzVisibleChange_39_listener", "VesselKayComponent_Template_nz_modal_nzOnCancel_39_listener", "VesselKayComponent_Template_nz_modal_nzOnOk_39_listener", "VesselKayComponent_ng_container_40_Template", "VesselKayComponent_Template_nz_modal_nzVisibleChange_41_listener", "VesselKayComponent_Template_nz_modal_nzOnCancel_41_listener", "VesselKayComponent_Template_nz_modal_nzOnOk_41_listener", "VesselKayComponent_ng_container_42_Template", "_c0", "_c1", "_c2", "ɵɵtwoWayProperty", "_c3", "_c4"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-kay\\vessel-kay.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-kay\\vessel-kay.component.html"], "sourcesContent": ["import { Component, OnD<PERSON>roy } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport {CwfBaseCrud, CwfBusContextService, DialogResultEnum, ModalTypeEnum} from 'cwf-ng-library';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { TAS_T_VESSEL_KAY } from '@store/TAS/TAS_T_VESSEL_KAY';\r\nimport { TAS_T_VESSEL_KAY_DEDUCT } from '@store/TAS/TAS_T_VESSEL_KAY_DEDUCT';\r\nimport { NzModalService } from 'ng-zorro-antd/modal';\r\n\r\n@Component({\r\n  selector: 'tas-vessel-kay-app',\r\n  templateUrl: './vessel-kay.component.html',\r\n  styleUrls: ['./vessel-kay.component.less']\r\n})\r\nexport class VesselKayComponent extends CwfBase<PERSON>rud implements OnDestroy {\r\n  mainStore = new TAS_T_VESSEL_KAY();\r\n  deductStore = new TAS_T_VESSEL_KAY_DEDUCT();\r\n  vesselId: string = '';\r\n  vesselNm: string = '';\r\n  selectedKayNo: string = '';\r\n  selectedKayData: any = null;\r\n  kayList: any[] = [];\r\n  kayPositions: any[] = [];\r\n  selectedPositions: any[] = [];\r\n  deductPositions: any[] = [];\r\n\r\n  // 模态框相关\r\n  isNewKayModalVisible = false;\r\n  isCopyKayModalVisible = false;\r\n  isRestorePositionModalVisible = false;\r\n  newKayForm: FormGroup;\r\n  copyKayForm: FormGroup;\r\n\r\n  // 恢复贝位功能相关属性\r\n  selectedDeletedPositions: string[] = [];\r\n  deletedPositionOptions: any[] = [];\r\n  deletedPositions: Set<string> = new Set();\r\n\r\n  // 恢复贝位弹出框优化相关属性（已简化，移除筛选功能）\r\n\r\n  // 预览功能相关属性\r\n  previewKayData: any = null;\r\n\r\n  // 防重复操作标志\r\n  private isDeleting = false;\r\n  private isRestoring = false;\r\n\r\n  // 数据加载状态标志 - 修复首次操作API调用失败问题\r\n  isDataLoading = false;\r\n  isDataLoaded = false;\r\n\r\n  // 多选拖拽相关属性\r\n  isDragging = false;\r\n  dragStartTime = 0;\r\n  dragStartX = 0;\r\n  dragStartY = 0;\r\n  hasMoved = false;\r\n  dragThreshold = 150;  // 拖拽时间阈值（毫秒）\r\n  moveThreshold = 5;    // 鼠标移动阈值（像素）\r\n\r\n  // 详细的数据初始化步骤跟踪 - 解决时序问题和竞态条件\r\n  private dataInitializationSteps = {\r\n    kayListLoaded: false,        // 贝位列表加载完成\r\n    kayDataSelected: false,      // 贝位数据选择完成\r\n    positionsGenerated: false,   // 贝位图生成完成\r\n    statusSyncCompleted: false   // 状态同步完成\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  constructor(\r\n    cwfBusContextService: CwfBusContextService,\r\n    private gol: GlobalDataService,\r\n    private cwfRestfulService: CwfRestfulService,\r\n    private modal: NzModalService\r\n  ) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  /**\r\n   * 页面加载后处理\r\n   */\r\n  onShow() {\r\n    this.vesselId = this.openParam['vesselId'] || '';\r\n    this.vesselNm = this.openParam['vesselNm'] || '';\r\n\r\n    console.log('客混船贝图 - 接收到的参数:', {\r\n      vesselId: this.vesselId,\r\n      vesselNm: this.vesselNm,\r\n      allParams: this.openParam\r\n    });\r\n\r\n    if (!this.vesselId) {\r\n      this.showState(ModalTypeEnum.error, '船舶ID参数缺失！');\r\n      this.openMainPage();\r\n      return;\r\n    }\r\n\r\n    this.initForms();\r\n    this.loadKayList();\r\n  }\r\n\r\n  /**\r\n   * 初始化表单\r\n   */\r\n  initForms() {\r\n    this.newKayForm = new FormGroup({\r\n      kayNo: new FormControl('', [Validators.required, Validators.maxLength(12)]),\r\n      rowNum: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      rowSep: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      rowFrom: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      columnNum: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      columnSep: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      columnFrom: new FormControl('', [Validators.required, Validators.pattern(/^\\d+$/)]),\r\n      remark: new FormControl('', [Validators.maxLength(255)])\r\n    });\r\n\r\n\r\n\r\n    this.copyKayForm = new FormGroup({\r\n      originalKayNo: new FormControl({value: '', disabled: true}),\r\n      newKayNo: new FormControl('', [Validators.required, Validators.maxLength(12)])\r\n    });\r\n\r\n    // 监听新建贝表单变化，实时生成预览\r\n    this.newKayForm.valueChanges.subscribe(() => {\r\n      this.generatePreviewKayData();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 加载客混船贝位列表 - 完全修复版本，解决时序问题和竞态条件\r\n   */\r\n  loadKayList() {\r\n    // 重置所有状态\r\n    this.isDataLoading = true;\r\n    this.resetDataInitializationSteps();\r\n\r\n    // 清除之前的数据，确保不会显示缓存的数据\r\n    this.kayList = [];\r\n    this.selectedKayNo = '';\r\n    this.selectedKayData = null;\r\n    this.kayPositions = [];\r\n    this.selectedPositions = [];\r\n    this.deductPositions = [];\r\n    this.deletedPositions.clear();\r\n\r\n    // 按照正确的参数格式构建请求参数\r\n    const requestData = {\r\n      data: {\r\n        vesselId: this.vesselId\r\n      }\r\n    };\r\n\r\n    console.log('客混船贝图 - 请求参数:', requestData);\r\n\r\n    this.cwfRestfulService.post('/vessel-kay/list', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        console.log('客混船贝图 - API响应:', rps);\r\n        if (rps.ok === true) {\r\n          this.kayList = rps.data || [];\r\n          console.log('客混船贝图 - 加载贝位列表成功:', {\r\n            vesselId: this.vesselId,\r\n            kayCount: this.kayList.length,\r\n            kayList: this.kayList\r\n          });\r\n\r\n          // 标记贝位列表加载完成\r\n          this.dataInitializationSteps.kayListLoaded = true;\r\n\r\n          if (this.kayList.length > 0) {\r\n            this.selectedKayNo = this.kayList[0].kayNo;\r\n            this.onKaySelect();\r\n            // 注意：不再使用固定延迟，而是依赖各个步骤的完成状态\r\n          } else {\r\n            console.log('客混船贝图 - 该船舶没有客混船贝位数据');\r\n            this.selectedKayNo = '';\r\n            this.selectedKayData = null;\r\n            // 没有数据时，直接完成初始化\r\n            this.dataInitializationSteps.kayDataSelected = true;\r\n            this.dataInitializationSteps.positionsGenerated = true;\r\n            this.dataInitializationSteps.statusSyncCompleted = true;\r\n            this.completeDataInitialization();\r\n          }\r\n        } else {\r\n          console.log('客混船贝图 - API错误:', rps.msg);\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n          this.isDataLoading = false;\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('客混船贝图 - API调用失败:', error);\r\n        this.showState(ModalTypeEnum.error, '加载客混船贝位列表失败');\r\n        this.isDataLoading = false;\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 加载特殊位置数据\r\n   */\r\n  loadDeductPositions() {\r\n    if (!this.selectedKayData) return;\r\n\r\n    const requestData = {\r\n      kayId: this.selectedKayData.id,\r\n      vesselId: this.vesselId\r\n    };\r\n\r\n    this.cwfRestfulService.post('/vessel-kay-deduct/list', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok === true) {\r\n          this.deductPositions = rps.data || [];\r\n          this.updatePositionStyles();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 贝号选择事件 - 修复版本，集成状态管理\r\n   */\r\n  onKaySelect() {\r\n    this.selectedKayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n    if (this.selectedKayData) {\r\n      console.log('客混船贝图 - 贝位数据选择完成:', {\r\n        kayNo: this.selectedKayData.kayNo,\r\n        kayId: this.selectedKayData.id\r\n      });\r\n\r\n      // 标记贝位数据选择完成\r\n      this.dataInitializationSteps.kayDataSelected = true;\r\n\r\n      this.generateKayPositions();\r\n      this.loadDeductPositions();\r\n\r\n      // 检查是否可以完成初始化\r\n      this.completeDataInitialization();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成客混船贝位图 - 从右上角开始，向左向下排列\r\n   * 右上角第一个贝位编号为0202，向左排列（02列方向），向下排列（行方向）\r\n   */\r\n  generateKayPositions() {\r\n    if (!this.selectedKayData) return;\r\n\r\n    const positions = [];\r\n    const data = this.selectedKayData;\r\n\r\n    console.log('客混船贝图 - 生成贝位数据调试信息:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      selectedKayData: data,\r\n      kayDataId: data.id,\r\n      kayDataRowtier: data.rowtier,\r\n      rowNum: data.rowNum,\r\n      columnNum: data.columnNum\r\n    });\r\n\r\n\r\n\r\n    // 生成行列贝位 - 从右上角开始布局\r\n    for (let row = 0; row < data.rowNum; row++) {\r\n      for (let col = 0; col < data.columnNum; col++) {\r\n        const actualRow = data.rowFrom + (row * data.rowSep);\r\n        // 从右上角开始，列数从右到左递减\r\n        // 确保右上角第一个贝位编号为0202\r\n        const actualCol = data.columnFrom + ((data.columnNum - 1 - col) * data.columnSep);\r\n\r\n        const position = {\r\n          row: actualRow,\r\n          column: actualCol,\r\n          position: `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`,\r\n          selected: false,\r\n          isDeduct: false,\r\n          rowIndex: row,  // 行索引，用于布局\r\n          colIndex: col,  // 列索引，用于布局\r\n          displayRow: actualRow,  // 显示用的行号\r\n          displayCol: actualCol,   // 显示用的列号\r\n          gridRow: row + 1,  // CSS Grid行位置（1-based）\r\n          gridColumn: col + 1  // CSS Grid列位置（1-based）\r\n        };\r\n\r\n        positions.push(position);\r\n      }\r\n    }\r\n\r\n\r\n\r\n    // 重新设计贝位状态管理逻辑 - 修复删除状态显示与恢复功能不一致问题\r\n    // 关键修复：正确初始化和管理deletedPositions集合\r\n\r\n    if (data.rowtier) {\r\n      const validPositions = data.rowtier.split(',').filter(pos => pos.trim() !== '');\r\n\r\n      console.log('客混船贝图 - 开始贝位状态分析:', {\r\n        selectedKayNo: this.selectedKayNo,\r\n        kayDataId: data.id,\r\n        rowtierOriginal: data.rowtier,\r\n        validPositionsFromRowtier: validPositions,\r\n        allGeneratedPositionsCount: positions.length,\r\n        allGeneratedPositions: positions.map(p => p.position),\r\n        currentDeletedPositionsSetSize: this.deletedPositions.size,\r\n        currentDeletedPositionsArray: Array.from(this.deletedPositions)\r\n      });\r\n\r\n      // 关键修复：识别真正被删除的贝位\r\n      // 如果deletedPositions集合为空（首次加载），需要根据rowtier和生成的贝位来推断删除状态\r\n      if (this.deletedPositions.size === 0) {\r\n        // 首次加载：将不在rowtier中但在生成贝位中的贝位标记为已删除\r\n        positions.forEach(position => {\r\n          if (!validPositions.includes(position.position)) {\r\n            // 这些贝位在配置范围内但不在rowtier中，应该被认为是已删除的\r\n            this.deletedPositions.add(position.position);\r\n          }\r\n        });\r\n\r\n        console.log('客混船贝图 - 首次加载，初始化deletedPositions集合:', {\r\n          initializedDeletedPositionsCount: this.deletedPositions.size,\r\n          initializedDeletedPositions: Array.from(this.deletedPositions)\r\n        });\r\n      }\r\n\r\n      // 标记每个贝位的状态\r\n      positions.forEach(position => {\r\n        if (validPositions.includes(position.position)) {\r\n          // 当前存在的贝位\r\n          position.deleted = false;\r\n          position.exists = true;\r\n          position.wasDeleted = false;\r\n          // 如果这个贝位之前被标记为已删除，现在恢复了，从已删除集合中移除\r\n          if (this.deletedPositions.has(position.position)) {\r\n            this.deletedPositions.delete(position.position);\r\n            console.log('客混船贝图 - 贝位已恢复，从deletedPositions中移除:', position.position);\r\n          }\r\n        } else if (this.deletedPositions.has(position.position)) {\r\n          // 真正被删除的贝位（在已删除集合中）\r\n          position.deleted = true;\r\n          position.exists = false;\r\n          position.wasDeleted = true; // 标记为真正被删除\r\n        } else {\r\n          // 从未存在的贝位（不在rowtier中，也不在deletedPositions中）\r\n          position.deleted = true;\r\n          position.exists = false;\r\n          position.wasDeleted = false; // 标记为从未存在\r\n        }\r\n      });\r\n\r\n      console.log('客混船贝图 - 贝位状态标记结果:', {\r\n        originalPositionsCount: positions.length,\r\n        validPositionsFromRowtier: validPositions,\r\n        existingPositionsCount: positions.filter(p => !p.deleted && p.exists).length,\r\n        trulyDeletedPositionsCount: positions.filter(p => p.deleted && p.wasDeleted).length,\r\n        neverExistedPositionsCount: positions.filter(p => p.deleted && !p.wasDeleted).length,\r\n        deletedPositionsSetSize: this.deletedPositions.size,\r\n        deletedPositionsArray: Array.from(this.deletedPositions),\r\n        trulyDeletedPositionsList: positions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\r\n        neverExistedPositionsList: positions.filter(p => p.deleted && !p.wasDeleted).map(p => p.position)\r\n      });\r\n    } else {\r\n      // 如果没有rowtier数据，需要特殊处理\r\n      console.log('客混船贝图 - 警告：没有rowtier数据，将所有生成的贝位标记为已删除');\r\n\r\n      // 如果deletedPositions集合为空，将所有生成的贝位添加到已删除集合\r\n      if (this.deletedPositions.size === 0) {\r\n        positions.forEach(position => {\r\n          this.deletedPositions.add(position.position);\r\n        });\r\n        console.log('客混船贝图 - 无rowtier数据时初始化deletedPositions集合:', {\r\n          addedPositionsCount: positions.length,\r\n          addedPositions: positions.map(p => p.position)\r\n        });\r\n      }\r\n\r\n      positions.forEach(position => {\r\n        if (this.deletedPositions.has(position.position)) {\r\n          // 真正被删除的贝位\r\n          position.deleted = true;\r\n          position.exists = false;\r\n          position.wasDeleted = true;\r\n        } else {\r\n          // 从未存在的贝位\r\n          position.deleted = true;\r\n          position.exists = false;\r\n          position.wasDeleted = false;\r\n        }\r\n      });\r\n\r\n      console.log('客混船贝图 - 没有rowtier数据，根据已删除集合标记状态:', {\r\n        allPositionsCount: positions.length,\r\n        deletedPositionsSetSize: this.deletedPositions.size,\r\n        deletedPositionsArray: Array.from(this.deletedPositions)\r\n      });\r\n    }\r\n\r\n    // 保留所有贝位用于显示（包括已删除的）\r\n    this.kayPositions = positions;\r\n    this.selectedPositions = [];\r\n\r\n    console.log('客混船贝图 - 最终贝位数据和状态同步验证:', {\r\n      totalPositionsCount: this.kayPositions.length,\r\n      validPositionsCount: this.kayPositions.filter(p => !p.deleted).length,\r\n      deletedPositionsCount: this.kayPositions.filter(p => p.deleted).length,\r\n      trulyDeletedPositionsCount: this.kayPositions.filter(p => p.deleted && p.wasDeleted).length,\r\n      neverExistedPositionsCount: this.kayPositions.filter(p => p.deleted && !p.wasDeleted).length,\r\n      deletedPositionsSetSize: this.deletedPositions.size,\r\n      deletedPositionsArray: Array.from(this.deletedPositions),\r\n      // 关键验证：UI显示的删除状态贝位是否与deletedPositions集合同步\r\n      uiDeletedPositions: this.kayPositions.filter(p => p.deleted).map(p => p.position),\r\n      uiTrulyDeletedPositions: this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\r\n      statusSyncCheck: {\r\n        deletedPositionsInSet: Array.from(this.deletedPositions),\r\n        trulyDeletedInUI: this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position),\r\n        isSync: JSON.stringify(Array.from(this.deletedPositions).sort()) === JSON.stringify(this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position).sort())\r\n      }\r\n    });\r\n\r\n    // 标记贝位图生成完成\r\n    this.dataInitializationSteps.positionsGenerated = true;\r\n    console.log('客混船贝图 - 贝位图生成完成');\r\n\r\n    // 关键修复：在贝位生成完成后进行状态同步验证\r\n    // 延迟执行，确保所有状态标记完成\r\n    setTimeout(() => {\r\n      this.validateAndFixStatusSync();\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * 更新位置样式（标记特殊位置）\r\n   */\r\n  updatePositionStyles() {\r\n    console.log('客混船贝图 - 开始更新位置样式:', {\r\n      kayPositionsCount: this.kayPositions.length,\r\n      deductPositionsCount: this.deductPositions.length,\r\n      deductPositions: this.deductPositions\r\n    });\r\n\r\n    let invalidCount = 0;\r\n    this.kayPositions.forEach(pos => {\r\n      const wasInvalid = pos.isDeduct;\r\n      pos.isDeduct = this.deductPositions.some(deduct =>\r\n        deduct.rowtier && deduct.rowtier.includes(pos.position)\r\n      );\r\n\r\n      if (pos.isDeduct) {\r\n        invalidCount++;\r\n        if (!wasInvalid) {\r\n          console.log('客混船贝图 - 贝位标记为无效:', pos.position);\r\n        }\r\n      } else if (wasInvalid) {\r\n        console.log('客混船贝图 - 贝位取消无效标记:', pos.position);\r\n      }\r\n    });\r\n\r\n    console.log('客混船贝图 - 位置样式更新完成:', {\r\n      totalInvalidPositions: invalidCount\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 贝位点击事件 - 参考vessel-bay的多选模式\r\n   */\r\n  onPositionClick(position: any, event?: MouseEvent) {\r\n    console.log('客混船贝图 - 贝位点击事件触发:', {\r\n      positionId: position.position,\r\n      currentSelected: position.selected,\r\n      isDragging: this.isDragging,\r\n      dragStartTime: this.dragStartTime,\r\n      hasEvent: !!event\r\n    });\r\n\r\n    // 无效位置、已删除的贝位或不存在的贝位不能被选中\r\n    if (position.isDeduct || position.deleted || !position.exists) {\r\n      console.log('客混船贝图 - 无效位置、已删除或不存在的贝位，忽略点击:', {\r\n        positionId: position.position,\r\n        isDeduct: position.isDeduct,\r\n        deleted: position.deleted,\r\n        exists: position.exists\r\n      });\r\n      return;\r\n    }\r\n\r\n    // 阻止事件冒泡和默认行为，避免触发容器的mousedown事件\r\n    if (event) {\r\n      event.stopPropagation();\r\n      event.preventDefault();\r\n    }\r\n\r\n    // 检查是否是真正的拖拽操作（鼠标移动了）\r\n    const timeSinceMouseDown = Date.now() - this.dragStartTime;\r\n\r\n    // 如果刚刚结束拖拽操作且鼠标确实移动了，忽略点击事件\r\n    if (this.dragStartTime > 0 && this.hasMoved && timeSinceMouseDown < 300) {\r\n      console.log('客混船贝图 - 刚结束拖拽操作，忽略点击事件，时间差:', timeSinceMouseDown);\r\n      return;\r\n    }\r\n\r\n    // 如果正在进行真正的拖拽（鼠标移动了），忽略点击事件\r\n    if (this.isDragging && this.hasMoved) {\r\n      console.log('客混船贝图 - 正在进行真正的拖拽，忽略点击事件');\r\n      return;\r\n    }\r\n\r\n    // 重置拖拽状态，确保单击操作能正常处理\r\n    if (this.dragStartTime > 0 && !this.hasMoved) {\r\n      console.log('客混船贝图 - 检测到单击操作，重置拖拽状态以正常处理点击');\r\n      this.isDragging = false;\r\n      this.dragStartTime = 0;\r\n    }\r\n\r\n    // 记录原始状态\r\n    const wasSelected = position.selected;\r\n\r\n    // 默认多选模式：普通点击也支持多选\r\n    position.selected = !position.selected;\r\n\r\n    if (position.selected) {\r\n      // 添加到选中列表\r\n      if (!this.selectedPositions.find(p => p.position === position.position)) {\r\n        this.selectedPositions.push(position);\r\n      }\r\n    } else {\r\n      // 从选中列表移除\r\n      this.selectedPositions = this.selectedPositions.filter(p => p.position !== position.position);\r\n    }\r\n\r\n    console.log('客混船贝图 - 贝位选择状态更新:', {\r\n      positionId: position.position,\r\n      wasSelected: wasSelected,\r\n      nowSelected: position.selected,\r\n      totalSelected: this.selectedPositions.length\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 鼠标按下事件（在贝位图容器上）\r\n   */\r\n  onMouseDown(event: MouseEvent) {\r\n    // 只处理左键\r\n    if (event.button !== 0) return;\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    // 修复点击区域敏感性问题：使用closest方法向上查找贝位容器\r\n    // 这样无论点击贝位的哪个区域（包括数字文本），都能正确识别\r\n    const positionCell = target.closest('.position-cell') as HTMLElement;\r\n    const isPositionCell = !!positionCell;\r\n    const positionId = positionCell?.getAttribute('data-position-id');\r\n\r\n    console.log('客混船贝图 - 鼠标按下事件（修复点击区域敏感性）:', {\r\n      originalTarget: target.className || target.tagName,\r\n      originalTargetText: target.textContent?.trim(),\r\n      positionCell: positionCell?.className,\r\n      isPositionCell: isPositionCell,\r\n      positionId: positionId,\r\n      currentTime: Date.now(),\r\n      clientX: event.clientX,\r\n      clientY: event.clientY\r\n    });\r\n\r\n    // 记录拖拽开始时间和位置\r\n    this.dragStartTime = Date.now();\r\n    this.dragStartX = event.clientX;\r\n    this.dragStartY = event.clientY;\r\n    this.hasMoved = false;\r\n\r\n    // 检查是否点击在贝位上\r\n    if (isPositionCell && positionId) {\r\n      // 点击在贝位上，立即选中该贝位并启动拖拽准备模式\r\n      const startPosition = this.kayPositions.find(p => p.position === positionId);\r\n      if (startPosition) {\r\n        // 检查是否可以选中（不是无效位置、已删除或不存在的贝位）\r\n        if (!startPosition.isDeduct && !startPosition.deleted && startPosition.exists) {\r\n          console.log('客混船贝图 - 选中拖拽起始贝位（修复后）:', {\r\n            positionId: positionId,\r\n            wasSelected: startPosition.selected,\r\n            currentSelectedCount: this.selectedPositions.length\r\n          });\r\n\r\n          // 不在mousedown中立即选中贝位，避免与click事件冲突\r\n          // 只记录起始贝位，等待鼠标移动时再处理拖拽选择\r\n          console.log('客混船贝图 - 记录起始贝位，等待确定是单击还是拖拽操作:', {\r\n            positionId: positionId,\r\n            currentSelected: startPosition.selected\r\n          });\r\n        } else {\r\n          console.log('客混船贝图 - 起始贝位不可选中:', {\r\n            positionId: positionId,\r\n            isDeduct: startPosition.isDeduct,\r\n            deleted: startPosition.deleted,\r\n            exists: startPosition.exists\r\n          });\r\n        }\r\n      } else {\r\n        console.log('客混船贝图 - 未找到对应的贝位数据:', positionId);\r\n      }\r\n\r\n      console.log('客混船贝图 - 点击在贝位上，拖拽选择模式已启动:', {\r\n        positionId: positionId,\r\n        isDragging: this.isDragging,\r\n        selectedCount: this.selectedPositions.length\r\n      });\r\n\r\n      // 阻止默认行为，避免文本选择等\r\n      event.preventDefault();\r\n    } else {\r\n      // 点击在空白区域\r\n      console.log('客混船贝图 - 点击在空白区域');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 鼠标移动事件\r\n   */\r\n  onMouseMove(event: MouseEvent) {\r\n    // 如果没有按下鼠标，不处理\r\n    if (this.dragStartTime === 0) return;\r\n\r\n    // 计算移动距离\r\n    const deltaX = Math.abs(event.clientX - this.dragStartX);\r\n    const deltaY = Math.abs(event.clientY - this.dragStartY);\r\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n    // 如果移动距离超过阈值，标记为已移动并启动拖拽模式\r\n    if (distance > this.moveThreshold) {\r\n      this.hasMoved = true;\r\n\r\n      // 如果还没有启动拖拽模式，现在启动并选中起始贝位\r\n      if (!this.isDragging) {\r\n        this.isDragging = true;\r\n\r\n        // 在拖拽开始时选中起始贝位\r\n        this.selectStartPositionForDrag();\r\n\r\n        console.log('客混船贝图 - 鼠标移动超过阈值，启动拖拽选择模式:', {\r\n          distance: distance,\r\n          threshold: this.moveThreshold,\r\n          isDragging: this.isDragging\r\n        });\r\n      }\r\n    }\r\n\r\n    // 如果已经开始拖拽，处理拖拽逻辑\r\n    if (this.isDragging) {\r\n      console.log('客混船贝图 - 拖拽中...', {\r\n        currentX: event.clientX,\r\n        currentY: event.clientY,\r\n        distance: distance\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 鼠标释放事件\r\n   */\r\n  onMouseUp(event: MouseEvent) {\r\n    console.log('客混船贝图 - 鼠标释放事件:', {\r\n      isDragging: this.isDragging,\r\n      hasMoved: this.hasMoved,\r\n      dragStartTime: this.dragStartTime,\r\n      totalSelected: this.selectedPositions.length\r\n    });\r\n\r\n    // 如果正在拖拽，完成拖拽选择操作\r\n    if (this.isDragging) {\r\n      console.log('客混船贝图 - 拖拽选择完成，选中贝位数量:', this.selectedPositions.length);\r\n    }\r\n\r\n    // 清理拖拽状态\r\n    this.isDragging = false;\r\n    this.hasMoved = false;\r\n    this.dragStartTime = 0;\r\n  }\r\n\r\n  /**\r\n   * 鼠标离开事件\r\n   */\r\n  onMouseLeave(event: MouseEvent) {\r\n    if (this.isDragging) {\r\n      this.onMouseUp(event);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 在拖拽开始时选中起始贝位\r\n   */\r\n  selectStartPositionForDrag() {\r\n    // 找到鼠标按下时的目标元素\r\n    const target = document.elementFromPoint(this.dragStartX, this.dragStartY) as HTMLElement;\r\n    if (!target) return;\r\n\r\n    // 使用closest方法向上查找贝位容器\r\n    const positionCell = target.closest('.position-cell') as HTMLElement;\r\n    if (!positionCell) return;\r\n\r\n    const positionId = positionCell.getAttribute('data-position-id');\r\n    if (!positionId) return;\r\n\r\n    // 找到对应的贝位数据\r\n    const startPosition = this.kayPositions.find(p => p.position === positionId);\r\n    if (!startPosition) return;\r\n\r\n    // 检查是否可以选中（不是无效位置、已删除或不存在的贝位）\r\n    if (startPosition.isDeduct || startPosition.deleted || !startPosition.exists) {\r\n      console.log('客混船贝图 - 拖拽起始贝位不可选中:', {\r\n        positionId: positionId,\r\n        isDeduct: startPosition.isDeduct,\r\n        deleted: startPosition.deleted,\r\n        exists: startPosition.exists\r\n      });\r\n      return;\r\n    }\r\n\r\n    // 选中起始贝位\r\n    if (!startPosition.selected) {\r\n      startPosition.selected = true;\r\n      if (!this.selectedPositions.find(p => p.position === startPosition.position)) {\r\n        this.selectedPositions.push(startPosition);\r\n        console.log('客混船贝图 - 拖拽开始时选中起始贝位:', {\r\n          positionId: positionId,\r\n          newSelectedCount: this.selectedPositions.length\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 贝位鼠标进入事件 - 拖拽连续选择\r\n   */\r\n  onPositionMouseEnter(position: any, event: MouseEvent) {\r\n    console.log('客混船贝图 - 鼠标进入贝位事件触发:', {\r\n      positionId: position.position,\r\n      isDragging: this.isDragging,\r\n      currentSelected: position.selected,\r\n      exists: position.exists,\r\n      deleted: position.deleted,\r\n      isDeduct: position.isDeduct\r\n    });\r\n\r\n    // 只在拖拽状态下处理\r\n    if (!this.isDragging) {\r\n      console.log('客混船贝图 - 非拖拽状态，忽略鼠标进入事件');\r\n      return;\r\n    }\r\n\r\n    // 无效位置、已删除的贝位或不存在的贝位不能被选中\r\n    if (position.isDeduct || position.deleted || !position.exists) {\r\n      console.log('客混船贝图 - 无效位置、已删除或不存在的贝位，跳过拖拽选择:', {\r\n        positionId: position.position,\r\n        isDeduct: position.isDeduct,\r\n        deleted: position.deleted,\r\n        exists: position.exists\r\n      });\r\n      return;\r\n    }\r\n\r\n    // 如果贝位还未被选中，则选中它\r\n    if (!position.selected) {\r\n      position.selected = true;\r\n\r\n      // 添加到选中列表（避免重复添加）\r\n      if (!this.selectedPositions.find(p => p.position === position.position)) {\r\n        this.selectedPositions.push(position);\r\n        console.log('客混船贝图 - 拖拽选中贝位成功:', {\r\n          positionId: position.position,\r\n          totalSelected: this.selectedPositions.length,\r\n          selectedPositions: this.selectedPositions.map(p => p.position)\r\n        });\r\n      } else {\r\n        console.log('客混船贝图 - 贝位已在选中列表中，跳过重复添加:', position.position);\r\n      }\r\n    } else {\r\n      console.log('客混船贝图 - 贝位已被选中，跳过重复选择:', position.position);\r\n    }\r\n\r\n    // 阻止事件冒泡\r\n    event.stopPropagation();\r\n  }\r\n\r\n  /**\r\n   * 容器双击事件 - 双击空白区域取消所有选中\r\n   */\r\n  onContainerDoubleClick(event: MouseEvent) {\r\n    const target = event.target as HTMLElement;\r\n    const isPositionCell = target.classList.contains('position-cell');\r\n\r\n    console.log('客混船贝图 - 容器双击事件:', {\r\n      target: target.className,\r\n      isPositionCell: isPositionCell,\r\n      currentSelectedCount: this.selectedPositions.length\r\n    });\r\n\r\n    // 只有点击在空白区域（非贝位元素）时才清除选中状态\r\n    if (!isPositionCell) {\r\n      console.log('客混船贝图 - 双击空白区域，清除所有选中状态');\r\n      this.clearAllSelections();\r\n    } else {\r\n      console.log('客混船贝图 - 双击在贝位上，不清除选中状态');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 清除所有选择\r\n   */\r\n  clearAllSelections() {\r\n    console.log('客混船贝图 - 清除所有选中状态，当前选中数量:', this.selectedPositions.length);\r\n    this.kayPositions.forEach(p => p.selected = false);\r\n    this.selectedPositions = [];\r\n    console.log('客混船贝图 - 选中状态已清除');\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * 获取网格样式 - 支持按行换行显示\r\n   */\r\n  getGridStyle() {\r\n    if (!this.selectedKayData) {\r\n      return {};\r\n    }\r\n\r\n    return {\r\n      'display': 'grid',\r\n      'grid-template-columns': `repeat(${this.selectedKayData.columnNum}, 50px)`,\r\n      'gap': '0',\r\n      'justify-content': 'start',\r\n      '--column-count': this.selectedKayData.columnNum.toString()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取列标识数组 - 与贝位图列顺序一致（从右到左排列）\r\n   */\r\n  getColumnLabels(): number[] {\r\n    if (!this.selectedKayData) return [];\r\n\r\n    const labels = [];\r\n    const data = this.selectedKayData;\r\n\r\n    // 按照贝位生成的顺序：从右上角开始，向左排列\r\n    for (let col = 0; col < data.columnNum; col++) {\r\n      // 与贝位生成逻辑保持一致：从右上角开始，列数从右到左递减\r\n      const actualCol = data.columnFrom + ((data.columnNum - 1 - col) * data.columnSep);\r\n      labels.push(actualCol);\r\n    }\r\n    return labels;\r\n  }\r\n\r\n  /**\r\n   * 获取行标识数组 - 与贝位图行顺序一致（从上到下排列）\r\n   */\r\n  getRowLabels(): number[] {\r\n    if (!this.selectedKayData) return [];\r\n\r\n    const labels = [];\r\n    const data = this.selectedKayData;\r\n\r\n    // 按照贝位生成的顺序：从上到下排列\r\n    for (let row = 0; row < data.rowNum; row++) {\r\n      // 与贝位生成逻辑保持一致：从上到下递增\r\n      const actualRow = data.rowFrom + (row * data.rowSep);\r\n      labels.push(actualRow);\r\n    }\r\n    return labels;\r\n  }\r\n\r\n  /**\r\n   * 格式化数字为两位字符串（补零）\r\n   * 用于解决模板中String()函数不可用的问题\r\n   */\r\n  formatNumber(num: number): string {\r\n    return num.toString().padStart(2, '0');\r\n  }\r\n\r\n  /**\r\n   * 新建贝\r\n   */\r\n  showNewKayModal() {\r\n    this.newKayForm.reset();\r\n    // 设置默认值\r\n    this.newKayForm.patchValue({\r\n      rowNum: 2,\r\n      rowSep: 1,\r\n      rowFrom: 3,\r\n      columnNum: 3,\r\n      columnSep: 1,\r\n      columnFrom: 7\r\n    });\r\n    this.isNewKayModalVisible = true;\r\n    // 生成初始预览\r\n    this.generatePreviewKayData();\r\n  }\r\n\r\n  /**\r\n   * 取消新建贝\r\n   */\r\n  cancelNewKay() {\r\n    this.isNewKayModalVisible = false;\r\n    this.previewKayData = null;\r\n  }\r\n\r\n  /**\r\n   * 生成预览贝图数据\r\n   */\r\n  generatePreviewKayData() {\r\n    const formValue = this.newKayForm.getRawValue();\r\n\r\n    if (!formValue.rowNum || !formValue.columnNum) {\r\n      this.previewKayData = null;\r\n      return;\r\n    }\r\n\r\n    const positions = [];\r\n\r\n    // 基于行列参数生成所有贝位编号\r\n    for (let row = 0; row < formValue.rowNum; row++) {\r\n      for (let col = 0; col < formValue.columnNum; col++) {\r\n        const actualRow = formValue.rowFrom + (row * formValue.rowSep);\r\n        // 从右上角开始，列数从右到左递减\r\n        const actualCol = formValue.columnFrom + ((formValue.columnNum - 1 - col) * formValue.columnSep);\r\n\r\n        const position = {\r\n          position: `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`,\r\n          gridRow: row + 1,  // CSS Grid行位置（1-based）\r\n          gridColumn: col + 1  // CSS Grid列位置（1-based）\r\n        };\r\n\r\n        positions.push(position);\r\n      }\r\n    }\r\n\r\n    this.previewKayData = {\r\n      rowNum: formValue.rowNum,\r\n      columnNum: formValue.columnNum,\r\n      totalPositions: positions.length,\r\n      positions: positions\r\n    };\r\n\r\n    console.log('客混船贝图 - 预览数据生成:', {\r\n      formValue: formValue,\r\n      previewKayData: this.previewKayData\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 保存新建贝\r\n   */\r\n  saveNewKay() {\r\n    for (const i in this.newKayForm.controls) {\r\n      this.newKayForm.controls[i].markAsDirty();\r\n      this.newKayForm.controls[i].updateValueAndValidity();\r\n    }\r\n\r\n    if (this.newKayForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    // 检查贝号唯一性\r\n    const kayNo = this.newKayForm.get('kayNo')?.value;\r\n    if (this.kayList.some(kay => kay.kayNo === kayNo)) {\r\n      this.showState(ModalTypeEnum.error, '贝号已存在，请重新输入！');\r\n      return;\r\n    }\r\n\r\n    // 生成贝位列表（rowtier字段）\r\n    const formValue = this.newKayForm.getRawValue();\r\n    const generatedPositions = this.generateKayPositionsList(formValue);\r\n\r\n    console.log('客混船贝图 - 新建贝位生成的贝位列表:', {\r\n      formValue: formValue,\r\n      generatedPositions: generatedPositions,\r\n      positionsCount: generatedPositions.length\r\n    });\r\n\r\n    const requestData = {\r\n      vesselId: this.vesselId,\r\n      kayNo: formValue.kayNo,\r\n      rowtier: generatedPositions.join(','), // 自动生成的贝位列表\r\n      rowNum: formValue.rowNum,\r\n      rowSep: formValue.rowSep,\r\n      rowFrom: formValue.rowFrom,\r\n      columnNum: formValue.columnNum,\r\n      columnSep: formValue.columnSep,\r\n      columnFrom: formValue.columnFrom,\r\n      remark: formValue.remark || ''\r\n    };\r\n\r\n    console.log('客混船贝图 - 新建贝位请求数据:', requestData);\r\n\r\n    this.cwfRestfulService.post('/vessel-kay', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.isNewKayModalVisible = false;\r\n          this.loadKayList();\r\n          this.selectedKayNo = kayNo;\r\n          setTimeout(() => this.onKaySelect(), 100);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 删除贝\r\n   */\r\n  async deleteKay() {\r\n    if (!this.selectedKayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要删除的贝！');\r\n      return;\r\n    }\r\n\r\n    const state = await this.showConfirm('确认', '是否确认删除？');\r\n    if (state !== DialogResultEnum.yes) {\r\n      return;\r\n    }\r\n\r\n    const kayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n    if (!kayData) return;\r\n\r\n    this.cwfRestfulService.delete(`/vessel-kay/${kayData.id}`, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '删除成功！');\r\n          this.loadKayList();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 生成客混船贝位列表（用于rowtier字段）\r\n   * 基于表单参数计算所有可能的贝位编号\r\n   */\r\n  generateKayPositionsList(formValue: any): string[] {\r\n    const positions = [];\r\n\r\n    // 基于行列参数生成所有贝位编号\r\n    for (let row = 0; row < formValue.rowNum; row++) {\r\n      for (let col = 0; col < formValue.columnNum; col++) {\r\n        const actualRow = formValue.rowFrom + (row * formValue.rowSep);\r\n        // 从右上角开始，列数从右到左递减\r\n        const actualCol = formValue.columnFrom + ((formValue.columnNum - 1 - col) * formValue.columnSep);\r\n\r\n        // 生成贝位编号：行号+列号，都补齐为2位数\r\n        const positionId = `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`;\r\n        positions.push(positionId);\r\n      }\r\n    }\r\n\r\n    return positions;\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * 删除贝位 - 修复版本，解决首次操作API调用失败问题\r\n   */\r\n  async deletePositions() {\r\n    // 防止重复调用\r\n    if (this.isDeleting) {\r\n      console.log('客混船贝图 - 删除操作正在进行中，忽略重复调用');\r\n      return;\r\n    }\r\n\r\n    // 关键修复：使用统一的数据验证方法\r\n    const validationResult = this.validateDataLoadingStatus();\r\n    if (!validationResult.isValid) {\r\n      this.showState(ModalTypeEnum.error, validationResult.errorMessage!);\r\n      return;\r\n    }\r\n\r\n    if (this.selectedPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\r\n      return;\r\n    }\r\n\r\n    const kayData = this.selectedKayData!; // 验证通过后可以安全使用\r\n\r\n    // 获取要删除的贝位编号列表\r\n    const deletePositions = this.selectedPositions.map(p => p.position);\r\n\r\n    // 关键优化：只删除rowtier中实际存在的贝位\r\n    const currentRowtier = kayData.rowtier || '';\r\n    const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\r\n    const validDeletePositions = deletePositions.filter(pos => existingPositions.includes(pos));\r\n    const invalidDeletePositions = deletePositions.filter(pos => !existingPositions.includes(pos));\r\n\r\n    console.log('客混船贝图 - 删除贝位验证结果:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      totalSelectedPositions: deletePositions.length,\r\n      existingPositionsInRowtier: existingPositions,\r\n      validDeletePositions: validDeletePositions,\r\n      invalidDeletePositions: invalidDeletePositions,\r\n      kayDataId: kayData.id,\r\n      kayDataRowtier: kayData.rowtier\r\n    });\r\n\r\n    // 如果没有有效的删除贝位，提示用户\r\n    if (validDeletePositions.length === 0) {\r\n      this.showState(ModalTypeEnum.info, '选中的贝位都不存在于当前贝中，无法删除！');\r\n      return;\r\n    }\r\n\r\n    // 直接删除，不再显示确认弹出框\r\n    console.log('客混船贝图 - 直接删除贝位:', {\r\n      totalSelectedPositions: deletePositions.length,\r\n      validDeletePositions: validDeletePositions,\r\n      invalidDeletePositions: invalidDeletePositions\r\n    });\r\n\r\n    // 设置删除标志，防止重复调用\r\n    this.isDeleting = true;\r\n\r\n    // 根据接口文档规范构建请求数据，只传递有效的删除贝位\r\n    const requestData = {\r\n      kayId: kayData.id,  // 客混船舶贝的ID\r\n      kayNos: validDeletePositions  // 只传递有效的删除贝位编号列表\r\n    };\r\n\r\n    console.log('客混船贝图 - 删除贝位请求数据:', {\r\n      requestData: requestData,\r\n      originalDeletePositions: deletePositions,\r\n      validDeletePositions: validDeletePositions,\r\n      invalidDeletePositions: invalidDeletePositions\r\n    });\r\n\r\n    // 使用正确的接口路径和HTTP方法\r\n    this.cwfRestfulService.delete('/vessel-kay/vessel/kay/batch-delete-positions', this.gol.serviceName['tas'].en, { body: requestData })\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, `成功删除 ${validDeletePositions.length} 个贝位！`);\r\n\r\n          // 只将有效删除的贝位添加到已删除集合\r\n          validDeletePositions.forEach(positionId => {\r\n            this.deletedPositions.add(positionId);\r\n          });\r\n\r\n          // 关键修复：同步更新selectedKayData的rowtier字段，移除已删除的贝位\r\n          if (this.selectedKayData.rowtier) {\r\n            const currentRowtier = this.selectedKayData.rowtier;\r\n            const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\r\n            const updatedPositions = existingPositions.filter(pos => !validDeletePositions.includes(pos));\r\n            this.selectedKayData.rowtier = updatedPositions.join(',');\r\n\r\n            console.log('客混船贝图 - 同步更新rowtier字段:', {\r\n              originalRowtier: currentRowtier,\r\n              deletedPositions: validDeletePositions,\r\n              updatedRowtier: this.selectedKayData.rowtier,\r\n              existingPositionsCount: existingPositions.length,\r\n              updatedPositionsCount: updatedPositions.length\r\n            });\r\n          }\r\n\r\n          // 更新UI状态：标记被删除的贝位\r\n          this.kayPositions.forEach(position => {\r\n            if (validDeletePositions.includes(position.position)) {\r\n              position.deleted = true;\r\n              position.selected = false; // 取消选中状态\r\n              position.exists = false;   // 标记为不存在（已被删除）\r\n              position.wasDeleted = true; // 标记为真正被删除\r\n            }\r\n          });\r\n\r\n          // 更新已删除贝位的下拉选项\r\n          this.updateDeletedPositionOptions();\r\n\r\n          // 清除选择状态\r\n          this.clearAllSelections();\r\n\r\n          // 仅更新本地数据状态，不重新加载页面\r\n          // 从selectedPositions中移除已删除的贝位\r\n          this.selectedPositions = this.selectedPositions.filter(pos =>\r\n            !validDeletePositions.includes(pos.position)\r\n          );\r\n\r\n          // 触发视图更新\r\n          this.updatePositionStyles();\r\n\r\n          console.log('客混船贝图 - 删除操作完成:', {\r\n            deletedPositions: validDeletePositions,\r\n            deletedPositionsSet: Array.from(this.deletedPositions),\r\n            deletedPositionsSetSize: this.deletedPositions.size,\r\n            updatedRowtier: this.selectedKayData.rowtier\r\n          });\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg || '删除失败');\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('客混船贝图 - 删除贝位失败:', error);\r\n        this.showState(ModalTypeEnum.error, '删除贝位失败，请重试');\r\n      })\r\n      .finally(() => {\r\n        // 清除删除标志\r\n        this.isDeleting = false;\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 显示恢复贝位模态框 - 修复版本，解决删除状态显示与恢复功能不一致问题\r\n   */\r\n  showRestorePositionModal() {\r\n    if (!this.selectedKayData) {\r\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\r\n      return;\r\n    }\r\n\r\n    console.log('客混船贝图 - 显示恢复贝位模态框，开始状态验证...');\r\n\r\n    // 关键修复：先进行状态同步验证和修复\r\n    this.validateAndFixStatusSync();\r\n\r\n    // 然后更新已删除贝位选项，使用新的数据比较机制\r\n    this.updateDeletedPositionOptions();\r\n\r\n    // 检查是否有真正可以恢复的贝位\r\n    if (this.deletedPositionOptions.length === 0) {\r\n      // 提供更详细的提示信息\r\n      const kayData = this.selectedKayData;\r\n      const currentRowtier = kayData.rowtier || '';\r\n      const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\r\n      const allPossiblePositions = this.generateAllPossiblePositions(kayData);\r\n\r\n      console.log('客混船贝图 - 无可恢复贝位详细信息:', {\r\n        selectedKayNo: this.selectedKayNo,\r\n        deletedPositionsSetSize: this.deletedPositions.size,\r\n        deletedPositionsArray: Array.from(this.deletedPositions),\r\n        existingPositionsCount: existingPositions.length,\r\n        existingPositions: existingPositions,\r\n        allPossiblePositionsCount: allPossiblePositions.length,\r\n        allPossiblePositions: allPossiblePositions,\r\n        deletedPositionOptionsCount: this.deletedPositionOptions.length\r\n      });\r\n\r\n      if (this.deletedPositions.size === 0) {\r\n        this.showState(ModalTypeEnum.info, '当前没有已删除的贝位记录！');\r\n      } else {\r\n        this.showState(ModalTypeEnum.info, '当前没有可以恢复的贝位！\\n已删除的贝位可能不属于当前贝的配置范围。');\r\n      }\r\n      return;\r\n    }\r\n\r\n    // 清空之前的选择\r\n    this.selectedDeletedPositions = [];\r\n\r\n    // 显示模态框\r\n    this.isRestorePositionModalVisible = true;\r\n\r\n    console.log('客混船贝图 - 显示恢复贝位模态框:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      availableRestorePositionsCount: this.deletedPositionOptions.length,\r\n      availableRestorePositions: this.deletedPositionOptions.map(opt => opt.value)\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 取消恢复贝位操作\r\n   */\r\n  cancelRestorePosition() {\r\n    this.isRestorePositionModalVisible = false;\r\n    this.selectedDeletedPositions = [];\r\n  }\r\n\r\n  /**\r\n   * 更新已删除贝位的选项列表 - 优化版本，基于数据比较机制\r\n   */\r\n  updateDeletedPositionOptions() {\r\n    if (!this.selectedKayData) {\r\n      this.deletedPositionOptions = [];\r\n      return;\r\n    }\r\n\r\n    // 1. 获取当前贝页面中已存在的贝位列表（从rowtier字段解析）\r\n    const currentRowtier = this.selectedKayData.rowtier || '';\r\n    const existingPositions = currentRowtier.split(',').filter(pos => pos.trim() !== '');\r\n\r\n    // 2. 获取根据配置参数计算出来的全部可能贝位列表\r\n    const allPossiblePositions = this.generateAllPossiblePositions(this.selectedKayData);\r\n\r\n    // 3. 通过数据比较确定真正被删除的贝位\r\n    const trulyDeletedPositions = this.identifyTrulyDeletedPositions(\r\n      allPossiblePositions,\r\n      existingPositions,\r\n      this.deletedPositions\r\n    );\r\n\r\n    // 4. 生成恢复列表选项\r\n    this.deletedPositionOptions = trulyDeletedPositions.map(positionId => ({\r\n      label: positionId,\r\n      value: positionId,\r\n      area: this.getPositionArea(positionId)\r\n    }));\r\n\r\n    console.log('客混船贝图 - 恢复贝位数据比较结果:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      kayDataId: this.selectedKayData.id,\r\n      currentRowtier: currentRowtier,\r\n      existingPositionsCount: existingPositions.length,\r\n      existingPositions: existingPositions,\r\n      allPossiblePositionsCount: allPossiblePositions.length,\r\n      allPossiblePositions: allPossiblePositions,\r\n      deletedPositionsSetSize: this.deletedPositions.size,\r\n      deletedPositionsArray: Array.from(this.deletedPositions),\r\n      trulyDeletedPositionsCount: trulyDeletedPositions.length,\r\n      trulyDeletedPositions: trulyDeletedPositions,\r\n      deletedPositionOptionsCount: this.deletedPositionOptions.length\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 根据配置参数生成所有可能的贝位编号列表\r\n   */\r\n  generateAllPossiblePositions(kayData: any): string[] {\r\n    const allPositions: string[] = [];\r\n\r\n    // 使用与generateKayPositions相同的算法生成所有可能的贝位\r\n    for (let row = 0; row < kayData.rowNum; row++) {\r\n      for (let col = 0; col < kayData.columnNum; col++) {\r\n        const actualRow = kayData.rowFrom + (row * kayData.rowSep);\r\n        // 从右上角开始，列数从右到左递减\r\n        const actualCol = kayData.columnFrom + ((kayData.columnNum - 1 - col) * kayData.columnSep);\r\n\r\n        const positionId = `${String(actualRow).padStart(2, '0')}${String(actualCol).padStart(2, '0')}`;\r\n        allPositions.push(positionId);\r\n      }\r\n    }\r\n\r\n    return allPositions;\r\n  }\r\n\r\n  /**\r\n   * 通过数据比较确定真正被删除的贝位\r\n   */\r\n  identifyTrulyDeletedPositions(\r\n    allPossiblePositions: string[],\r\n    existingPositions: string[],\r\n    deletedPositionsSet: Set<string>\r\n  ): string[] {\r\n    const trulyDeletedPositions: string[] = [];\r\n\r\n    // 遍历所有可能的贝位\r\n    for (const positionId of allPossiblePositions) {\r\n      // 判断条件：\r\n      // 1. 不在当前已存在的贝位中（说明当前不存在）\r\n      // 2. 在deletedPositions集合中（说明曾经被删除过）\r\n      if (!existingPositions.includes(positionId) && deletedPositionsSet.has(positionId)) {\r\n        trulyDeletedPositions.push(positionId);\r\n      }\r\n    }\r\n\r\n    return trulyDeletedPositions.sort(); // 排序便于查看\r\n  }\r\n\r\n  /**\r\n   * 检查数据初始化是否完成 - 解决时序问题和竞态条件\r\n   */\r\n  private checkDataInitializationComplete(): boolean {\r\n    const steps = this.dataInitializationSteps;\r\n    const isComplete = steps.kayListLoaded && steps.kayDataSelected &&\r\n                      steps.positionsGenerated && steps.statusSyncCompleted;\r\n\r\n    console.log('客混船贝图 - 数据初始化步骤检查:', {\r\n      kayListLoaded: steps.kayListLoaded,\r\n      kayDataSelected: steps.kayDataSelected,\r\n      positionsGenerated: steps.positionsGenerated,\r\n      statusSyncCompleted: steps.statusSyncCompleted,\r\n      isComplete: isComplete\r\n    });\r\n\r\n    return isComplete;\r\n  }\r\n\r\n  /**\r\n   * 完成数据初始化 - 统一的初始化完成管理\r\n   */\r\n  private completeDataInitialization(): void {\r\n    if (this.checkDataInitializationComplete()) {\r\n      this.isDataLoaded = true;\r\n      this.isDataLoading = false;\r\n      console.log('客混船贝图 - 所有数据初始化完成，可以进行操作');\r\n    } else {\r\n      console.log('客混船贝图 - 数据初始化尚未完成，等待其他步骤');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 重置数据初始化状态 - 用于数据重新加载\r\n   */\r\n  private resetDataInitializationSteps(): void {\r\n    this.dataInitializationSteps = {\r\n      kayListLoaded: false,\r\n      kayDataSelected: false,\r\n      positionsGenerated: false,\r\n      statusSyncCompleted: false\r\n    };\r\n    this.isDataLoaded = false;\r\n    console.log('客混船贝图 - 数据初始化状态已重置');\r\n  }\r\n\r\n  /**\r\n   * 验证数据加载状态 - 修复首次操作API调用失败问题（增强版）\r\n   */\r\n  validateDataLoadingStatus(): { isValid: boolean; errorMessage?: string } {\r\n    console.log('客混船贝图 - 验证数据加载状态（增强版）:', {\r\n      isDataLoading: this.isDataLoading,\r\n      isDataLoaded: this.isDataLoaded,\r\n      selectedKayNo: this.selectedKayNo,\r\n      selectedKayData: this.selectedKayData,\r\n      kayListLength: this.kayList.length,\r\n      kayPositionsLength: this.kayPositions.length,\r\n      selectedKayDataId: this.selectedKayData?.id,\r\n      dataInitializationSteps: this.dataInitializationSteps\r\n    });\r\n\r\n    // 0. 检查数据是否正在加载\r\n    if (this.isDataLoading) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '数据正在加载中，请稍后重试！'\r\n      };\r\n    }\r\n\r\n    // 1. 检查详细的数据初始化步骤\r\n    if (!this.checkDataInitializationComplete()) {\r\n      const steps = this.dataInitializationSteps;\r\n      let missingSteps = [];\r\n      if (!steps.kayListLoaded) missingSteps.push('贝位列表加载');\r\n      if (!steps.kayDataSelected) missingSteps.push('贝位数据选择');\r\n      if (!steps.positionsGenerated) missingSteps.push('贝位图生成');\r\n      if (!steps.statusSyncCompleted) missingSteps.push('状态同步');\r\n\r\n      return {\r\n        isValid: false,\r\n        errorMessage: `数据初始化未完成，缺少步骤：${missingSteps.join('、')}，请稍后重试！`\r\n      };\r\n    }\r\n\r\n    // 2. 检查基础数据是否加载\r\n    if (this.kayList.length === 0) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '贝位列表数据未加载完成，请稍后重试！'\r\n      };\r\n    }\r\n\r\n    // 2. 检查是否选中了贝号\r\n    if (!this.selectedKayNo) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '请先选择贝号！'\r\n      };\r\n    }\r\n\r\n    // 3. 检查selectedKayData是否有效\r\n    if (!this.selectedKayData) {\r\n      // 尝试重新获取\r\n      this.selectedKayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n      if (!this.selectedKayData) {\r\n        return {\r\n          isValid: false,\r\n          errorMessage: '未找到对应的贝位数据！请刷新页面后重试。'\r\n        };\r\n      }\r\n    }\r\n\r\n    // 4. 检查kayData.id是否有效\r\n    if (!this.selectedKayData.id) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '贝位数据ID无效！请刷新页面后重试。'\r\n      };\r\n    }\r\n\r\n    // 5. 检查贝位图数据是否生成\r\n    if (this.kayPositions.length === 0) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '贝位图数据未生成！请稍后重试或刷新页面。'\r\n      };\r\n    }\r\n\r\n    // 6. 检查deletedPositions集合是否已初始化（通过状态同步完成标志）\r\n    if (!this.dataInitializationSteps.statusSyncCompleted) {\r\n      return {\r\n        isValid: false,\r\n        errorMessage: '贝位状态同步未完成！请稍后重试。'\r\n      };\r\n    }\r\n\r\n    console.log('客混船贝图 - 数据加载状态验证通过（增强版）:', {\r\n      kayDataId: this.selectedKayData.id,\r\n      kayNo: this.selectedKayData.kayNo,\r\n      kayPositionsLength: this.kayPositions.length,\r\n      deletedPositionsSize: this.deletedPositions.size,\r\n      allStepsCompleted: this.checkDataInitializationComplete()\r\n    });\r\n\r\n    return { isValid: true };\r\n  }\r\n\r\n  /**\r\n   * 验证和修复删除状态与恢复功能的同步问题\r\n   */\r\n  validateAndFixStatusSync(): void {\r\n    if (!this.selectedKayData) {\r\n      console.log('客混船贝图 - 状态同步验证：没有选中的贝数据');\r\n      return;\r\n    }\r\n\r\n    console.log('客混船贝图 - 开始状态同步验证和修复...');\r\n\r\n    // 1. 获取当前UI中显示为删除状态的贝位\r\n    const uiDeletedPositions = this.kayPositions.filter(p => p.deleted).map(p => p.position);\r\n    const uiTrulyDeletedPositions = this.kayPositions.filter(p => p.deleted && p.wasDeleted).map(p => p.position);\r\n\r\n    // 2. 获取deletedPositions集合中的贝位\r\n    const setDeletedPositions = Array.from(this.deletedPositions);\r\n\r\n    // 3. 检查同步状态\r\n    const syncStatus = {\r\n      uiDeletedCount: uiDeletedPositions.length,\r\n      uiTrulyDeletedCount: uiTrulyDeletedPositions.length,\r\n      setDeletedCount: setDeletedPositions.length,\r\n      isSync: JSON.stringify(uiTrulyDeletedPositions.sort()) === JSON.stringify(setDeletedPositions.sort())\r\n    };\r\n\r\n    console.log('客混船贝图 - 状态同步验证结果:', {\r\n      uiDeletedPositions: uiDeletedPositions,\r\n      uiTrulyDeletedPositions: uiTrulyDeletedPositions,\r\n      setDeletedPositions: setDeletedPositions,\r\n      syncStatus: syncStatus\r\n    });\r\n\r\n    // 4. 如果不同步，进行修复\r\n    if (!syncStatus.isSync) {\r\n      console.log('客混船贝图 - 检测到状态不同步，开始修复...');\r\n\r\n      // 修复策略：以UI显示的真正删除状态为准，更新deletedPositions集合\r\n      this.deletedPositions.clear();\r\n      uiTrulyDeletedPositions.forEach(positionId => {\r\n        this.deletedPositions.add(positionId);\r\n      });\r\n\r\n      console.log('客混船贝图 - 状态同步修复完成:', {\r\n        fixedDeletedPositionsCount: this.deletedPositions.size,\r\n        fixedDeletedPositions: Array.from(this.deletedPositions)\r\n      });\r\n\r\n      // 重新更新恢复选项\r\n      this.updateDeletedPositionOptions();\r\n    } else {\r\n      console.log('客混船贝图 - 状态同步验证通过，无需修复');\r\n    }\r\n\r\n    // 标记状态同步完成\r\n    this.dataInitializationSteps.statusSyncCompleted = true;\r\n    console.log('客混船贝图 - 状态同步完成');\r\n\r\n    // 检查是否可以完成整体初始化\r\n    this.completeDataInitialization();\r\n  }\r\n\r\n  /**\r\n   * 获取贝位所属区域\r\n   */\r\n  getPositionArea(positionId: string): string {\r\n    // 客混船贝图中所有贝位都在同一区域，这里返回默认值\r\n    return 'kay';\r\n  }\r\n\r\n  /**\r\n   * 检查贝位是否被选中\r\n   */\r\n  isPositionSelected(positionId: string): boolean {\r\n    return this.selectedDeletedPositions.includes(positionId);\r\n  }\r\n\r\n  /**\r\n   * 切换贝位选中状态\r\n   */\r\n  togglePositionSelection(positionId: string) {\r\n    const index = this.selectedDeletedPositions.indexOf(positionId);\r\n    if (index > -1) {\r\n      this.selectedDeletedPositions.splice(index, 1);\r\n    } else {\r\n      this.selectedDeletedPositions.push(positionId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 全选已删除贝位\r\n   */\r\n  selectAllRestorePositions() {\r\n    this.selectedDeletedPositions = [...this.deletedPositionOptions.map(item => item.value)];\r\n  }\r\n\r\n  /**\r\n   * 反选已删除贝位\r\n   */\r\n  deselectAllRestorePositions() {\r\n    const currentSelected = new Set(this.selectedDeletedPositions);\r\n    this.selectedDeletedPositions = this.deletedPositionOptions\r\n      .map(item => item.value)\r\n      .filter(id => !currentSelected.has(id));\r\n  }\r\n\r\n  /**\r\n   * 清空选择\r\n   */\r\n  clearRestoreSelection() {\r\n    this.selectedDeletedPositions = [];\r\n  }\r\n\r\n  /**\r\n   * 跟踪函数，用于优化列表渲染性能\r\n   */\r\n  trackByPositionId(index: number, item: any): string {\r\n    return item.value;\r\n  }\r\n\r\n  /**\r\n   * 恢复选中的已删除贝位 - 修复版本，解决首次操作API调用失败问题\r\n   */\r\n  restoreSelectedPositions() {\r\n    // 防止重复调用\r\n    if (this.isRestoring) {\r\n      console.log('客混船贝图 - 恢复操作正在进行中，忽略重复调用');\r\n      return;\r\n    }\r\n\r\n    // 关键修复：使用统一的数据验证方法\r\n    const validationResult = this.validateDataLoadingStatus();\r\n    if (!validationResult.isValid) {\r\n      this.showState(ModalTypeEnum.error, validationResult.errorMessage!);\r\n      return;\r\n    }\r\n\r\n    if (this.selectedDeletedPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.error, '请选择要恢复的贝位！');\r\n      return;\r\n    }\r\n\r\n    const kayData = this.selectedKayData!; // 验证通过后可以安全使用\r\n\r\n    // 关键优化：验证要恢复的贝位是否真正在已删除集合中\r\n    const validRestorePositions = this.selectedDeletedPositions.filter(pos => this.deletedPositions.has(pos));\r\n    const invalidRestorePositions = this.selectedDeletedPositions.filter(pos => !this.deletedPositions.has(pos));\r\n\r\n    console.log('客混船贝图 - 恢复贝位验证结果:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      totalSelectedPositions: this.selectedDeletedPositions.length,\r\n      deletedPositionsSet: Array.from(this.deletedPositions),\r\n      validRestorePositions: validRestorePositions,\r\n      invalidRestorePositions: invalidRestorePositions,\r\n      kayDataId: kayData.id,\r\n      kayDataRowtier: kayData.rowtier\r\n    });\r\n\r\n    // 如果没有有效的恢复贝位，提示用户\r\n    if (validRestorePositions.length === 0) {\r\n      this.showState(ModalTypeEnum.info, '选中的贝位都不在已删除列表中，无法恢复！');\r\n      return;\r\n    }\r\n\r\n    // 如果有无效的恢复贝位，提示用户\r\n    if (invalidRestorePositions.length > 0) {\r\n      console.warn('客混船贝图 - 警告：以下贝位不在已删除集合中:', invalidRestorePositions);\r\n    }\r\n\r\n    // 设置恢复标志，防止重复调用\r\n    this.isRestoring = true;\r\n\r\n    // 根据接口文档规范构建请求数据，只传递有效的恢复贝位\r\n    const requestData = {\r\n      kayId: kayData.id,  // 客混船贝的ID\r\n      kayNos: validRestorePositions  // 只传递有效的恢复贝位编号列表\r\n    };\r\n\r\n    console.log('客混船贝图 - 恢复贝位请求数据:', {\r\n      requestData: requestData,\r\n      originalRestorePositions: this.selectedDeletedPositions,\r\n      validRestorePositions: validRestorePositions,\r\n      invalidRestorePositions: invalidRestorePositions\r\n    });\r\n\r\n    // 调用批量插入贝位接口\r\n    this.cwfRestfulService.post('/vessel-kay/vessel/kay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en)\r\n      .then((response: any) => {\r\n        console.log('客混船贝图 - 恢复贝位响应:', response);\r\n        if (response && response.ok) {\r\n          this.showState(ModalTypeEnum.success, `成功恢复 ${validRestorePositions.length} 个贝位！`);\r\n\r\n          // 只将有效恢复的贝位从已删除集合中移除\r\n          validRestorePositions.forEach(positionId => {\r\n            this.deletedPositions.delete(positionId);\r\n          });\r\n\r\n          // 更新UI状态：标记恢复的贝位\r\n          this.kayPositions.forEach(position => {\r\n            if (validRestorePositions.includes(position.position)) {\r\n              position.deleted = false;\r\n              position.exists = true;    // 标记为存在（已恢复）\r\n              position.wasDeleted = false; // 标记为不再是已删除状态\r\n            }\r\n          });\r\n\r\n          // 清空选中的已删除贝位\r\n          this.selectedDeletedPositions = [];\r\n\r\n          // 更新已删除贝位的下拉选项\r\n          this.updateDeletedPositionOptions();\r\n\r\n          // 重新加载贝位列表，确保数据同步\r\n          this.loadKayList();\r\n\r\n          // 延迟重新选择当前贝位，确保数据加载完成\r\n          setTimeout(() => {\r\n            this.onKaySelect();\r\n          }, 200);\r\n\r\n          // 关闭模态框\r\n          this.isRestorePositionModalVisible = false;\r\n\r\n          console.log('客混船贝图 - 恢复操作完成:', {\r\n            restoredPositions: validRestorePositions,\r\n            deletedPositionsSet: Array.from(this.deletedPositions),\r\n            deletedPositionsSetSize: this.deletedPositions.size\r\n          });\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, response?.msg || '恢复失败！');\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('客混船贝图 - 恢复贝位失败:', error);\r\n        this.showState(ModalTypeEnum.error, '恢复贝位失败！');\r\n      })\r\n      .finally(() => {\r\n        // 清除恢复标志\r\n        this.isRestoring = false;\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 复制贝\r\n   */\r\n  showCopyKayModal() {\r\n    if (!this.selectedKayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要复制的贝位信息！');\r\n      return;\r\n    }\r\n\r\n    this.copyKayForm.reset();\r\n    this.copyKayForm.patchValue({\r\n      originalKayNo: this.selectedKayNo\r\n    });\r\n    this.isCopyKayModalVisible = true;\r\n  }\r\n\r\n  /**\r\n   * 保存复制贝\r\n   */\r\n  async saveCopyKay() {\r\n    for (const i in this.copyKayForm.controls) {\r\n      this.copyKayForm.controls[i].markAsDirty();\r\n      this.copyKayForm.controls[i].updateValueAndValidity();\r\n    }\r\n\r\n    if (this.copyKayForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    const newKayNo = this.copyKayForm.get('newKayNo')?.value;\r\n\r\n    // 验证格式 - 允许数字和字母\r\n    const regex = /^[a-zA-Z0-9]+$/;\r\n    if (!regex.test(newKayNo.replace(/\\s/g, ''))) {\r\n      this.showState(ModalTypeEnum.error, '新贝号格式不正确，只能包含数字和字母，请检查后重新输入！');\r\n      return;\r\n    }\r\n\r\n    // 检查唯一性\r\n    if (this.kayList.some(kay => kay.kayNo === newKayNo)) {\r\n      const state = await this.showConfirm('确认', '新贝号已存在，是否覆盖？');\r\n      if (state !== DialogResultEnum.yes) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    const originalKayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n    if (!originalKayData) return;\r\n\r\n    // 使用正确的接口路径，根据错误信息使用targetKayNo作为查询参数\r\n    const apiPath = `/vessel-kay/vessel/${this.vesselId}/kay/${this.selectedKayNo}/copy?targetKayNo=${encodeURIComponent(newKayNo)}`;\r\n\r\n    this.cwfRestfulService.post(apiPath, {}, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '复制成功！');\r\n          this.isCopyKayModalVisible = false;\r\n          this.loadKayList();\r\n          this.selectedKayNo = newKayNo;\r\n          setTimeout(() => this.onKaySelect(), 100);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 标记无效位置 - 优化版本，去掉确认弹出框\r\n   */\r\n  async markInvalidPositions() {\r\n    if (!this.selectedKayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要标记无效的贝！');\r\n      return;\r\n    }\r\n\r\n    if (this.selectedPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.error, '请选择要标记无效的贝位！');\r\n      return;\r\n    }\r\n\r\n    // 检查是否已经标记过无效\r\n    const alreadyDeduct = this.selectedPositions.some(pos => pos.isDeduct);\r\n    if (alreadyDeduct) {\r\n      this.showState(ModalTypeEnum.error, '所选的贝位中包含已经标记过无效的位置！');\r\n      return;\r\n    }\r\n\r\n    // 直接执行标记操作，不再显示确认弹出框\r\n    console.log('客混船贝图 - 直接标记无效位置:', {\r\n      selectedPositions: this.selectedPositions.map(p => p.position),\r\n      selectedKayNo: this.selectedKayNo\r\n    });\r\n\r\n    const kayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n    if (!kayData) return;\r\n\r\n    const invalidPositions = this.selectedPositions.map(p => p.position).join(',');\r\n    const requestData = {\r\n      kayId: kayData.id,\r\n      vesselId: this.vesselId,\r\n      rowtier: invalidPositions\r\n    };\r\n\r\n    this.cwfRestfulService.post('/vessel-kay-deduct', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          console.log('客混船贝图 - 标记无效位置API调用成功');\r\n\r\n          // 立即更新本地状态，提供即时视觉反馈\r\n          this.selectedPositions.forEach(pos => {\r\n            pos.isDeduct = true;\r\n            console.log('客混船贝图 - 本地标记贝位为无效:', pos.position);\r\n          });\r\n\r\n          this.showState(ModalTypeEnum.success, '标记成功！');\r\n\r\n          // 重新加载无效位置数据以确保数据一致性\r\n          this.loadDeductPositions();\r\n\r\n          // 清除选中状态\r\n          this.clearAllSelections();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * 取消无效标记 - 修复版本，自动识别已标记为无效的贝位\r\n   */\r\n  async clearInvalidPositions() {\r\n    if (!this.selectedKayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要操作的贝！');\r\n      return;\r\n    }\r\n\r\n    // 修复：自动识别当前贝中所有已标记为无效的贝位\r\n    const invalidPositions = this.kayPositions.filter(pos => pos.isDeduct);\r\n\r\n    console.log('客混船贝图 - 检查无效标记贝位:', {\r\n      selectedKayNo: this.selectedKayNo,\r\n      totalPositions: this.kayPositions.length,\r\n      invalidPositions: invalidPositions.map(p => ({ position: p.position, isDeduct: p.isDeduct })),\r\n      deductPositions: this.deductPositions\r\n    });\r\n\r\n    if (invalidPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.info, '当前贝中没有已标记为无效的贝位！');\r\n      return;\r\n    }\r\n\r\n    // 直接执行取消标记操作，不再显示确认弹出框\r\n    console.log('客混船贝图 - 直接取消无效标记:', {\r\n      invalidPositionsCount: invalidPositions.length,\r\n      invalidPositionsList: invalidPositions.map(p => p.position)\r\n    });\r\n\r\n    const kayData = this.kayList.find(kay => kay.kayNo === this.selectedKayNo);\r\n    if (!kayData) return;\r\n\r\n    // 修复：使用自动识别的无效贝位列表\r\n    const positionsToUnmark = invalidPositions.map(p => p.position).join(',');\r\n\r\n    console.log('客混船贝图 - 取消无效标记请求数据:', {\r\n      kayId: kayData.id,\r\n      vesselId: this.vesselId,\r\n      positionsToUnmark: positionsToUnmark,\r\n      invalidPositionsCount: invalidPositions.length,\r\n      correctApiPath: `/vessel-kay-deduct/clear/${kayData.id}`\r\n    });\r\n\r\n    // 修复：使用正确的接口路径和HTTP方法\r\n    // 接口：DELETE /vessel-kay-deduct/clear/{kayId}\r\n    // 该接口会清除指定贝位下的所有无效位置标记\r\n    this.cwfRestfulService.delete(`/vessel-kay-deduct/clear/${kayData.id}`, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '取消无效标记成功！');\r\n          this.loadDeductPositions();\r\n          this.clearAllSelections();\r\n\r\n          console.log('客混船贝图 - 取消无效标记成功:', {\r\n            kayId: kayData.id,\r\n            clearedPositionsCount: invalidPositions.length,\r\n            response: rps\r\n          });\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n          console.error('客混船贝图 - 取消无效标记失败:', rps);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('客混船贝图 - 取消无效标记API调用失败:', error);\r\n        this.showState(ModalTypeEnum.error, '取消无效标记失败，请重试');\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 返回主页面\r\n   */\r\n  goBack() {\r\n    this.openPage('/tas/vessel/list');\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n}\r\n", "<!-- 顶部标题卡片 -->\r\n<nz-card class=\"title-card\" [nzBodyStyle]=\"{'padding': '16px 20px' }\">\r\n  <div class=\"page-title\">\r\n    <h2>客混船贝位图管理</h2>\r\n  </div>\r\n</nz-card>\r\n\r\n<!-- 功能按钮卡片 -->\r\n<nz-card class=\"operations-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <!-- 功能按钮栏 - 参考船舶贝位图管理页面样式 -->\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div class=\"kay-operations\">\r\n        <!-- 新建贝按钮（最左侧） -->\r\n        <button nz-button [nzType]=\"'primary'\" (click)=\"showNewKayModal()\">\r\n          新建贝\r\n        </button>\r\n\r\n        <!-- 复制贝按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"showCopyKayModal()\">\r\n          复制贝\r\n        </button>\r\n\r\n        <!-- 删除贝按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"deleteKay()\">\r\n          删除贝\r\n        </button>\r\n\r\n        <!-- 恢复贝位按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"showRestorePositionModal()\">\r\n          恢复贝位\r\n        </button>\r\n\r\n        <!-- 删除贝位按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"deletePositions()\">\r\n          删除贝位\r\n        </button>\r\n\r\n        <!-- 标记无效位置按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"markInvalidPositions()\">\r\n          标记无效位置\r\n        </button>\r\n\r\n        <!-- 取消无效标记按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"clearInvalidPositions()\">\r\n          取消无效标记\r\n        </button>\r\n\r\n        <!-- 返回按钮（最右侧） -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"goBack()\" class=\"return-button\">\r\n          返回\r\n        </button>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n</nz-card>\r\n\r\n<!-- 贝号选择卡片 -->\r\n<nz-card class=\"selector-card\" [nzBodyStyle]=\"{'padding': '12px 20px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div class=\"kay-selector-container\">\r\n        <div class=\"kay-selector\">\r\n          <span class=\"kay-selector-label\">贝号选择:</span>\r\n          <nz-select [(ngModel)]=\"selectedKayNo\" (ngModelChange)=\"onKaySelect()\" class=\"kay-selector-dropdown\">\r\n            <nz-option *ngFor=\"let kay of kayList\" [nzValue]=\"kay.kayNo\" [nzLabel]=\"kay.kayNo\"></nz-option>\r\n          </nz-select>\r\n        </div>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n</nz-card>\r\n\r\n<!-- 贝位图展示卡片 -->\r\n<nz-card class=\"diagram-card\" [nzBodyStyle]=\"{'padding': '0' }\">\r\n  <div class=\"kay-diagram-container\"\r\n       [class.dragging]=\"isDragging\"\r\n       (mousedown)=\"onMouseDown($event)\"\r\n       (mousemove)=\"onMouseMove($event)\"\r\n       (mouseup)=\"onMouseUp($event)\"\r\n       (mouseleave)=\"onMouseLeave($event)\"\r\n       (dblclick)=\"onContainerDoubleClick($event)\">\r\n\r\n    <div class=\"kay-diagram\" *ngIf=\"kayPositions.length > 0\" [ngStyle]=\"getGridStyle()\">\r\n      <!-- 贝位图和行标识的容器 -->\r\n      <div class=\"diagram-with-row-labels\">\r\n        <!-- 列标签容器 -->\r\n        <div class=\"column-labels-container\">\r\n          <!-- 列标签网格，与贝位图左对齐 -->\r\n          <div class=\"column-labels-grid\" [ngStyle]=\"getGridStyle()\">\r\n            <div class=\"column-label\" *ngFor=\"let colLabel of getColumnLabels()\">\r\n              {{ formatNumber(colLabel) }}\r\n            </div>\r\n          </div>\r\n          <!-- 右侧空白区域，对应行标签的位置 -->\r\n          <div class=\"column-labels-spacer\"></div>\r\n        </div>\r\n\r\n        <!-- 贝位图和行标识的行容器 -->\r\n        <div class=\"position-row-container\">\r\n          <!-- 贝位图 -->\r\n          <div class=\"position-grid\" [ngStyle]=\"getGridStyle()\">\r\n            <div *ngFor=\"let position of kayPositions\"\r\n                 [class]=\"'position-cell' + (position.selected ? ' selected' : '') + (position.isDeduct ? ' invalid' : '') + (position.dragHighlight ? ' drag-highlight' : '') + (position.deleted ? ' deleted' : '') + (position.deleted && position.wasDeleted ? ' truly-deleted' : '')\"\r\n                 [attr.data-position-id]=\"position.position\"\r\n                 [style.grid-row]=\"position.gridRow\"\r\n                 [style.grid-column]=\"position.gridColumn\"\r\n                 (click)=\"onPositionClick(position, $event)\"\r\n                 (mouseenter)=\"onPositionMouseEnter(position, $event)\">\r\n              <span *ngIf=\"!position.deleted\">{{ position.position }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 行标识移到右侧 -->\r\n          <div class=\"row-labels\">\r\n            <div class=\"row-label\" *ngFor=\"let rowLabel of getRowLabels()\">\r\n              {{ formatNumber(rowLabel) }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"kayPositions.length === 0 && selectedKayData\" class=\"no-positions\">\r\n      <nz-empty nzNotFoundContent=\"暂无贝位数据\"></nz-empty>\r\n    </div>\r\n  </div>\r\n</nz-card>\r\n\r\n<!-- 新建贝模态框 -->\r\n<nz-modal [(nzVisible)]=\"isNewKayModalVisible\" nzTitle=\"新建客混船贝\" (nzOnCancel)=\"cancelNewKay()\"\r\n          (nzOnOk)=\"saveNewKay()\" nzOkText=\"保存\" nzCancelText=\"取消\" [nzWidth]=\"1200\">\r\n  <ng-container *nzModalContent>\r\n    <div nz-row [nzGutter]=\"[24, 0]\">\r\n      <!-- 左侧参数配置 -->\r\n      <div nz-col nzSpan=\"10\">\r\n        <nz-card nzTitle=\"参数配置\" [nzSize]=\"'small'\">\r\n          <form nz-form [formGroup]=\"newKayForm\">\r\n            <div nz-row [nzGutter]=\"[16, 8]\">\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">贝号</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入贝号\">\r\n                    <input nz-input placeholder=\"贝号\" formControlName=\"kayNo\">\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <!-- 第一列：行相关参数 -->\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">行数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入行数\">\r\n                    <nz-input-number nz-input placeholder=\"行数\" formControlName=\"rowNum\" [nzMin]=\"1\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <!-- 第二列：列相关参数 -->\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">列数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入列数\">\r\n                    <nz-input-number nz-input placeholder=\"列数\" formControlName=\"columnNum\" [nzMin]=\"1\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">行步长</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入行步长\">\r\n                    <nz-input-number nz-input placeholder=\"行步长\" formControlName=\"rowSep\" [nzMin]=\"1\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">列步长</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入列步长\">\r\n                    <nz-input-number nz-input placeholder=\"列步长\" formControlName=\"columnSep\" [nzMin]=\"1\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">行起始</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入行起始\">\r\n                    <nz-input-number nz-input placeholder=\"行起始\" formControlName=\"rowFrom\" [nzMin]=\"0\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired nzLabelAlign=\"right\">列起始</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入列起始\">\r\n                    <nz-input-number nz-input placeholder=\"列起始\" formControlName=\"columnFrom\" [nzMin]=\"0\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzLabelAlign=\"right\">备注</nz-form-label>\r\n                  <nz-form-control>\r\n                    <textarea nz-input placeholder=\"备注\" formControlName=\"remark\" [nzAutosize]=\"{ minRows: 2, maxRows: 4 }\"></textarea>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </nz-card>\r\n      </div>\r\n\r\n      <!-- 右侧贝图预览 -->\r\n      <div nz-col nzSpan=\"14\">\r\n        <nz-card nzTitle=\"贝图预览\" [nzSize]=\"'small'\">\r\n          <div class=\"kay-diagram-container\">\r\n            <div class=\"kay-diagram\" *ngIf=\"previewKayData\">\r\n              <!-- 贝位统计信息 -->\r\n              <div class=\"preview-stats\">\r\n                <nz-statistic nzTitle=\"贝位总数\" [nzValue]=\"previewKayData.totalPositions\" nzSuffix=\"个\"></nz-statistic>\r\n              </div>\r\n\r\n              <!-- 贝位网格 -->\r\n              <div class=\"kay-grid\" [style.grid-template-columns]=\"'repeat(' + previewKayData.columnNum + ', 1fr)'\"\r\n                   [style.grid-template-rows]=\"'repeat(' + previewKayData.rowNum + ', 1fr)'\">\r\n                <div class=\"position-cell kay-position\"\r\n                     *ngFor=\"let position of previewKayData.positions; trackBy: trackByPositionId\"\r\n                     [style.grid-row]=\"position.gridRow\"\r\n                     [style.grid-column]=\"position.gridColumn\">\r\n                  {{position.position}}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 贝位编号列表 -->\r\n              <div class=\"position-list\">\r\n                <nz-divider nzText=\"贝位编号列表\" nzOrientation=\"left\"></nz-divider>\r\n                <div class=\"position-tags\">\r\n                  <nz-tag *ngFor=\"let position of previewKayData.positions\" nzColor=\"blue\">\r\n                    {{position.position}}\r\n                  </nz-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空状态提示 -->\r\n            <nz-empty *ngIf=\"!previewKayData || previewKayData.totalPositions === 0\"\r\n                      nzNotFoundImage=\"simple\"\r\n                      nzNotFoundContent=\"请配置参数以生成贝图预览\">\r\n            </nz-empty>\r\n          </div>\r\n        </nz-card>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</nz-modal>\r\n\r\n\r\n\r\n<!-- 复制贝模态框 -->\r\n<nz-modal [(nzVisible)]=\"isCopyKayModalVisible\" nzTitle=\"复制贝\" (nzOnCancel)=\"isCopyKayModalVisible = false\"\r\n          (nzOnOk)=\"saveCopyKay()\" nzOkText=\"保存\" nzCancelText=\"取消\" [nzWidth]=\"500\">\r\n  <ng-container *nzModalContent>\r\n    <form nz-form [formGroup]=\"copyKayForm\">\r\n      <nz-form-item>\r\n        <nz-form-label>原贝号</nz-form-label>\r\n        <nz-form-control>\r\n          <input nz-input formControlName=\"originalKayNo\">\r\n        </nz-form-control>\r\n      </nz-form-item>\r\n\r\n      <nz-form-item>\r\n        <nz-form-label nzRequired>新贝号</nz-form-label>\r\n        <nz-form-control nzErrorTip=\"请输入新贝号\">\r\n          <input nz-input placeholder=\"新贝号\" formControlName=\"newKayNo\">\r\n        </nz-form-control>\r\n      </nz-form-item>\r\n\r\n      <nz-alert nzType=\"info\" nzMessage=\"格式说明：可以包含数字和字母，系统会自动剔除空格\" nzShowIcon></nz-alert>\r\n    </form>\r\n  </ng-container>\r\n</nz-modal>\r\n\r\n<!-- 恢复贝位模态框 -->\r\n<nz-modal [(nzVisible)]=\"isRestorePositionModalVisible\" nzTitle=\"恢复贝位\" (nzOnCancel)=\"cancelRestorePosition()\"\r\n          (nzOnOk)=\"restoreSelectedPositions()\" nzOkText=\"恢复选中贝位\" nzCancelText=\"取消\" [nzWidth]=\"800\" [nzBodyStyle]=\"{'height': '450px', 'overflow': 'hidden'}\">\r\n  <ng-container *nzModalContent>\r\n    <div class=\"restore-position-container\">\r\n      <!-- 批量操作区域 -->\r\n      <div class=\"restore-batch-operations\">\r\n        <nz-space>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"selectAllRestorePositions()\">\r\n            <i nz-icon nzType=\"check-square\" nzTheme=\"outline\"></i>\r\n            全选\r\n          </button>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"deselectAllRestorePositions()\">\r\n            <i nz-icon nzType=\"border\" nzTheme=\"outline\"></i>\r\n            反选\r\n          </button>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"clearRestoreSelection()\">\r\n            <i nz-icon nzType=\"close-square\" nzTheme=\"outline\"></i>\r\n            清空\r\n          </button>\r\n          <span *nzSpaceItem class=\"restore-selection-count\">\r\n            已选择: <strong>{{ selectedDeletedPositions.length }}</strong> / {{ deletedPositionOptions.length }}\r\n          </span>\r\n        </nz-space>\r\n      </div>\r\n\r\n      <!-- 已删除贝位网格 -->\r\n      <div class=\"restore-positions-grid-container\">\r\n        <div class=\"restore-positions-grid\" *ngIf=\"deletedPositionOptions.length > 0\">\r\n          <div\r\n            class=\"restore-position-card\"\r\n            *ngFor=\"let position of deletedPositionOptions; trackBy: trackByPositionId\"\r\n            [class.selected]=\"isPositionSelected(position.value)\"\r\n            (click)=\"togglePositionSelection(position.value)\">\r\n            <div class=\"position-card-content\">\r\n              <div class=\"position-id\">{{ position.label }}</div>\r\n              <div class=\"position-area\">{{ getPositionArea(position.value) }}</div>\r\n              <i class=\"position-check-icon\" nz-icon\r\n                 [nzType]=\"isPositionSelected(position.value) ? 'check-circle' : 'plus-circle'\"\r\n                 [nzTheme]=\"isPositionSelected(position.value) ? 'fill' : 'outline'\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"restore-empty-state\" *ngIf=\"deletedPositionOptions.length === 0\">\r\n          <nz-empty nzNotFoundImage=\"simple\" nzNotFoundContent=\"没有找到已删除的贝位\"></nz-empty>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 信息提示区域 -->\r\n      <div class=\"restore-info-section\">\r\n        <nz-alert nzType=\"info\" nzShowIcon\r\n                  nzMessage=\"选择要恢复的已删除贝位，点击'恢复选中贝位'按钮即可将这些贝位重新添加到当前贝中。恢复后的贝位将重新显示在贝位图中。\">\r\n        </nz-alert>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</nz-modal>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AACnE,SAAQC,WAAW,EAAwBC,gBAAgB,EAAEC,aAAa,QAAO,gBAAgB;AAIjG,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,QAAQ,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC0DhEC,EAAA,CAAAC,SAAA,oBAA+F;;;;IAAlCD,EAAtB,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAqB,YAAAD,MAAA,CAAAC,KAAA,CAAsB;;;;;IAyBlFJ,EAAA,CAAAK,cAAA,cAAqE;IACnEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,WAAA,OACF;;;;;IAiBEZ,EAAA,CAAAK,cAAA,WAAgC;IAAAL,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAA9BP,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAa,iBAAA,CAAAC,WAAA,CAAAC,QAAA,CAAuB;;;;;;IAPzDf,EAAA,CAAAK,cAAA,cAM2D;IAAtDL,EADA,CAAAgB,UAAA,mBAAAC,8DAAAC,MAAA;MAAA,MAAAJ,WAAA,GAAAd,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAc,eAAA,CAAAV,WAAA,EAAAI,MAAA,CAAiC;IAAA,EAAC,wBAAAO,mEAAAP,MAAA;MAAA,MAAAJ,WAAA,GAAAd,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAC7Bb,MAAA,CAAAgB,oBAAA,CAAAZ,WAAA,EAAAI,MAAA,CAAsC;IAAA,EAAC;IACxDlB,EAAA,CAAA2B,UAAA,IAAAC,+CAAA,mBAAgC;IAClC5B,EAAA,CAAAO,YAAA,EAAM;;;;IAPDP,EAAA,CAAA6B,UAAA,oBAAAf,WAAA,CAAAgB,QAAA,wBAAAhB,WAAA,CAAAiB,QAAA,uBAAAjB,WAAA,CAAAkB,aAAA,8BAAAlB,WAAA,CAAAmB,OAAA,uBAAAnB,WAAA,CAAAmB,OAAA,IAAAnB,WAAA,CAAAoB,UAAA,0BAAyQ;IAGzQlC,EADA,CAAAmC,WAAA,aAAArB,WAAA,CAAAsB,OAAA,CAAmC,gBAAAtB,WAAA,CAAAuB,UAAA,CACM;;IAGrCrC,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAE,UAAA,UAAAY,WAAA,CAAAmB,OAAA,CAAuB;;;;;IAMhCjC,EAAA,CAAAK,cAAA,cAA+D;IAC7DL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAA2B,WAAA,OACF;;;;;IA5BFtC,EANN,CAAAK,cAAA,cAAoF,cAE7C,cAEE,cAEwB;IACzDL,EAAA,CAAA2B,UAAA,IAAAY,wCAAA,kBAAqE;IAGvEvC,EAAA,CAAAO,YAAA,EAAM;IAENP,EAAA,CAAAC,SAAA,cAAwC;IAC1CD,EAAA,CAAAO,YAAA,EAAM;IAKJP,EAFF,CAAAK,cAAA,cAAoC,cAEoB;IACpDL,EAAA,CAAA2B,UAAA,IAAAa,wCAAA,kBAM2D;IAG7DxC,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAK,cAAA,cAAwB;IACtBL,EAAA,CAAA2B,UAAA,KAAAc,yCAAA,kBAA+D;IAMvEzC,EAHM,CAAAO,YAAA,EAAM,EACF,EACF,EACF;;;;IAtCmDP,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAgC,YAAA,GAA0B;IAM7C1C,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAgC,YAAA,GAA0B;IACT1C,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAiC,eAAA,GAAoB;IAW1C3C,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAgC,YAAA,GAA0B;IACzB1C,EAAA,CAAAQ,SAAA,EAAe;IAAfR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAkC,YAAA,CAAe;IAaG5C,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAmC,YAAA,GAAiB;;;;;IAQrE7C,EAAA,CAAAK,cAAA,cAA+E;IAC7EL,EAAA,CAAAC,SAAA,mBAAgD;IAClDD,EAAA,CAAAO,YAAA,EAAM;;;;;IAyGMP,EAAA,CAAAK,cAAA,cAG+C;IAC7CL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IAFDP,EADA,CAAAmC,WAAA,aAAAW,WAAA,CAAAV,OAAA,CAAmC,gBAAAU,WAAA,CAAAT,UAAA,CACM;IAC5CrC,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAqC,WAAA,CAAA/B,QAAA,MACF;;;;;IAOEf,EAAA,CAAAK,cAAA,iBAAyE;IACvEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;IADPP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAsC,WAAA,CAAAhC,QAAA,MACF;;;;;IArBJf,EAFF,CAAAK,cAAA,cAAgD,cAEnB;IACzBL,EAAA,CAAAC,SAAA,uBAAmG;IACrGD,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAK,cAAA,cAC+E;IAC7EL,EAAA,CAAA2B,UAAA,IAAAqB,wDAAA,kBAG+C;IAGjDhD,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAK,cAAA,cAA2B;IACzBL,EAAA,CAAAC,SAAA,qBAA8D;IAC9DD,EAAA,CAAAK,cAAA,cAA2B;IACzBL,EAAA,CAAA2B,UAAA,IAAAsB,2DAAA,qBAAyE;IAK/EjD,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;IAvB2BP,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAwC,cAAA,CAAAC,cAAA,CAAyC;IAIlDnD,EAAA,CAAAQ,SAAA,EAA+E;IAChGR,EADiB,CAAAmC,WAAA,sCAAAzB,MAAA,CAAAwC,cAAA,CAAAE,SAAA,YAA+E,mCAAA1C,MAAA,CAAAwC,cAAA,CAAAG,MAAA,YACvB;IAElDrD,EAAA,CAAAQ,SAAA,EAA6B;IAAAR,EAA7B,CAAAE,UAAA,YAAAQ,MAAA,CAAAwC,cAAA,CAAAI,SAAA,CAA6B,iBAAA5C,MAAA,CAAA6C,iBAAA,CAA0B;IAWlDvD,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAE,UAAA,YAAAQ,MAAA,CAAAwC,cAAA,CAAAI,SAAA,CAA2B;;;;;IAQ9DtD,EAAA,CAAAC,SAAA,mBAGW;;;;;IAzHrBD,EAAA,CAAAwD,uBAAA,GAA8B;IASdxD,EARd,CAAAK,cAAA,cAAiC,cAEP,kBACqB,eACF,cACJ,cACP,mBACR,wBACmC;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IACjEP,EAAA,CAAAK,cAAA,2BAAoC;IAClCL,EAAA,CAAAC,SAAA,iBAAyD;IAG/DD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAKFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IACjEP,EAAA,CAAAK,cAAA,2BAAoC;IAClCL,EAAA,CAAAC,SAAA,2BAAkG;IAGxGD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAKFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IACjEP,EAAA,CAAAK,cAAA,2BAAoC;IAClCL,EAAA,CAAAC,SAAA,2BAAqG;IAG3GD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,0BAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClEP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,2BAAmG;IAGzGD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,0BAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClEP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,2BAAsG;IAG5GD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,0BAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClEP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,2BAAoG;IAG1GD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACmC;IAAAL,EAAA,CAAAM,MAAA,0BAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClEP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,2BAAuG;IAG7GD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACwB;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IACtDP,EAAA,CAAAK,cAAA,uBAAiB;IACfL,EAAA,CAAAC,SAAA,oBAAkH;IAOhID,EANY,CAAAO,YAAA,EAAkB,EACL,EACX,EACF,EACD,EACC,EACN;IAKFP,EAFJ,CAAAK,cAAA,eAAwB,mBACqB,eACN;IA8BjCL,EA7BA,CAAA2B,UAAA,KAAA8B,kDAAA,kBAAgD,KAAAC,uDAAA,uBA+BL;IAKnD1D,EAHM,CAAAO,YAAA,EAAM,EACE,EACN,EACF;;;;;IA5HMP,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAA2D,eAAA,KAAAC,GAAA,EAAoB;IAGJ5D,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAE,UAAA,mBAAkB;IAC1BF,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAE,UAAA,cAAAQ,MAAA,CAAAmD,UAAA,CAAwB;IACxB7D,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAA2D,eAAA,KAAAG,GAAA,EAAoB;IAe4C9D,EAAA,CAAAQ,SAAA,IAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IAURF,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IASbF,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IASRF,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IASbF,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IASRF,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAE,UAAA,YAAW;IASvBF,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAA2D,eAAA,KAAAI,GAAA,EAAyC;IAW1F/D,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAE,UAAA,mBAAkB;IAEZF,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAwC,cAAA,CAAoB;IA6BnClD,EAAA,CAAAQ,SAAA,EAA4D;IAA5DR,EAAA,CAAAE,UAAA,UAAAQ,MAAA,CAAAwC,cAAA,IAAAxC,MAAA,CAAAwC,cAAA,CAAAC,cAAA,OAA4D;;;;;IAgBjFnD,EAAA,CAAAwD,uBAAA,GAA8B;IAGxBxD,EAFJ,CAAAK,cAAA,eAAwC,mBACxB,oBACG;IAAAL,EAAA,CAAAM,MAAA,yBAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClCP,EAAA,CAAAK,cAAA,sBAAiB;IACfL,EAAA,CAAAC,SAAA,gBAAgD;IAEpDD,EADE,CAAAO,YAAA,EAAkB,EACL;IAGbP,EADF,CAAAK,cAAA,mBAAc,wBACc;IAAAL,EAAA,CAAAM,MAAA,yBAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAC7CP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,iBAA6D;IAEjED,EADE,CAAAO,YAAA,EAAkB,EACL;IAEfP,EAAA,CAAAC,SAAA,oBAAmF;IACrFD,EAAA,CAAAO,YAAA,EAAO;;;;;IAhBOP,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAE,UAAA,cAAAQ,MAAA,CAAAsD,WAAA,CAAyB;;;;;;IA4BjChE,EAAA,CAAAK,cAAA,iBAAoF;IAAtCL,EAAA,CAAAgB,UAAA,mBAAAiD,6EAAA;MAAAjE,EAAA,CAAAmB,aAAA,CAAA+C,GAAA;MAAA,MAAAxD,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAyD,yBAAA,EAA2B;IAAA,EAAC;IACjFnE,EAAA,CAAAC,SAAA,YAAuD;IACvDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;;IACTP,EAAA,CAAAK,cAAA,iBAAsF;IAAxCL,EAAA,CAAAgB,UAAA,mBAAAoD,6EAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAkD,IAAA;MAAA,MAAA3D,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA4D,2BAAA,EAA6B;IAAA,EAAC;IACnFtE,EAAA,CAAAC,SAAA,YAAiD;IACjDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;;IACTP,EAAA,CAAAK,cAAA,iBAAgF;IAAlCL,EAAA,CAAAgB,UAAA,mBAAAuD,6EAAA;MAAAvE,EAAA,CAAAmB,aAAA,CAAAqD,IAAA;MAAA,MAAA9D,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAA+D,qBAAA,EAAuB;IAAA,EAAC;IAC7EzE,EAAA,CAAAC,SAAA,YAAuD;IACvDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;IACTP,EAAA,CAAAK,cAAA,eAAmD;IACjDL,EAAA,CAAAM,MAAA,4BAAK;IAAAN,EAAA,CAAAK,cAAA,aAAQ;IAAAL,EAAA,CAAAM,MAAA,GAAqC;IAAAN,EAAA,CAAAO,YAAA,EAAS;IAACP,EAAA,CAAAM,MAAA,GAC9D;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IADQP,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAa,iBAAA,CAAAH,MAAA,CAAAgE,wBAAA,CAAAC,MAAA,CAAqC;IAAU3E,EAAA,CAAAQ,SAAA,EAC9D;IAD8DR,EAAA,CAAAS,kBAAA,QAAAC,MAAA,CAAAkE,sBAAA,CAAAD,MAAA,MAC9D;;;;;;IAOA3E,EAAA,CAAAK,cAAA,eAIoD;IAAlDL,EAAA,CAAAgB,UAAA,mBAAA6D,6EAAA;MAAA,MAAAC,YAAA,GAAA9E,EAAA,CAAAmB,aAAA,CAAA4D,IAAA,EAAA1D,SAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASb,MAAA,CAAAsE,uBAAA,CAAAF,YAAA,CAAAG,KAAA,CAAuC;IAAA,EAAC;IAE/CjF,EADF,CAAAK,cAAA,eAAmC,eACR;IAAAL,EAAA,CAAAM,MAAA,GAAoB;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnDP,EAAA,CAAAK,cAAA,eAA2B;IAAAL,EAAA,CAAAM,MAAA,GAAqC;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtEP,EAAA,CAAAC,SAAA,aAE2E;IAE/ED,EADE,CAAAO,YAAA,EAAM,EACF;;;;;IATJP,EAAA,CAAAkF,WAAA,aAAAxE,MAAA,CAAAyE,kBAAA,CAAAL,YAAA,CAAAG,KAAA,EAAqD;IAG1BjF,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAa,iBAAA,CAAAiE,YAAA,CAAAM,KAAA,CAAoB;IAClBpF,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAa,iBAAA,CAAAH,MAAA,CAAA2E,eAAA,CAAAP,YAAA,CAAAG,KAAA,EAAqC;IAE7DjF,EAAA,CAAAQ,SAAA,EAA8E;IAC9ER,EADA,CAAAE,UAAA,WAAAQ,MAAA,CAAAyE,kBAAA,CAAAL,YAAA,CAAAG,KAAA,mCAA8E,YAAAvE,MAAA,CAAAyE,kBAAA,CAAAL,YAAA,CAAAG,KAAA,uBACX;;;;;IAX5EjF,EAAA,CAAAK,cAAA,cAA8E;IAC5EL,EAAA,CAAA2B,UAAA,IAAA2D,uDAAA,kBAIoD;IAStDtF,EAAA,CAAAO,YAAA,EAAM;;;;IAXmBP,EAAA,CAAAQ,SAAA,EAA2B;IAAAR,EAA3B,CAAAE,UAAA,YAAAQ,MAAA,CAAAkE,sBAAA,CAA2B,iBAAAlE,MAAA,CAAA6C,iBAAA,CAA0B;;;;;IAa9EvD,EAAA,CAAAK,cAAA,eAA6E;IAC3EL,EAAA,CAAAC,SAAA,oBAA6E;IAC/ED,EAAA,CAAAO,YAAA,EAAM;;;;;IA3CZP,EAAA,CAAAwD,uBAAA,GAA8B;IAIxBxD,EAHJ,CAAAK,cAAA,cAAwC,cAEA,eAC1B;IAaRL,EAZA,CAAA2B,UAAA,IAAA4D,oDAAA,qBAAoF,IAAAC,oDAAA,qBAIE,IAAAC,oDAAA,qBAIN,IAAAC,kDAAA,mBAI7B;IAIvD1F,EADE,CAAAO,YAAA,EAAW,EACP;IAGNP,EAAA,CAAAK,cAAA,cAA8C;IAiB5CL,EAhBA,CAAA2B,UAAA,IAAAgE,iDAAA,kBAA8E,KAAAC,kDAAA,kBAgBD;IAG/E5F,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAK,cAAA,eAAkC;IAChCL,EAAA,CAAAC,SAAA,oBAEW;IAEfD,EADE,CAAAO,YAAA,EAAM,EACF;;;;;IA3BmCP,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAkE,sBAAA,CAAAD,MAAA,KAAuC;IAgB1C3E,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAE,UAAA,SAAAQ,MAAA,CAAAkE,sBAAA,CAAAD,MAAA,OAAyC;;;AD5TnF,OAAM,MAAOkB,kBAAmB,SAAQlG,WAAW;EA0DjDmG,YACEC,oBAA0C,EAClCC,GAAsB,EACtBC,iBAAoC,EACpCC,KAAqB;IAE7B,KAAK,CAACH,oBAAoB,CAAC;IAJnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IA7Df,KAAAC,SAAS,GAAG,IAAIrG,gBAAgB,EAAE;IAClC,KAAAsG,WAAW,GAAG,IAAIrG,uBAAuB,EAAE;IAC3C,KAAAsG,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAA7D,YAAY,GAAU,EAAE;IACxB,KAAA8D,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,eAAe,GAAU,EAAE;IAE3B;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,6BAA6B,GAAG,KAAK;IAIrC;IACA,KAAApC,wBAAwB,GAAa,EAAE;IACvC,KAAAE,sBAAsB,GAAU,EAAE;IAClC,KAAAmC,gBAAgB,GAAgB,IAAIC,GAAG,EAAE;IAEzC;IAEA;IACA,KAAA9D,cAAc,GAAQ,IAAI;IAE1B;IACQ,KAAA+D,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,KAAK;IAE3B;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,aAAa,GAAG,GAAG,CAAC,CAAE;IACtB,KAAAC,aAAa,GAAG,CAAC,CAAC,CAAI;IAEtB;IACQ,KAAAC,uBAAuB,GAAG;MAChCC,aAAa,EAAE,KAAK;MAAS;MAC7BC,eAAe,EAAE,KAAK;MAAO;MAC7BC,kBAAkB,EAAE,KAAK;MAAI;MAC7BC,mBAAmB,EAAE,KAAK,CAAG;KAC9B;EAaD;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAAC5B,QAAQ,GAAG,IAAI,CAAC6B,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE;IAChD,IAAI,CAAC5B,QAAQ,GAAG,IAAI,CAAC4B,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE;IAEhDC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7B/B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB+B,SAAS,EAAE,IAAI,CAACH;KACjB,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE;MAClB,IAAI,CAACiC,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,WAAW,CAAC;MAChD,IAAI,CAACC,YAAY,EAAE;MACnB;IACF;IAEA,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA;;;EAGAD,SAASA,CAAA;IACP,IAAI,CAAC5E,UAAU,GAAG,IAAIpE,SAAS,CAAC;MAC9BW,KAAK,EAAE,IAAIZ,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACkJ,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EvF,MAAM,EAAE,IAAI7D,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAC/EC,MAAM,EAAE,IAAItJ,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAC/EE,OAAO,EAAE,IAAIvJ,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAChFzF,SAAS,EAAE,IAAI5D,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAClFG,SAAS,EAAE,IAAIxJ,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAClFI,UAAU,EAAE,IAAIzJ,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACmJ,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MACnFK,MAAM,EAAE,IAAI1J,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACkJ,SAAS,CAAC,GAAG,CAAC,CAAC;KACxD,CAAC;IAIF,IAAI,CAAC5E,WAAW,GAAG,IAAIvE,SAAS,CAAC;MAC/B0J,aAAa,EAAE,IAAI3J,WAAW,CAAC;QAACyF,KAAK,EAAE,EAAE;QAAEmE,QAAQ,EAAE;MAAI,CAAC,CAAC;MAC3DC,QAAQ,EAAE,IAAI7J,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACiJ,QAAQ,EAAEjJ,UAAU,CAACkJ,SAAS,CAAC,EAAE,CAAC,CAAC;KAC9E,CAAC;IAEF;IACA,IAAI,CAAC/E,UAAU,CAACyF,YAAY,CAACC,SAAS,CAAC,MAAK;MAC1C,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC;EACJ;EAEA;;;EAGAd,WAAWA,CAAA;IACT;IACA,IAAI,CAACvB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsC,4BAA4B,EAAE;IAEnC;IACA,IAAI,CAAChD,OAAO,GAAG,EAAE;IACjB,IAAI,CAACF,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC5D,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC8D,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACI,gBAAgB,CAAC2C,KAAK,EAAE;IAE7B;IACA,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAE;QACJvD,QAAQ,EAAE,IAAI,CAACA;;KAElB;IAED8B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuB,WAAW,CAAC;IAEzC,IAAI,CAAC1D,iBAAiB,CAAC4D,IAAI,CAAC,kBAAkB,EAAEF,WAAW,EAAE,IAAI,CAAC3D,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACzFC,IAAI,CAAEC,GAAsB,IAAI;MAC/B9B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6B,GAAG,CAAC;MAClC,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;QACnB,IAAI,CAACzD,OAAO,GAAGwD,GAAG,CAACL,IAAI,IAAI,EAAE;QAC7BzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/B/B,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB8D,QAAQ,EAAE,IAAI,CAAC1D,OAAO,CAAC9B,MAAM;UAC7B8B,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;QAEF;QACA,IAAI,CAACmB,uBAAuB,CAACC,aAAa,GAAG,IAAI;QAEjD,IAAI,IAAI,CAACpB,OAAO,CAAC9B,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAAC4B,aAAa,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACrG,KAAK;UAC1C,IAAI,CAACgK,WAAW,EAAE;UAClB;QACF,CAAC,MAAM;UACLjC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACnC,IAAI,CAAC7B,aAAa,GAAG,EAAE;UACvB,IAAI,CAACC,eAAe,GAAG,IAAI;UAC3B;UACA,IAAI,CAACoB,uBAAuB,CAACE,eAAe,GAAG,IAAI;UACnD,IAAI,CAACF,uBAAuB,CAACG,kBAAkB,GAAG,IAAI;UACtD,IAAI,CAACH,uBAAuB,CAACI,mBAAmB,GAAG,IAAI;UACvD,IAAI,CAACqC,0BAA0B,EAAE;QACnC;MACF,CAAC,MAAM;QACLlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6B,GAAG,CAACK,GAAG,CAAC;QACtC,IAAI,CAAChC,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;QAC5C,IAAI,CAACnD,aAAa,GAAG,KAAK;MAC5B;IACF,CAAC,CAAC,CACDoD,KAAK,CAAEhC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,IAAI,CAACD,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,aAAa,CAAC;MAClD,IAAI,CAACpB,aAAa,GAAG,KAAK;IAC5B,CAAC,CAAC;EACN;EAEA;;;EAGAqD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAChE,eAAe,EAAE;IAE3B,MAAMmD,WAAW,GAAG;MAClBc,KAAK,EAAE,IAAI,CAACjE,eAAe,CAACkE,EAAE;MAC9BrE,QAAQ,EAAE,IAAI,CAACA;KAChB;IAED,IAAI,CAACJ,iBAAiB,CAAC4D,IAAI,CAAC,yBAAyB,EAAEF,WAAW,EAAE,IAAI,CAAC3D,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAChGC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;QACnB,IAAI,CAACvD,eAAe,GAAGsD,GAAG,CAACL,IAAI,IAAI,EAAE;QACrC,IAAI,CAACe,oBAAoB,EAAE;MAC7B,CAAC,MAAM;QACL,IAAI,CAACrC,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;;;EAGAF,WAAWA,CAAA;IACT,IAAI,CAAC5D,eAAe,GAAG,IAAI,CAACC,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAK,IAAI,CAACmG,aAAa,CAAC;IACjF,IAAI,IAAI,CAACC,eAAe,EAAE;MACxB2B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/BhI,KAAK,EAAE,IAAI,CAACoG,eAAe,CAACpG,KAAK;QACjCqK,KAAK,EAAE,IAAI,CAACjE,eAAe,CAACkE;OAC7B,CAAC;MAEF;MACA,IAAI,CAAC9C,uBAAuB,CAACE,eAAe,GAAG,IAAI;MAEnD,IAAI,CAACgD,oBAAoB,EAAE;MAC3B,IAAI,CAACN,mBAAmB,EAAE;MAE1B;MACA,IAAI,CAACH,0BAA0B,EAAE;IACnC;EACF;EAEA;;;;EAIAS,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACtE,eAAe,EAAE;IAE3B,MAAMlD,SAAS,GAAG,EAAE;IACpB,MAAMsG,IAAI,GAAG,IAAI,CAACpD,eAAe;IAEjC2B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC7B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,eAAe,EAAEoD,IAAI;MACrBmB,SAAS,EAAEnB,IAAI,CAACc,EAAE;MAClBM,cAAc,EAAEpB,IAAI,CAACqB,OAAO;MAC5B5H,MAAM,EAAEuG,IAAI,CAACvG,MAAM;MACnBD,SAAS,EAAEwG,IAAI,CAACxG;KACjB,CAAC;IAIF;IACA,KAAK,IAAI8H,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,IAAI,CAACvG,MAAM,EAAE6H,GAAG,EAAE,EAAE;MAC1C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvB,IAAI,CAACxG,SAAS,EAAE+H,GAAG,EAAE,EAAE;QAC7C,MAAMC,SAAS,GAAGxB,IAAI,CAACb,OAAO,GAAImC,GAAG,GAAGtB,IAAI,CAACd,MAAO;QACpD;QACA;QACA,MAAMuC,SAAS,GAAGzB,IAAI,CAACX,UAAU,GAAI,CAACW,IAAI,CAACxG,SAAS,GAAG,CAAC,GAAG+H,GAAG,IAAIvB,IAAI,CAACZ,SAAU;QAEjF,MAAMjI,QAAQ,GAAG;UACfmK,GAAG,EAAEE,SAAS;UACdE,MAAM,EAAED,SAAS;UACjBtK,QAAQ,EAAE,GAAGwK,MAAM,CAACH,SAAS,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGD,MAAM,CAACF,SAAS,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;UACtF1J,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,KAAK;UACf0J,QAAQ,EAAEP,GAAG;UAAG;UAChBQ,QAAQ,EAAEP,GAAG;UAAG;UAChBQ,UAAU,EAAEP,SAAS;UAAG;UACxBQ,UAAU,EAAEP,SAAS;UAAI;UACzBjJ,OAAO,EAAE8I,GAAG,GAAG,CAAC;UAAG;UACnB7I,UAAU,EAAE8I,GAAG,GAAG,CAAC,CAAE;SACtB;QAED7H,SAAS,CAACuI,IAAI,CAAC9K,QAAQ,CAAC;MAC1B;IACF;IAIA;IACA;IAEA,IAAI6I,IAAI,CAACqB,OAAO,EAAE;MAChB,MAAMa,cAAc,GAAGlC,IAAI,CAACqB,OAAO,CAACc,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MAE/E/D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/B7B,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCwE,SAAS,EAAEnB,IAAI,CAACc,EAAE;QAClByB,eAAe,EAAEvC,IAAI,CAACqB,OAAO;QAC7BmB,yBAAyB,EAAEN,cAAc;QACzCO,0BAA0B,EAAE/I,SAAS,CAACqB,MAAM;QAC5C2H,qBAAqB,EAAEhJ,SAAS,CAACiJ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;QACrD0L,8BAA8B,EAAE,IAAI,CAAC1F,gBAAgB,CAAC2F,IAAI;QAC1DC,4BAA4B,EAAEC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB;OAC/D,CAAC;MAEF;MACA;MACA,IAAI,IAAI,CAACA,gBAAgB,CAAC2F,IAAI,KAAK,CAAC,EAAE;QACpC;QACApJ,SAAS,CAACwJ,OAAO,CAAC/L,QAAQ,IAAG;UAC3B,IAAI,CAAC+K,cAAc,CAACiB,QAAQ,CAAChM,QAAQ,CAACA,QAAQ,CAAC,EAAE;YAC/C;YACA,IAAI,CAACgG,gBAAgB,CAACiG,GAAG,CAACjM,QAAQ,CAACA,QAAQ,CAAC;UAC9C;QACF,CAAC,CAAC;QAEFoH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjD6E,gCAAgC,EAAE,IAAI,CAAClG,gBAAgB,CAAC2F,IAAI;UAC5DQ,2BAA2B,EAAEN,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB;SAC9D,CAAC;MACJ;MAEA;MACAzD,SAAS,CAACwJ,OAAO,CAAC/L,QAAQ,IAAG;QAC3B,IAAI+K,cAAc,CAACiB,QAAQ,CAAChM,QAAQ,CAACA,QAAQ,CAAC,EAAE;UAC9C;UACAA,QAAQ,CAACkB,OAAO,GAAG,KAAK;UACxBlB,QAAQ,CAACoM,MAAM,GAAG,IAAI;UACtBpM,QAAQ,CAACmB,UAAU,GAAG,KAAK;UAC3B;UACA,IAAI,IAAI,CAAC6E,gBAAgB,CAACqG,GAAG,CAACrM,QAAQ,CAACA,QAAQ,CAAC,EAAE;YAChD,IAAI,CAACgG,gBAAgB,CAACsG,MAAM,CAACtM,QAAQ,CAACA,QAAQ,CAAC;YAC/CoH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAErH,QAAQ,CAACA,QAAQ,CAAC;UACvE;QACF,CAAC,MAAM,IAAI,IAAI,CAACgG,gBAAgB,CAACqG,GAAG,CAACrM,QAAQ,CAACA,QAAQ,CAAC,EAAE;UACvD;UACAA,QAAQ,CAACkB,OAAO,GAAG,IAAI;UACvBlB,QAAQ,CAACoM,MAAM,GAAG,KAAK;UACvBpM,QAAQ,CAACmB,UAAU,GAAG,IAAI,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL;UACAnB,QAAQ,CAACkB,OAAO,GAAG,IAAI;UACvBlB,QAAQ,CAACoM,MAAM,GAAG,KAAK;UACvBpM,QAAQ,CAACmB,UAAU,GAAG,KAAK,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MAEFiG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/BkF,sBAAsB,EAAEhK,SAAS,CAACqB,MAAM;QACxCyH,yBAAyB,EAAEN,cAAc;QACzCyB,sBAAsB,EAAEjK,SAAS,CAAC0I,MAAM,CAACQ,CAAC,IAAI,CAACA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACW,MAAM,CAAC,CAACxI,MAAM;QAC5E6I,0BAA0B,EAAElK,SAAS,CAAC0I,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACyC,MAAM;QACnF8I,0BAA0B,EAAEnK,SAAS,CAAC0I,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAI,CAACuK,CAAC,CAACtK,UAAU,CAAC,CAACyC,MAAM;QACpF+I,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F,IAAI;QACnDiB,qBAAqB,EAAEf,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;QACxD6G,yBAAyB,EAAEtK,SAAS,CAAC0I,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;QAChG8M,yBAAyB,EAAEvK,SAAS,CAAC0I,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAI,CAACuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ;OACjG,CAAC;IACJ,CAAC,MAAM;MACL;MACAoH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEpD;MACA,IAAI,IAAI,CAACrB,gBAAgB,CAAC2F,IAAI,KAAK,CAAC,EAAE;QACpCpJ,SAAS,CAACwJ,OAAO,CAAC/L,QAAQ,IAAG;UAC3B,IAAI,CAACgG,gBAAgB,CAACiG,GAAG,CAACjM,QAAQ,CAACA,QAAQ,CAAC;QAC9C,CAAC,CAAC;QACFoH,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;UACvD0F,mBAAmB,EAAExK,SAAS,CAACqB,MAAM;UACrCoJ,cAAc,EAAEzK,SAAS,CAACiJ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ;SAC9C,CAAC;MACJ;MAEAuC,SAAS,CAACwJ,OAAO,CAAC/L,QAAQ,IAAG;QAC3B,IAAI,IAAI,CAACgG,gBAAgB,CAACqG,GAAG,CAACrM,QAAQ,CAACA,QAAQ,CAAC,EAAE;UAChD;UACAA,QAAQ,CAACkB,OAAO,GAAG,IAAI;UACvBlB,QAAQ,CAACoM,MAAM,GAAG,KAAK;UACvBpM,QAAQ,CAACmB,UAAU,GAAG,IAAI;QAC5B,CAAC,MAAM;UACL;UACAnB,QAAQ,CAACkB,OAAO,GAAG,IAAI;UACvBlB,QAAQ,CAACoM,MAAM,GAAG,KAAK;UACvBpM,QAAQ,CAACmB,UAAU,GAAG,KAAK;QAC7B;MACF,CAAC,CAAC;MAEFiG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9C4F,iBAAiB,EAAE1K,SAAS,CAACqB,MAAM;QACnC+I,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F,IAAI;QACnDiB,qBAAqB,EAAEf,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB;OACxD,CAAC;IACJ;IAEA;IACA,IAAI,CAACnE,YAAY,GAAGU,SAAS;IAC7B,IAAI,CAACoD,iBAAiB,GAAG,EAAE;IAE3ByB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpC6F,mBAAmB,EAAE,IAAI,CAACrL,YAAY,CAAC+B,MAAM;MAC7CuJ,mBAAmB,EAAE,IAAI,CAACtL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAI,CAACA,CAAC,CAACvK,OAAO,CAAC,CAAC0C,MAAM;MACrEwJ,qBAAqB,EAAE,IAAI,CAACvL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,CAAC,CAAC0C,MAAM;MACtE6I,0BAA0B,EAAE,IAAI,CAAC5K,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACyC,MAAM;MAC3F8I,0BAA0B,EAAE,IAAI,CAAC7K,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAI,CAACuK,CAAC,CAACtK,UAAU,CAAC,CAACyC,MAAM;MAC5F+I,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F,IAAI;MACnDiB,qBAAqB,EAAEf,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;MACxD;MACAqH,kBAAkB,EAAE,IAAI,CAACxL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,CAAC,CAACsK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;MACjFsN,uBAAuB,EAAE,IAAI,CAACzL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;MACtGuN,eAAe,EAAE;QACfC,qBAAqB,EAAE3B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;QACxDyH,gBAAgB,EAAE,IAAI,CAAC5L,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;QAC/F0N,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC/B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC,CAAC6H,IAAI,EAAE,CAAC,KAAKF,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/L,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC,CAAC6N,IAAI,EAAE;;KAE3K,CAAC;IAEF;IACA,IAAI,CAAChH,uBAAuB,CAACG,kBAAkB,GAAG,IAAI;IACtDI,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9B;IACA;IACAyG,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAnE,oBAAoBA,CAAA;IAClBxC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/B2G,iBAAiB,EAAE,IAAI,CAACnM,YAAY,CAAC+B,MAAM;MAC3CqK,oBAAoB,EAAE,IAAI,CAACrI,eAAe,CAAChC,MAAM;MACjDgC,eAAe,EAAE,IAAI,CAACA;KACvB,CAAC;IAEF,IAAIsI,YAAY,GAAG,CAAC;IACpB,IAAI,CAACrM,YAAY,CAACkK,OAAO,CAACb,GAAG,IAAG;MAC9B,MAAMiD,UAAU,GAAGjD,GAAG,CAAClK,QAAQ;MAC/BkK,GAAG,CAAClK,QAAQ,GAAG,IAAI,CAAC4E,eAAe,CAACwI,IAAI,CAACC,MAAM,IAC7CA,MAAM,CAACnE,OAAO,IAAImE,MAAM,CAACnE,OAAO,CAAC8B,QAAQ,CAACd,GAAG,CAAClL,QAAQ,CAAC,CACxD;MAED,IAAIkL,GAAG,CAAClK,QAAQ,EAAE;QAChBkN,YAAY,EAAE;QACd,IAAI,CAACC,UAAU,EAAE;UACf/G,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6D,GAAG,CAAClL,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM,IAAImO,UAAU,EAAE;QACrB/G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6D,GAAG,CAAClL,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC;IAEFoH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BiH,qBAAqB,EAAEJ;KACxB,CAAC;EACJ;EAEA;;;EAGAzN,eAAeA,CAACT,QAAa,EAAEuO,KAAkB;IAC/CnH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;MAC7ByO,eAAe,EAAEzO,QAAQ,CAACe,QAAQ;MAClCuF,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCmI,QAAQ,EAAE,CAAC,CAACH;KACb,CAAC;IAEF;IACA,IAAIvO,QAAQ,CAACgB,QAAQ,IAAIhB,QAAQ,CAACkB,OAAO,IAAI,CAAClB,QAAQ,CAACoM,MAAM,EAAE;MAC7DhF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;QAC7BgB,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ;QAC3BE,OAAO,EAAElB,QAAQ,CAACkB,OAAO;QACzBkL,MAAM,EAAEpM,QAAQ,CAACoM;OAClB,CAAC;MACF;IACF;IAEA;IACA,IAAImC,KAAK,EAAE;MACTA,KAAK,CAACI,eAAe,EAAE;MACvBJ,KAAK,CAACK,cAAc,EAAE;IACxB;IAEA;IACA,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACxI,aAAa;IAE1D;IACA,IAAI,IAAI,CAACA,aAAa,GAAG,CAAC,IAAI,IAAI,CAACG,QAAQ,IAAImI,kBAAkB,GAAG,GAAG,EAAE;MACvEzH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEwH,kBAAkB,CAAC;MAC9D;IACF;IAEA;IACA,IAAI,IAAI,CAACvI,UAAU,IAAI,IAAI,CAACI,QAAQ,EAAE;MACpCU,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACA,IAAI,IAAI,CAACd,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAACG,QAAQ,EAAE;MAC5CU,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACf,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,aAAa,GAAG,CAAC;IACxB;IAEA;IACA,MAAMyI,WAAW,GAAGhP,QAAQ,CAACe,QAAQ;IAErC;IACAf,QAAQ,CAACe,QAAQ,GAAG,CAACf,QAAQ,CAACe,QAAQ;IAEtC,IAAIf,QAAQ,CAACe,QAAQ,EAAE;MACrB;MACA,IAAI,CAAC,IAAI,CAAC4E,iBAAiB,CAACkE,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKA,QAAQ,CAACA,QAAQ,CAAC,EAAE;QACvE,IAAI,CAAC2F,iBAAiB,CAACmF,IAAI,CAAC9K,QAAQ,CAAC;MACvC;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC2F,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACsF,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKA,QAAQ,CAACA,QAAQ,CAAC;IAC/F;IAEAoH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;MAC7BgP,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEjP,QAAQ,CAACe,QAAQ;MAC9BmO,aAAa,EAAE,IAAI,CAACvJ,iBAAiB,CAAC/B;KACvC,CAAC;EACJ;EAEA;;;EAGAuL,WAAWA,CAACZ,KAAiB;IAC3B;IACA,IAAIA,KAAK,CAACa,MAAM,KAAK,CAAC,EAAE;IAExB,MAAMC,MAAM,GAAGd,KAAK,CAACc,MAAqB;IAE1C;IACA;IACA,MAAMC,YAAY,GAAGD,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAgB;IACpE,MAAMC,cAAc,GAAG,CAAC,CAACF,YAAY;IACrC,MAAMd,UAAU,GAAGc,YAAY,EAAEG,YAAY,CAAC,kBAAkB,CAAC;IAEjErI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxCqI,cAAc,EAAEL,MAAM,CAACM,SAAS,IAAIN,MAAM,CAACO,OAAO;MAClDC,kBAAkB,EAAER,MAAM,CAACS,WAAW,EAAE3E,IAAI,EAAE;MAC9CmE,YAAY,EAAEA,YAAY,EAAEK,SAAS;MACrCH,cAAc,EAAEA,cAAc;MAC9BhB,UAAU,EAAEA,UAAU;MACtBuB,WAAW,EAAEjB,IAAI,CAACC,GAAG,EAAE;MACvBiB,OAAO,EAAEzB,KAAK,CAACyB,OAAO;MACtBC,OAAO,EAAE1B,KAAK,CAAC0B;KAChB,CAAC;IAEF;IACA,IAAI,CAAC1J,aAAa,GAAGuI,IAAI,CAACC,GAAG,EAAE;IAC/B,IAAI,CAACvI,UAAU,GAAG+H,KAAK,CAACyB,OAAO;IAC/B,IAAI,CAACvJ,UAAU,GAAG8H,KAAK,CAAC0B,OAAO;IAC/B,IAAI,CAACvJ,QAAQ,GAAG,KAAK;IAErB;IACA,IAAI8I,cAAc,IAAIhB,UAAU,EAAE;MAChC;MACA,MAAM0B,aAAa,GAAG,IAAI,CAACrO,YAAY,CAACgI,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKwO,UAAU,CAAC;MAC5E,IAAI0B,aAAa,EAAE;QACjB;QACA,IAAI,CAACA,aAAa,CAAClP,QAAQ,IAAI,CAACkP,aAAa,CAAChP,OAAO,IAAIgP,aAAa,CAAC9D,MAAM,EAAE;UAC7EhF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;YACpCmH,UAAU,EAAEA,UAAU;YACtBQ,WAAW,EAAEkB,aAAa,CAACnP,QAAQ;YACnCoP,oBAAoB,EAAE,IAAI,CAACxK,iBAAiB,CAAC/B;WAC9C,CAAC;UAEF;UACA;UACAwD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;YAC3CmH,UAAU,EAAEA,UAAU;YACtBC,eAAe,EAAEyB,aAAa,CAACnP;WAChC,CAAC;QACJ,CAAC,MAAM;UACLqG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;YAC/BmH,UAAU,EAAEA,UAAU;YACtBxN,QAAQ,EAAEkP,aAAa,CAAClP,QAAQ;YAChCE,OAAO,EAAEgP,aAAa,CAAChP,OAAO;YAC9BkL,MAAM,EAAE8D,aAAa,CAAC9D;WACvB,CAAC;QACJ;MACF,CAAC,MAAM;QACLhF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmH,UAAU,CAAC;MAChD;MAEApH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCmH,UAAU,EAAEA,UAAU;QACtBlI,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B8J,aAAa,EAAE,IAAI,CAACzK,iBAAiB,CAAC/B;OACvC,CAAC;MAEF;MACA2K,KAAK,CAACK,cAAc,EAAE;IACxB,CAAC,MAAM;MACL;MACAxH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC;EACF;EAEA;;;EAGAgJ,WAAWA,CAAC9B,KAAiB;IAC3B;IACA,IAAI,IAAI,CAAChI,aAAa,KAAK,CAAC,EAAE;IAE9B;IACA,MAAM+J,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACjC,KAAK,CAACyB,OAAO,GAAG,IAAI,CAACxJ,UAAU,CAAC;IACxD,MAAMiK,MAAM,GAAGF,IAAI,CAACC,GAAG,CAACjC,KAAK,CAAC0B,OAAO,GAAG,IAAI,CAACxJ,UAAU,CAAC;IACxD,MAAMiK,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,MAAM,GAAGA,MAAM,GAAGG,MAAM,GAAGA,MAAM,CAAC;IAE7D;IACA,IAAIC,QAAQ,GAAG,IAAI,CAAC9J,aAAa,EAAE;MACjC,IAAI,CAACF,QAAQ,GAAG,IAAI;MAEpB;MACA,IAAI,CAAC,IAAI,CAACJ,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QAEtB;QACA,IAAI,CAACsK,0BAA0B,EAAE;QAEjCxJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCqJ,QAAQ,EAAEA,QAAQ;UAClBG,SAAS,EAAE,IAAI,CAACjK,aAAa;UAC7BN,UAAU,EAAE,IAAI,CAACA;SAClB,CAAC;MACJ;IACF;IAEA;IACA,IAAI,IAAI,CAACA,UAAU,EAAE;MACnBc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAC5ByJ,QAAQ,EAAEvC,KAAK,CAACyB,OAAO;QACvBe,QAAQ,EAAExC,KAAK,CAAC0B,OAAO;QACvBS,QAAQ,EAAEA;OACX,CAAC;IACJ;EACF;EAEA;;;EAGAM,SAASA,CAACzC,KAAiB;IACzBnH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7Bf,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BI,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBH,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC2I,aAAa,EAAE,IAAI,CAACvJ,iBAAiB,CAAC/B;KACvC,CAAC;IAEF;IACA,IAAI,IAAI,CAAC0C,UAAU,EAAE;MACnBc,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC1B,iBAAiB,CAAC/B,MAAM,CAAC;IACtE;IAEA;IACA,IAAI,CAAC0C,UAAU,GAAG,KAAK;IACvB,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACH,aAAa,GAAG,CAAC;EACxB;EAEA;;;EAGA0K,YAAYA,CAAC1C,KAAiB;IAC5B,IAAI,IAAI,CAACjI,UAAU,EAAE;MACnB,IAAI,CAAC0K,SAAS,CAACzC,KAAK,CAAC;IACvB;EACF;EAEA;;;EAGAqC,0BAA0BA,CAAA;IACxB;IACA,MAAMvB,MAAM,GAAG6B,QAAQ,CAACC,gBAAgB,CAAC,IAAI,CAAC3K,UAAU,EAAE,IAAI,CAACC,UAAU,CAAgB;IACzF,IAAI,CAAC4I,MAAM,EAAE;IAEb;IACA,MAAMC,YAAY,GAAGD,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAgB;IACpE,IAAI,CAACD,YAAY,EAAE;IAEnB,MAAMd,UAAU,GAAGc,YAAY,CAACG,YAAY,CAAC,kBAAkB,CAAC;IAChE,IAAI,CAACjB,UAAU,EAAE;IAEjB;IACA,MAAM0B,aAAa,GAAG,IAAI,CAACrO,YAAY,CAACgI,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKwO,UAAU,CAAC;IAC5E,IAAI,CAAC0B,aAAa,EAAE;IAEpB;IACA,IAAIA,aAAa,CAAClP,QAAQ,IAAIkP,aAAa,CAAChP,OAAO,IAAI,CAACgP,aAAa,CAAC9D,MAAM,EAAE;MAC5EhF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCmH,UAAU,EAAEA,UAAU;QACtBxN,QAAQ,EAAEkP,aAAa,CAAClP,QAAQ;QAChCE,OAAO,EAAEgP,aAAa,CAAChP,OAAO;QAC9BkL,MAAM,EAAE8D,aAAa,CAAC9D;OACvB,CAAC;MACF;IACF;IAEA;IACA,IAAI,CAAC8D,aAAa,CAACnP,QAAQ,EAAE;MAC3BmP,aAAa,CAACnP,QAAQ,GAAG,IAAI;MAC7B,IAAI,CAAC,IAAI,CAAC4E,iBAAiB,CAACkE,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKkQ,aAAa,CAAClQ,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAAC2F,iBAAiB,CAACmF,IAAI,CAACoF,aAAa,CAAC;QAC1C9I,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAClCmH,UAAU,EAAEA,UAAU;UACtB4C,gBAAgB,EAAE,IAAI,CAACzL,iBAAiB,CAAC/B;SAC1C,CAAC;MACJ;IACF;EACF;EAEA;;;EAGAjD,oBAAoBA,CAACX,QAAa,EAAEuO,KAAiB;IACnDnH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;MAC7BsG,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BmI,eAAe,EAAEzO,QAAQ,CAACe,QAAQ;MAClCqL,MAAM,EAAEpM,QAAQ,CAACoM,MAAM;MACvBlL,OAAO,EAAElB,QAAQ,CAACkB,OAAO;MACzBF,QAAQ,EAAEhB,QAAQ,CAACgB;KACpB,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACsF,UAAU,EAAE;MACpBc,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA;IACA,IAAIrH,QAAQ,CAACgB,QAAQ,IAAIhB,QAAQ,CAACkB,OAAO,IAAI,CAAClB,QAAQ,CAACoM,MAAM,EAAE;MAC7DhF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7CmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;QAC7BgB,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ;QAC3BE,OAAO,EAAElB,QAAQ,CAACkB,OAAO;QACzBkL,MAAM,EAAEpM,QAAQ,CAACoM;OAClB,CAAC;MACF;IACF;IAEA;IACA,IAAI,CAACpM,QAAQ,CAACe,QAAQ,EAAE;MACtBf,QAAQ,CAACe,QAAQ,GAAG,IAAI;MAExB;MACA,IAAI,CAAC,IAAI,CAAC4E,iBAAiB,CAACkE,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACzL,QAAQ,KAAKA,QAAQ,CAACA,QAAQ,CAAC,EAAE;QACvE,IAAI,CAAC2F,iBAAiB,CAACmF,IAAI,CAAC9K,QAAQ,CAAC;QACrCoH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BmH,UAAU,EAAExO,QAAQ,CAACA,QAAQ;UAC7BkP,aAAa,EAAE,IAAI,CAACvJ,iBAAiB,CAAC/B,MAAM;UAC5C+B,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAAC6F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ;SAC9D,CAAC;MACJ,CAAC,MAAM;QACLoH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAErH,QAAQ,CAACA,QAAQ,CAAC;MAC7D;IACF,CAAC,MAAM;MACLoH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAErH,QAAQ,CAACA,QAAQ,CAAC;IAC1D;IAEA;IACAuO,KAAK,CAACI,eAAe,EAAE;EACzB;EAEA;;;EAGA0C,sBAAsBA,CAAC9C,KAAiB;IACtC,MAAMc,MAAM,GAAGd,KAAK,CAACc,MAAqB;IAC1C,MAAMG,cAAc,GAAGH,MAAM,CAACiC,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC;IAEjEnK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7BgI,MAAM,EAAEA,MAAM,CAACM,SAAS;MACxBH,cAAc,EAAEA,cAAc;MAC9BW,oBAAoB,EAAE,IAAI,CAACxK,iBAAiB,CAAC/B;KAC9C,CAAC;IAEF;IACA,IAAI,CAAC4L,cAAc,EAAE;MACnBpI,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,IAAI,CAACmK,kBAAkB,EAAE;IAC3B,CAAC,MAAM;MACLpK,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACvC;EACF;EAEA;;;EAGAmK,kBAAkBA,CAAA;IAChBpK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC1B,iBAAiB,CAAC/B,MAAM,CAAC;IACtE,IAAI,CAAC/B,YAAY,CAACkK,OAAO,CAACN,CAAC,IAAIA,CAAC,CAAC1K,QAAQ,GAAG,KAAK,CAAC;IAClD,IAAI,CAAC4E,iBAAiB,GAAG,EAAE;IAC3ByB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAIA;;;EAGA1F,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC8D,eAAe,EAAE;MACzB,OAAO,EAAE;IACX;IAEA,OAAO;MACL,SAAS,EAAE,MAAM;MACjB,uBAAuB,EAAE,UAAU,IAAI,CAACA,eAAe,CAACpD,SAAS,SAAS;MAC1E,KAAK,EAAE,GAAG;MACV,iBAAiB,EAAE,OAAO;MAC1B,gBAAgB,EAAE,IAAI,CAACoD,eAAe,CAACpD,SAAS,CAACoP,QAAQ;KAC1D;EACH;EAEA;;;EAGA7P,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC6D,eAAe,EAAE,OAAO,EAAE;IAEpC,MAAMiM,MAAM,GAAG,EAAE;IACjB,MAAM7I,IAAI,GAAG,IAAI,CAACpD,eAAe;IAEjC;IACA,KAAK,IAAI2E,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvB,IAAI,CAACxG,SAAS,EAAE+H,GAAG,EAAE,EAAE;MAC7C;MACA,MAAME,SAAS,GAAGzB,IAAI,CAACX,UAAU,GAAI,CAACW,IAAI,CAACxG,SAAS,GAAG,CAAC,GAAG+H,GAAG,IAAIvB,IAAI,CAACZ,SAAU;MACjFyJ,MAAM,CAAC5G,IAAI,CAACR,SAAS,CAAC;IACxB;IACA,OAAOoH,MAAM;EACf;EAEA;;;EAGA5P,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC2D,eAAe,EAAE,OAAO,EAAE;IAEpC,MAAMiM,MAAM,GAAG,EAAE;IACjB,MAAM7I,IAAI,GAAG,IAAI,CAACpD,eAAe;IAEjC;IACA,KAAK,IAAI0E,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,IAAI,CAACvG,MAAM,EAAE6H,GAAG,EAAE,EAAE;MAC1C;MACA,MAAME,SAAS,GAAGxB,IAAI,CAACb,OAAO,GAAImC,GAAG,GAAGtB,IAAI,CAACd,MAAO;MACpD2J,MAAM,CAAC5G,IAAI,CAACT,SAAS,CAAC;IACxB;IACA,OAAOqH,MAAM;EACf;EAEA;;;;EAIA9R,YAAYA,CAAC+R,GAAW;IACtB,OAAOA,GAAG,CAACF,QAAQ,EAAE,CAAChH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACxC;EAEA;;;EAGAmH,eAAeA,CAAA;IACb,IAAI,CAAC9O,UAAU,CAAC+O,KAAK,EAAE;IACvB;IACA,IAAI,CAAC/O,UAAU,CAACgP,UAAU,CAAC;MACzBxP,MAAM,EAAE,CAAC;MACTyF,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACV3F,SAAS,EAAE,CAAC;MACZ4F,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE;KACb,CAAC;IACF,IAAI,CAACrC,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAAC4C,sBAAsB,EAAE;EAC/B;EAEA;;;EAGAsJ,YAAYA,CAAA;IACV,IAAI,CAAClM,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAAC1D,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGAsG,sBAAsBA,CAAA;IACpB,MAAMuJ,SAAS,GAAG,IAAI,CAAClP,UAAU,CAACmP,WAAW,EAAE;IAE/C,IAAI,CAACD,SAAS,CAAC1P,MAAM,IAAI,CAAC0P,SAAS,CAAC3P,SAAS,EAAE;MAC7C,IAAI,CAACF,cAAc,GAAG,IAAI;MAC1B;IACF;IAEA,MAAMI,SAAS,GAAG,EAAE;IAEpB;IACA,KAAK,IAAI4H,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG6H,SAAS,CAAC1P,MAAM,EAAE6H,GAAG,EAAE,EAAE;MAC/C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG4H,SAAS,CAAC3P,SAAS,EAAE+H,GAAG,EAAE,EAAE;QAClD,MAAMC,SAAS,GAAG2H,SAAS,CAAChK,OAAO,GAAImC,GAAG,GAAG6H,SAAS,CAACjK,MAAO;QAC9D;QACA,MAAMuC,SAAS,GAAG0H,SAAS,CAAC9J,UAAU,GAAI,CAAC8J,SAAS,CAAC3P,SAAS,GAAG,CAAC,GAAG+H,GAAG,IAAI4H,SAAS,CAAC/J,SAAU;QAEhG,MAAMjI,QAAQ,GAAG;UACfA,QAAQ,EAAE,GAAGwK,MAAM,CAACH,SAAS,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGD,MAAM,CAACF,SAAS,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;UACtFpJ,OAAO,EAAE8I,GAAG,GAAG,CAAC;UAAG;UACnB7I,UAAU,EAAE8I,GAAG,GAAG,CAAC,CAAE;SACtB;QAED7H,SAAS,CAACuI,IAAI,CAAC9K,QAAQ,CAAC;MAC1B;IACF;IAEA,IAAI,CAACmC,cAAc,GAAG;MACpBG,MAAM,EAAE0P,SAAS,CAAC1P,MAAM;MACxBD,SAAS,EAAE2P,SAAS,CAAC3P,SAAS;MAC9BD,cAAc,EAAEG,SAAS,CAACqB,MAAM;MAChCrB,SAAS,EAAEA;KACZ;IAED6E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7B2K,SAAS,EAAEA,SAAS;MACpB7P,cAAc,EAAE,IAAI,CAACA;KACtB,CAAC;EACJ;EAEA;;;EAGA+P,UAAUA,CAAA;IACR,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACrP,UAAU,CAACsP,QAAQ,EAAE;MACxC,IAAI,CAACtP,UAAU,CAACsP,QAAQ,CAACD,CAAC,CAAC,CAACE,WAAW,EAAE;MACzC,IAAI,CAACvP,UAAU,CAACsP,QAAQ,CAACD,CAAC,CAAC,CAACG,sBAAsB,EAAE;IACtD;IAEA,IAAI,IAAI,CAACxP,UAAU,CAACyP,OAAO,EAAE;MAC3B;IACF;IAEA;IACA,MAAMlT,KAAK,GAAG,IAAI,CAACyD,UAAU,CAAC0P,GAAG,CAAC,OAAO,CAAC,EAAEtO,KAAK;IACjD,IAAI,IAAI,CAACwB,OAAO,CAAC0I,IAAI,CAACtE,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAKA,KAAK,CAAC,EAAE;MACjD,IAAI,CAACkI,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,cAAc,CAAC;MACnD;IACF;IAEA;IACA,MAAMwK,SAAS,GAAG,IAAI,CAAClP,UAAU,CAACmP,WAAW,EAAE;IAC/C,MAAMQ,kBAAkB,GAAG,IAAI,CAACC,wBAAwB,CAACV,SAAS,CAAC;IAEnE5K,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClC2K,SAAS,EAAEA,SAAS;MACpBS,kBAAkB,EAAEA,kBAAkB;MACtCE,cAAc,EAAEF,kBAAkB,CAAC7O;KACpC,CAAC;IAEF,MAAMgF,WAAW,GAAG;MAClBtD,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBjG,KAAK,EAAE2S,SAAS,CAAC3S,KAAK;MACtB6K,OAAO,EAAEuI,kBAAkB,CAACG,IAAI,CAAC,GAAG,CAAC;MAAE;MACvCtQ,MAAM,EAAE0P,SAAS,CAAC1P,MAAM;MACxByF,MAAM,EAAEiK,SAAS,CAACjK,MAAM;MACxBC,OAAO,EAAEgK,SAAS,CAAChK,OAAO;MAC1B3F,SAAS,EAAE2P,SAAS,CAAC3P,SAAS;MAC9B4F,SAAS,EAAE+J,SAAS,CAAC/J,SAAS;MAC9BC,UAAU,EAAE8J,SAAS,CAAC9J,UAAU;MAChCC,MAAM,EAAE6J,SAAS,CAAC7J,MAAM,IAAI;KAC7B;IAEDf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuB,WAAW,CAAC;IAE7C,IAAI,CAAC1D,iBAAiB,CAAC4D,IAAI,CAAC,aAAa,EAAEF,WAAW,EAAE,IAAI,CAAC3D,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACpFC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAC5B,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,OAAO,CAAC;QAC9C,IAAI,CAAChN,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC8B,WAAW,EAAE;QAClB,IAAI,CAACnC,aAAa,GAAGnG,KAAK;QAC1ByO,UAAU,CAAC,MAAM,IAAI,CAACzE,WAAW,EAAE,EAAE,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAC9B,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;;;EAGMuJ,SAASA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAI,CAACD,KAAI,CAACvN,aAAa,EAAE;QACvBuN,KAAI,CAACxL,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,WAAW,CAAC;QAChD;MACF;MAEA,MAAMyL,KAAK,SAASF,KAAI,CAACG,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;MACrD,IAAID,KAAK,KAAKpU,gBAAgB,CAACsU,GAAG,EAAE;QAClC;MACF;MAEA,MAAMC,OAAO,GAAGL,KAAI,CAACrN,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAK0T,KAAI,CAACvN,aAAa,CAAC;MAC1E,IAAI,CAAC4N,OAAO,EAAE;MAEdL,KAAI,CAAC7N,iBAAiB,CAACoH,MAAM,CAAC,eAAe8G,OAAO,CAACzJ,EAAE,EAAE,EAAEoJ,KAAI,CAAC9N,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACvFC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV4J,KAAI,CAACxL,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,OAAO,CAAC;UAC9CE,KAAI,CAACpL,WAAW,EAAE;QACpB,CAAC,MAAM;UACLoL,KAAI,CAACxL,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAEA;;;;EAIAmJ,wBAAwBA,CAACV,SAAc;IACrC,MAAMzP,SAAS,GAAG,EAAE;IAEpB;IACA,KAAK,IAAI4H,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG6H,SAAS,CAAC1P,MAAM,EAAE6H,GAAG,EAAE,EAAE;MAC/C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG4H,SAAS,CAAC3P,SAAS,EAAE+H,GAAG,EAAE,EAAE;QAClD,MAAMC,SAAS,GAAG2H,SAAS,CAAChK,OAAO,GAAImC,GAAG,GAAG6H,SAAS,CAACjK,MAAO;QAC9D;QACA,MAAMuC,SAAS,GAAG0H,SAAS,CAAC9J,UAAU,GAAI,CAAC8J,SAAS,CAAC3P,SAAS,GAAG,CAAC,GAAG+H,GAAG,IAAI4H,SAAS,CAAC/J,SAAU;QAEhG;QACA,MAAMuG,UAAU,GAAG,GAAGhE,MAAM,CAACH,SAAS,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGD,MAAM,CAACF,SAAS,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC/FlI,SAAS,CAACuI,IAAI,CAAC0D,UAAU,CAAC;MAC5B;IACF;IAEA,OAAOjM,SAAS;EAClB;EAIA;;;EAGM8Q,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACnB;MACA,IAAIM,MAAI,CAACpN,UAAU,EAAE;QACnBkB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvC;MACF;MAEA;MACA,MAAMkM,gBAAgB,GAAGD,MAAI,CAACE,yBAAyB,EAAE;MACzD,IAAI,CAACD,gBAAgB,CAACE,OAAO,EAAE;QAC7BH,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE+L,gBAAgB,CAACG,YAAa,CAAC;QACnE;MACF;MAEA,IAAIJ,MAAI,CAAC3N,iBAAiB,CAAC/B,MAAM,KAAK,CAAC,EAAE;QACvC0P,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,cAAc,CAAC;QACnD;MACF;MAEA,MAAM4L,OAAO,GAAGE,MAAI,CAAC7N,eAAgB,CAAC,CAAC;MAEvC;MACA,MAAM4N,eAAe,GAAGC,MAAI,CAAC3N,iBAAiB,CAAC6F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;MAEnE;MACA,MAAM2T,cAAc,GAAGP,OAAO,CAAClJ,OAAO,IAAI,EAAE;MAC5C,MAAM0J,iBAAiB,GAAGD,cAAc,CAAC3I,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACpF,MAAM0I,oBAAoB,GAAGR,eAAe,CAACpI,MAAM,CAACC,GAAG,IAAI0I,iBAAiB,CAAC5H,QAAQ,CAACd,GAAG,CAAC,CAAC;MAC3F,MAAM4I,sBAAsB,GAAGT,eAAe,CAACpI,MAAM,CAACC,GAAG,IAAI,CAAC0I,iBAAiB,CAAC5H,QAAQ,CAACd,GAAG,CAAC,CAAC;MAE9F9D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/B7B,aAAa,EAAE8N,MAAI,CAAC9N,aAAa;QACjCuO,sBAAsB,EAAEV,eAAe,CAACzP,MAAM;QAC9CoQ,0BAA0B,EAAEJ,iBAAiB;QAC7CC,oBAAoB,EAAEA,oBAAoB;QAC1CC,sBAAsB,EAAEA,sBAAsB;QAC9C9J,SAAS,EAAEoJ,OAAO,CAACzJ,EAAE;QACrBM,cAAc,EAAEmJ,OAAO,CAAClJ;OACzB,CAAC;MAEF;MACA,IAAI2J,oBAAoB,CAACjQ,MAAM,KAAK,CAAC,EAAE;QACrC0P,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAACmV,IAAI,EAAE,sBAAsB,CAAC;QAC1D;MACF;MAEA;MACA7M,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC7B0M,sBAAsB,EAAEV,eAAe,CAACzP,MAAM;QAC9CiQ,oBAAoB,EAAEA,oBAAoB;QAC1CC,sBAAsB,EAAEA;OACzB,CAAC;MAEF;MACAR,MAAI,CAACpN,UAAU,GAAG,IAAI;MAEtB;MACA,MAAM0C,WAAW,GAAG;QAClBc,KAAK,EAAE0J,OAAO,CAACzJ,EAAE;QAAG;QACpBuK,MAAM,EAAEL,oBAAoB,CAAE;OAC/B;MAEDzM,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/BuB,WAAW,EAAEA,WAAW;QACxBuL,uBAAuB,EAAEd,eAAe;QACxCQ,oBAAoB,EAAEA,oBAAoB;QAC1CC,sBAAsB,EAAEA;OACzB,CAAC;MAEF;MACAR,MAAI,CAACpO,iBAAiB,CAACoH,MAAM,CAAC,+CAA+C,EAAEgH,MAAI,CAACrO,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,EAAE;QAAEoL,IAAI,EAAExL;MAAW,CAAE,CAAC,CAClIK,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACVmK,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,QAAQgB,oBAAoB,CAACjQ,MAAM,OAAO,CAAC;UAEjF;UACAiQ,oBAAoB,CAAC9H,OAAO,CAACyC,UAAU,IAAG;YACxC8E,MAAI,CAACtN,gBAAgB,CAACiG,GAAG,CAACuC,UAAU,CAAC;UACvC,CAAC,CAAC;UAEF;UACA,IAAI8E,MAAI,CAAC7N,eAAe,CAACyE,OAAO,EAAE;YAChC,MAAMyJ,cAAc,GAAGL,MAAI,CAAC7N,eAAe,CAACyE,OAAO;YACnD,MAAM0J,iBAAiB,GAAGD,cAAc,CAAC3I,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;YACpF,MAAMkJ,gBAAgB,GAAGT,iBAAiB,CAAC3I,MAAM,CAACC,GAAG,IAAI,CAAC2I,oBAAoB,CAAC7H,QAAQ,CAACd,GAAG,CAAC,CAAC;YAC7FoI,MAAI,CAAC7N,eAAe,CAACyE,OAAO,GAAGmK,gBAAgB,CAACzB,IAAI,CAAC,GAAG,CAAC;YAEzDxL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;cACpCiN,eAAe,EAAEX,cAAc;cAC/B3N,gBAAgB,EAAE6N,oBAAoB;cACtCU,cAAc,EAAEjB,MAAI,CAAC7N,eAAe,CAACyE,OAAO;cAC5CsC,sBAAsB,EAAEoH,iBAAiB,CAAChQ,MAAM;cAChD4Q,qBAAqB,EAAEH,gBAAgB,CAACzQ;aACzC,CAAC;UACJ;UAEA;UACA0P,MAAI,CAACzR,YAAY,CAACkK,OAAO,CAAC/L,QAAQ,IAAG;YACnC,IAAI6T,oBAAoB,CAAC7H,QAAQ,CAAChM,QAAQ,CAACA,QAAQ,CAAC,EAAE;cACpDA,QAAQ,CAACkB,OAAO,GAAG,IAAI;cACvBlB,QAAQ,CAACe,QAAQ,GAAG,KAAK,CAAC,CAAC;cAC3Bf,QAAQ,CAACoM,MAAM,GAAG,KAAK,CAAC,CAAG;cAC3BpM,QAAQ,CAACmB,UAAU,GAAG,IAAI,CAAC,CAAC;YAC9B;UACF,CAAC,CAAC;UAEF;UACAmS,MAAI,CAACmB,4BAA4B,EAAE;UAEnC;UACAnB,MAAI,CAAC9B,kBAAkB,EAAE;UAEzB;UACA;UACA8B,MAAI,CAAC3N,iBAAiB,GAAG2N,MAAI,CAAC3N,iBAAiB,CAACsF,MAAM,CAACC,GAAG,IACxD,CAAC2I,oBAAoB,CAAC7H,QAAQ,CAACd,GAAG,CAAClL,QAAQ,CAAC,CAC7C;UAED;UACAsT,MAAI,CAAC1J,oBAAoB,EAAE;UAE3BxC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;YAC7BrB,gBAAgB,EAAE6N,oBAAoB;YACtCa,mBAAmB,EAAE7I,KAAK,CAACC,IAAI,CAACwH,MAAI,CAACtN,gBAAgB,CAAC;YACtD2G,uBAAuB,EAAE2G,MAAI,CAACtN,gBAAgB,CAAC2F,IAAI;YACnD4I,cAAc,EAAEjB,MAAI,CAAC7N,eAAe,CAACyE;WACtC,CAAC;QACJ,CAAC,MAAM;UACLoJ,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,CACDC,KAAK,CAAEhC,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC8L,MAAI,CAAC/L,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,YAAY,CAAC;MACnD,CAAC,CAAC,CACDmN,OAAO,CAAC,MAAK;QACZ;QACArB,MAAI,CAACpN,UAAU,GAAG,KAAK;MACzB,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGA0O,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACnP,eAAe,EAAE;MACzB,IAAI,CAAC8B,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,SAAS,CAAC;MAC9C;IACF;IAEAJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAE1C;IACA,IAAI,CAAC0G,wBAAwB,EAAE;IAE/B;IACA,IAAI,CAAC0G,4BAA4B,EAAE;IAEnC;IACA,IAAI,IAAI,CAAC5Q,sBAAsB,CAACD,MAAM,KAAK,CAAC,EAAE;MAC5C;MACA,MAAMwP,OAAO,GAAG,IAAI,CAAC3N,eAAe;MACpC,MAAMkO,cAAc,GAAGP,OAAO,CAAClJ,OAAO,IAAI,EAAE;MAC5C,MAAM0J,iBAAiB,GAAGD,cAAc,CAAC3I,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;MACpF,MAAM0J,oBAAoB,GAAG,IAAI,CAACC,4BAA4B,CAAC1B,OAAO,CAAC;MAEvEhM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjC7B,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCmH,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F,IAAI;QACnDiB,qBAAqB,EAAEf,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;QACxDwG,sBAAsB,EAAEoH,iBAAiB,CAAChQ,MAAM;QAChDgQ,iBAAiB,EAAEA,iBAAiB;QACpCmB,yBAAyB,EAAEF,oBAAoB,CAACjR,MAAM;QACtDiR,oBAAoB,EAAEA,oBAAoB;QAC1CG,2BAA2B,EAAE,IAAI,CAACnR,sBAAsB,CAACD;OAC1D,CAAC;MAEF,IAAI,IAAI,CAACoC,gBAAgB,CAAC2F,IAAI,KAAK,CAAC,EAAE;QACpC,IAAI,CAACpE,SAAS,CAACzI,aAAa,CAACmV,IAAI,EAAE,eAAe,CAAC;MACrD,CAAC,MAAM;QACL,IAAI,CAAC1M,SAAS,CAACzI,aAAa,CAACmV,IAAI,EAAE,oCAAoC,CAAC;MAC1E;MACA;IACF;IAEA;IACA,IAAI,CAACtQ,wBAAwB,GAAG,EAAE;IAElC;IACA,IAAI,CAACoC,6BAA6B,GAAG,IAAI;IAEzCqB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChC7B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCyP,8BAA8B,EAAE,IAAI,CAACpR,sBAAsB,CAACD,MAAM;MAClEsR,yBAAyB,EAAE,IAAI,CAACrR,sBAAsB,CAAC2H,GAAG,CAAC2J,GAAG,IAAIA,GAAG,CAACjR,KAAK;KAC5E,CAAC;EACJ;EAEA;;;EAGAkR,qBAAqBA,CAAA;IACnB,IAAI,CAACrP,6BAA6B,GAAG,KAAK;IAC1C,IAAI,CAACpC,wBAAwB,GAAG,EAAE;EACpC;EAEA;;;EAGA8Q,4BAA4BA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAAChP,eAAe,EAAE;MACzB,IAAI,CAAC5B,sBAAsB,GAAG,EAAE;MAChC;IACF;IAEA;IACA,MAAM8P,cAAc,GAAG,IAAI,CAAClO,eAAe,CAACyE,OAAO,IAAI,EAAE;IACzD,MAAM0J,iBAAiB,GAAGD,cAAc,CAAC3I,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;IAEpF;IACA,MAAM0J,oBAAoB,GAAG,IAAI,CAACC,4BAA4B,CAAC,IAAI,CAACrP,eAAe,CAAC;IAEpF;IACA,MAAM4P,qBAAqB,GAAG,IAAI,CAACC,6BAA6B,CAC9DT,oBAAoB,EACpBjB,iBAAiB,EACjB,IAAI,CAAC5N,gBAAgB,CACtB;IAED;IACA,IAAI,CAACnC,sBAAsB,GAAGwR,qBAAqB,CAAC7J,GAAG,CAACgD,UAAU,KAAK;MACrEnK,KAAK,EAAEmK,UAAU;MACjBtK,KAAK,EAAEsK,UAAU;MACjB+G,IAAI,EAAE,IAAI,CAACjR,eAAe,CAACkK,UAAU;KACtC,CAAC,CAAC;IAEHpH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjC7B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCwE,SAAS,EAAE,IAAI,CAACvE,eAAe,CAACkE,EAAE;MAClCgK,cAAc,EAAEA,cAAc;MAC9BnH,sBAAsB,EAAEoH,iBAAiB,CAAChQ,MAAM;MAChDgQ,iBAAiB,EAAEA,iBAAiB;MACpCmB,yBAAyB,EAAEF,oBAAoB,CAACjR,MAAM;MACtDiR,oBAAoB,EAAEA,oBAAoB;MAC1ClI,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F,IAAI;MACnDiB,qBAAqB,EAAEf,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;MACxDyG,0BAA0B,EAAE4I,qBAAqB,CAACzR,MAAM;MACxDyR,qBAAqB,EAAEA,qBAAqB;MAC5CL,2BAA2B,EAAE,IAAI,CAACnR,sBAAsB,CAACD;KAC1D,CAAC;EACJ;EAEA;;;EAGAkR,4BAA4BA,CAAC1B,OAAY;IACvC,MAAMoC,YAAY,GAAa,EAAE;IAEjC;IACA,KAAK,IAAIrL,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGiJ,OAAO,CAAC9Q,MAAM,EAAE6H,GAAG,EAAE,EAAE;MAC7C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGgJ,OAAO,CAAC/Q,SAAS,EAAE+H,GAAG,EAAE,EAAE;QAChD,MAAMC,SAAS,GAAG+I,OAAO,CAACpL,OAAO,GAAImC,GAAG,GAAGiJ,OAAO,CAACrL,MAAO;QAC1D;QACA,MAAMuC,SAAS,GAAG8I,OAAO,CAAClL,UAAU,GAAI,CAACkL,OAAO,CAAC/Q,SAAS,GAAG,CAAC,GAAG+H,GAAG,IAAIgJ,OAAO,CAACnL,SAAU;QAE1F,MAAMuG,UAAU,GAAG,GAAGhE,MAAM,CAACH,SAAS,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGD,MAAM,CAACF,SAAS,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC/F+K,YAAY,CAAC1K,IAAI,CAAC0D,UAAU,CAAC;MAC/B;IACF;IAEA,OAAOgH,YAAY;EACrB;EAEA;;;EAGAF,6BAA6BA,CAC3BT,oBAA8B,EAC9BjB,iBAA2B,EAC3Bc,mBAAgC;IAEhC,MAAMW,qBAAqB,GAAa,EAAE;IAE1C;IACA,KAAK,MAAM7G,UAAU,IAAIqG,oBAAoB,EAAE;MAC7C;MACA;MACA;MACA,IAAI,CAACjB,iBAAiB,CAAC5H,QAAQ,CAACwC,UAAU,CAAC,IAAIkG,mBAAmB,CAACrI,GAAG,CAACmC,UAAU,CAAC,EAAE;QAClF6G,qBAAqB,CAACvK,IAAI,CAAC0D,UAAU,CAAC;MACxC;IACF;IAEA,OAAO6G,qBAAqB,CAACxH,IAAI,EAAE,CAAC,CAAC;EACvC;EAEA;;;EAGQ4H,+BAA+BA,CAAA;IACrC,MAAMC,KAAK,GAAG,IAAI,CAAC7O,uBAAuB;IAC1C,MAAM8O,UAAU,GAAGD,KAAK,CAAC5O,aAAa,IAAI4O,KAAK,CAAC3O,eAAe,IAC7C2O,KAAK,CAAC1O,kBAAkB,IAAI0O,KAAK,CAACzO,mBAAmB;IAEvEG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAChCP,aAAa,EAAE4O,KAAK,CAAC5O,aAAa;MAClCC,eAAe,EAAE2O,KAAK,CAAC3O,eAAe;MACtCC,kBAAkB,EAAE0O,KAAK,CAAC1O,kBAAkB;MAC5CC,mBAAmB,EAAEyO,KAAK,CAACzO,mBAAmB;MAC9C0O,UAAU,EAAEA;KACb,CAAC;IAEF,OAAOA,UAAU;EACnB;EAEA;;;EAGQrM,0BAA0BA,CAAA;IAChC,IAAI,IAAI,CAACmM,+BAA+B,EAAE,EAAE;MAC1C,IAAI,CAACpP,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,aAAa,GAAG,KAAK;MAC1BgB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF;EAEA;;;EAGQqB,4BAA4BA,CAAA;IAClC,IAAI,CAAC7B,uBAAuB,GAAG;MAC7BC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,KAAK;MACtBC,kBAAkB,EAAE,KAAK;MACzBC,mBAAmB,EAAE;KACtB;IACD,IAAI,CAACZ,YAAY,GAAG,KAAK;IACzBe,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;EACnC;EAEA;;;EAGAmM,yBAAyBA,CAAA;IACvBpM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCjB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/Bb,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCmQ,aAAa,EAAE,IAAI,CAAClQ,OAAO,CAAC9B,MAAM;MAClCiS,kBAAkB,EAAE,IAAI,CAAChU,YAAY,CAAC+B,MAAM;MAC5CkS,iBAAiB,EAAE,IAAI,CAACrQ,eAAe,EAAEkE,EAAE;MAC3C9C,uBAAuB,EAAE,IAAI,CAACA;KAC/B,CAAC;IAEF;IACA,IAAI,IAAI,CAACT,aAAa,EAAE;MACtB,OAAO;QACLqN,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEA;IACA,IAAI,CAAC,IAAI,CAAC+B,+BAA+B,EAAE,EAAE;MAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC7O,uBAAuB;MAC1C,IAAIkP,YAAY,GAAG,EAAE;MACrB,IAAI,CAACL,KAAK,CAAC5O,aAAa,EAAEiP,YAAY,CAACjL,IAAI,CAAC,QAAQ,CAAC;MACrD,IAAI,CAAC4K,KAAK,CAAC3O,eAAe,EAAEgP,YAAY,CAACjL,IAAI,CAAC,QAAQ,CAAC;MACvD,IAAI,CAAC4K,KAAK,CAAC1O,kBAAkB,EAAE+O,YAAY,CAACjL,IAAI,CAAC,OAAO,CAAC;MACzD,IAAI,CAAC4K,KAAK,CAACzO,mBAAmB,EAAE8O,YAAY,CAACjL,IAAI,CAAC,MAAM,CAAC;MAEzD,OAAO;QACL2I,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,iBAAiBqC,YAAY,CAACnD,IAAI,CAAC,GAAG,CAAC;OACtD;IACH;IAEA;IACA,IAAI,IAAI,CAAClN,OAAO,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO;QACL6P,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEA;IACA,IAAI,CAAC,IAAI,CAAClO,aAAa,EAAE;MACvB,OAAO;QACLiO,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEA;IACA,IAAI,CAAC,IAAI,CAACjO,eAAe,EAAE;MACzB;MACA,IAAI,CAACA,eAAe,GAAG,IAAI,CAACC,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAK,IAAI,CAACmG,aAAa,CAAC;MACjF,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;QACzB,OAAO;UACLgO,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;SACf;MACH;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAACjO,eAAe,CAACkE,EAAE,EAAE;MAC5B,OAAO;QACL8J,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEA;IACA,IAAI,IAAI,CAAC7R,YAAY,CAAC+B,MAAM,KAAK,CAAC,EAAE;MAClC,OAAO;QACL6P,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEA;IACA,IAAI,CAAC,IAAI,CAAC7M,uBAAuB,CAACI,mBAAmB,EAAE;MACrD,OAAO;QACLwM,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;OACf;IACH;IAEAtM,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACtC2C,SAAS,EAAE,IAAI,CAACvE,eAAe,CAACkE,EAAE;MAClCtK,KAAK,EAAE,IAAI,CAACoG,eAAe,CAACpG,KAAK;MACjCwW,kBAAkB,EAAE,IAAI,CAAChU,YAAY,CAAC+B,MAAM;MAC5CoS,oBAAoB,EAAE,IAAI,CAAChQ,gBAAgB,CAAC2F,IAAI;MAChDsK,iBAAiB,EAAE,IAAI,CAACR,+BAA+B;KACxD,CAAC;IAEF,OAAO;MAAEhC,OAAO,EAAE;IAAI,CAAE;EAC1B;EAEA;;;EAGA1F,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACtI,eAAe,EAAE;MACzB2B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC;IACA,MAAMgG,kBAAkB,GAAG,IAAI,CAACxL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,CAAC,CAACsK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;IACxF,MAAMsN,uBAAuB,GAAG,IAAI,CAACzL,YAAY,CAACoJ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACvK,OAAO,IAAIuK,CAAC,CAACtK,UAAU,CAAC,CAACqK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;IAE7G;IACA,MAAMkW,mBAAmB,GAAGrK,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;IAE7D;IACA,MAAMmQ,UAAU,GAAG;MACjBC,cAAc,EAAE/I,kBAAkB,CAACzJ,MAAM;MACzCyS,mBAAmB,EAAE/I,uBAAuB,CAAC1J,MAAM;MACnD0S,eAAe,EAAEJ,mBAAmB,CAACtS,MAAM;MAC3C8J,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,uBAAuB,CAACO,IAAI,EAAE,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACsI,mBAAmB,CAACrI,IAAI,EAAE;KACrG;IAEDzG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BgG,kBAAkB,EAAEA,kBAAkB;MACtCC,uBAAuB,EAAEA,uBAAuB;MAChD4I,mBAAmB,EAAEA,mBAAmB;MACxCC,UAAU,EAAEA;KACb,CAAC;IAEF;IACA,IAAI,CAACA,UAAU,CAACzI,MAAM,EAAE;MACtBtG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC;MACA,IAAI,CAACrB,gBAAgB,CAAC2C,KAAK,EAAE;MAC7B2E,uBAAuB,CAACvB,OAAO,CAACyC,UAAU,IAAG;QAC3C,IAAI,CAACxI,gBAAgB,CAACiG,GAAG,CAACuC,UAAU,CAAC;MACvC,CAAC,CAAC;MAEFpH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/BkP,0BAA0B,EAAE,IAAI,CAACvQ,gBAAgB,CAAC2F,IAAI;QACtD6K,qBAAqB,EAAE3K,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB;OACxD,CAAC;MAEF;MACA,IAAI,CAACyO,4BAA4B,EAAE;IACrC,CAAC,MAAM;MACLrN,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACtC;IAEA;IACA,IAAI,CAACR,uBAAuB,CAACI,mBAAmB,GAAG,IAAI;IACvDG,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B;IACA,IAAI,CAACiC,0BAA0B,EAAE;EACnC;EAEA;;;EAGAhF,eAAeA,CAACkK,UAAkB;IAChC;IACA,OAAO,KAAK;EACd;EAEA;;;EAGApK,kBAAkBA,CAACoK,UAAkB;IACnC,OAAO,IAAI,CAAC7K,wBAAwB,CAACqI,QAAQ,CAACwC,UAAU,CAAC;EAC3D;EAEA;;;EAGAvK,uBAAuBA,CAACuK,UAAkB;IACxC,MAAMiI,KAAK,GAAG,IAAI,CAAC9S,wBAAwB,CAAC+S,OAAO,CAAClI,UAAU,CAAC;IAC/D,IAAIiI,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC9S,wBAAwB,CAACgT,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAAC9S,wBAAwB,CAACmH,IAAI,CAAC0D,UAAU,CAAC;IAChD;EACF;EAEA;;;EAGApL,yBAAyBA,CAAA;IACvB,IAAI,CAACO,wBAAwB,GAAG,CAAC,GAAG,IAAI,CAACE,sBAAsB,CAAC2H,GAAG,CAACoL,IAAI,IAAIA,IAAI,CAAC1S,KAAK,CAAC,CAAC;EAC1F;EAEA;;;EAGAX,2BAA2BA,CAAA;IACzB,MAAMkL,eAAe,GAAG,IAAIxI,GAAG,CAAC,IAAI,CAACtC,wBAAwB,CAAC;IAC9D,IAAI,CAACA,wBAAwB,GAAG,IAAI,CAACE,sBAAsB,CACxD2H,GAAG,CAACoL,IAAI,IAAIA,IAAI,CAAC1S,KAAK,CAAC,CACvB+G,MAAM,CAACtB,EAAE,IAAI,CAAC8E,eAAe,CAACpC,GAAG,CAAC1C,EAAE,CAAC,CAAC;EAC3C;EAEA;;;EAGAjG,qBAAqBA,CAAA;IACnB,IAAI,CAACC,wBAAwB,GAAG,EAAE;EACpC;EAEA;;;EAGAnB,iBAAiBA,CAACiU,KAAa,EAAEG,IAAS;IACxC,OAAOA,IAAI,CAAC1S,KAAK;EACnB;EAEA;;;EAGA2S,wBAAwBA,CAAA;IACtB;IACA,IAAI,IAAI,CAAC1Q,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACA,MAAMkM,gBAAgB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IACzD,IAAI,CAACD,gBAAgB,CAACE,OAAO,EAAE;MAC7B,IAAI,CAAClM,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE+L,gBAAgB,CAACG,YAAa,CAAC;MACnE;IACF;IAEA,IAAI,IAAI,CAAC/P,wBAAwB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAAC2D,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,YAAY,CAAC;MACjD;IACF;IAEA,MAAM4L,OAAO,GAAG,IAAI,CAAC3N,eAAgB,CAAC,CAAC;IAEvC;IACA,MAAMqR,qBAAqB,GAAG,IAAI,CAACnT,wBAAwB,CAACsH,MAAM,CAACC,GAAG,IAAI,IAAI,CAAClF,gBAAgB,CAACqG,GAAG,CAACnB,GAAG,CAAC,CAAC;IACzG,MAAM6L,uBAAuB,GAAG,IAAI,CAACpT,wBAAwB,CAACsH,MAAM,CAACC,GAAG,IAAI,CAAC,IAAI,CAAClF,gBAAgB,CAACqG,GAAG,CAACnB,GAAG,CAAC,CAAC;IAE5G9D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/B7B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCuO,sBAAsB,EAAE,IAAI,CAACpQ,wBAAwB,CAACC,MAAM;MAC5D8Q,mBAAmB,EAAE7I,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;MACtD8Q,qBAAqB,EAAEA,qBAAqB;MAC5CC,uBAAuB,EAAEA,uBAAuB;MAChD/M,SAAS,EAAEoJ,OAAO,CAACzJ,EAAE;MACrBM,cAAc,EAAEmJ,OAAO,CAAClJ;KACzB,CAAC;IAEF;IACA,IAAI4M,qBAAqB,CAAClT,MAAM,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC2D,SAAS,CAACzI,aAAa,CAACmV,IAAI,EAAE,sBAAsB,CAAC;MAC1D;IACF;IAEA;IACA,IAAI8C,uBAAuB,CAACnT,MAAM,GAAG,CAAC,EAAE;MACtCwD,OAAO,CAAC4P,IAAI,CAAC,0BAA0B,EAAED,uBAAuB,CAAC;IACnE;IAEA;IACA,IAAI,CAAC5Q,WAAW,GAAG,IAAI;IAEvB;IACA,MAAMyC,WAAW,GAAG;MAClBc,KAAK,EAAE0J,OAAO,CAACzJ,EAAE;MAAG;MACpBuK,MAAM,EAAE4C,qBAAqB,CAAE;KAChC;IAED1P,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BuB,WAAW,EAAEA,WAAW;MACxBqO,wBAAwB,EAAE,IAAI,CAACtT,wBAAwB;MACvDmT,qBAAqB,EAAEA,qBAAqB;MAC5CC,uBAAuB,EAAEA;KAC1B,CAAC;IAEF;IACA,IAAI,CAAC7R,iBAAiB,CAAC4D,IAAI,CAAC,+CAA+C,EAAEF,WAAW,EAAE,IAAI,CAAC3D,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACtHC,IAAI,CAAEiO,QAAa,IAAI;MACtB9P,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6P,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAIA,QAAQ,CAAC/N,EAAE,EAAE;QAC3B,IAAI,CAAC5B,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,QAAQiE,qBAAqB,CAAClT,MAAM,OAAO,CAAC;QAElF;QACAkT,qBAAqB,CAAC/K,OAAO,CAACyC,UAAU,IAAG;UACzC,IAAI,CAACxI,gBAAgB,CAACsG,MAAM,CAACkC,UAAU,CAAC;QAC1C,CAAC,CAAC;QAEF;QACA,IAAI,CAAC3M,YAAY,CAACkK,OAAO,CAAC/L,QAAQ,IAAG;UACnC,IAAI8W,qBAAqB,CAAC9K,QAAQ,CAAChM,QAAQ,CAACA,QAAQ,CAAC,EAAE;YACrDA,QAAQ,CAACkB,OAAO,GAAG,KAAK;YACxBlB,QAAQ,CAACoM,MAAM,GAAG,IAAI,CAAC,CAAI;YAC3BpM,QAAQ,CAACmB,UAAU,GAAG,KAAK,CAAC,CAAC;UAC/B;QACF,CAAC,CAAC;QAEF;QACA,IAAI,CAACwC,wBAAwB,GAAG,EAAE;QAElC;QACA,IAAI,CAAC8Q,4BAA4B,EAAE;QAEnC;QACA,IAAI,CAAC9M,WAAW,EAAE;QAElB;QACAmG,UAAU,CAAC,MAAK;UACd,IAAI,CAACzE,WAAW,EAAE;QACpB,CAAC,EAAE,GAAG,CAAC;QAEP;QACA,IAAI,CAACtD,6BAA6B,GAAG,KAAK;QAE1CqB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;UAC7B8P,iBAAiB,EAAEL,qBAAqB;UACxCpC,mBAAmB,EAAE7I,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;UACtD2G,uBAAuB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC2F;SAChD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpE,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0P,QAAQ,EAAE3N,GAAG,IAAI,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,CACDC,KAAK,CAAEhC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACD,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,SAAS,CAAC;IAChD,CAAC,CAAC,CACDmN,OAAO,CAAC,MAAK;MACZ;MACA,IAAI,CAACxO,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;EACN;EAEA;;;EAGAiR,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC5R,aAAa,EAAE;MACvB,IAAI,CAAC+B,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,cAAc,CAAC;MACnD;IACF;IAEA,IAAI,CAACvE,WAAW,CAAC4O,KAAK,EAAE;IACxB,IAAI,CAAC5O,WAAW,CAAC6O,UAAU,CAAC;MAC1B1J,aAAa,EAAE,IAAI,CAAC5C;KACrB,CAAC;IACF,IAAI,CAACM,qBAAqB,GAAG,IAAI;EACnC;EAEA;;;EAGMuR,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtE,iBAAA;MACf,KAAK,MAAMb,CAAC,IAAImF,MAAI,CAACrU,WAAW,CAACmP,QAAQ,EAAE;QACzCkF,MAAI,CAACrU,WAAW,CAACmP,QAAQ,CAACD,CAAC,CAAC,CAACE,WAAW,EAAE;QAC1CiF,MAAI,CAACrU,WAAW,CAACmP,QAAQ,CAACD,CAAC,CAAC,CAACG,sBAAsB,EAAE;MACvD;MAEA,IAAIgF,MAAI,CAACrU,WAAW,CAACsP,OAAO,EAAE;QAC5B;MACF;MAEA,MAAMjK,QAAQ,GAAGgP,MAAI,CAACrU,WAAW,CAACuP,GAAG,CAAC,UAAU,CAAC,EAAEtO,KAAK;MAExD;MACA,MAAMqT,KAAK,GAAG,gBAAgB;MAC9B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAClP,QAAQ,CAACmP,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;QAC5CH,MAAI,CAAC/P,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,8BAA8B,CAAC;QACnE;MACF;MAEA;MACA,IAAI8P,MAAI,CAAC5R,OAAO,CAAC0I,IAAI,CAACtE,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAKiJ,QAAQ,CAAC,EAAE;QACpD,MAAM2K,KAAK,SAASqE,MAAI,CAACpE,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC;QAC1D,IAAID,KAAK,KAAKpU,gBAAgB,CAACsU,GAAG,EAAE;UAClC;QACF;MACF;MAEA,MAAMuE,eAAe,GAAGJ,MAAI,CAAC5R,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAKiY,MAAI,CAAC9R,aAAa,CAAC;MAClF,IAAI,CAACkS,eAAe,EAAE;MAEtB;MACA,MAAMC,OAAO,GAAG,sBAAsBL,MAAI,CAAChS,QAAQ,QAAQgS,MAAI,CAAC9R,aAAa,qBAAqBoS,kBAAkB,CAACtP,QAAQ,CAAC,EAAE;MAEhIgP,MAAI,CAACpS,iBAAiB,CAAC4D,IAAI,CAAC6O,OAAO,EAAE,EAAE,EAAEL,MAAI,CAACrS,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACrEC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACVmO,MAAI,CAAC/P,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,OAAO,CAAC;UAC9CyE,MAAI,CAACxR,qBAAqB,GAAG,KAAK;UAClCwR,MAAI,CAAC3P,WAAW,EAAE;UAClB2P,MAAI,CAAC9R,aAAa,GAAG8C,QAAQ;UAC7BwF,UAAU,CAAC,MAAMwJ,MAAI,CAACjO,WAAW,EAAE,EAAE,GAAG,CAAC;QAC3C,CAAC,MAAM;UACLiO,MAAI,CAAC/P,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGMsO,oBAAoBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9E,iBAAA;MACxB,IAAI,CAAC8E,MAAI,CAACtS,aAAa,EAAE;QACvBsS,MAAI,CAACvQ,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,aAAa,CAAC;QAClD;MACF;MAEA,IAAIsQ,MAAI,CAACnS,iBAAiB,CAAC/B,MAAM,KAAK,CAAC,EAAE;QACvCkU,MAAI,CAACvQ,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,cAAc,CAAC;QACnD;MACF;MAEA;MACA,MAAMuQ,aAAa,GAAGD,MAAI,CAACnS,iBAAiB,CAACyI,IAAI,CAAClD,GAAG,IAAIA,GAAG,CAAClK,QAAQ,CAAC;MACtE,IAAI+W,aAAa,EAAE;QACjBD,MAAI,CAACvQ,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,qBAAqB,CAAC;QAC1D;MACF;MAEA;MACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/B1B,iBAAiB,EAAEmS,MAAI,CAACnS,iBAAiB,CAAC6F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC;QAC9DwF,aAAa,EAAEsS,MAAI,CAACtS;OACrB,CAAC;MAEF,MAAM4N,OAAO,GAAG0E,MAAI,CAACpS,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAKyY,MAAI,CAACtS,aAAa,CAAC;MAC1E,IAAI,CAAC4N,OAAO,EAAE;MAEd,MAAM4E,gBAAgB,GAAGF,MAAI,CAACnS,iBAAiB,CAAC6F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC,CAAC4S,IAAI,CAAC,GAAG,CAAC;MAC9E,MAAMhK,WAAW,GAAG;QAClBc,KAAK,EAAE0J,OAAO,CAACzJ,EAAE;QACjBrE,QAAQ,EAAEwS,MAAI,CAACxS,QAAQ;QACvB4E,OAAO,EAAE8N;OACV;MAEDF,MAAI,CAAC5S,iBAAiB,CAAC4D,IAAI,CAAC,oBAAoB,EAAEF,WAAW,EAAEkP,MAAI,CAAC7S,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAC3FC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV/B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UAEpC;UACAyQ,MAAI,CAACnS,iBAAiB,CAACoG,OAAO,CAACb,GAAG,IAAG;YACnCA,GAAG,CAAClK,QAAQ,GAAG,IAAI;YACnBoG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6D,GAAG,CAAClL,QAAQ,CAAC;UACjD,CAAC,CAAC;UAEF8X,MAAI,CAACvQ,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,OAAO,CAAC;UAE9C;UACAiF,MAAI,CAACrO,mBAAmB,EAAE;UAE1B;UACAqO,MAAI,CAACtG,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACLsG,MAAI,CAACvQ,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAIA;;;EAGM0O,qBAAqBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlF,iBAAA;MACzB,IAAI,CAACkF,MAAI,CAAC1S,aAAa,EAAE;QACvB0S,MAAI,CAAC3Q,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,WAAW,CAAC;QAChD;MACF;MAEA;MACA,MAAMwQ,gBAAgB,GAAGE,MAAI,CAACrW,YAAY,CAACoJ,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAClK,QAAQ,CAAC;MAEtEoG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/B7B,aAAa,EAAE0S,MAAI,CAAC1S,aAAa;QACjCpD,cAAc,EAAE8V,MAAI,CAACrW,YAAY,CAAC+B,MAAM;QACxCoU,gBAAgB,EAAEA,gBAAgB,CAACxM,GAAG,CAACC,CAAC,KAAK;UAAEzL,QAAQ,EAAEyL,CAAC,CAACzL,QAAQ;UAAEgB,QAAQ,EAAEyK,CAAC,CAACzK;QAAQ,CAAE,CAAC,CAAC;QAC7F4E,eAAe,EAAEsS,MAAI,CAACtS;OACvB,CAAC;MAEF,IAAIoS,gBAAgB,CAACpU,MAAM,KAAK,CAAC,EAAE;QACjCsU,MAAI,CAAC3Q,SAAS,CAACzI,aAAa,CAACmV,IAAI,EAAE,kBAAkB,CAAC;QACtD;MACF;MAEA;MACA7M,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/B8Q,qBAAqB,EAAEH,gBAAgB,CAACpU,MAAM;QAC9CwU,oBAAoB,EAAEJ,gBAAgB,CAACxM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ;OAC3D,CAAC;MAEF,MAAMoT,OAAO,GAAG8E,MAAI,CAACxS,OAAO,CAACmE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACzK,KAAK,KAAK6Y,MAAI,CAAC1S,aAAa,CAAC;MAC1E,IAAI,CAAC4N,OAAO,EAAE;MAEd;MACA,MAAMiF,iBAAiB,GAAGL,gBAAgB,CAACxM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzL,QAAQ,CAAC,CAAC4S,IAAI,CAAC,GAAG,CAAC;MAEzExL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCqC,KAAK,EAAE0J,OAAO,CAACzJ,EAAE;QACjBrE,QAAQ,EAAE4S,MAAI,CAAC5S,QAAQ;QACvB+S,iBAAiB,EAAEA,iBAAiB;QACpCF,qBAAqB,EAAEH,gBAAgB,CAACpU,MAAM;QAC9C0U,cAAc,EAAE,4BAA4BlF,OAAO,CAACzJ,EAAE;OACvD,CAAC;MAEF;MACA;MACA;MACAuO,MAAI,CAAChT,iBAAiB,CAACoH,MAAM,CAAC,4BAA4B8G,OAAO,CAACzJ,EAAE,EAAE,EAAEuO,MAAI,CAACjT,GAAG,CAAC8D,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACpGC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV+O,MAAI,CAAC3Q,SAAS,CAACzI,aAAa,CAAC+T,OAAO,EAAE,WAAW,CAAC;UAClDqF,MAAI,CAACzO,mBAAmB,EAAE;UAC1ByO,MAAI,CAAC1G,kBAAkB,EAAE;UAEzBpK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;YAC/BqC,KAAK,EAAE0J,OAAO,CAACzJ,EAAE;YACjB4O,qBAAqB,EAAEP,gBAAgB,CAACpU,MAAM;YAC9CsT,QAAQ,EAAEhO;WACX,CAAC;QACJ,CAAC,MAAM;UACLgP,MAAI,CAAC3Q,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE0B,GAAG,CAACK,GAAG,CAAC;UAC5CnC,OAAO,CAACI,KAAK,CAAC,mBAAmB,EAAE0B,GAAG,CAAC;QACzC;MACF,CAAC,CAAC,CACDM,KAAK,CAAEhC,KAAK,IAAI;QACfJ,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C0Q,MAAI,CAAC3Q,SAAS,CAACzI,aAAa,CAAC0I,KAAK,EAAE,cAAc,CAAC;MACrD,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGAgR,MAAMA,CAAA;IACJ,IAAI,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EACnC;;;uBAl5DW3T,kBAAkB,EAAA7F,EAAA,CAAAyZ,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA3Z,EAAA,CAAAyZ,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA7Z,EAAA,CAAAyZ,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA/Z,EAAA,CAAAyZ,iBAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAlBpU,kBAAkB;MAAAqU,SAAA;MAAAC,QAAA,GAAAna,EAAA,CAAAoa,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ3B1a,EAFJ,CAAAK,cAAA,iBAAsE,aAC5C,SAClB;UAAAL,EAAA,CAAAM,MAAA,uDAAQ;UAEhBN,EAFgB,CAAAO,YAAA,EAAK,EACb,EACE;UASFP,EANR,CAAAK,cAAA,iBAAsE,aAE5D,gBACc,aACU,gBAEyC;UAA5BL,EAAA,CAAAgB,UAAA,mBAAA4Z,oDAAA;YAAA,OAASD,GAAA,CAAAhI,eAAA,EAAiB;UAAA,EAAC;UAChE3S,EAAA,CAAAM,MAAA,2BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAoE;UAA7BL,EAAA,CAAAgB,UAAA,mBAAA6Z,qDAAA;YAAA,OAASF,GAAA,CAAAxC,gBAAA,EAAkB;UAAA,EAAC;UACjEnY,EAAA,CAAAM,MAAA,4BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAA6D;UAAtBL,EAAA,CAAAgB,UAAA,mBAAA8Z,qDAAA;YAAA,OAASH,GAAA,CAAA9G,SAAA,EAAW;UAAA,EAAC;UAC1D7T,EAAA,CAAAM,MAAA,4BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAA4E;UAArCL,EAAA,CAAAgB,UAAA,mBAAA+Z,qDAAA;YAAA,OAASJ,GAAA,CAAAhF,wBAAA,EAA0B;UAAA,EAAC;UACzE3V,EAAA,CAAAM,MAAA,kCACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAmE;UAA5BL,EAAA,CAAAgB,UAAA,mBAAAga,qDAAA;YAAA,OAASL,GAAA,CAAAvG,eAAA,EAAiB;UAAA,EAAC;UAChEpU,EAAA,CAAAM,MAAA,kCACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAwE;UAAjCL,EAAA,CAAAgB,UAAA,mBAAAia,qDAAA;YAAA,OAASN,GAAA,CAAA/B,oBAAA,EAAsB;UAAA,EAAC;UACrE5Y,EAAA,CAAAM,MAAA,8CACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAyE;UAAlCL,EAAA,CAAAgB,UAAA,mBAAAka,qDAAA;YAAA,OAASP,GAAA,CAAA3B,qBAAA,EAAuB;UAAA,EAAC;UACtEhZ,EAAA,CAAAM,MAAA,8CACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAgF;UAAzCL,EAAA,CAAAgB,UAAA,mBAAAma,qDAAA;YAAA,OAASR,GAAA,CAAApB,MAAA,EAAQ;UAAA,EAAC;UACvDvZ,EAAA,CAAAM,MAAA,sBACF;UAIRN,EAJQ,CAAAO,YAAA,EAAS,EACL,EACC,EACF,EACD;UAQAP,EALV,CAAAK,cAAA,kBAAyE,cAC/D,iBACc,cACkB,cACR,gBACS;UAAAL,EAAA,CAAAM,MAAA,iCAAK;UAAAN,EAAA,CAAAO,YAAA,EAAO;UAC7CP,EAAA,CAAAK,cAAA,qBAAqG;UAA1FL,EAAA,CAAAob,gBAAA,2BAAAC,gEAAAna,MAAA;YAAAlB,EAAA,CAAAsb,kBAAA,CAAAX,GAAA,CAAApU,aAAA,EAAArF,MAAA,MAAAyZ,GAAA,CAAApU,aAAA,GAAArF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAClB,EAAA,CAAAgB,UAAA,2BAAAqa,gEAAA;YAAA,OAAiBV,GAAA,CAAAvQ,WAAA,EAAa;UAAA,EAAC;UACpEpK,EAAA,CAAA2B,UAAA,KAAA4Z,wCAAA,wBAAmF;UAM/Fvb,EALU,CAAAO,YAAA,EAAY,EACR,EACF,EACC,EACF,EACD;UAIRP,EADF,CAAAK,cAAA,mBAAgE,eAOb;UAA5CL,EAJA,CAAAgB,UAAA,uBAAAwa,sDAAAta,MAAA;YAAA,OAAayZ,GAAA,CAAAzK,WAAA,CAAAhP,MAAA,CAAmB;UAAA,EAAC,uBAAAua,sDAAAva,MAAA;YAAA,OACpByZ,GAAA,CAAAvJ,WAAA,CAAAlQ,MAAA,CAAmB;UAAA,EAAC,qBAAAwa,oDAAAxa,MAAA;YAAA,OACtByZ,GAAA,CAAA5I,SAAA,CAAA7Q,MAAA,CAAiB;UAAA,EAAC,wBAAAya,uDAAAza,MAAA;YAAA,OACfyZ,GAAA,CAAA3I,YAAA,CAAA9Q,MAAA,CAAoB;UAAA,EAAC,sBAAA0a,qDAAA1a,MAAA;YAAA,OACvByZ,GAAA,CAAAvI,sBAAA,CAAAlR,MAAA,CAA8B;UAAA,EAAC;UA0C9ClB,EAxCA,CAAA2B,UAAA,KAAAka,kCAAA,mBAAoF,KAAAC,kCAAA,kBAwCL;UAInF9b,EADE,CAAAO,YAAA,EAAM,EACE;UAGVP,EAAA,CAAAK,cAAA,oBACmF;UADzEL,EAAA,CAAAob,gBAAA,6BAAAW,iEAAA7a,MAAA;YAAAlB,EAAA,CAAAsb,kBAAA,CAAAX,GAAA,CAAA/T,oBAAA,EAAA1F,MAAA,MAAAyZ,GAAA,CAAA/T,oBAAA,GAAA1F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UACpClB,EADsD,CAAAgB,UAAA,wBAAAgb,4DAAA;YAAA,OAAcrB,GAAA,CAAA7H,YAAA,EAAc;UAAA,EAAC,oBAAAmJ,wDAAA;YAAA,OACzEtB,GAAA,CAAA1H,UAAA,EAAY;UAAA,EAAC;UAC/BjT,EAAA,CAAA2B,UAAA,KAAAua,2CAAA,6BAA8B;UA+HhClc,EAAA,CAAAO,YAAA,EAAW;UAKXP,EAAA,CAAAK,cAAA,oBACmF;UADzEL,EAAA,CAAAob,gBAAA,6BAAAe,iEAAAjb,MAAA;YAAAlB,EAAA,CAAAsb,kBAAA,CAAAX,GAAA,CAAA9T,qBAAA,EAAA3F,MAAA,MAAAyZ,GAAA,CAAA9T,qBAAA,GAAA3F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UACrClB,EADoD,CAAAgB,UAAA,wBAAAob,4DAAA;YAAA,OAAAzB,GAAA,CAAA9T,qBAAA,GAAsC,KAAK;UAAA,EAAC,oBAAAwV,wDAAA;YAAA,OACtF1B,GAAA,CAAAvC,WAAA,EAAa;UAAA,EAAC;UAChCpY,EAAA,CAAA2B,UAAA,KAAA2a,2CAAA,4BAA8B;UAmBhCtc,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAK,cAAA,oBAC8J;UADpJL,EAAA,CAAAob,gBAAA,6BAAAmB,iEAAArb,MAAA;YAAAlB,EAAA,CAAAsb,kBAAA,CAAAX,GAAA,CAAA7T,6BAAA,EAAA5F,MAAA,MAAAyZ,GAAA,CAAA7T,6BAAA,GAAA5F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6C;UAC7ClB,EAD6D,CAAAgB,UAAA,wBAAAwb,4DAAA;YAAA,OAAc7B,GAAA,CAAAxE,qBAAA,EAAuB;UAAA,EAAC,oBAAAsG,wDAAA;YAAA,OACzF9B,GAAA,CAAA/C,wBAAA,EAA0B;UAAA,EAAC;UAC7C5X,EAAA,CAAA2B,UAAA,KAAA+a,2CAAA,4BAA8B;UAsDhC1c,EAAA,CAAAO,YAAA,EAAW;;;UAvViBP,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAA2D,eAAA,KAAAgZ,GAAA,EAAyC;UAOpC3c,EAAA,CAAAQ,SAAA,GAAoC;UAApCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAA2D,eAAA,KAAAiZ,GAAA,EAAoC;UAM3C5c,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UASfF,EAAA,CAAAQ,SAAA,GAAyC;UAAzCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAA2D,eAAA,KAAAkZ,GAAA,EAAyC;UAMnD7c,EAAA,CAAAQ,SAAA,GAA2B;UAA3BR,EAAA,CAAA8c,gBAAA,YAAAnC,GAAA,CAAApU,aAAA,CAA2B;UACTvG,EAAA,CAAAQ,SAAA,EAAU;UAAVR,EAAA,CAAAE,UAAA,YAAAya,GAAA,CAAAlU,OAAA,CAAU;UASnBzG,EAAA,CAAAQ,SAAA,EAAiC;UAAjCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAA2D,eAAA,KAAAoZ,GAAA,EAAiC;UAExD/c,EAAA,CAAAQ,SAAA,EAA6B;UAA7BR,EAAA,CAAAkF,WAAA,aAAAyV,GAAA,CAAAtT,UAAA,CAA6B;UAONrH,EAAA,CAAAQ,SAAA,EAA6B;UAA7BR,EAAA,CAAAE,UAAA,SAAAya,GAAA,CAAA/X,YAAA,CAAA+B,MAAA,KAA6B;UAwCjD3E,EAAA,CAAAQ,SAAA,EAAkD;UAAlDR,EAAA,CAAAE,UAAA,SAAAya,GAAA,CAAA/X,YAAA,CAAA+B,MAAA,UAAAgW,GAAA,CAAAnU,eAAA,CAAkD;UAOlDxG,EAAA,CAAAQ,SAAA,EAAoC;UAApCR,EAAA,CAAA8c,gBAAA,cAAAnC,GAAA,CAAA/T,oBAAA,CAAoC;UACoB5G,EAAA,CAAAE,UAAA,iBAAgB;UAqIxEF,EAAA,CAAAQ,SAAA,GAAqC;UAArCR,EAAA,CAAA8c,gBAAA,cAAAnC,GAAA,CAAA9T,qBAAA,CAAqC;UACoB7G,EAAA,CAAAE,UAAA,gBAAe;UAuBxEF,EAAA,CAAAQ,SAAA,GAA6C;UAA7CR,EAAA,CAAA8c,gBAAA,cAAAnC,GAAA,CAAA7T,6BAAA,CAA6C;UAC6C9G,EAAhB,CAAAE,UAAA,gBAAe,gBAAAF,EAAA,CAAA2D,eAAA,KAAAqZ,GAAA,EAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}