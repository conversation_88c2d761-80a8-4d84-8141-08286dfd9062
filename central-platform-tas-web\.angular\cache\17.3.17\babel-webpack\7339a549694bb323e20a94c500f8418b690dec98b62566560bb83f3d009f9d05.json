{"ast": null, "code": "// Base64 JavaScript decoder\n// Copyright (c) 2008-2013 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nexport var Base64 = {\n  decode: function (a) {\n    var i;\n    if (decoder === undefined) {\n      var b64 = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n      var ignore = \"= \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n      decoder = Object.create(null);\n      for (i = 0; i < 64; ++i) {\n        decoder[b64.charAt(i)] = i;\n      }\n      decoder['-'] = 62; //+\n      decoder['_'] = 63; //-\n      for (i = 0; i < ignore.length; ++i) {\n        decoder[ignore.charAt(i)] = -1;\n      }\n    }\n    var out = [];\n    var bits = 0;\n    var char_count = 0;\n    for (i = 0; i < a.length; ++i) {\n      var c = a.charAt(i);\n      if (c == \"=\") {\n        break;\n      }\n      c = decoder[c];\n      if (c == -1) {\n        continue;\n      }\n      if (c === undefined) {\n        throw new Error(\"Illegal character at offset \" + i);\n      }\n      bits |= c;\n      if (++char_count >= 4) {\n        out[out.length] = bits >> 16;\n        out[out.length] = bits >> 8 & 0xFF;\n        out[out.length] = bits & 0xFF;\n        bits = 0;\n        char_count = 0;\n      } else {\n        bits <<= 6;\n      }\n    }\n    switch (char_count) {\n      case 1:\n        throw new Error(\"Base64 encoding incomplete: at least 2 bits missing\");\n      case 2:\n        out[out.length] = bits >> 10;\n        break;\n      case 3:\n        out[out.length] = bits >> 16;\n        out[out.length] = bits >> 8 & 0xFF;\n        break;\n    }\n    return out;\n  },\n  re: /-----BEGIN [^-]+-----([A-Za-z0-9+\\/=\\s]+)-----END [^-]+-----|begin-base64[^\\n]+\\n([A-Za-z0-9+\\/=\\s]+)====/,\n  unarmor: function (a) {\n    var m = Base64.re.exec(a);\n    if (m) {\n      if (m[1]) {\n        a = m[1];\n      } else if (m[2]) {\n        a = m[2];\n      } else {\n        throw new Error(\"RegExp out of sync\");\n      }\n    }\n    return Base64.decode(a);\n  }\n};", "map": {"version": 3, "names": ["decoder", "Base64", "decode", "a", "i", "undefined", "b64", "ignore", "Object", "create", "char<PERSON>t", "length", "out", "bits", "char_count", "c", "Error", "re", "unarmor", "m", "exec"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/asn1js/base64.js"], "sourcesContent": ["// Base64 JavaScript decoder\n// Copyright (c) 2008-2013 <PERSON><PERSON> <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nexport var Base64 = {\n    decode: function (a) {\n        var i;\n        if (decoder === undefined) {\n            var b64 = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n            var ignore = \"= \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n            decoder = Object.create(null);\n            for (i = 0; i < 64; ++i) {\n                decoder[b64.charAt(i)] = i;\n            }\n            decoder['-'] = 62; //+\n            decoder['_'] = 63; //-\n            for (i = 0; i < ignore.length; ++i) {\n                decoder[ignore.charAt(i)] = -1;\n            }\n        }\n        var out = [];\n        var bits = 0;\n        var char_count = 0;\n        for (i = 0; i < a.length; ++i) {\n            var c = a.charAt(i);\n            if (c == \"=\") {\n                break;\n            }\n            c = decoder[c];\n            if (c == -1) {\n                continue;\n            }\n            if (c === undefined) {\n                throw new Error(\"Illegal character at offset \" + i);\n            }\n            bits |= c;\n            if (++char_count >= 4) {\n                out[out.length] = (bits >> 16);\n                out[out.length] = (bits >> 8) & 0xFF;\n                out[out.length] = bits & 0xFF;\n                bits = 0;\n                char_count = 0;\n            }\n            else {\n                bits <<= 6;\n            }\n        }\n        switch (char_count) {\n            case 1:\n                throw new Error(\"Base64 encoding incomplete: at least 2 bits missing\");\n            case 2:\n                out[out.length] = (bits >> 10);\n                break;\n            case 3:\n                out[out.length] = (bits >> 16);\n                out[out.length] = (bits >> 8) & 0xFF;\n                break;\n        }\n        return out;\n    },\n    re: /-----BEGIN [^-]+-----([A-Za-z0-9+\\/=\\s]+)-----END [^-]+-----|begin-base64[^\\n]+\\n([A-Za-z0-9+\\/=\\s]+)====/,\n    unarmor: function (a) {\n        var m = Base64.re.exec(a);\n        if (m) {\n            if (m[1]) {\n                a = m[1];\n            }\n            else if (m[2]) {\n                a = m[2];\n            }\n            else {\n                throw new Error(\"RegExp out of sync\");\n            }\n        }\n        return Base64.decode(a);\n    }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,OAAO;AACX,OAAO,IAAIC,MAAM,GAAG;EAChBC,MAAM,EAAE,SAAAA,CAAUC,CAAC,EAAE;IACjB,IAAIC,CAAC;IACL,IAAIJ,OAAO,KAAKK,SAAS,EAAE;MACvB,IAAIC,GAAG,GAAG,kEAAkE;MAC5E,IAAIC,MAAM,GAAG,8BAA8B;MAC3CP,OAAO,GAAGQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC7B,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QACrBJ,OAAO,CAACM,GAAG,CAACI,MAAM,CAACN,CAAC,CAAC,CAAC,GAAGA,CAAC;MAC9B;MACAJ,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;MACnBA,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;MACnB,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,CAACI,MAAM,EAAE,EAAEP,CAAC,EAAE;QAChCJ,OAAO,CAACO,MAAM,CAACG,MAAM,CAACN,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAClC;IACJ;IACA,IAAIQ,GAAG,GAAG,EAAE;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAKV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACQ,MAAM,EAAE,EAAEP,CAAC,EAAE;MAC3B,IAAIW,CAAC,GAAGZ,CAAC,CAACO,MAAM,CAACN,CAAC,CAAC;MACnB,IAAIW,CAAC,IAAI,GAAG,EAAE;QACV;MACJ;MACAA,CAAC,GAAGf,OAAO,CAACe,CAAC,CAAC;MACd,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;QACT;MACJ;MACA,IAAIA,CAAC,KAAKV,SAAS,EAAE;QACjB,MAAM,IAAIW,KAAK,CAAC,8BAA8B,GAAGZ,CAAC,CAAC;MACvD;MACAS,IAAI,IAAIE,CAAC;MACT,IAAI,EAAED,UAAU,IAAI,CAAC,EAAE;QACnBF,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAIE,IAAI,IAAI,EAAG;QAC9BD,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAIE,IAAI,IAAI,CAAC,GAAI,IAAI;QACpCD,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAGE,IAAI,GAAG,IAAI;QAC7BA,IAAI,GAAG,CAAC;QACRC,UAAU,GAAG,CAAC;MAClB,CAAC,MACI;QACDD,IAAI,KAAK,CAAC;MACd;IACJ;IACA,QAAQC,UAAU;MACd,KAAK,CAAC;QACF,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;MAC1E,KAAK,CAAC;QACFJ,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAIE,IAAI,IAAI,EAAG;QAC9B;MACJ,KAAK,CAAC;QACFD,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAIE,IAAI,IAAI,EAAG;QAC9BD,GAAG,CAACA,GAAG,CAACD,MAAM,CAAC,GAAIE,IAAI,IAAI,CAAC,GAAI,IAAI;QACpC;IACR;IACA,OAAOD,GAAG;EACd,CAAC;EACDK,EAAE,EAAE,2GAA2G;EAC/GC,OAAO,EAAE,SAAAA,CAAUf,CAAC,EAAE;IAClB,IAAIgB,CAAC,GAAGlB,MAAM,CAACgB,EAAE,CAACG,IAAI,CAACjB,CAAC,CAAC;IACzB,IAAIgB,CAAC,EAAE;MACH,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;QACNhB,CAAC,GAAGgB,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,MACI,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;QACXhB,CAAC,GAAGgB,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,MACI;QACD,MAAM,IAAIH,KAAK,CAAC,oBAAoB,CAAC;MACzC;IACJ;IACA,OAAOf,MAAM,CAACC,MAAM,CAACC,CAAC,CAAC;EAC3B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}