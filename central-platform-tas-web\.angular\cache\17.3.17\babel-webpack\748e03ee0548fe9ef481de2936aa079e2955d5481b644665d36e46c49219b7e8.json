{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'গত' eeee 'সময়' p\",\n  yesterday: \"'গতকাল' 'সময়' p\",\n  today: \"'আজ' 'সময়' p\",\n  tomorrow: \"'আগামীকাল' 'সময়' p\",\n  nextWeek: \"eeee 'সময়' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/bn/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'গত' eeee 'সময়' p\",\n  yesterday: \"'গতকাল' 'সময়' p\",\n  today: \"'আজ' 'সময়' p\",\n  tomorrow: \"'আগামীকাল' 'সময়' p\",\n  nextWeek: \"eeee 'সময়' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,iBAAiB;EAC5BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}