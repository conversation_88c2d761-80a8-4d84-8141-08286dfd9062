{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_COMPARECODE extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"对照码表主键\",\n      \"comparecode_cd\": \"对照码代码\",\n      \"comparecode_nm\": \"对照码名称\",\n      \"comparecode_nm_en\": \"对照码名称\",\n      \"org_id\": \"所属组织主键\",\n      \"org_level_no\": \"所属组织机构代码\",\n      \"ent_level_no\": \"所属公司代码\",\n      \"saturday_tag\": \"\",\n      \"sunday_tag\": \"\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_COMPARECODE'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_COMPARECODE", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_COMPARECODE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_COMPARECODE extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_COMPARECODE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"对照码表主键\",\r\n      \"comparecode_cd\":\"对照码代码\",\r\n      \"comparecode_nm\":\"对照码名称\",\r\n      \"comparecode_nm_en\":\"对照码名称\",\r\n      \"org_id\":\"所属组织主键\",\r\n      \"org_level_no\":\"所属组织机构代码\",\r\n      \"ent_level_no\":\"所属公司代码\",\r\n      \"saturday_tag\":\"\",\r\n      \"sunday_tag\":\"\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,kBAAmB,SAAQD,QAAQ;EAQ9CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,QAAQ;MACb,gBAAgB,EAAC,OAAO;MACxB,gBAAgB,EAAC,OAAO;MACxB,mBAAmB,EAAC,OAAO;MAC3B,QAAQ,EAAC,QAAQ;MACjB,cAAc,EAAC,UAAU;MACzB,cAAc,EAAC,QAAQ;MACvB,cAAc,EAAC,EAAE;MACjB,YAAY,EAAC,EAAE;MACf,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IAzBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,mBAAmB,CAAC,CAAC;IACjC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAsBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}