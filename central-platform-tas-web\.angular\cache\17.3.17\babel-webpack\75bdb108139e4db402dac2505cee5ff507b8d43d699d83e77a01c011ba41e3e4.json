{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_KA_PARTNER extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"理货KA表主键\",\n      \"partner_cd\": \"客户代码\",\n      \"partner_nm\": \"客户名称\",\n      \"partner_nm_en\": \"客户英文名称\",\n      \"start_year\": \"起始年\",\n      \"end_year\": \"结束年\",\n      \"seq\": \"序号\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_KA_PARTNER'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_KA_PARTNER", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\TAS_T_KA_PARTNER.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_KA_PARTNER extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_KA_PARTNER'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"理货KA表主键\",\r\n      \"partner_cd\":\"客户代码\",\r\n      \"partner_nm\":\"客户名称\",\r\n      \"partner_nm_en\":\"客户英文名称\",\r\n      \"start_year\":\"起始年\",\r\n      \"end_year\":\"结束年\",\r\n      \"seq\":\"序号\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,gBAAiB,SAAQD,QAAQ;EAQ5CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,YAAY,EAAC,MAAM;MACnB,YAAY,EAAC,MAAM;MACnB,eAAe,EAAC,QAAQ;MACxB,YAAY,EAAC,KAAK;MAClB,UAAU,EAAC,KAAK;MAChB,KAAK,EAAC,IAAI;MACV,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IAvBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC;IAChC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAoBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}