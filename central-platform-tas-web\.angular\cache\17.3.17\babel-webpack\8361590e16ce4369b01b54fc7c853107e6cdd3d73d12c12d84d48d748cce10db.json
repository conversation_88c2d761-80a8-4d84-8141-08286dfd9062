{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['C', 'O'],\n  abbreviated: ['CC', 'OC'],\n  wide: ['Cy<PERSON>', '<PERSON><PERSON> <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Ch1', 'Ch2', 'Ch3', 'Ch4'],\n  wide: ['Chwarter 1af', '2ail chwarter', '3ydd chwarter', '4ydd chwarter']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['I', 'Ch', 'Ma', 'E', 'Mi', 'Me', 'G', 'A', 'Md', 'H', 'T', 'Rh'],\n  abbreviated: ['<PERSON>', 'Chwe', 'Maw', 'Ebr', 'Mai', 'Meh', 'Gor', 'Aws', 'Med', 'Hyd', 'Tach', 'Rhag'],\n  wide: ['Ionawr', 'Chwefror', 'Mawrth', 'Ebrill', 'Mai', 'Mehefin', 'Gorffennaf', 'Awst', 'Medi', 'Hydref', 'Tachwedd', 'Rhagfyr']\n};\nvar dayValues = {\n  narrow: ['S', 'Ll', 'M', 'M', 'I', 'G', 'S'],\n  short: ['Su', 'Ll', 'Ma', 'Me', 'Ia', 'Gw', 'Sa'],\n  abbreviated: ['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],\n  wide: ['dydd Sul', 'dydd Llun', 'dydd Mawrth', 'dydd Mercher', 'dydd Iau', 'dydd Gwener', 'dydd Sadwrn']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  if (number < 20) {\n    switch (number) {\n      case 0:\n        return number + 'fed';\n      case 1:\n        return number + 'af';\n      case 2:\n        return number + 'ail';\n      case 3:\n      case 4:\n        return number + 'ydd';\n      case 5:\n      case 6:\n        return number + 'ed';\n      case 7:\n      case 8:\n      case 9:\n      case 10:\n      case 12:\n      case 15:\n      case 18:\n        return number + 'fed';\n      case 11:\n      case 13:\n      case 14:\n      case 16:\n      case 17:\n      case 19:\n        return number + 'eg';\n    }\n  } else if (number >= 50 && number <= 60 || number === 80 || number >= 100) {\n    return number + 'fed';\n  }\n  return number + 'ain';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/cy/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['C', 'O'],\n  abbreviated: ['CC', 'OC'],\n  wide: ['Cy<PERSON>', '<PERSON><PERSON> <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Ch1', 'Ch2', 'Ch3', 'Ch4'],\n  wide: ['Chwarter 1af', '2ail chwarter', '3ydd chwarter', '4ydd chwarter']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['I', 'Ch', 'Ma', 'E', 'Mi', 'Me', 'G', 'A', 'Md', 'H', 'T', 'Rh'],\n  abbreviated: ['<PERSON>', 'Chwe', 'Maw', 'Ebr', 'Mai', 'Meh', 'Gor', 'Aws', 'Med', 'Hyd', 'Tach', 'Rhag'],\n  wide: ['Ionawr', 'Chwefror', 'Mawrth', 'Ebrill', 'Mai', 'Mehefin', 'Gorffennaf', 'Awst', 'Medi', 'Hydref', 'Tachwedd', 'Rhagfyr']\n};\nvar dayValues = {\n  narrow: ['S', 'Ll', 'M', 'M', 'I', 'G', 'S'],\n  short: ['Su', 'Ll', 'Ma', 'Me', 'Ia', 'Gw', 'Sa'],\n  abbreviated: ['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],\n  wide: ['dydd Sul', 'dydd Llun', 'dydd Mawrth', 'dydd Mercher', 'dydd Iau', 'dydd Gwener', 'dydd Sadwrn']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  if (number < 20) {\n    switch (number) {\n      case 0:\n        return number + 'fed';\n      case 1:\n        return number + 'af';\n      case 2:\n        return number + 'ail';\n      case 3:\n      case 4:\n        return number + 'ydd';\n      case 5:\n      case 6:\n        return number + 'ed';\n      case 7:\n      case 8:\n      case 9:\n      case 10:\n      case 12:\n      case 15:\n      case 18:\n        return number + 'fed';\n      case 11:\n      case 13:\n      case 14:\n      case 16:\n      case 17:\n      case 19:\n        return number + 'eg';\n    }\n  } else if (number >= 50 && number <= 60 || number === 80 || number >= 100) {\n    return number + 'fed';\n  }\n  return number + 'ain';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,WAAW,EAAE,aAAa;AACnC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzCC,IAAI,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1EC,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;EACpGC,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS;AAClI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC/DC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa;AACzG,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAIE,MAAM,GAAG,EAAE,EAAE;IACf,QAAQA,MAAM;MACZ,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,IAAI;MACtB,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,IAAI;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;QACL,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;MACP,KAAK,EAAE;QACL,OAAOA,MAAM,GAAG,IAAI;IACxB;EACF,CAAC,MAAM,IAAIA,MAAM,IAAI,EAAE,IAAIA,MAAM,IAAI,EAAE,IAAIA,MAAM,KAAK,EAAE,IAAIA,MAAM,IAAI,GAAG,EAAE;IACzE,OAAOA,MAAM,GAAG,KAAK;EACvB;EACA,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}