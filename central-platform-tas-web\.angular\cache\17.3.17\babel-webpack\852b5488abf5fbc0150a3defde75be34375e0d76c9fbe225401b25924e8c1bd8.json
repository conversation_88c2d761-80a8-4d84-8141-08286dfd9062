{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_CUSTOMS_CODE extends CwfStore {\n  constructor() {\n    super({\n      id: '',\n      remark: '',\n      createdUser: '',\n      // 创建人\n      createdTime: '',\n      // 创建时间\n      modifiedUser: '',\n      // 修改人\n      modifiedTime: '',\n      // 修改时间\n      version: '',\n      // 版本号\n      isDelete: '',\n      // 是否删除\n      region: '',\n      tenantId: '',\n      isuniversal: ''\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_CUSTOMS_CODE'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_CUSTOMS_CODE", "constructor", "id", "remark", "created<PERSON>ser", "createdTime", "modifiedUser", "modifiedTime", "version", "isDelete", "region", "tenantId", "isuniversal", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\TAS\\TAS_T_CUSTOMS_CODE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_CUSTOMS_CODE extends CwfStore {\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_CUSTOMS_CODE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      id: '',\r\n\r\n      remark: '',\r\n      createdUser: '', // 创建人\r\n      createdTime: '', // 创建时间\r\n      modifiedUser: '', // 修改人\r\n      modifiedTime: '', // 修改时间\r\n      version: '', // 版本号\r\n      isDelete: '', // 是否删除\r\n      region: '',\r\n      tenantId: '',\r\n      isuniversal: '',\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,kBAAmB,SAAQD,QAAQ;EAO9CE,YAAA;IACE,KAAK,CAAC;MACJC,EAAE,EAAE,EAAE;MAENC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MAAE;MACjBC,WAAW,EAAE,EAAE;MAAE;MACjBC,YAAY,EAAE,EAAE;MAAE;MAClBC,YAAY,EAAE,EAAE;MAAE;MAClBC,OAAO,EAAE,EAAE;MAAE;MACbC,QAAQ,EAAE,EAAE;MAAE;MACdC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;KACd,CAAC;IApBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,oBAAoB,CAAC,CAAC;IAClC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAiBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}