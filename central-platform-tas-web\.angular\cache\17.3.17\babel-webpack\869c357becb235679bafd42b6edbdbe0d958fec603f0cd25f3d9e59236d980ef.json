{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VesselBayComponent } from './vessel-bay.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: VesselBayComponent\n}];\nexport class VesselBayRoutingModule {\n  static {\n    this.ɵfac = function VesselBayRoutingModule_Factory(t) {\n      return new (t || VesselBayRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselBayRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselBayRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VesselBayComponent", "routes", "path", "component", "VesselBayRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-bay\\vessel-bay-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VesselBayComponent } from './vessel-bay.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: VesselBayComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class VesselBayRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,wBAAwB;;;AAE3D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAkB,CAAE,CAC5C;AAMD,OAAM,MAAOI,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFvBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}