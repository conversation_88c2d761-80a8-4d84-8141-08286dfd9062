{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var weekday = date.getUTCDay();\n    var last = weekday === 0 || weekday === 6 ? 'último' : 'última';\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "weekday", "getUTCDay", "last", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/pt-BR/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var weekday = date.getUTCDay();\n    var last = weekday === 0 || weekday === 6 ? 'último' : 'última';\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,IAAIC,OAAO,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC9B,IAAIC,IAAI,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ;IAC/D,OAAO,GAAG,GAAGE,IAAI,GAAG,eAAe;EACrC,CAAC;EACDC,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEW,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGf,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACb,IAAI,CAAC;EACrB;EACA,OAAOa,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}