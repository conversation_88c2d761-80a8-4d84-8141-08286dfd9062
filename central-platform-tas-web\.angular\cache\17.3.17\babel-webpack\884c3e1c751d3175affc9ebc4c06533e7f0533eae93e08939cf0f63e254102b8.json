{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { T_CBC_CONFIGPAGE } from '@store/CBC/T_CBC_CONFIGPAGE';\nimport { CwfModalCrud } from '@core/cwfmodalcrud';\nimport { CwfNewRequest } from '@core/cwfNewRequest';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"ng-zorro-antd/modal\";\nimport * as i3 from \"@service/globaldata.service\";\nimport * as i4 from \"ng-zorro-antd/message\";\nimport * as i5 from \"@service/cwfRestful.service\";\nimport * as i6 from \"@service/common.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ng-zorro-antd/grid\";\nimport * as i10 from \"ng-zorro-antd/button\";\nimport * as i11 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i12 from \"ng-zorro-antd/core/wave\";\nimport * as i13 from \"ng-zorro-antd/input-number\";\nimport * as i14 from \"ng-zorro-antd/card\";\nimport * as i15 from \"ng-zorro-antd/table\";\nimport * as i16 from \"ng-zorro-antd/icon\";\nimport * as i17 from \"@angular/cdk/drag-drop\";\nconst _c0 = a0 => [a0];\nconst _c1 = a0 => ({\n  x: a0\n});\nfunction SetTableComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SetTableComponent_ng_template_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onConfirm());\n    });\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵtext(2, \"\\u786E\\u5B9A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function SetTableComponent_ng_template_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onReview());\n    });\n    i0.ɵɵelement(4, \"i\", 25);\n    i0.ɵɵtext(5, \"\\u6062\\u590D\\u9ED8\\u8BA4 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function SetTableComponent_ng_template_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCancel());\n    });\n    i0.ɵɵelement(7, \"i\", 25);\n    i0.ɵɵtext(8, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"nzType\", \"primary\");\n  }\n}\nfunction SetTableComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"span\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"div\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"nz-input-number\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SetTableComponent_div_23_Template_nz_input_number_ngModelChange_8_listener($event) {\n      const leftinfo_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(leftinfo_r5.tableWidth, $event) || (leftinfo_r5.tableWidth = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const leftinfo_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵproperty(\"cdkDragData\", leftinfo_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", leftinfo_r5.customizedName || leftinfo_r5.remark, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", leftinfo_r5.tableWidth);\n  }\n}\nfunction SetTableComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 34)(2, \"span\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"span\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rightinfo_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵproperty(\"cdkDragData\", rightinfo_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(rightinfo_r7.customizednName || rightinfo_r7.remark);\n  }\n}\nfunction SetTableComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", data_r9.customizedName || data_r9.remark, \" \");\n  }\n}\nfunction SetTableComponent_td_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 36);\n    i0.ɵɵtext(1, \" xxx \");\n    i0.ɵɵelementEnd();\n  }\n}\n/**\n * 查询页面自定义配置\n * <AUTHOR>\n */\nexport class SetTableComponent extends CwfModalCrud {\n  /** 构造器 */\n  constructor(Context, matDialogRef, glo, message, cwfRestfulService, commonService) {\n    super(Context, matDialogRef);\n    this.Context = Context;\n    this.glo = glo;\n    this.message = message;\n    this.cwfRestfulService = cwfRestfulService;\n    this.commonService = commonService;\n    /** 页面主仓库 */\n    this.mainStore = new T_CBC_CONFIGPAGE();\n    this.sysData = [];\n    /** 页面标题 */\n    this.colLeftSource = [];\n    this.colRightSource = [];\n    // cmsAlert = '个人显示列设置';//提示信息\n    this.IS_ADMIN = 'N';\n    this.USER_CD = '';\n    this.system_cd = this.commonService.getSystem_cd();\n    this.editId = ''; // 修改列\n    this.editSystem_cd = ''; // 板块过滤条件\n    this.scope = this;\n  }\n  /** 页面加载 */\n  onLoad() {\n    this.onQueryInit();\n  }\n  onShow() {}\n  // 查询数据\n  onQueryInit() {\n    const obj = {\n      systemCd: this.config['system_cd'],\n      pageCd: this.config['page_cd'],\n      compCd: this.config['comp_cd'],\n      compType: 'TABLE',\n      tableName: this.config['type']\n    };\n    const request = new CwfNewRequest();\n    request.ISPAGING = true;\n    request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\n    request.OPERATION = 'query';\n    request.CONDITION = obj;\n    request.BU_CD = 'admin';\n    request.BU_NM = 'admin';\n    // request.SYSTEM_CD = this.config['system_cd'];\n    request.SYSTEM_CD = this.system_cd;\n    request.SYSTEMVERSION = 'admin';\n    this.cwfRestfulService.post('/PageColumnConfigService/query', obj, 'system-service').then(reps => {\n      if (reps.ok) {\n        let arr = reps.data;\n        let width = 90;\n        this.sysData = this.config['sysData'];\n        if (!(arr?.length > 0)) {\n          arr = this.config['sysData'];\n        }\n        arr.forEach(attr => {\n          this.colLeftSource.push(attr);\n          width += Number(attr.tableWidth);\n        });\n        this.xScroll = width + 'px';\n        this.sysData.forEach(info => {\n          // tslint:disable-next-line:max-line-length\n          if (!this.colLeftSource.find(c => c.controlname === info.controlname && c.columnKey === info.columnKey)) {\n            this.colRightSource.push(info);\n          }\n        });\n      }\n    });\n  }\n  // 保存接口\n  onSaveInit() {\n    const sysData = {\n      'system_cd': this.config['system_cd'],\n      'gridArray': []\n    };\n    sysData.gridArray = this.colLeftSource.map((info, i) => ({\n      attr: {\n        required: info.requiredFlag,\n        displayFlag: 1,\n        formControlName: info.controlname,\n        remark: info.remark,\n        seq: i + 1,\n        key: info.columnKey,\n        id: info.id,\n        nzWidth: info.tableWidth,\n        customizedName: info.customizedName\n      },\n      event: {}\n    }));\n    const obj = {\n      modalId: this.config['modalId'],\n      data: sysData,\n      pageCd: this.config['page_cd'],\n      pageNm: this.config['page_cd'],\n      compCd: this.config['comp_cd'],\n      compNm: this.config['comp_cd'],\n      compType: 'TABLE',\n      tableName: this.config['type']\n    };\n    // const request = new CwfNewRequest();\n    // request.ISPAGING = true;\n    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\n    // request.OPERATION = 'save';\n    // request.CONDITION = obj;\n    //\n    // request.BU_CD = 'admin';\n    // request.BU_NM = 'admin';\n    // // request.SYSTEM_CD = this.config['system_cd'];\n    // request.SYSTEM_CD = this.system_cd;\n    // request.SYSTEMVERSION = 'admin';\n    return this.cwfRestfulService.post('/PageColumnConfigService/save', obj, 'system-service').then(reps => {\n      if (reps.ok) {\n        this.message.success('保存成功,刷新后生效！');\n        // 构建返回数据，包含更新后的列配置\n        const returnData = {\n          array: sysData.gridArray\n        };\n        this.matDialogRef.close(returnData);\n      }\n    });\n  }\n  startEdit(id) {\n    this.editId = id;\n  }\n  stopEdit() {\n    this.editId = null;\n  }\n  onConfirm() {\n    this.onSaveInit();\n  }\n  onCancel() {\n    this.matDialogRef.close();\n  }\n  onReview() {\n    const obj = {\n      modalId: this.config['modalId'],\n      pageCd: this.config['page_cd'],\n      pageNm: this.config['page_cd'],\n      compCd: this.config['comp_cd'],\n      compNm: this.config['comp_cd'],\n      compType: 'TABLE',\n      tableName: this.config['type']\n    };\n    // const request = new CwfNewRequest();\n    // request.ISPAGING = true;\n    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\n    // request.OPERATION = 'delete';\n    // request.CONDITION = obj;\n    // request.BU_CD = 'admin';\n    // request.BU_NM = 'admin';\n    // request.SYSTEM_CD = this.config['system_cd'];\n    // request.SYSTEMVERSION = 'admin';\n    this.cwfRestfulService.post('/PageColumnConfigService/delete', obj, 'system-service').then(reps => {\n      if (reps.ok) {\n        this.message.success('恢复默认成功,刷新后生效！');\n        // 构建默认配置数据，使用原始sysData作为默认配置\n        const defaultData = {\n          array: this.sysData.map((info, i) => ({\n            attr: {\n              required: info.requiredFlag,\n              displayFlag: info.displayFlag,\n              formControlName: info.controlname,\n              remark: info.remark,\n              seq: i + 1,\n              key: info.columnKey,\n              id: info.id,\n              nzWidth: info.tableWidth,\n              customizedName: info.customizedName\n            },\n            event: {}\n          }))\n        };\n        this.matDialogRef.close(defaultData);\n      } else {\n        this.message.error(reps.msg);\n      }\n    });\n  }\n  /** 只接受右边的list的非必输的数据 */\n  evenPredicate(item) {\n    const gridInfo = item.data;\n    return gridInfo['requiredFlag'] !== '1';\n  }\n  /**\n  * 拖动的时候，list交换item或者单个list里面item位置的变换\n  */\n  drop(event) {\n    if (event.previousContainer === event.container) {\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n    } else {\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n    }\n  }\n  static {\n    this.ɵfac = function SetTableComponent_Factory(t) {\n      return new (t || SetTableComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.NzModalRef), i0.ɵɵdirectiveInject(i3.GlobalDataService), i0.ɵɵdirectiveInject(i4.NzMessageService), i0.ɵɵdirectiveInject(i5.CwfRestfulService), i0.ɵɵdirectiveInject(i6.CommonService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SetTableComponent,\n      selectors: [[\"app-setTable\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 47,\n      vars: 22,\n      consts: [[\"titleBtn\", \"\"], [\"leftColList\", \"cdkDropList\"], [\"rightColList\", \"cdkDropList\"], [1, \"modal__form\", 3, \"nzBordered\", \"nzTitle\"], [\"nz-row\", \"\", \"nzGutter\", \"2\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"nzGutter\", \"2\"], [2, \"width\", \"65%\"], [2, \"padding-right\", \"10px\"], [\"nzGutter\", \"2\", 2, \"border\", \"1px #e8e8e8 solid\", \"background-color\", \"#f5f5f5\", \"height\", \"28.2px\"], [2, \"width\", \"17%\", \"text-align\", \"center\", \"padding\", \"5px 8px\"], [2, \"width\", \"55%\", \"text-align\", \"center\", \"padding\", \"5px 8px\", \"border-left\", \"1px #e8e8e8 solid\", \"border-right\", \"1px #e8e8e8 solid\"], [2, \"width\", \"28%\", \"text-align\", \"center\", \"padding\", \"5px 8px\"], [\"cdkDropList\", \"\", 1, \"example-list\", 2, \"height\", \"390px\", 3, \"cdkDropListDropped\", \"cdkDropListData\", \"cdkDropListConnectedTo\"], [\"nzGutter\", \"2\", 2, \"display\", \"inline\", \"width\", \"100%\"], [\"style\", \"height: 35px;\", \"class\", \"example-box\", \"cdkDrag\", \"\", 3, \"cdkDragData\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"35%\"], [1, \"example-container\"], [2, \"width\", \"25%\", \"text-align\", \"center\", \"padding\", \"5px 8px\", \"border-right\", \"1px #e8e8e8 solid\"], [2, \"width\", \"75%\", \"text-align\", \"center\", \"padding\", \"5px 8px\"], [\"cdkDropList\", \"\", 1, \"example-list\", 2, \"height\", \"390px\", \"font-size\", \"12px\", 3, \"cdkDropListDropped\", \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListEnterPredicate\"], [3, \"nzData\", \"nzScroll\", \"nzBordered\", \"nzFrontPagination\", \"nzShowPagination\"], [4, \"ngFor\", \"ngForOf\"], [\"style\", \"text-align: center;\", 4, \"ngFor\", \"ngForOf\"], [\"nz-button\", \"\", 1, \"mx-sm\", 3, \"click\", \"nzType\"], [\"nz-icon\", \"\", \"nzType\", \"redo\"], [\"nz-button\", \"\", 1, \"mx-sm\", 3, \"click\"], [\"cdkDrag\", \"\", 1, \"example-box\", 2, \"height\", \"35px\", 3, \"cdkDragData\"], [\"nz-col\", \"\", 2, \"width\", \"17%\", \"text-align\", \"center\"], [2, \"font-size\", \"12px\"], [\"nz-col\", \"\", 2, \"width\", \"55%\"], [1, \"editable-cell\"], [\"nz-col\", \"\", 2, \"width\", \"28%\"], [2, \"width\", \"90%\", \"margin-left\", \"9px\", 3, \"ngModelChange\", \"ngModel\"], [\"nz-col\", \"\", 2, \"width\", \"25%\", \"text-align\", \"center\"], [\"nz-col\", \"\", 2, \"width\", \"75%\"], [2, \"text-align\", \"center\"]],\n      template: function SetTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 3);\n          i0.ɵɵtemplate(1, SetTableComponent_ng_template_1_Template, 9, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"nz-row\", 6)(6, \"nz-col\", 7)(7, \"div\", 8)(8, \"h2\");\n          i0.ɵɵtext(9, \"\\u5C55\\u793A\\u5217\\u8868\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nz-row\", 9)(11, \"nz-col\", 10)(12, \"span\");\n          i0.ɵɵtext(13, \"\\u5E8F\\u53F7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"nz-col\", 11)(15, \"span\");\n          i0.ɵɵtext(16, \"\\u5217\\u540D\\u79F0\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"nz-col\", 12)(18, \"span\");\n          i0.ɵɵtext(19, \"\\u63A7\\u4EF6\\u957F\\u5EA6\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 13, 1);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function SetTableComponent_Template_div_cdkDropListDropped_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.drop($event));\n          });\n          i0.ɵɵelementStart(22, \"nz-row\", 14);\n          i0.ɵɵtemplate(23, SetTableComponent_div_23_Template, 9, 4, \"div\", 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"nz-col\", 16)(25, \"div\", 17)(26, \"h2\");\n          i0.ɵɵtext(27, \"\\u4E0D\\u5C55\\u793A\\u5217\\u8868222222\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nz-row\", 9)(29, \"nz-col\", 18)(30, \"span\");\n          i0.ɵɵtext(31, \"\\u5E8F\\u53F7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"nz-col\", 19)(33, \"span\");\n          i0.ɵɵtext(34, \"\\u5217\\u540D\\u79F0\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 20, 2);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function SetTableComponent_Template_div_cdkDropListDropped_35_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.drop($event));\n          });\n          i0.ɵɵelementStart(37, \"nz-row\", 14);\n          i0.ɵɵtemplate(38, SetTableComponent_div_38_Template, 7, 3, \"div\", 15);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(39, \"h2\");\n          i0.ɵɵtext(40, \"\\u5C55\\u793A\\u5217\\u8868\\u5E8F\\u5217\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"nz-table\", 21)(42, \"thead\")(43, \"tr\");\n          i0.ɵɵtemplate(44, SetTableComponent_th_44_Template, 2, 1, \"th\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"tr\");\n          i0.ɵɵtemplate(46, SetTableComponent_td_46_Template, 2, 0, \"td\", 23);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const titleBtn_r10 = i0.ɵɵreference(2);\n          const leftColList_r11 = i0.ɵɵreference(21);\n          const rightColList_r12 = i0.ɵɵreference(36);\n          i0.ɵɵproperty(\"nzBordered\", false)(\"nzTitle\", titleBtn_r10);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"cdkDropListData\", ctx.colLeftSource)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(16, _c0, rightColList_r12));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.colLeftSource);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"cdkDropListData\", ctx.colRightSource)(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction1(18, _c0, leftColList_r11))(\"cdkDropListEnterPredicate\", ctx.evenPredicate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.colRightSource);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzData\", ctx.colLeftSource)(\"nzScroll\", i0.ɵɵpureFunction1(20, _c1, ctx.xScroll))(\"nzBordered\", true)(\"nzFrontPagination\", false)(\"nzShowPagination\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.colLeftSource);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.colLeftSource);\n        }\n      },\n      dependencies: [i7.NgControlStatus, i7.NgModel, i8.NgForOf, i9.NzColDirective, i9.NzRowDirective, i10.NzButtonComponent, i11.ɵNzTransitionPatchDirective, i12.NzWaveDirective, i13.NzInputNumberComponent, i14.NzCardComponent, i15.NzTableComponent, i15.NzTableCellDirective, i15.NzThMeasureDirective, i15.NzTheadComponent, i15.NzTrDirective, i16.NzIconDirective, i17.CdkDropList, i17.CdkDrag],\n      styles: [\".editable-cell[_ngcontent-%COMP%] {\\n      position: relative;\\n      padding: 5px 12px;\\n      cursor: pointer;\\n    }\\n\\n    .editable-row[_ngcontent-%COMP%]:hover   .editable-cell[_ngcontent-%COMP%] {\\n      border: 1px solid #d9d9d9;\\n      border-radius: 4px;\\n      padding: 4px 11px;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYnVzaW5lc3MtbW9kYWwvc2V0VGFibGUvc2V0VGFibGUuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFDSTtNQUNFLGtCQUFrQjtNQUNsQixpQkFBaUI7TUFDakIsZUFBZTtJQUNqQjs7SUFFQTtNQUNFLHlCQUF5QjtNQUN6QixrQkFBa0I7TUFDbEIsaUJBQWlCO0lBQ25CIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmVkaXRhYmxlLWNlbGwge1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgcGFkZGluZzogNXB4IDEycHg7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgfVxuXG4gICAgLmVkaXRhYmxlLXJvdzpob3ZlciAuZWRpdGFibGUtY2VsbCB7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5O1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgcGFkZGluZzogNHB4IDExcHg7XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moveItemInArray", "transferArrayItem", "T_CBC_CONFIGPAGE", "CwfModalCrud", "CwfNewRequest", "i0", "ɵɵelementStart", "ɵɵlistener", "SetTableComponent_ng_template_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onConfirm", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SetTableComponent_ng_template_1_Template_button_click_3_listener", "onReview", "SetTableComponent_ng_template_1_Template_button_click_6_listener", "onCancel", "ɵɵproperty", "ɵɵtwoWayListener", "SetTableComponent_div_23_Template_nz_input_number_ngModelChange_8_listener", "$event", "leftinfo_r5", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "tableWidth", "ɵɵadvance", "ɵɵtextInterpolate", "i_r6", "ɵɵtextInterpolate1", "customizedName", "remark", "ɵɵtwoWayProperty", "rightinfo_r7", "i_r8", "customizednName", "data_r9", "SetTableComponent", "constructor", "Context", "matDialogRef", "glo", "message", "cwfRestfulService", "commonService", "mainStore", "sysData", "colLeftSource", "colRightSource", "IS_ADMIN", "USER_CD", "system_cd", "getSystem_cd", "editId", "editSystem_cd", "scope", "onLoad", "onQueryInit", "onShow", "obj", "systemCd", "config", "pageCd", "compCd", "compType", "tableName", "request", "ISPAGING", "ACTIONID", "OPERATION", "CONDITION", "BU_CD", "BU_NM", "SYSTEM_CD", "SYSTEMVERSION", "post", "then", "reps", "ok", "arr", "data", "width", "length", "for<PERSON>ach", "attr", "push", "Number", "xScroll", "info", "find", "c", "controlname", "column<PERSON>ey", "onSaveInit", "gridArray", "map", "i", "required", "requiredFlag", "displayFlag", "formControlName", "seq", "key", "id", "nzWidth", "event", "modalId", "pageNm", "compNm", "success", "returnData", "array", "close", "startEdit", "stopEdit", "defaultData", "error", "msg", "evenPredicate", "item", "gridInfo", "drop", "previousContainer", "container", "previousIndex", "currentIndex", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "NzModalRef", "i3", "GlobalDataService", "i4", "NzMessageService", "i5", "CwfRestfulService", "i6", "CommonService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SetTableComponent_Template", "rf", "ctx", "ɵɵtemplate", "SetTableComponent_ng_template_1_Template", "ɵɵtemplateRefExtractor", "SetTableComponent_Template_div_cdkDropListDropped_20_listener", "_r1", "SetTableComponent_div_23_Template", "SetTableComponent_Template_div_cdkDropListDropped_35_listener", "SetTableComponent_div_38_Template", "SetTableComponent_th_44_Template", "SetTableComponent_td_46_Template", "titleBtn_r10", "ɵɵpureFunction1", "_c0", "rightColList_r12", "leftColList_r11", "_c1"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business-modal\\setTable\\setTable.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business-modal\\setTable\\setTable.component.html"], "sourcesContent": ["import {CdkDrag, CdkDragDrop, moveItemInArray, transferArrayItem} from '@angular/cdk/drag-drop';\r\nimport {T_CBC_CONFIGPAGE} from '@store/CBC/T_CBC_CONFIGPAGE';\r\nimport {GlobalDataService} from '@service/globaldata.service';\r\nimport {CwfModalCrud} from '@core/cwfmodalcrud';\r\nimport {Component} from '@angular/core';\r\nimport {NzMessageService} from 'ng-zorro-antd/message';\r\nimport {NzModalRef} from 'ng-zorro-antd/modal';\r\nimport {CommonService} from '@service/common.service';\r\nimport {CwfBusContextService, CwfRequest} from 'cwf-ng-library';\r\nimport {CwfNewRequest} from '@core/cwfNewRequest';\r\nimport {CwfRestfulService} from '@service/cwfRestful.service';\r\nimport {responseInterface} from '../../interface/request.interface';\r\n\r\n@Component({\r\n  selector: 'app-setTable',\r\n  templateUrl: './setTable.component.html',\r\n  styles: [`\r\n    .editable-cell {\r\n      position: relative;\r\n      padding: 5px 12px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .editable-row:hover .editable-cell {\r\n      border: 1px solid #d9d9d9;\r\n      border-radius: 4px;\r\n      padding: 4px 11px;\r\n    }\r\n  `],\r\n})\r\n\r\n/**\r\n * 查询页面自定义配置\r\n * <AUTHOR>\r\n */\r\nexport class SetTableComponent extends CwfModalCrud {\r\n\r\n  /** 构造器 */\r\n  constructor(\r\n    private Context: CwfBusContextService,\r\n    matDialogRef: NzModalRef,\r\n    private glo: GlobalDataService,\r\n    private message: NzMessageService,\r\n    private cwfRestfulService: CwfRestfulService,\r\n    private commonService: CommonService) {\r\n    super(Context, matDialogRef);\r\n    this.scope = this;\r\n  }\r\n  /** 页面主仓库 */\r\n  mainStore = new T_CBC_CONFIGPAGE();\r\n  scope: any;\r\n  sysData = [];\r\n  /** 页面标题 */\r\n  colLeftSource = [];\r\n  colRightSource = [];\r\n  // cmsAlert = '个人显示列设置';//提示信息\r\n  IS_ADMIN = 'N';\r\n  USER_CD = '';\r\n  system_cd = this.commonService.getSystem_cd();\r\n  // system_version = this.cwfBusContext.getContext().getSystemVersion(); // 获取维度代码\r\n  xScroll: any;\r\n\r\n  editId = ''; // 修改列\r\n  editSystem_cd = ''; // 板块过滤条件\r\n\r\n  /** 页面加载 */\r\n  onLoad() {\r\n    this.onQueryInit();\r\n  }\r\n\r\n  onShow() {\r\n  }\r\n\r\n  // 查询数据\r\n  onQueryInit() {\r\n    const obj = {\r\n      systemCd: this.config['system_cd'],\r\n      pageCd: this.config['page_cd'],\r\n      compCd: this.config['comp_cd'],\r\n      compType: 'TABLE',\r\n      tableName: this.config['type']\r\n    };\r\n    const request = new CwfNewRequest();\r\n    request.ISPAGING = true;\r\n    request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\r\n    request.OPERATION = 'query';\r\n    request.CONDITION = obj;\r\n    request.BU_CD = 'admin';\r\n    request.BU_NM = 'admin';\r\n    // request.SYSTEM_CD = this.config['system_cd'];\r\n    request.SYSTEM_CD = this.system_cd;\r\n    request.SYSTEMVERSION = 'admin';\r\n\r\n    this.cwfRestfulService.post('/PageColumnConfigService/query', obj, 'system-service').then((reps: responseInterface) => {\r\n      if (reps.ok) {\r\n        let arr = reps.data;\r\n        let width = 90;\r\n        this.sysData = this.config['sysData'];\r\n        if (!(arr?.length > 0)) {\r\n          arr = this.config['sysData'];\r\n        }\r\n        arr.forEach(attr => {\r\n          this.colLeftSource.push(attr);\r\n          width += Number(attr.tableWidth);\r\n        });\r\n        this.xScroll = width + 'px';\r\n        this.sysData.forEach(info => {\r\n          // tslint:disable-next-line:max-line-length\r\n          if (!this.colLeftSource.find(c => c.controlname ===  info.controlname && c.columnKey === info.columnKey)) {\r\n            this.colRightSource.push(info);\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // 保存接口\r\n  onSaveInit() {\r\n    const sysData = {\r\n        'system_cd': this.config['system_cd'],\r\n        'gridArray': []\r\n    };\r\n    sysData.gridArray = this.colLeftSource.map((info, i) => ({\r\n      attr: {\r\n        required: info.requiredFlag,\r\n        displayFlag: 1,\r\n        formControlName: info.controlname,\r\n        remark: info.remark,\r\n        seq: i + 1,\r\n        key: info.columnKey,\r\n        id: info.id,\r\n        nzWidth: info.tableWidth,\r\n        customizedName: info.customizedName\r\n      },\r\n      event: {}\r\n    }));\r\n\r\n    const obj = {\r\n      modalId: this.config['modalId'],\r\n      data: sysData,\r\n      pageCd: this.config['page_cd'],\r\n      pageNm: this.config['page_cd'],\r\n      compCd: this.config['comp_cd'],\r\n      compNm: this.config['comp_cd'],\r\n      compType: 'TABLE',\r\n      tableName: this.config['type']\r\n    };\r\n    // const request = new CwfNewRequest();\r\n    // request.ISPAGING = true;\r\n    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\r\n    // request.OPERATION = 'save';\r\n    // request.CONDITION = obj;\r\n    //\r\n    // request.BU_CD = 'admin';\r\n    // request.BU_NM = 'admin';\r\n    // // request.SYSTEM_CD = this.config['system_cd'];\r\n    // request.SYSTEM_CD = this.system_cd;\r\n    // request.SYSTEMVERSION = 'admin';\r\n    return  this.cwfRestfulService.post('/PageColumnConfigService/save', obj, 'system-service').then((reps: responseInterface) => {\r\n      if (reps.ok) {\r\n        this.message.success('保存成功,刷新后生效！');\r\n        // 构建返回数据，包含更新后的列配置\r\n        const returnData = {\r\n          array: sysData.gridArray\r\n        };\r\n        this.matDialogRef.close(returnData);\r\n      }\r\n      }\r\n    );\r\n  }\r\n\r\n  startEdit(id: string): void {\r\n    this.editId = id;\r\n  }\r\n\r\n  stopEdit(): void {\r\n    this.editId = null;\r\n  }\r\n\r\n  onConfirm() {\r\n    this.onSaveInit();\r\n  }\r\n  onCancel() {\r\n    this.matDialogRef.close();\r\n  }\r\n  onReview() {\r\n    const obj = {\r\n      modalId: this.config['modalId'],\r\n      pageCd: this.config['page_cd'],\r\n      pageNm: this.config['page_cd'],\r\n      compCd: this.config['comp_cd'],\r\n      compNm: this.config['comp_cd'],\r\n      compType: 'TABLE',\r\n      tableName: this.config['type']\r\n    };\r\n    // const request = new CwfNewRequest();\r\n    // request.ISPAGING = true;\r\n    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';\r\n    // request.OPERATION = 'delete';\r\n    // request.CONDITION = obj;\r\n    // request.BU_CD = 'admin';\r\n    // request.BU_NM = 'admin';\r\n    // request.SYSTEM_CD = this.config['system_cd'];\r\n    // request.SYSTEMVERSION = 'admin';\r\n\r\n    this.cwfRestfulService.post('/PageColumnConfigService/delete', obj, 'system-service').then((reps: responseInterface) => {\r\n      if (reps.ok) {\r\n        this.message.success('恢复默认成功,刷新后生效！');\r\n        // 构建默认配置数据，使用原始sysData作为默认配置\r\n        const defaultData = {\r\n          array: this.sysData.map((info, i) => ({\r\n            attr: {\r\n              required: info.requiredFlag,\r\n              displayFlag: info.displayFlag,\r\n              formControlName: info.controlname,\r\n              remark: info.remark,\r\n              seq: i + 1,\r\n              key: info.columnKey,\r\n              id: info.id,\r\n              nzWidth: info.tableWidth,\r\n              customizedName: info.customizedName\r\n            },\r\n            event: {}\r\n          }))\r\n        };\r\n        this.matDialogRef.close(defaultData);\r\n      } else {\r\n        this.message.error(reps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n  /** 只接受右边的list的非必输的数据 */\r\n  evenPredicate(item: CdkDrag<number>) {\r\n    const gridInfo = item.data;\r\n    return gridInfo['requiredFlag'] !== '1';\r\n  }\r\n\r\n  /**\r\n  * 拖动的时候，list交换item或者单个list里面item位置的变换\r\n  */\r\n  drop(event: CdkDragDrop<string[]>) {\r\n    if (event.previousContainer === event.container) {\r\n      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\r\n    } else {\r\n      transferArrayItem(event.previousContainer.data,\r\n        event.container.data,\r\n        event.previousIndex,\r\n        event.currentIndex);\r\n    }\r\n  }\r\n}\r\n", "<nz-card [nzBordered]=\"false\" [nzTitle]=\"titleBtn\" class=\"modal__form\">\r\n  <ng-template #titleBtn>\r\n    <button nz-button (click)=\"onConfirm()\" [nzType]=\"'primary'\" class=\"mx-sm\">\r\n      <i nz-icon nzType=\"redo\"></i>确定\r\n    </button>\r\n    <button nz-button (click)=\"onReview()\" class=\"mx-sm\">\r\n      <i nz-icon nzType=\"redo\"></i>恢复默认\r\n    </button>\r\n    <button nz-button (click)=\"onCancel()\" class=\"mx-sm\">\r\n      <i nz-icon nzType=\"redo\"></i>取消\r\n    </button>\r\n  </ng-template>\r\n  <div nz-row nzGutter=\"2\">\r\n    <div nz-col nzSpan=\"24\">\r\n      <nz-row nzGutter=\"2\">\r\n        <nz-col style=\"width: 65%;\">\r\n          <div style=\"padding-right: 10px;\">\r\n            <h2>展示列表</h2>\r\n            <nz-row\r\n              nzGutter=\"2\"\r\n              style=\"\r\n                border: 1px #e8e8e8 solid;\r\n                background-color: #f5f5f5;\r\n                height: 28.2px;\r\n              \"\r\n            >\r\n              <nz-col style=\"width: 17%; text-align: center; padding: 5px 8px;\"\r\n                ><span>序号</span></nz-col\r\n              >\r\n              <nz-col\r\n                style=\"\r\n                  width: 55%;\r\n                  text-align: center;\r\n                  padding: 5px 8px;\r\n                  border-left: 1px #e8e8e8 solid;\r\n                  border-right: 1px #e8e8e8 solid;\r\n                \"\r\n              >\r\n                <span>列名称</span></nz-col\r\n              >\r\n              <nz-col style=\"width: 28%; text-align: center; padding: 5px 8px;\"\r\n                ><span>控件长度</span></nz-col\r\n              >\r\n            </nz-row>\r\n            <div\r\n              style=\"height: 390px;\"\r\n              cdkDropList\r\n              #leftColList=\"cdkDropList\"\r\n              [cdkDropListData]=\"colLeftSource\"\r\n              [cdkDropListConnectedTo]=\"[rightColList]\"\r\n              class=\"example-list\"\r\n              (cdkDropListDropped)=\"drop($event)\"\r\n            >\r\n              <nz-row nzGutter=\"2\" style=\"display: inline;width: 100%\">\r\n                <div\r\n                  style=\"height: 35px;\"\r\n                  class=\"example-box\"\r\n                  *ngFor=\"let leftinfo of colLeftSource; let i = index\"\r\n                  [cdkDragData]=\"leftinfo\"\r\n                  cdkDrag\r\n                >\r\n                  <div nz-col style=\"width: 17%; text-align: center;\">\r\n                    <span style=\"font-size: 12px;\">{{ i + 1 }}</span>\r\n                  </div>\r\n                  <div nz-col style=\"width: 55%;\">\r\n                    <div class=\"editable-cell\" ><!--[hidden]=\"editId === leftinfo.CONTROLNAME + leftinfo.COLUMN_KEY + 'name'\" (click)=\"startEdit(leftinfo.CONTROLNAME+leftinfo.COLUMN_KEY+'name')\"-->\r\n                      {{ leftinfo.customizedName || leftinfo.remark }}\r\n                    </div>\r\n                    <!--<input *ngIf=\"config['type'] !== 'cbc_t_column_user'\"  type=\"text\" nz-input [(ngModel)]=\"leftinfo.CUSTOMIZED_NAME\" (blur)=\"stopEdit()\" />-->\r\n                  </div>\r\n                  <div nz-col style=\"width: 28%;\">\r\n                    <nz-input-number\r\n                      [(ngModel)]=\"leftinfo.tableWidth\"\r\n                      style=\"width: 90%; margin-left: 9px;\"\r\n                    >\r\n                    </nz-input-number>\r\n                  </div>\r\n                </div>\r\n              </nz-row>\r\n            </div>\r\n          </div>\r\n        </nz-col>\r\n        <nz-col style=\"width: 35%;\">\r\n          <div class=\"example-container\">\r\n            <h2>不展示列表222222</h2>\r\n            <nz-row\r\n              nzGutter=\"2\"\r\n              style=\"\r\n                border: 1px #e8e8e8 solid;\r\n                background-color: #f5f5f5;\r\n                height: 28.2px;\r\n              \"\r\n            >\r\n              <nz-col\r\n                style=\"\r\n                  width: 25%;\r\n                  text-align: center;\r\n                  padding: 5px 8px;\r\n                  border-right: 1px #e8e8e8 solid;\r\n                \"\r\n                ><span>序号</span></nz-col\r\n              >\r\n              <nz-col style=\"width: 75%; text-align: center; padding: 5px 8px;\"\r\n                ><span>列名称</span></nz-col\r\n              >\r\n              <!-- <nz-col nzSpan=\"10\"><span>控件长度</span></nz-col> -->\r\n            </nz-row>\r\n            <div\r\n              style=\"height: 390px; font-size: 12px;\"\r\n              cdkDropList\r\n              #rightColList=\"cdkDropList\"\r\n              [cdkDropListData]=\"colRightSource\"\r\n              [cdkDropListConnectedTo]=\"[leftColList]\"\r\n              [cdkDropListEnterPredicate]=\"evenPredicate\"\r\n              class=\"example-list\"\r\n              (cdkDropListDropped)=\"drop($event)\"\r\n            >\r\n              <nz-row nzGutter=\"2\" style=\"display: inline;width: 100%\">\r\n                <div\r\n                  style=\"height: 35px;\"\r\n                  class=\"example-box\"\r\n                  *ngFor=\"let rightinfo of colRightSource; let i = index\"\r\n                  [cdkDragData]=\"rightinfo\"\r\n                  cdkDrag\r\n                >\r\n                  <div nz-col style=\"width: 25%; text-align: center;\">\r\n                    <span style=\"font-size: 12px;\">{{ i + 1 }}</span>\r\n                  </div>\r\n                  <div nz-col style=\"width: 75%;\">\r\n                    <span style=\"font-size: 12px;\">{{ rightinfo.customizednName || rightinfo.remark }}</span>\r\n                  </div>\r\n                </div>\r\n              </nz-row>\r\n            </div>\r\n          </div>\r\n        </nz-col>\r\n      </nz-row>\r\n    </div>\r\n  </div>\r\n  <!-- 数据列展示行 -->\r\n  <h2>展示列表序列</h2>\r\n  <nz-table\r\n    [nzData]=\"colLeftSource\"\r\n    [nzScroll]=\"{ x: xScroll }\"\r\n    [nzBordered]=\"true\"\r\n    [nzFrontPagination]=\"false\"\r\n    [nzShowPagination]=\"false\"\r\n  >\r\n    <thead>\r\n      <tr>\r\n        <th *ngFor=\"let data of colLeftSource; index as i\">\r\n          {{ data.customizedName || data.remark }}\r\n        </th>\r\n      </tr>\r\n      <tr>\r\n        <td\r\n          *ngFor=\"let data of colLeftSource; index as i\"\r\n          style=\"text-align: center;\"\r\n        >\r\n          xxx\r\n        </td>\r\n      </tr>\r\n    </thead>\r\n  </nz-table>\r\n</nz-card>\r\n"], "mappings": "AAAA,SAA8BA,eAAe,EAAEC,iBAAiB,QAAO,wBAAwB;AAC/F,SAAQC,gBAAgB,QAAO,6BAA6B;AAE5D,SAAQC,YAAY,QAAO,oBAAoB;AAM/C,SAAQC,aAAa,QAAO,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;ICP7CC,EAAA,CAAAC,cAAA,iBAA2E;IAAzDD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACrCT,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,oBAC/B;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAqD;IAAnCD,EAAA,CAAAE,UAAA,mBAAAW,iEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,QAAA,EAAU;IAAA,EAAC;IACpCd,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,gCAC/B;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAqD;IAAnCD,EAAA,CAAAE,UAAA,mBAAAa,iEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,QAAA,EAAU;IAAA,EAAC;IACpChB,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,oBAC/B;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;IAR+BZ,EAAA,CAAAiB,UAAA,qBAAoB;;;;;;IA4D5CjB,EARJ,CAAAC,cAAA,cAMC,cACqD,eACnB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAC5CX,EAD4C,CAAAY,YAAA,EAAO,EAC7C;IAEJZ,EADF,CAAAC,cAAA,cAAgC,cACF;IAC1BD,EAAA,CAAAW,MAAA,GACF;IAEFX,EAFE,CAAAY,YAAA,EAAM,EAEF;IAEJZ,EADF,CAAAC,cAAA,cAAgC,0BAI7B;IAFCD,EAAA,CAAAkB,gBAAA,2BAAAC,2EAAAC,MAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAAvB,EAAA,CAAAwB,kBAAA,CAAAH,WAAA,CAAAI,UAAA,EAAAL,MAAA,MAAAC,WAAA,CAAAI,UAAA,GAAAL,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAiC;IAKvCpB,EAFI,CAAAY,YAAA,EAAkB,EACd,EACF;;;;;IAnBJZ,EAAA,CAAAiB,UAAA,gBAAAI,WAAA,CAAwB;IAISrB,EAAA,CAAA0B,SAAA,GAAW;IAAX1B,EAAA,CAAA2B,iBAAA,CAAAC,IAAA,KAAW;IAIxC5B,EAAA,CAAA0B,SAAA,GACF;IADE1B,EAAA,CAAA6B,kBAAA,MAAAR,WAAA,CAAAS,cAAA,IAAAT,WAAA,CAAAU,MAAA,MACF;IAKE/B,EAAA,CAAA0B,SAAA,GAAiC;IAAjC1B,EAAA,CAAAgC,gBAAA,YAAAX,WAAA,CAAAI,UAAA,CAAiC;;;;;IAsDnCzB,EARJ,CAAAC,cAAA,cAMC,cACqD,eACnB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAC5CX,EAD4C,CAAAY,YAAA,EAAO,EAC7C;IAEJZ,EADF,CAAAC,cAAA,cAAgC,eACC;IAAAD,EAAA,CAAAW,MAAA,GAAmD;IAEtFX,EAFsF,CAAAY,YAAA,EAAO,EACrF,EACF;;;;;IATJZ,EAAA,CAAAiB,UAAA,gBAAAgB,YAAA,CAAyB;IAIQjC,EAAA,CAAA0B,SAAA,GAAW;IAAX1B,EAAA,CAAA2B,iBAAA,CAAAO,IAAA,KAAW;IAGXlC,EAAA,CAAA0B,SAAA,GAAmD;IAAnD1B,EAAA,CAAA2B,iBAAA,CAAAM,YAAA,CAAAE,eAAA,IAAAF,YAAA,CAAAF,MAAA,CAAmD;;;;;IAqB9F/B,EAAA,CAAAC,cAAA,SAAmD;IACjDD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IADHZ,EAAA,CAAA0B,SAAA,EACF;IADE1B,EAAA,CAAA6B,kBAAA,MAAAO,OAAA,CAAAN,cAAA,IAAAM,OAAA,CAAAL,MAAA,MACF;;;;;IAGA/B,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAAW,MAAA,YACF;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;ADjIb;;;;AAIA,OAAM,MAAOyB,iBAAkB,SAAQvC,YAAY;EAEjD;EACAwC,YACUC,OAA6B,EACrCC,YAAwB,EAChBC,GAAsB,EACtBC,OAAyB,EACzBC,iBAAoC,EACpCC,aAA4B;IACpC,KAAK,CAACL,OAAO,EAAEC,YAAY,CAAC;IANpB,KAAAD,OAAO,GAAPA,OAAO;IAEP,KAAAE,GAAG,GAAHA,GAAG;IACH,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IAIvB;IACA,KAAAC,SAAS,GAAG,IAAIhD,gBAAgB,EAAE;IAElC,KAAAiD,OAAO,GAAG,EAAE;IACZ;IACA,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,cAAc,GAAG,EAAE;IACnB;IACA,KAAAC,QAAQ,GAAG,GAAG;IACd,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,SAAS,GAAG,IAAI,CAACP,aAAa,CAACQ,YAAY,EAAE;IAI7C,KAAAC,MAAM,GAAG,EAAE,CAAC,CAAC;IACb,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IAjBlB,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;EAkBA;EACAC,MAAMA,CAAA;IACJ,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,MAAMA,CAAA,GACN;EAEA;EACAD,WAAWA,CAAA;IACT,MAAME,GAAG,GAAG;MACVC,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC,WAAW,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACD,MAAM,CAAC,SAAS,CAAC;MAC9BE,MAAM,EAAE,IAAI,CAACF,MAAM,CAAC,SAAS,CAAC;MAC9BG,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC,MAAM;KAC9B;IACD,MAAMK,OAAO,GAAG,IAAInE,aAAa,EAAE;IACnCmE,OAAO,CAACC,QAAQ,GAAG,IAAI;IACvBD,OAAO,CAACE,QAAQ,GAAG,qCAAqC;IACxDF,OAAO,CAACG,SAAS,GAAG,OAAO;IAC3BH,OAAO,CAACI,SAAS,GAAGX,GAAG;IACvBO,OAAO,CAACK,KAAK,GAAG,OAAO;IACvBL,OAAO,CAACM,KAAK,GAAG,OAAO;IACvB;IACAN,OAAO,CAACO,SAAS,GAAG,IAAI,CAACtB,SAAS;IAClCe,OAAO,CAACQ,aAAa,GAAG,OAAO;IAE/B,IAAI,CAAC/B,iBAAiB,CAACgC,IAAI,CAAC,gCAAgC,EAAEhB,GAAG,EAAE,gBAAgB,CAAC,CAACiB,IAAI,CAAEC,IAAuB,IAAI;MACpH,IAAIA,IAAI,CAACC,EAAE,EAAE;QACX,IAAIC,GAAG,GAAGF,IAAI,CAACG,IAAI;QACnB,IAAIC,KAAK,GAAG,EAAE;QACd,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACe,MAAM,CAAC,SAAS,CAAC;QACrC,IAAI,EAAEkB,GAAG,EAAEG,MAAM,GAAG,CAAC,CAAC,EAAE;UACtBH,GAAG,GAAG,IAAI,CAAClB,MAAM,CAAC,SAAS,CAAC;QAC9B;QACAkB,GAAG,CAACI,OAAO,CAACC,IAAI,IAAG;UACjB,IAAI,CAACrC,aAAa,CAACsC,IAAI,CAACD,IAAI,CAAC;UAC7BH,KAAK,IAAIK,MAAM,CAACF,IAAI,CAAC3D,UAAU,CAAC;QAClC,CAAC,CAAC;QACF,IAAI,CAAC8D,OAAO,GAAGN,KAAK,GAAG,IAAI;QAC3B,IAAI,CAACnC,OAAO,CAACqC,OAAO,CAACK,IAAI,IAAG;UAC1B;UACA,IAAI,CAAC,IAAI,CAACzC,aAAa,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAMH,IAAI,CAACG,WAAW,IAAID,CAAC,CAACE,SAAS,KAAKJ,IAAI,CAACI,SAAS,CAAC,EAAE;YACxG,IAAI,CAAC5C,cAAc,CAACqC,IAAI,CAACG,IAAI,CAAC;UAChC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA;EACAK,UAAUA,CAAA;IACR,MAAM/C,OAAO,GAAG;MACZ,WAAW,EAAE,IAAI,CAACe,MAAM,CAAC,WAAW,CAAC;MACrC,WAAW,EAAE;KAChB;IACDf,OAAO,CAACgD,SAAS,GAAG,IAAI,CAAC/C,aAAa,CAACgD,GAAG,CAAC,CAACP,IAAI,EAAEQ,CAAC,MAAM;MACvDZ,IAAI,EAAE;QACJa,QAAQ,EAAET,IAAI,CAACU,YAAY;QAC3BC,WAAW,EAAE,CAAC;QACdC,eAAe,EAAEZ,IAAI,CAACG,WAAW;QACjC5D,MAAM,EAAEyD,IAAI,CAACzD,MAAM;QACnBsE,GAAG,EAAEL,CAAC,GAAG,CAAC;QACVM,GAAG,EAAEd,IAAI,CAACI,SAAS;QACnBW,EAAE,EAAEf,IAAI,CAACe,EAAE;QACXC,OAAO,EAAEhB,IAAI,CAAC/D,UAAU;QACxBK,cAAc,EAAE0D,IAAI,CAAC1D;OACtB;MACD2E,KAAK,EAAE;KACR,CAAC,CAAC;IAEH,MAAM9C,GAAG,GAAG;MACV+C,OAAO,EAAE,IAAI,CAAC7C,MAAM,CAAC,SAAS,CAAC;MAC/BmB,IAAI,EAAElC,OAAO;MACbgB,MAAM,EAAE,IAAI,CAACD,MAAM,CAAC,SAAS,CAAC;MAC9B8C,MAAM,EAAE,IAAI,CAAC9C,MAAM,CAAC,SAAS,CAAC;MAC9BE,MAAM,EAAE,IAAI,CAACF,MAAM,CAAC,SAAS,CAAC;MAC9B+C,MAAM,EAAE,IAAI,CAAC/C,MAAM,CAAC,SAAS,CAAC;MAC9BG,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC,MAAM;KAC9B;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAAClB,iBAAiB,CAACgC,IAAI,CAAC,+BAA+B,EAAEhB,GAAG,EAAE,gBAAgB,CAAC,CAACiB,IAAI,CAAEC,IAAuB,IAAI;MAC3H,IAAIA,IAAI,CAACC,EAAE,EAAE;QACX,IAAI,CAACpC,OAAO,CAACmE,OAAO,CAAC,aAAa,CAAC;QACnC;QACA,MAAMC,UAAU,GAAG;UACjBC,KAAK,EAAEjE,OAAO,CAACgD;SAChB;QACD,IAAI,CAACtD,YAAY,CAACwE,KAAK,CAACF,UAAU,CAAC;MACrC;IACA,CAAC,CACF;EACH;EAEAG,SAASA,CAACV,EAAU;IAClB,IAAI,CAAClD,MAAM,GAAGkD,EAAE;EAClB;EAEAW,QAAQA,CAAA;IACN,IAAI,CAAC7D,MAAM,GAAG,IAAI;EACpB;EAEA5C,SAASA,CAAA;IACP,IAAI,CAACoF,UAAU,EAAE;EACnB;EACA7E,QAAQA,CAAA;IACN,IAAI,CAACwB,YAAY,CAACwE,KAAK,EAAE;EAC3B;EACAlG,QAAQA,CAAA;IACN,MAAM6C,GAAG,GAAG;MACV+C,OAAO,EAAE,IAAI,CAAC7C,MAAM,CAAC,SAAS,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACD,MAAM,CAAC,SAAS,CAAC;MAC9B8C,MAAM,EAAE,IAAI,CAAC9C,MAAM,CAAC,SAAS,CAAC;MAC9BE,MAAM,EAAE,IAAI,CAACF,MAAM,CAAC,SAAS,CAAC;MAC9B+C,MAAM,EAAE,IAAI,CAAC/C,MAAM,CAAC,SAAS,CAAC;MAC9BG,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC,MAAM;KAC9B;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAI,CAAClB,iBAAiB,CAACgC,IAAI,CAAC,iCAAiC,EAAEhB,GAAG,EAAE,gBAAgB,CAAC,CAACiB,IAAI,CAAEC,IAAuB,IAAI;MACrH,IAAIA,IAAI,CAACC,EAAE,EAAE;QACX,IAAI,CAACpC,OAAO,CAACmE,OAAO,CAAC,eAAe,CAAC;QACrC;QACA,MAAMM,WAAW,GAAG;UAClBJ,KAAK,EAAE,IAAI,CAACjE,OAAO,CAACiD,GAAG,CAAC,CAACP,IAAI,EAAEQ,CAAC,MAAM;YACpCZ,IAAI,EAAE;cACJa,QAAQ,EAAET,IAAI,CAACU,YAAY;cAC3BC,WAAW,EAAEX,IAAI,CAACW,WAAW;cAC7BC,eAAe,EAAEZ,IAAI,CAACG,WAAW;cACjC5D,MAAM,EAAEyD,IAAI,CAACzD,MAAM;cACnBsE,GAAG,EAAEL,CAAC,GAAG,CAAC;cACVM,GAAG,EAAEd,IAAI,CAACI,SAAS;cACnBW,EAAE,EAAEf,IAAI,CAACe,EAAE;cACXC,OAAO,EAAEhB,IAAI,CAAC/D,UAAU;cACxBK,cAAc,EAAE0D,IAAI,CAAC1D;aACtB;YACD2E,KAAK,EAAE;WACR,CAAC;SACH;QACD,IAAI,CAACjE,YAAY,CAACwE,KAAK,CAACG,WAAW,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACzE,OAAO,CAAC0E,KAAK,CAACvC,IAAI,CAACwC,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAEA;EACAC,aAAaA,CAACC,IAAqB;IACjC,MAAMC,QAAQ,GAAGD,IAAI,CAACvC,IAAI;IAC1B,OAAOwC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG;EACzC;EAEA;;;EAGAC,IAAIA,CAAChB,KAA4B;IAC/B,IAAIA,KAAK,CAACiB,iBAAiB,KAAKjB,KAAK,CAACkB,SAAS,EAAE;MAC/ChI,eAAe,CAAC8G,KAAK,CAACkB,SAAS,CAAC3C,IAAI,EAAEyB,KAAK,CAACmB,aAAa,EAAEnB,KAAK,CAACoB,YAAY,CAAC;IAChF,CAAC,MAAM;MACLjI,iBAAiB,CAAC6G,KAAK,CAACiB,iBAAiB,CAAC1C,IAAI,EAC5CyB,KAAK,CAACkB,SAAS,CAAC3C,IAAI,EACpByB,KAAK,CAACmB,aAAa,EACnBnB,KAAK,CAACoB,YAAY,CAAC;IACvB;EACF;;;uBAvNWxF,iBAAiB,EAAArC,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAApI,EAAA,CAAA8H,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAtI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAC,iBAAA,GAAAxI,EAAA,CAAA8H,iBAAA,CAAAW,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAjBrG,iBAAiB;MAAAsG,SAAA;MAAAC,QAAA,GAAA5I,EAAA,CAAA6I,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnC9BnJ,EAAA,CAAAC,cAAA,iBAAuE;UACrED,EAAA,CAAAqJ,UAAA,IAAAC,wCAAA,gCAAAtJ,EAAA,CAAAuJ,sBAAA,CAAuB;UAgBbvJ,EALV,CAAAC,cAAA,aAAyB,aACC,gBACD,gBACS,aACQ,SAC5B;UAAAD,EAAA,CAAAW,MAAA,+BAAI;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAURZ,EATL,CAAAC,cAAA,iBAOC,kBAEI,YAAM;UAAAD,EAAA,CAAAW,MAAA,oBAAE;UAAOX,EAAP,CAAAY,YAAA,EAAO,EACjB;UAUCZ,EATF,CAAAC,cAAA,kBAQC,YACO;UAAAD,EAAA,CAAAW,MAAA,0BAAG;UAAOX,EAAP,CAAAY,YAAA,EAAO,EACjB;UAEEZ,EADH,CAAAC,cAAA,kBACG,YAAM;UAAAD,EAAA,CAAAW,MAAA,gCAAI;UAEfX,EAFe,CAAAY,YAAA,EAAO,EACnB,EACM;UACTZ,EAAA,CAAAC,cAAA,kBAQC;UADCD,EAAA,CAAAE,UAAA,gCAAAsJ,8DAAApI,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAAqJ,GAAA;YAAA,OAAAzJ,EAAA,CAAAQ,WAAA,CAAsB4I,GAAA,CAAA3B,IAAA,CAAArG,MAAA,CAAY;UAAA,EAAC;UAEnCpB,EAAA,CAAAC,cAAA,kBAAyD;UACvDD,EAAA,CAAAqJ,UAAA,KAAAK,iCAAA,kBAMC;UAqBT1J,EAHM,CAAAY,YAAA,EAAS,EACL,EACF,EACC;UAGLZ,EAFJ,CAAAC,cAAA,kBAA4B,eACK,UACzB;UAAAD,EAAA,CAAAW,MAAA,4CAAW;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAgBfZ,EAfL,CAAAC,cAAA,iBAOC,kBAQI,YAAM;UAAAD,EAAA,CAAAW,MAAA,oBAAE;UAAOX,EAAP,CAAAY,YAAA,EAAO,EACjB;UAEEZ,EADH,CAAAC,cAAA,kBACG,YAAM;UAAAD,EAAA,CAAAW,MAAA,0BAAG;UAGdX,EAHc,CAAAY,YAAA,EAAO,EAClB,EAEM;UACTZ,EAAA,CAAAC,cAAA,kBASC;UADCD,EAAA,CAAAE,UAAA,gCAAAyJ,8DAAAvI,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAAqJ,GAAA;YAAA,OAAAzJ,EAAA,CAAAQ,WAAA,CAAsB4I,GAAA,CAAA3B,IAAA,CAAArG,MAAA,CAAY;UAAA,EAAC;UAEnCpB,EAAA,CAAAC,cAAA,kBAAyD;UACvDD,EAAA,CAAAqJ,UAAA,KAAAO,iCAAA,kBAMC;UAcf5J,EANY,CAAAY,YAAA,EAAS,EACL,EACF,EACC,EACF,EACL,EACF;UAENZ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAW,MAAA,4CAAM;UAAAX,EAAA,CAAAY,YAAA,EAAK;UASXZ,EARJ,CAAAC,cAAA,oBAMC,aACQ,UACD;UACFD,EAAA,CAAAqJ,UAAA,KAAAQ,gCAAA,iBAAmD;UAGrD7J,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,UAAI;UACFD,EAAA,CAAAqJ,UAAA,KAAAS,gCAAA,iBAGC;UAMT9J,EAHM,CAAAY,YAAA,EAAK,EACC,EACC,EACH;;;;;;UApKoBZ,EAArB,CAAAiB,UAAA,qBAAoB,YAAA8I,YAAA,CAAqB;UAgDpC/J,EAAA,CAAA0B,SAAA,IAAiC;UACjC1B,EADA,CAAAiB,UAAA,oBAAAmI,GAAA,CAAArG,aAAA,CAAiC,2BAAA/C,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAC,gBAAA,EACQ;UAQhBlK,EAAA,CAAA0B,SAAA,GAAkB;UAAlB1B,EAAA,CAAAiB,UAAA,YAAAmI,GAAA,CAAArG,aAAA,CAAkB;UAsD3C/C,EAAA,CAAA0B,SAAA,IAAkC;UAElC1B,EAFA,CAAAiB,UAAA,oBAAAmI,GAAA,CAAApG,cAAA,CAAkC,2BAAAhD,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAE,eAAA,EACM,8BAAAf,GAAA,CAAA9B,aAAA,CACG;UAQjBtH,EAAA,CAAA0B,SAAA,GAAmB;UAAnB1B,EAAA,CAAAiB,UAAA,YAAAmI,GAAA,CAAApG,cAAA,CAAmB;UAqBvDhD,EAAA,CAAA0B,SAAA,GAAwB;UAIxB1B,EAJA,CAAAiB,UAAA,WAAAmI,GAAA,CAAArG,aAAA,CAAwB,aAAA/C,EAAA,CAAAgK,eAAA,KAAAI,GAAA,EAAAhB,GAAA,CAAA7D,OAAA,EACG,oBACR,4BACQ,2BACD;UAIDvF,EAAA,CAAA0B,SAAA,GAAkB;UAAlB1B,EAAA,CAAAiB,UAAA,YAAAmI,GAAA,CAAArG,aAAA,CAAkB;UAMpB/C,EAAA,CAAA0B,SAAA,GAAkB;UAAlB1B,EAAA,CAAAiB,UAAA,YAAAmI,GAAA,CAAArG,aAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}