{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['P', 'M'],\n  abbreviated: ['PK', 'MK'],\n  wide: ['<PERSON> Krishtit', '<PERSON><PERSON>htit']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['4-mujori I', '4-mujori II', '4-mujori III', '4-mujori IV']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'S', 'M', 'P', 'M', 'Q', 'K', 'G', 'S', 'T', 'N', 'D'],\n  abbreviated: ['Jan', 'Shk', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>er', '<PERSON>r', '<PERSON>', '<PERSON>ht', '<PERSON>t', 'Nën', 'Dhj'],\n  wide: ['Janar', 'Shkurt', 'Mars', 'Prill', 'Maj', 'Qershor', 'Korrik', 'Gusht', 'Shtator', 'Tetor', 'Nëntor', 'Dhjetor']\n};\nvar dayValues = {\n  narrow: ['D', 'H', 'M', 'M', 'E', 'P', 'S'],\n  short: ['Di', 'Hë', 'Ma', 'Më', 'En', 'Pr', 'Sh'],\n  abbreviated: ['Die', 'Hën', 'Mar', 'Mër', 'Enj', 'Pre', 'Sht'],\n  wide: ['Dielë', 'Hënë', 'Martë', 'Mërkurë', 'Enjte', 'Premte', 'Shtunë']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'p',\n    pm: 'm',\n    midnight: 'm',\n    noon: 'd',\n    morning: 'mëngjes',\n    afternoon: 'dite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  },\n  abbreviated: {\n    am: 'PD',\n    pm: 'MD',\n    midnight: 'mesnëtë',\n    noon: 'drek',\n    morning: 'mëngjes',\n    afternoon: 'mbasdite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  },\n  wide: {\n    am: 'p.d.',\n    pm: 'm.d.',\n    midnight: 'mesnëtë',\n    noon: 'drek',\n    morning: 'mëngjes',\n    afternoon: 'mbasdite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'p',\n    pm: 'm',\n    midnight: 'm',\n    noon: 'd',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  },\n  abbreviated: {\n    am: 'PD',\n    pm: 'MD',\n    midnight: 'mesnatë',\n    noon: 'drek',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  },\n  wide: {\n    am: 'p.d.',\n    pm: 'm.d.',\n    midnight: 'mesnatë',\n    noon: 'drek',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  if ((options === null || options === void 0 ? void 0 : options.unit) === 'hour') return String(number);\n  if (number === 1) return number + '-rë';\n  if (number === 4) return number + 't';\n  return number + '-të';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/sq/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['P', 'M'],\n  abbreviated: ['PK', 'MK'],\n  wide: ['<PERSON> Krishtit', '<PERSON><PERSON>htit']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['4-mujori I', '4-mujori II', '4-mujori III', '4-mujori IV']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'S', 'M', 'P', 'M', 'Q', 'K', 'G', 'S', 'T', 'N', 'D'],\n  abbreviated: ['Jan', 'Shk', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>er', '<PERSON>r', '<PERSON>', '<PERSON>ht', '<PERSON>t', 'Nën', 'Dhj'],\n  wide: ['Janar', 'Shkurt', 'Mars', 'Prill', 'Maj', 'Qershor', 'Korrik', 'Gusht', 'Shtator', 'Tetor', 'Nëntor', 'Dhjetor']\n};\nvar dayValues = {\n  narrow: ['D', 'H', 'M', 'M', 'E', 'P', 'S'],\n  short: ['Di', 'Hë', 'Ma', 'Më', 'En', 'Pr', 'Sh'],\n  abbreviated: ['Die', 'Hën', 'Mar', 'Mër', 'Enj', 'Pre', 'Sht'],\n  wide: ['Dielë', 'Hënë', 'Martë', 'Mërkurë', 'Enjte', 'Premte', 'Shtunë']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'p',\n    pm: 'm',\n    midnight: 'm',\n    noon: 'd',\n    morning: 'mëngjes',\n    afternoon: 'dite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  },\n  abbreviated: {\n    am: 'PD',\n    pm: 'MD',\n    midnight: 'mesnëtë',\n    noon: 'drek',\n    morning: 'mëngjes',\n    afternoon: 'mbasdite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  },\n  wide: {\n    am: 'p.d.',\n    pm: 'm.d.',\n    midnight: 'mesnëtë',\n    noon: 'drek',\n    morning: 'mëngjes',\n    afternoon: 'mbasdite',\n    evening: 'mbrëmje',\n    night: 'natë'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'p',\n    pm: 'm',\n    midnight: 'm',\n    noon: 'd',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  },\n  abbreviated: {\n    am: 'PD',\n    pm: 'MD',\n    midnight: 'mesnatë',\n    noon: 'drek',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  },\n  wide: {\n    am: 'p.d.',\n    pm: 'm.d.',\n    midnight: 'mesnatë',\n    noon: 'drek',\n    morning: 'në mëngjes',\n    afternoon: 'në mbasdite',\n    evening: 'në mbrëmje',\n    night: 'në mesnatë'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  if ((options === null || options === void 0 ? void 0 : options.unit) === 'hour') return String(number);\n  if (number === 1) return number + '-rë';\n  if (number === 4) return number + 't';\n  return number + '-të';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe;AACzC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa;AACnE,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS;AACzH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;AACzE,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAI,CAACC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI,MAAM,MAAM,EAAE,OAAOC,MAAM,CAACH,MAAM,CAAC;EACtG,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAOA,MAAM,GAAG,KAAK;EACvC,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAOA,MAAM,GAAG,GAAG;EACrC,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AACD,IAAII,QAAQ,GAAG;EACbP,aAAa,EAAEA,aAAa;EAC5BQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAEjC,eAAe,CAAC;IACnB4B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAElC,eAAe,CAAC;IACzB4B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}