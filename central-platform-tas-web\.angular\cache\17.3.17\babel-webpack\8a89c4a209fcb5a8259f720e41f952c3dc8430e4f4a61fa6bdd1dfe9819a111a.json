{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BusinessLineComponent } from './businessline.component';\nimport { BusinessLineEditComponent } from '@business/tas/businessline/businessline-edit/businessline-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: BusinessLineComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: BusinessLineEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class BusinessLineRoutingModule {\n  static {\n    this.ɵfac = function BusinessLineRoutingModule_Factory(t) {\n      return new (t || BusinessLineRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BusinessLineRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BusinessLineRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "BusinessLineComponent", "BusinessLineEditComponent", "routes", "path", "component", "data", "cache", "BusinessLineRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\businessline\\businessline-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { BusinessLineComponent } from './businessline.component';\r\nimport {BusinessLineEditComponent} from '@business/tas/businessline/businessline-edit/businessline-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: BusinessLineComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: BusinessLineEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class BusinessLineRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAAQC,yBAAyB,QAAO,0EAA0E;;;AAClH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,qBAAqB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACzE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,yBAAyB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACnF;AAMD,OAAM,MAAOC,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBAH1BR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,yBAAyB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAF1BZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}