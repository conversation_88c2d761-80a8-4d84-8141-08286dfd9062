{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { WarehouseComponent } from './warehouse.component';\nimport { WarehouseRoutingModule } from './warehouse-routing.module';\nimport { WarehouseEditComponent } from '@business/tas/warehouse/warehouse-edit/warehouse-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [WarehouseComponent, WarehouseEditComponent];\nexport class WarehouseModule {\n  static {\n    this.ɵfac = function WarehouseModule_Factory(t) {\n      return new (t || WarehouseModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WarehouseModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, WarehouseRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WarehouseModule, {\n    declarations: [WarehouseComponent, WarehouseEditComponent],\n    imports: [SharedModule, WarehouseRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "WarehouseComponent", "WarehouseRoutingModule", "WarehouseEditComponent", "COMPONENTS", "WarehouseModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\warehouse\\warehouse.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { WarehouseComponent } from './warehouse.component';\r\nimport { WarehouseRoutingModule } from './warehouse-routing.module';\r\nimport {WarehouseEditComponent} from '@business/tas/warehouse/warehouse-edit/warehouse-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  WarehouseComponent,\r\n  WarehouseEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, WarehouseRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class WarehouseModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAAQC,sBAAsB,QAAO,iEAAiE;;AAEtG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}