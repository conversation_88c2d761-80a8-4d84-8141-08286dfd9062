{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['v.C.', 'n.C.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['voor <PERSON>', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1e kwartaal', '2e kwartaal', '3e kwartaal', '4e kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mrt.', 'apr.', 'mei', 'jun.', 'jul.', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'maart', 'april', 'mei', 'juni', 'juli', 'augustus', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['Z', 'M', 'D', 'W', 'D', 'V', 'Z'],\n  short: ['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],\n  abbreviated: ['zon', 'maa', 'din', 'woe', 'don', 'vri', 'zat'],\n  wide: ['zondag', 'maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/nl-BE/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['v.C.', 'n.C.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['voor <PERSON>', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1e kwartaal', '2e kwartaal', '3e kwartaal', '4e kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mrt.', 'apr.', 'mei', 'jun.', 'jul.', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'maart', 'april', 'mei', 'juni', 'juli', 'augustus', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['Z', 'M', 'D', 'W', 'D', 'V', 'Z'],\n  short: ['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],\n  abbreviated: ['zon', 'maa', 'din', 'woe', 'don', 'vri', 'zat'],\n  wide: ['zondag', 'maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middag',\n    morning: \"'s ochtends\",\n    afternoon: \"'s namiddags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACxBC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EACjCC,IAAI,EAAE,CAAC,eAAe,EAAE,aAAa;AACvC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AACnI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU;AACvF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}