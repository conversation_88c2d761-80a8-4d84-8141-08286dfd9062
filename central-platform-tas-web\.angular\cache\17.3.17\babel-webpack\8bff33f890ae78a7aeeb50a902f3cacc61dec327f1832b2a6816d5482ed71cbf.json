{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'פחות משנייה',\n    two: 'פחות משתי שניות',\n    other: 'פחות מ־{{count}} שניות'\n  },\n  xSeconds: {\n    one: 'שנייה',\n    two: 'שתי שניות',\n    other: '{{count}} שניות'\n  },\n  halfAMinute: 'חצי דקה',\n  lessThanXMinutes: {\n    one: 'פחות מדקה',\n    two: 'פחות משתי דקות',\n    other: 'פחות מ־{{count}} דקות'\n  },\n  xMinutes: {\n    one: 'דקה',\n    two: 'שתי דקות',\n    other: '{{count}} דקות'\n  },\n  aboutXHours: {\n    one: 'כשעה',\n    two: 'כשעתיים',\n    other: 'כ־{{count}} שעות'\n  },\n  xHours: {\n    one: 'שעה',\n    two: 'שעתיים',\n    other: '{{count}} שעות'\n  },\n  xDays: {\n    one: 'יום',\n    two: 'יומיים',\n    other: '{{count}} ימים'\n  },\n  aboutXWeeks: {\n    one: 'כשבוע',\n    two: 'כשבועיים',\n    other: 'כ־{{count}} שבועות'\n  },\n  xWeeks: {\n    one: 'שבוע',\n    two: 'שבועיים',\n    other: '{{count}} שבועות'\n  },\n  aboutXMonths: {\n    one: 'כחודש',\n    two: 'כחודשיים',\n    other: 'כ־{{count}} חודשים'\n  },\n  xMonths: {\n    one: 'חודש',\n    two: 'חודשיים',\n    other: '{{count}} חודשים'\n  },\n  aboutXYears: {\n    one: 'כשנה',\n    two: 'כשנתיים',\n    other: 'כ־{{count}} שנים'\n  },\n  xYears: {\n    one: 'שנה',\n    two: 'שנתיים',\n    other: '{{count}} שנים'\n  },\n  overXYears: {\n    one: 'יותר משנה',\n    two: 'יותר משנתיים',\n    other: 'יותר מ־{{count}} שנים'\n  },\n  almostXYears: {\n    one: 'כמעט שנה',\n    two: 'כמעט שנתיים',\n    other: 'כמעט {{count}} שנים'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === 'xDays' && options !== null && options !== void 0 && options.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? 'מחר' : 'מחרתיים';\n    }\n    return count === 1 ? 'אתמול' : 'שלשום';\n  }\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'בעוד ' + result;\n    } else {\n      return 'לפני ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "addSuffix", "comparison", "result", "tokenValue", "replace", "String"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/he/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'פחות משנייה',\n    two: 'פחות משתי שניות',\n    other: 'פחות מ־{{count}} שניות'\n  },\n  xSeconds: {\n    one: 'שנייה',\n    two: 'שתי שניות',\n    other: '{{count}} שניות'\n  },\n  halfAMinute: 'חצי דקה',\n  lessThanXMinutes: {\n    one: 'פחות מדקה',\n    two: 'פחות משתי דקות',\n    other: 'פחות מ־{{count}} דקות'\n  },\n  xMinutes: {\n    one: 'דקה',\n    two: 'שתי דקות',\n    other: '{{count}} דקות'\n  },\n  aboutXHours: {\n    one: 'כשעה',\n    two: 'כשעתיים',\n    other: 'כ־{{count}} שעות'\n  },\n  xHours: {\n    one: 'שעה',\n    two: 'שעתיים',\n    other: '{{count}} שעות'\n  },\n  xDays: {\n    one: 'יום',\n    two: 'יומיים',\n    other: '{{count}} ימים'\n  },\n  aboutXWeeks: {\n    one: 'כשבוע',\n    two: 'כשבועיים',\n    other: 'כ־{{count}} שבועות'\n  },\n  xWeeks: {\n    one: 'שבוע',\n    two: 'שבועיים',\n    other: '{{count}} שבועות'\n  },\n  aboutXMonths: {\n    one: 'כחודש',\n    two: 'כחודשיים',\n    other: 'כ־{{count}} חודשים'\n  },\n  xMonths: {\n    one: 'חודש',\n    two: 'חודשיים',\n    other: '{{count}} חודשים'\n  },\n  aboutXYears: {\n    one: 'כשנה',\n    two: 'כשנתיים',\n    other: 'כ־{{count}} שנים'\n  },\n  xYears: {\n    one: 'שנה',\n    two: 'שנתיים',\n    other: '{{count}} שנים'\n  },\n  overXYears: {\n    one: 'יותר משנה',\n    two: 'יותר משנתיים',\n    other: 'יותר מ־{{count}} שנים'\n  },\n  almostXYears: {\n    one: 'כמעט שנה',\n    two: 'כמעט שנתיים',\n    other: 'כמעט {{count}} שנים'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === 'xDays' && options !== null && options !== void 0 && options.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? 'מחר' : 'מחרתיים';\n    }\n    return count === 1 ? 'אתמול' : 'שלשום';\n  }\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'בעוד ' + result;\n    } else {\n      return 'לפני ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRH,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,SAAS;EACtBC,gBAAgB,EAAE;IAChBL,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRN,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXP,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNR,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLT,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXV,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNX,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZZ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPb,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXd,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNf,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVhB,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZjB,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE;EACA,IAAIF,KAAK,KAAK,OAAO,IAAIE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,IAAIF,KAAK,IAAI,CAAC,EAAE;IAClG,IAAIC,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOH,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;IACxC;IACA,OAAOA,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO;EACxC;EACA,IAAII,MAAM;EACV,IAAIC,UAAU,GAAG3B,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOM,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIL,KAAK,KAAK,CAAC,EAAE;IACtBI,MAAM,GAAGC,UAAU,CAACzB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,CAAC,EAAE;IACtBI,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM;IACLuB,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;IAC/D,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGC,MAAM;IACzB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}