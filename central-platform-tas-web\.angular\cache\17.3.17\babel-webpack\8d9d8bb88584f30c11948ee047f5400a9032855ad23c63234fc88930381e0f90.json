{"ast": null, "code": "import { VesselListRoutingModule } from './vessel-list-routing.module';\nimport { VesselListComponent } from './vessel-list.component';\nimport { VesselEditComponent } from './vessel-edit/vessel-edit.component';\nimport { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [VesselListComponent, VesselEditComponent];\nexport class VesselListModule {\n  static {\n    this.ɵfac = function VesselListModule_Factory(t) {\n      return new (t || VesselListModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselListModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VesselListRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselListModule, {\n    declarations: [VesselListComponent, VesselEditComponent],\n    imports: [SharedModule, VesselListRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["VesselListRoutingModule", "VesselListComponent", "VesselEditComponent", "SharedModule", "LayoutModule", "COMPONENTS", "VesselListModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-list\\vessel-list.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { VesselListRoutingModule } from './vessel-list-routing.module';\r\nimport { VesselListComponent } from './vessel-list.component';\r\nimport { VesselEditComponent } from './vessel-edit/vessel-edit.component';\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\n\r\nconst COMPONENTS = [VesselListComponent, VesselEditComponent];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VesselListRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VesselListModule { }\r\n"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD,MAAMC,UAAU,GAAG,CAACJ,mBAAmB,EAAEC,mBAAmB,CAAC;AAM7D,OAAM,MAAOI,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBH,YAAY,EAAEH,uBAAuB,EAAEI,YAAY;IAAA;EAAA;;;2EAGlDE,gBAAgB;IAAAC,YAAA,GANTN,mBAAmB,EAAEC,mBAAmB;IAAAM,OAAA,GAGhDL,YAAY,EAAEH,uBAAuB,EAAEI,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}