{"ast": null, "code": "function buildLocalizeTokenFn(schema) {\n  return function (count, options) {\n    if (count === 1) {\n      if (options !== null && options !== void 0 && options.addSuffix) {\n        return schema.one[0].replace('{{time}}', schema.one[2]);\n      } else {\n        return schema.one[0].replace('{{time}}', schema.one[1]);\n      }\n    } else {\n      var rem = count % 10 === 1 && count % 100 !== 11;\n      if (options !== null && options !== void 0 && options.addSuffix) {\n        return schema.other[0].replace('{{time}}', rem ? schema.other[3] : schema.other[4]).replace('{{count}}', String(count));\n      } else {\n        return schema.other[0].replace('{{time}}', rem ? schema.other[1] : schema.other[2]).replace('{{count}}', String(count));\n      }\n    }\n  };\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    one: ['mazāk par {{time}}', 'sekundi', 'sekundi'],\n    other: ['mazāk nekā {{count}} {{time}}', 'sekunde', 'sekundes', 'sekundes', 'sekundēm']\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'sekunde', 'sekundes'],\n    other: ['{{count}} {{time}}', 'sekunde', 'sekundes', 'sekundes', 'sekundēm']\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      return 'pusminūtes';\n    } else {\n      return 'pusminūte';\n    }\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    one: ['mazāk par {{time}}', 'minūti', 'minūti'],\n    other: ['mazāk nekā {{count}} {{time}}', 'minūte', 'minūtes', 'minūtes', 'minūtēm']\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'minūte', 'minūtes'],\n    other: ['{{count}} {{time}}', 'minūte', 'minūtes', 'minūtes', 'minūtēm']\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'stunda', 'stundas'],\n    other: ['apmēram {{count}} {{time}}', 'stunda', 'stundas', 'stundas', 'stundām']\n  }),\n  xHours: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'stunda', 'stundas'],\n    other: ['{{count}} {{time}}', 'stunda', 'stundas', 'stundas', 'stundām']\n  }),\n  xDays: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'diena', 'dienas'],\n    other: ['{{count}} {{time}}', 'diena', 'dienas', 'dienas', 'dienām']\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'nedēļa', 'nedēļas'],\n    other: ['apmēram {{count}} {{time}}', 'nedēļa', 'nedēļu', 'nedēļas', 'nedēļām']\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'nedēļa', 'nedēļas'],\n    other: ['{{count}} {{time}}',\n    // TODO\n    'nedēļa', 'nedēļu', 'nedēļas', 'nedēļām']\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'mēnesis', 'mēneša'],\n    other: ['apmēram {{count}} {{time}}', 'mēnesis', 'mēneši', 'mēneša', 'mēnešiem']\n  }),\n  xMonths: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'mēnesis', 'mēneša'],\n    other: ['{{count}} {{time}}', 'mēnesis', 'mēneši', 'mēneša', 'mēnešiem']\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'gads', 'gada'],\n    other: ['apmēram {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  xYears: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'gads', 'gada'],\n    other: ['{{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  overXYears: buildLocalizeTokenFn({\n    one: ['ilgāk par 1 {{time}}', 'gadu', 'gadu'],\n    other: ['vairāk nekā {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    one: ['gandrīz 1 {{time}}', 'gads', 'gada'],\n    other: ['vairāk nekā {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result = formatDistanceLocale[token](count, options);\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'pēc ' + result;\n    } else {\n      return 'pirms ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["buildLocalizeTokenFn", "schema", "count", "options", "addSuffix", "one", "replace", "rem", "other", "String", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_count", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "result", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/lv/_lib/formatDistance/index.js"], "sourcesContent": ["function buildLocalizeTokenFn(schema) {\n  return function (count, options) {\n    if (count === 1) {\n      if (options !== null && options !== void 0 && options.addSuffix) {\n        return schema.one[0].replace('{{time}}', schema.one[2]);\n      } else {\n        return schema.one[0].replace('{{time}}', schema.one[1]);\n      }\n    } else {\n      var rem = count % 10 === 1 && count % 100 !== 11;\n      if (options !== null && options !== void 0 && options.addSuffix) {\n        return schema.other[0].replace('{{time}}', rem ? schema.other[3] : schema.other[4]).replace('{{count}}', String(count));\n      } else {\n        return schema.other[0].replace('{{time}}', rem ? schema.other[1] : schema.other[2]).replace('{{count}}', String(count));\n      }\n    }\n  };\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    one: ['mazāk par {{time}}', 'sekundi', 'sekundi'],\n    other: ['mazāk nekā {{count}} {{time}}', 'sekunde', 'sekundes', 'sekundes', 'sekundēm']\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'sekunde', 'sekundes'],\n    other: ['{{count}} {{time}}', 'sekunde', 'sekundes', 'sekundes', 'sekundēm']\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      return 'pusminūtes';\n    } else {\n      return 'pusminūte';\n    }\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    one: ['mazāk par {{time}}', 'minūti', 'minūti'],\n    other: ['mazāk nekā {{count}} {{time}}', 'minūte', 'minūtes', 'minūtes', 'minūtēm']\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'minūte', 'minūtes'],\n    other: ['{{count}} {{time}}', 'minūte', 'minūtes', 'minūtes', 'minūtēm']\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'stunda', 'stundas'],\n    other: ['apmēram {{count}} {{time}}', 'stunda', 'stundas', 'stundas', 'stundām']\n  }),\n  xHours: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'stunda', 'stundas'],\n    other: ['{{count}} {{time}}', 'stunda', 'stundas', 'stundas', 'stundām']\n  }),\n  xDays: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'diena', 'dienas'],\n    other: ['{{count}} {{time}}', 'diena', 'dienas', 'dienas', 'dienām']\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'nedēļa', 'nedēļas'],\n    other: ['apmēram {{count}} {{time}}', 'nedēļa', 'nedēļu', 'nedēļas', 'nedēļām']\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'nedēļa', 'nedēļas'],\n    other: ['{{count}} {{time}}',\n    // TODO\n    'nedēļa', 'nedēļu', 'nedēļas', 'nedēļām']\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'mēnesis', 'mēneša'],\n    other: ['apmēram {{count}} {{time}}', 'mēnesis', 'mēneši', 'mēneša', 'mēnešiem']\n  }),\n  xMonths: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'mēnesis', 'mēneša'],\n    other: ['{{count}} {{time}}', 'mēnesis', 'mēneši', 'mēneša', 'mēnešiem']\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    one: ['apmēram 1 {{time}}', 'gads', 'gada'],\n    other: ['apmēram {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  xYears: buildLocalizeTokenFn({\n    one: ['1 {{time}}', 'gads', 'gada'],\n    other: ['{{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  overXYears: buildLocalizeTokenFn({\n    one: ['ilgāk par 1 {{time}}', 'gadu', 'gadu'],\n    other: ['vairāk nekā {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    one: ['gandrīz 1 {{time}}', 'gads', 'gada'],\n    other: ['vairāk nekā {{count}} {{time}}', 'gads', 'gadi', 'gada', 'gadiem']\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result = formatDistanceLocale[token](count, options);\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'pēc ' + result;\n    } else {\n      return 'pirms ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,MAAM,EAAE;EACpC,OAAO,UAAUC,KAAK,EAAEC,OAAO,EAAE;IAC/B,IAAID,KAAK,KAAK,CAAC,EAAE;MACf,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;QAC/D,OAAOH,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAEL,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACL,OAAOJ,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAEL,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzD;IACF,CAAC,MAAM;MACL,IAAIE,GAAG,GAAGL,KAAK,GAAG,EAAE,KAAK,CAAC,IAAIA,KAAK,GAAG,GAAG,KAAK,EAAE;MAChD,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;QAC/D,OAAOH,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,UAAU,EAAEC,GAAG,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,GAAGP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,WAAW,EAAEG,MAAM,CAACP,KAAK,CAAC,CAAC;MACzH,CAAC,MAAM;QACL,OAAOD,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,UAAU,EAAEC,GAAG,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,GAAGP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAAC,WAAW,EAAEG,MAAM,CAACP,KAAK,CAAC,CAAC;MACzH;IACF;EACF,CAAC;AACH;AACA,IAAIQ,oBAAoB,GAAG;EACzBC,gBAAgB,EAAEX,oBAAoB,CAAC;IACrCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,SAAS,CAAC;IACjDG,KAAK,EAAE,CAAC,+BAA+B,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;EACxF,CAAC,CAAC;EACFI,QAAQ,EAAEZ,oBAAoB,CAAC;IAC7BK,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC;IAC1CG,KAAK,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;EAC7E,CAAC,CAAC;EACFK,WAAW,EAAE,SAASA,WAAWA,CAACC,MAAM,EAAEX,OAAO,EAAE;IACjD,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;MAC/D,OAAO,YAAY;IACrB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF,CAAC;EACDW,gBAAgB,EAAEf,oBAAoB,CAAC;IACrCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC/CG,KAAK,EAAE,CAAC,+BAA+B,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACpF,CAAC,CAAC;EACFQ,QAAQ,EAAEhB,oBAAoB,CAAC;IAC7BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACzE,CAAC,CAAC;EACFS,WAAW,EAAEjB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAChDG,KAAK,EAAE,CAAC,4BAA4B,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACjF,CAAC,CAAC;EACFU,MAAM,EAAElB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;EACzE,CAAC,CAAC;EACFW,KAAK,EAAEnB,oBAAoB,CAAC;IAC1BK,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACtCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;EACrE,CAAC,CAAC;EACFY,WAAW,EAAEpB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,QAAQ,EAAE,SAAS,CAAC;IAChDG,KAAK,EAAE,CAAC,4BAA4B,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS;EAChF,CAAC,CAAC;EACFa,MAAM,EAAErB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB;IAC5B;IACA,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS;EAC1C,CAAC,CAAC;EACFc,YAAY,EAAEtB,oBAAoB,CAAC;IACjCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,QAAQ,CAAC;IAChDG,KAAK,EAAE,CAAC,4BAA4B,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;EACjF,CAAC,CAAC;EACFe,OAAO,EAAEvB,oBAAoB,CAAC;IAC5BK,GAAG,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;IACxCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;EACzE,CAAC,CAAC;EACFgB,WAAW,EAAExB,oBAAoB,CAAC;IAChCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CG,KAAK,EAAE,CAAC,4BAA4B,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EACxE,CAAC,CAAC;EACFiB,MAAM,EAAEzB,oBAAoB,CAAC;IAC3BK,GAAG,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC;IACnCG,KAAK,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAChE,CAAC,CAAC;EACFkB,UAAU,EAAE1B,oBAAoB,CAAC;IAC/BK,GAAG,EAAE,CAAC,sBAAsB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7CG,KAAK,EAAE,CAAC,gCAAgC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAC5E,CAAC,CAAC;EACFmB,YAAY,EAAE3B,oBAAoB,CAAC;IACjCK,GAAG,EAAE,CAAC,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CG,KAAK,EAAE,CAAC,gCAAgC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;EAC5E,CAAC;AACH,CAAC;AACD,IAAIoB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE3B,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAI2B,MAAM,GAAGpB,oBAAoB,CAACmB,KAAK,CAAC,CAAC3B,KAAK,EAAEC,OAAO,CAAC;EACxD,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;IAC/D,IAAID,OAAO,CAAC4B,UAAU,IAAI5B,OAAO,CAAC4B,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGD,MAAM;IACxB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}