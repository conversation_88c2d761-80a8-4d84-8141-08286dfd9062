{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class TAS_T_VESSEL_KAY extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"主键\",\n      \"vesselId\": \"船舶主表主键\",\n      \"kayNo\": \"贝号\",\n      \"rowtier\": \"贝位信息\",\n      \"rowNum\": \"行数\",\n      \"rowSep\": \"行步长\",\n      \"rowFrom\": \"行起始\",\n      \"columnNum\": \"列数\",\n      \"columnSep\": \"列步长\",\n      \"columnFrom\": \"列起始\",\n      \"orgId\": \"所属组织机构主键\",\n      \"orgLevelNo\": \"所属组织机构代码\",\n      \"entLevelNo\": \"所属公司代码\",\n      \"remark\": \"备注\",\n      \"isDelete\": \"是否删除,1：是,0：否\",\n      \"version\": \"版本号\",\n      \"tenantId\": \"租户ID\",\n      \"createdUser\": \"新建人ID\",\n      \"createdTime\": \"创建时间\",\n      \"modifiedUser\": \"修改人ID\",\n      \"modifiedTime\": \"修改时间\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_VESSEL_KAY'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "TAS_T_VESSEL_KAY", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\TAS\\TAS_T_VESSEL_KAY.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class TAS_T_VESSEL_KAY extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_VESSEL_KAY'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\": \"主键\",\r\n      \"vesselId\": \"船舶主表主键\",\r\n      \"kayNo\": \"贝号\",\r\n      \"rowtier\": \"贝位信息\",\r\n      \"rowNum\": \"行数\",\r\n      \"rowSep\": \"行步长\",\r\n      \"rowFrom\": \"行起始\",\r\n      \"columnNum\": \"列数\",\r\n      \"columnSep\": \"列步长\",\r\n      \"columnFrom\": \"列起始\",\r\n      \"orgId\": \"所属组织机构主键\",\r\n      \"orgLevelNo\": \"所属组织机构代码\",\r\n      \"entLevelNo\": \"所属公司代码\",\r\n      \"remark\": \"备注\",\r\n      \"isDelete\": \"是否删除,1：是,0：否\",\r\n      \"version\": \"版本号\",\r\n      \"tenantId\": \"租户ID\",\r\n      \"createdUser\": \"新建人ID\",\r\n      \"createdTime\": \"创建时间\",\r\n      \"modifiedUser\": \"修改人ID\",\r\n      \"modifiedTime\": \"修改时间\"\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,gBAAiB,SAAQD,QAAQ;EAQ5CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAE,IAAI;MACV,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,IAAI;MACb,SAAS,EAAE,MAAM;MACjB,QAAQ,EAAE,IAAI;MACd,QAAQ,EAAE,KAAK;MACf,SAAS,EAAE,KAAK;MAChB,WAAW,EAAE,IAAI;MACjB,WAAW,EAAE,KAAK;MAClB,YAAY,EAAE,KAAK;MACnB,OAAO,EAAE,UAAU;MACnB,YAAY,EAAE,UAAU;MACxB,YAAY,EAAE,QAAQ;MACtB,QAAQ,EAAE,IAAI;MACd,UAAU,EAAE,cAAc;MAC1B,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,MAAM;MAClB,aAAa,EAAE,OAAO;MACtB,aAAa,EAAE,MAAM;MACrB,cAAc,EAAE,OAAO;MACvB,cAAc,EAAE;KACjB,CAAC;IA7BJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC;IAChC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EA0BnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}