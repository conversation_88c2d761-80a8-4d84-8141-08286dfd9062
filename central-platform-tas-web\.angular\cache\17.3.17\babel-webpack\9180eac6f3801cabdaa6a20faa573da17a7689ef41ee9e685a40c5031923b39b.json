{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { IngeneralcargoComponent } from './ingeneralcargo.component';\nimport { IngeneralcargoEditComponent } from '@business/tas/ingeneralcargo/ingeneralcargo-edit/ingeneralcargo-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: IngeneralcargoComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: IngeneralcargoEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class IngeneralcargoRoutingModule {\n  static {\n    this.ɵfac = function IngeneralcargoRoutingModule_Factory(t) {\n      return new (t || IngeneralcargoRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IngeneralcargoRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IngeneralcargoRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "IngeneralcargoComponent", "IngeneralcargoEditComponent", "routes", "path", "component", "data", "cache", "IngeneralcargoRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\ingeneralcargo\\ingeneralcargo-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { IngeneralcargoComponent } from './ingeneralcargo.component';\r\nimport {IngeneralcargoEditComponent} from '@business/tas/ingeneralcargo/ingeneralcargo-edit/ingeneralcargo-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: IngeneralcargoComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: IngeneralcargoEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class IngeneralcargoRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAAQC,2BAA2B,QAAO,gFAAgF;;;AAC1H,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,uBAAuB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EAC3E;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,2BAA2B;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACrF;AAMD,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAF5BZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}