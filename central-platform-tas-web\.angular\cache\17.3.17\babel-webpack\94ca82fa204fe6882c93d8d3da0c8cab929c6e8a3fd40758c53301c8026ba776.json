{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { EmailTemplateComponent } from './emailtemplate.component';\nimport { EmailTemplateRoutingModule } from './emailtemplate-routing.module';\nimport { EmailTemplateEditComponent } from '@business/tas/emailtemplate/emailtemplate-edit/emailtemplate-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [EmailTemplateComponent, EmailTemplateEditComponent];\nexport class EmailTemplateModule {\n  static {\n    this.ɵfac = function EmailTemplateModule_Factory(t) {\n      return new (t || EmailTemplateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EmailTemplateModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, EmailTemplateRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EmailTemplateModule, {\n    declarations: [EmailTemplateComponent, EmailTemplateEditComponent],\n    imports: [SharedModule, EmailTemplateRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "EmailTemplateComponent", "EmailTemplateRoutingModule", "EmailTemplateEditComponent", "COMPONENTS", "EmailTemplateModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\emailtemplate\\emailtemplate.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { EmailTemplateComponent } from './emailtemplate.component';\r\nimport { EmailTemplateRoutingModule } from './emailtemplate-routing.module';\r\nimport {EmailTemplateEditComponent} from '@business/tas/emailtemplate/emailtemplate-edit/emailtemplate-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  EmailTemplateComponent,\r\n  EmailTemplateEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, EmailTemplateRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class EmailTemplateModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAAQC,0BAA0B,QAAO,6EAA6E;;AAEtH,MAAMC,UAAU,GAAG,CACjBH,sBAAsB,EACtBE,0BAA0B,CAC3B;AAMD,OAAM,MAAOE,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBN,YAAY,EAAEG,0BAA0B,EAAEF,YAAY;IAAA;EAAA;;;2EAGrDK,mBAAmB;IAAAC,YAAA,GAR9BL,sBAAsB,EACtBE,0BAA0B;IAAAI,OAAA,GAIhBR,YAAY,EAAEG,0BAA0B,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}