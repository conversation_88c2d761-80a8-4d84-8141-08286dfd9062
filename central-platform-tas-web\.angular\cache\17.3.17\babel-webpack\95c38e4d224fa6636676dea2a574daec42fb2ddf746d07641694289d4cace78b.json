{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { PdaComponent } from './pda.component';\nimport { PdaEditComponent } from '@business/tas/pda/pda-edit/pda-edit.component';\nimport { PdaRoutingModule } from './pda-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [PdaComponent, PdaEditComponent];\nexport class PdaModule {\n  static {\n    this.ɵfac = function PdaModule_Factory(t) {\n      return new (t || PdaModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PdaModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, PdaRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PdaModule, {\n    declarations: [PdaComponent, PdaEditComponent],\n    imports: [SharedModule, PdaRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "PdaComponent", "PdaEditComponent", "PdaRoutingModule", "COMPONENTS", "PdaModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\pda\\pda.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { PdaComponent } from './pda.component';\r\nimport { PdaEditComponent } from '@business/tas/pda/pda-edit/pda-edit.component';\r\nimport { PdaRoutingModule } from './pda-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  PdaComponent,\r\n  PdaEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, PdaRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class PdaModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,gBAAgB,QAAQ,sBAAsB;;AAEvD,MAAMC,UAAU,GAAG,CACjBH,YAAY,EACZC,gBAAgB,CACjB;AAMD,OAAM,MAAOG,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA;IAAS;EAAA;;;gBAHVN,YAAY,EAAEI,gBAAgB,EAAEH,YAAY;IAAA;EAAA;;;2EAG3CK,SAAS;IAAAC,YAAA,GARpBL,YAAY,EACZC,gBAAgB;IAAAK,OAAA,GAINR,YAAY,EAAEI,gBAAgB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}