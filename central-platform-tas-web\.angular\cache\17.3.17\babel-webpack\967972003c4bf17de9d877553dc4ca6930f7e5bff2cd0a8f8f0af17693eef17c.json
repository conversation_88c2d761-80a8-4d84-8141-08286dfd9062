{"ast": null, "code": "'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\nmodule.exports = {\n  2: 'need dictionary',\n  /* Z_NEED_DICT       2  */\n  1: 'stream end',\n  /* Z_STREAM_END      1  */\n  0: '',\n  /* Z_OK              0  */\n  '-1': 'file error',\n  /* Z_ERRNO         (-1) */\n  '-2': 'stream error',\n  /* Z_STREAM_ERROR  (-2) */\n  '-3': 'data error',\n  /* Z_DATA_ERROR    (-3) */\n  '-4': 'insufficient memory',\n  /* Z_MEM_ERROR     (-4) */\n  '-5': 'buffer error',\n  /* Z_BUF_ERROR     (-5) */\n  '-6': 'incompatible version' /* Z_VERSION_ERROR (-6) */\n};", "map": {"version": 3, "names": ["module", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/zlib/messages.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nmodule.exports = {\n  2:      'need dictionary',     /* Z_NEED_DICT       2  */\n  1:      'stream end',          /* Z_STREAM_END      1  */\n  0:      '',                    /* Z_OK              0  */\n  '-1':   'file error',          /* Z_ERRNO         (-1) */\n  '-2':   'stream error',        /* Z_STREAM_ERROR  (-2) */\n  '-3':   'data error',          /* Z_DATA_ERROR    (-3) */\n  '-4':   'insufficient memory', /* Z_MEM_ERROR     (-4) */\n  '-5':   'buffer error',        /* Z_BUF_ERROR     (-5) */\n  '-6':   'incompatible version' /* Z_VERSION_ERROR (-6) */\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEAA,MAAM,CAACC,OAAO,GAAG;EACf,CAAC,EAAO,iBAAiB;EAAM;EAC/B,CAAC,EAAO,YAAY;EAAW;EAC/B,CAAC,EAAO,EAAE;EAAqB;EAC/B,IAAI,EAAI,YAAY;EAAW;EAC/B,IAAI,EAAI,cAAc;EAAS;EAC/B,IAAI,EAAI,YAAY;EAAW;EAC/B,IAAI,EAAI,qBAAqB;EAAE;EAC/B,IAAI,EAAI,cAAc;EAAS;EAC/B,IAAI,EAAI,sBAAsB,CAAC;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}