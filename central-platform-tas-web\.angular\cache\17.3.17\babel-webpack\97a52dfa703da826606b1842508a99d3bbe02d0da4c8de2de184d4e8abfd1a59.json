{"ast": null, "code": "/*!\nCopyright (c) 2011, Yahoo! Inc. All rights reserved.\nCode licensed under the BSD License:\nhttp://developer.yahoo.com/yui/license.html\nversion: 2.9.0\n*/\nexport var YAHOO = {};\nYAHOO.lang = {\n  /**\n   * Utility to set up the prototype, constructor and superclass properties to\n   * support an inheritance strategy that can chain constructors and methods.\n   * Static members will not be inherited.\n   *\n   * @method extend\n   * @static\n   * @param {Function} subc   the object to modify\n   * @param {Function} superc the object to inherit\n   * @param {Object} overrides  additional properties/methods to add to the\n   *                              subclass prototype.  These will override the\n   *                              matching items obtained from the superclass\n   *                              if present.\n   */\n  extend: function (subc, superc, overrides) {\n    if (!superc || !subc) {\n      throw new Error(\"YAHOO.lang.extend failed, please check that \" + \"all dependencies are included.\");\n    }\n    var F = function () {};\n    F.prototype = superc.prototype;\n    subc.prototype = new F();\n    subc.prototype.constructor = subc;\n    subc.superclass = superc.prototype;\n    if (superc.prototype.constructor == Object.prototype.constructor) {\n      superc.prototype.constructor = superc;\n    }\n    if (overrides) {\n      var i;\n      for (i in overrides) {\n        subc.prototype[i] = overrides[i];\n      }\n      /*\n       * IE will not enumerate native functions in a derived object even if the\n       * function was overridden.  This is a workaround for specific functions\n       * we care about on the Object prototype.\n       * @property _IEEnumFix\n       * @param {Function} r  the object to receive the augmentation\n       * @param {Function} s  the object that supplies the properties to augment\n       * @static\n       * @private\n       */\n      var _IEEnumFix = function () {},\n        ADD = [\"toString\", \"valueOf\"];\n      try {\n        if (/MSIE/.test(navigator.userAgent)) {\n          _IEEnumFix = function (r, s) {\n            for (i = 0; i < ADD.length; i = i + 1) {\n              var fname = ADD[i],\n                f = s[fname];\n              if (typeof f === 'function' && f != Object.prototype[fname]) {\n                r[fname] = f;\n              }\n            }\n          };\n        }\n      } catch (ex) {}\n      ;\n      _IEEnumFix(subc.prototype, overrides);\n    }\n  }\n};", "map": {"version": 3, "names": ["YAHOO", "lang", "extend", "subc", "superc", "overrides", "Error", "F", "prototype", "constructor", "superclass", "Object", "i", "_IEEnumFix", "ADD", "test", "navigator", "userAgent", "r", "s", "length", "fname", "f", "ex"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/jsrsasign/yahoo.js"], "sourcesContent": ["/*!\nCopyright (c) 2011, Yahoo! Inc. All rights reserved.\nCode licensed under the BSD License:\nhttp://developer.yahoo.com/yui/license.html\nversion: 2.9.0\n*/\nexport var YAHOO = {};\nYAHOO.lang = {\n    /**\n     * Utility to set up the prototype, constructor and superclass properties to\n     * support an inheritance strategy that can chain constructors and methods.\n     * Static members will not be inherited.\n     *\n     * @method extend\n     * @static\n     * @param {Function} subc   the object to modify\n     * @param {Function} superc the object to inherit\n     * @param {Object} overrides  additional properties/methods to add to the\n     *                              subclass prototype.  These will override the\n     *                              matching items obtained from the superclass\n     *                              if present.\n     */\n    extend: function (subc, superc, overrides) {\n        if (!superc || !subc) {\n            throw new Error(\"YAHOO.lang.extend failed, please check that \" +\n                \"all dependencies are included.\");\n        }\n        var F = function () { };\n        F.prototype = superc.prototype;\n        subc.prototype = new F();\n        subc.prototype.constructor = subc;\n        subc.superclass = superc.prototype;\n        if (superc.prototype.constructor == Object.prototype.constructor) {\n            superc.prototype.constructor = superc;\n        }\n        if (overrides) {\n            var i;\n            for (i in overrides) {\n                subc.prototype[i] = overrides[i];\n            }\n            /*\n             * IE will not enumerate native functions in a derived object even if the\n             * function was overridden.  This is a workaround for specific functions\n             * we care about on the Object prototype.\n             * @property _IEEnumFix\n             * @param {Function} r  the object to receive the augmentation\n             * @param {Function} s  the object that supplies the properties to augment\n             * @static\n             * @private\n             */\n            var _IEEnumFix = function () { }, ADD = [\"toString\", \"valueOf\"];\n            try {\n                if (/MSIE/.test(navigator.userAgent)) {\n                    _IEEnumFix = function (r, s) {\n                        for (i = 0; i < ADD.length; i = i + 1) {\n                            var fname = ADD[i], f = s[fname];\n                            if (typeof f === 'function' && f != Object.prototype[fname]) {\n                                r[fname] = f;\n                            }\n                        }\n                    };\n                }\n            }\n            catch (ex) { }\n            ;\n            _IEEnumFix(subc.prototype, overrides);\n        }\n    }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,KAAK,GAAG,CAAC,CAAC;AACrBA,KAAK,CAACC,IAAI,GAAG;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAM,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAE;IACvC,IAAI,CAACD,MAAM,IAAI,CAACD,IAAI,EAAE;MAClB,MAAM,IAAIG,KAAK,CAAC,8CAA8C,GAC1D,gCAAgC,CAAC;IACzC;IACA,IAAIC,CAAC,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;IACvBA,CAAC,CAACC,SAAS,GAAGJ,MAAM,CAACI,SAAS;IAC9BL,IAAI,CAACK,SAAS,GAAG,IAAID,CAAC,CAAC,CAAC;IACxBJ,IAAI,CAACK,SAAS,CAACC,WAAW,GAAGN,IAAI;IACjCA,IAAI,CAACO,UAAU,GAAGN,MAAM,CAACI,SAAS;IAClC,IAAIJ,MAAM,CAACI,SAAS,CAACC,WAAW,IAAIE,MAAM,CAACH,SAAS,CAACC,WAAW,EAAE;MAC9DL,MAAM,CAACI,SAAS,CAACC,WAAW,GAAGL,MAAM;IACzC;IACA,IAAIC,SAAS,EAAE;MACX,IAAIO,CAAC;MACL,KAAKA,CAAC,IAAIP,SAAS,EAAE;QACjBF,IAAI,CAACK,SAAS,CAACI,CAAC,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC;MACpC;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;QAAEC,GAAG,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MAC/D,IAAI;QACA,IAAI,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;UAClCJ,UAAU,GAAG,SAAAA,CAAUK,CAAC,EAAEC,CAAC,EAAE;YACzB,KAAKP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,GAAG,CAACM,MAAM,EAAER,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;cACnC,IAAIS,KAAK,GAAGP,GAAG,CAACF,CAAC,CAAC;gBAAEU,CAAC,GAAGH,CAAC,CAACE,KAAK,CAAC;cAChC,IAAI,OAAOC,CAAC,KAAK,UAAU,IAAIA,CAAC,IAAIX,MAAM,CAACH,SAAS,CAACa,KAAK,CAAC,EAAE;gBACzDH,CAAC,CAACG,KAAK,CAAC,GAAGC,CAAC;cAChB;YACJ;UACJ,CAAC;QACL;MACJ,CAAC,CACD,OAAOC,EAAE,EAAE,CAAE;MACb;MACAV,UAAU,CAACV,IAAI,CAACK,SAAS,EAAEH,SAAS,CAAC;IACzC;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}