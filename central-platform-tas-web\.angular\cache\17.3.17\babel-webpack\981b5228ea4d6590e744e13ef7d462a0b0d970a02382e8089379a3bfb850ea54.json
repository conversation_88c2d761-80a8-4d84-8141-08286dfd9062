{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { NightComponent } from './night.component';\nimport { NightRoutingModule } from './night-routing.module';\nimport { NightEditComponent } from '@business/tas/night/night-edit/night-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [NightComponent, NightEditComponent];\nexport class NightModule {\n  static {\n    this.ɵfac = function NightModule_Factory(t) {\n      return new (t || NightModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NightModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, NightRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NightModule, {\n    declarations: [NightComponent, NightEditComponent],\n    imports: [SharedModule, NightRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "NightComponent", "NightRoutingModule", "NightEditComponent", "COMPONENTS", "NightModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\night\\night.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { NightComponent } from './night.component';\r\nimport { NightRoutingModule } from './night-routing.module';\r\nimport {NightEditComponent} from '@business/tas/night/night-edit/night-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  NightComponent,\r\n  NightEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, NightRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class NightModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAAQC,kBAAkB,QAAO,qDAAqD;;AAEtF,MAAMC,UAAU,GAAG,CACjBH,cAAc,EACdE,kBAAkB,CACnB;AAMD,OAAM,MAAOE,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZN,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;IAAA;EAAA;;;2EAG7CK,WAAW;IAAAC,YAAA,GARtBL,cAAc,EACdE,kBAAkB;IAAAI,OAAA,GAIRR,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}