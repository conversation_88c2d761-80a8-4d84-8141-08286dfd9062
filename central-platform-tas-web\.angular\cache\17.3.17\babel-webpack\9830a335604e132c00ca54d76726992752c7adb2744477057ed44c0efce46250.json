{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CwfBaseCrud, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\nimport { FormControl, Validators } from '@angular/forms';\nimport { TAS_T_SA_BUSINESS } from '@store/BCD/TAS_T_SA_BUSINESS';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"ng-zorro-antd/message\";\nimport * as i4 from \"@service/cwfRestful.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"angular-svg-icon\";\nimport * as i8 from \"ng-zorro-antd/grid\";\nimport * as i9 from \"ng-zorro-antd/form\";\nimport * as i10 from \"ng-zorro-antd/button\";\nimport * as i11 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i12 from \"ng-zorro-antd/core/wave\";\nimport * as i13 from \"ng-zorro-antd/input\";\nimport * as i14 from \"ng-zorro-antd/select\";\nimport * as i15 from \"ng-zorro-antd/card\";\nimport * as i16 from \"ng-zorro-antd/popconfirm\";\nimport * as i17 from \"ng-zorro-antd/table\";\nimport * as i18 from \"ng-zorro-antd/icon\";\nimport * as i19 from \"../../../../pipe/decimal.pipe\";\nimport * as i20 from \"@ngx-translate/core\";\nconst _c0 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c1 = () => [8, 10];\nconst _c2 = () => ({\n  x: \"1500px\"\n});\nfunction SuperviseEditComponent_nz_col_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SuperviseEditComponent_nz_col_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveData());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function SuperviseEditComponent_nz_col_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzLoading\", ctx_r2.loading)(\"nzType\", \"primary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"FP.SAVE\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 6, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction SuperviseEditComponent_nz_col_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 26)(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function SuperviseEditComponent_nz_col_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction SuperviseEditComponent_nz_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 29);\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r5.label)(\"nzValue\", option_r5.value);\n  }\n}\nfunction SuperviseEditComponent_nz_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 29);\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r6.label)(\"nzValue\", option_r6.value);\n  }\n}\nfunction SuperviseEditComponent_tr_75_nz_select_4_nz_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 29);\n  }\n  if (rf & 2) {\n    const option_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r12.label)(\"nzValue\", option_r12.value);\n  }\n}\nfunction SuperviseEditComponent_tr_75_nz_select_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_nz_select_4_Template_nz_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.partnerCd, $event) || (info_r9.partnerCd = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SuperviseEditComponent_tr_75_nz_select_4_Template_nz_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      const info_r9 = ctx_r9.$implicit;\n      const i_r11 = ctx_r9.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPartnerChange($event, info_r9, i_r11));\n    });\n    i0.ɵɵtemplate(1, SuperviseEditComponent_tr_75_nz_select_4_nz_option_1_Template, 1, 2, \"nz-option\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.partnerCd);\n    i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u516C\\u53F8\")(\"nzShowSearch\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.partnerData);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.partnerNm);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.dtShips, $event) || (info_r9.dtShips = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.dtShips);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.dtShips);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_13_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.dtTeu, $event) || (info_r9.dtTeu = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.dtTeu);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.dtTeu);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_16_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.dtWeight, $event) || (info_r9.dtWeight = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.dtWeight);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.dtWeight);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_19_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.dtQuantity, $event) || (info_r9.dtQuantity = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.dtQuantity);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.dtQuantity);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_22_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.dtIncome, $event) || (info_r9.dtIncome = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.dtIncome);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.dtIncome);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_25_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.ftShips, $event) || (info_r9.ftShips = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.ftShips);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.ftShips);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_28_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.ftTeu, $event) || (info_r9.ftTeu = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.ftTeu);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.ftTeu);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_31_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.ftWeight, $event) || (info_r9.ftWeight = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.ftWeight);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.ftWeight);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_34_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.ftQuantity, $event) || (info_r9.ftQuantity = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.ftQuantity);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.ftQuantity);\n  }\n}\nfunction SuperviseEditComponent_tr_75_input_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuperviseEditComponent_tr_75_input_37_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(info_r9.ftIncome, $event) || (info_r9.ftIncome = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", info_r9.ftIncome);\n  }\n}\nfunction SuperviseEditComponent_tr_75_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r9.ftIncome);\n  }\n}\nfunction SuperviseEditComponent_tr_75_a_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function SuperviseEditComponent_tr_75_a_41_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateDtl(info_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuperviseEditComponent_tr_75_a_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function SuperviseEditComponent_tr_75_a_42_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const info_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveFront(info_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuperviseEditComponent_tr_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, SuperviseEditComponent_tr_75_nz_select_4_Template, 2, 4, \"nz-select\", 30)(5, SuperviseEditComponent_tr_75_span_5_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SuperviseEditComponent_tr_75_input_10_Template, 1, 1, \"input\", 31)(11, SuperviseEditComponent_tr_75_span_11_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SuperviseEditComponent_tr_75_input_13_Template, 1, 1, \"input\", 32)(14, SuperviseEditComponent_tr_75_span_14_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtemplate(16, SuperviseEditComponent_tr_75_input_16_Template, 1, 1, \"input\", 33)(17, SuperviseEditComponent_tr_75_span_17_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtemplate(19, SuperviseEditComponent_tr_75_input_19_Template, 1, 1, \"input\", 34)(20, SuperviseEditComponent_tr_75_span_20_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtemplate(22, SuperviseEditComponent_tr_75_input_22_Template, 1, 1, \"input\", 35)(23, SuperviseEditComponent_tr_75_span_23_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\");\n    i0.ɵɵtemplate(25, SuperviseEditComponent_tr_75_input_25_Template, 1, 1, \"input\", 36)(26, SuperviseEditComponent_tr_75_span_26_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\");\n    i0.ɵɵtemplate(28, SuperviseEditComponent_tr_75_input_28_Template, 1, 1, \"input\", 37)(29, SuperviseEditComponent_tr_75_span_29_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\");\n    i0.ɵɵtemplate(31, SuperviseEditComponent_tr_75_input_31_Template, 1, 1, \"input\", 38)(32, SuperviseEditComponent_tr_75_span_32_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"td\");\n    i0.ɵɵtemplate(34, SuperviseEditComponent_tr_75_input_34_Template, 1, 1, \"input\", 39)(35, SuperviseEditComponent_tr_75_span_35_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\");\n    i0.ɵɵtemplate(37, SuperviseEditComponent_tr_75_input_37_Template, 1, 1, \"input\", 40)(38, SuperviseEditComponent_tr_75_span_38_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"td\", 41)(40, \"span\");\n    i0.ɵɵtemplate(41, SuperviseEditComponent_tr_75_a_41_Template, 2, 0, \"a\", 42)(42, SuperviseEditComponent_tr_75_a_42_Template, 2, 0, \"a\", 42);\n    i0.ɵɵelementStart(43, \"a\", 43);\n    i0.ɵɵpipe(44, \"translate\");\n    i0.ɵɵlistener(\"nzOnConfirm\", function SuperviseEditComponent_tr_75_Template_a_nzOnConfirm_43_listener() {\n      const ctx_r24 = i0.ɵɵrestoreView(_r7);\n      const info_r9 = ctx_r24.$implicit;\n      const i_r11 = ctx_r24.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.delDtl(info_r9, i_r11));\n    });\n    i0.ɵɵelement(45, \"i\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const info_r9 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r11 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r9.partnerCd);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", info_r9.isEditing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !info_r9.isEditing);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !info_r9.editFlag);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", info_r9.editFlag);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzPopconfirmTitle\", i0.ɵɵpipeBind1(44, 27, \"MSG.WEB0020\"));\n  }\n}\nfunction SuperviseEditComponent_div_76_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\");\n    i0.ɵɵelement(2, \"div\", 61);\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63)(6, \"div\", 64)(7, \"span\", 65);\n    i0.ɵɵtext(8, \"\\u8239\\u8236\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 64)(11, \"span\", 65);\n    i0.ɵɵtext(12, \"\\u96C6\\u88C5\\u7BB1\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"decimal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 64)(16, \"span\", 65);\n    i0.ɵɵtext(17, \"\\u8D27\\u7269\\u91CD\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"decimal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 64)(21, \"span\", 65);\n    i0.ɵɵtext(22, \"\\u8D27\\u7269\\u4EF6\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"decimal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 64)(26, \"span\", 65);\n    i0.ɵɵtext(27, \"\\u6536\\u5165\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"decimal\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const info_r26 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(info_r26.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", info_r26.totalShips, \" \\u8258\\u6B21\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(14, 6, info_r26.totalTeu, 1), \" TEU\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 9, info_r26.totalWeight, 4), \" \\u4E07\\u5428\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(24, 12, info_r26.totalQuantity, 4), \" \\u4E07\\u4EF6\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(29, 15, info_r26.totalIncome, 4), \" \\u4E07\\u5143\");\n  }\n}\nfunction SuperviseEditComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SuperviseEditComponent_div_76_div_1_Template, 30, 18, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.sumInfo);\n  }\n}\nexport class SuperviseEditComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, message, cwfRestfulService) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.message = message;\n    this.cwfRestfulService = cwfRestfulService;\n    this.mainStore = new TAS_T_SA_BUSINESS();\n    this.editStores = [this.mainStore];\n    this.reportMonthData = [];\n    this.initData = [];\n    this.partnerData = [];\n    this.id = null;\n    this.showDiv = false;\n    this.isEditing = false;\n    this.dtlListOfData = [];\n    this.sumInfo = [];\n    // 定义禁用编辑表单的对象，用于控制表单的禁用状态\n    // ALL属性表示是否全局禁用编辑表单的功能\n    this.disabledEditForm = {\n      ALL: false\n    };\n  }\n  /**\n  * 初始化查询条件\n  */\n  initEdit() {\n    return {\n      id: new FormControl('', Validators.nullValidator),\n      // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\n      reportMoonId: new FormControl('', Validators.required),\n      reportYm: new FormControl('', Validators.required),\n      reportDt: new FormControl('', Validators.required),\n      reportOrgCd: new FormControl('', Validators.required),\n      reportOrgNm: new FormControl('', Validators.required),\n      businessTypeCd: new FormControl('SL', Validators.required),\n      businessTypeNm: new FormControl('监装监卸', Validators.required),\n      remark: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\n      createdUser: new FormControl('', Validators.nullValidator),\n      // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\n      createdTime: new FormControl('', Validators.nullValidator),\n      // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedUser: new FormControl('', Validators.nullValidator),\n      // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedTime: new FormControl('', Validators.nullValidator),\n      // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\n      isDelete: new FormControl('', Validators.nullValidator),\n      // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\n      version: new FormControl('', Validators.nullValidator) // 版本号，初始值为空，验证规则为nullValidator（允许为空）\n    };\n  }\n  /**\n   * 页面加载完成后出发\n   */\n  onShow() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.id = null;\n      if (_this.openParam['state'] === PageModeEnum.Custom) {\n        _this.disabledEditForm['ALL'] = true;\n        _this.cwfRestfulService.get('/saBusiness/' + _this.openParam['id'], _this.gol.serviceName['tas'].en).then(rps => {\n          if (rps.ok) {\n            _this.editForm.patchValue(rps.data);\n          } else {\n            _this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\n            _this.openMainPage();\n          }\n        });\n      }\n      if (_this.openParam['state'] !== PageModeEnum.Add) {\n        _this.editForm.controls['reportMoonId'].disable();\n        _this.editForm.controls['reportOrgCd'].disable();\n        _this.queryDtlList(true);\n        _this.getPartnerData(null);\n        _this.showDiv = true;\n      }\n      _this.getOrgData();\n      _this.getTimeLimitData();\n    })();\n  }\n  /**\n   * desc:保存数据\n   * by:\n   */\n  saveData() {\n    const url = '/saBusiness';\n    for (const i in this.editForm.controls) {\n      this.editForm.controls[i].markAsDirty();\n      this.editForm.controls[i].updateValueAndValidity();\n    }\n    if (this.editForm.invalid) {\n      return;\n    }\n    if (new Date(this.editForm.controls['endDt']?.value) < new Date(this.editForm.controls['startDt']?.value)) {\n      this.message.warning('统计截止时间不能小于统计开始时间', {\n        nzDuration: 3000\n      });\n      return;\n    }\n    if (new Date(this.editForm.controls['pmDt']?.value) <= new Date(this.editForm.controls['endDt']?.value)) {\n      this.message.warning('禁止修改时间不能小于等于统计截止时间', {\n        nzDuration: 3000\n      });\n      return;\n    }\n    let reportYm = new Date(this.editForm.controls['reportYm'].value);\n    let month = reportYm.getMonth() + 1;\n    this.editForm.controls['reportYm'].setValue(reportYm.getFullYear() + \"-\" + (month < 10 ? \"0\" + month : month));\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\n    this.loading = true;\n    if (this.openParam['state'] === PageModeEnum.Add) {\n      this.editForm.removeControl('id');\n      this.cwfRestfulService.post(url + '/save', {\n        saBusiness: this.editForm.getRawValue(),\n        dtls: this.dtlListOfData.filter(item => item.partnerCd != null)\n      }, this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    } else {\n      this.cwfRestfulService.post(url + '/update', {\n        saBusiness: this.editForm.getRawValue(),\n        dtls: this.dtlListOfData.filter(item => item.partnerCd != null)\n      }, this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    }\n  }\n  onClose() {\n    if (this.checkSave()) {\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\n        switch (state) {\n          case DialogResultEnum.yes:\n            // 是\n            this.saveData();\n            return true;\n          case DialogResultEnum.no:\n            // 否\n            this.openMainPage({});\n            return true;\n          case DialogResultEnum.cancel:\n            // 取消\n            return false;\n        }\n      });\n    } else {\n      this.openMainPage({});\n      return true;\n    }\n  }\n  getEditFromDisabled(feildName) {\n    return this.disabledEditForm[feildName];\n  }\n  onInputChange(value) {\n    if (this.openParam['state'] === PageModeEnum.Add) {\n      this.editForm.controls['startDt'].setValue(new Date(value.getFullYear(), value.getMonth(), 1));\n      this.editForm.controls['endDt'].setValue(new Date(value.getFullYear(), value.getMonth() + 1, 0));\n      this.editForm.controls['pmDt'].setValue(new Date(value.getFullYear(), value.getMonth() + 2, 0));\n    }\n  }\n  getOrgData() {\n    const rdata = {\n      type: 'company'\n    };\n    this.cwfRestfulService.post('/org/getRelateChildren', rdata, this.gol.serviceName['main'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 initData 数组\n        this.initData = rps.data.map(item => ({\n          label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\n          value: item.orgCode,\n          orgName: item.orgName\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  getTimeLimitData() {\n    const rdata = {\n      size: 1000\n    };\n    this.cwfRestfulService.post('/saTimeLimit/getReportMonthInfo', rdata, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 initData 数组\n        this.reportMonthData = rps.data.map(item => ({\n          label: item.reportYm + ' 范围:' + item.startDt + '到' + item.endDt + ' 不可修改日期:' + item.pmDt,\n          value: item.id,\n          reportYm: item.reportYm\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  onReportMonthChange(value) {\n    if (value == null) {\n      // 清理数据\n      this.editForm.controls['reportYm'].setValue(\"\");\n      this.editForm.controls['reportDt'].setValue(\"\");\n    } else {\n      let model = this.reportMonthData.find(item => item.value === value);\n      this.editForm.controls['reportYm'].setValue(model.reportYm);\n      this.editForm.controls['reportDt'].setValue(new Date(model.reportYm));\n    }\n  }\n  onOrgChange(value) {\n    if (value == null) {\n      // 清理数据\n      this.editForm.controls['reportOrgNm'].setValue(\"\");\n    } else {\n      let model = this.initData.find(item => item.value === value);\n      this.editForm.controls['reportOrgNm'].setValue(model.orgName);\n    }\n  }\n  queryDtlList(reset) {\n    if (reset) {\n      this.mainStore.pageing.PAGE = 1;\n    }\n    const requestData = {\n      page: this.mainStore.pageing.PAGE,\n      size: this.mainStore.pageing.LIMIT,\n      sortBy: {\n        createdTime: 'DESC'\n      }\n    };\n    requestData['data'] = {\n      businessId: this.editForm.controls['id']?.value\n    };\n    if (requestData['data'].businessId == null) {\n      requestData['data'].businessId = this.id;\n    }\n    if (this.id == null) {\n      this.id = this.editForm.controls['id']?.value;\n    }\n    this.mainStore.clearData();\n    this.cwfRestfulService.post('/saBusinessDtl/list/page', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok === true) {\n        this.mainStore.loadDatas(rps.data.content);\n        this.dtlListOfData = this.mainStore.getDatas()?.reduce((itemNew, itemOld) => {\n          itemNew.push({\n            id: itemOld.id,\n            businessId: itemOld.businessId,\n            partnerCd: itemOld.partnerCd,\n            partnerNm: itemOld.partnerNm,\n            dtShips: itemOld.dtShips == null ? 0 : itemOld.dtShips,\n            dtTeu: itemOld.dtTeu == null ? 0 : itemOld.dtTeu,\n            dtWeight: itemOld.dtWeight == null ? 0 : itemOld.dtWeight,\n            dtQuantity: itemOld.dtQuantity == null ? 0 : itemOld.dtQuantity,\n            dtIncome: itemOld.dtIncome == null ? 0 : itemOld.dtIncome,\n            ftShips: itemOld.ftShips == null ? 0 : itemOld.ftShips,\n            ftTeu: itemOld.ftTeu == null ? 0 : itemOld.ftTeu,\n            ftWeight: itemOld.ftWeight == null ? 0 : itemOld.ftWeight,\n            ftQuantity: itemOld.ftQuantity == null ? 0 : itemOld.ftQuantity,\n            ftIncome: itemOld.ftIncome == null ? 0 : itemOld.ftIncome,\n            version: itemOld.version,\n            isEditing: false,\n            editFlag: false\n          });\n          return itemNew;\n        }, []);\n        this.mainStore.pageing.TOTAL = rps.data.totalElements; // 总条数\n        if (this.dtlListOfData.length > 0) {\n          this.showDiv = true;\n        } else {\n          this.showDiv = false;\n        }\n        this.sumDtl(this.dtlListOfData);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  sumDtl(dtlData) {\n    this.sumInfo = [];\n    if (dtlData.length === 0) {\n      dtlData = this.dtlListOfData;\n    }\n    // 进行数据汇总\n    let dt = dtlData.reduce((acc, cur) => {\n      acc.title = '内贸汇总信息';\n      acc.totalShips += cur.dtShips;\n      acc.totalTeu += cur.dtTeu;\n      acc.totalWeight += cur.dtWeight;\n      acc.totalQuantity += cur.dtQuantity;\n      acc.totalIncome += cur.dtIncome;\n      return acc;\n    }, {\n      title: '',\n      totalShips: 0,\n      totalTeu: 0,\n      totalWeight: 0,\n      totalQuantity: 0,\n      totalIncome: 0\n    });\n    let ft = dtlData.reduce((acc, cur) => {\n      acc.title = '外贸汇总信息';\n      acc.totalShips += cur.ftShips;\n      acc.totalTeu += cur.ftTeu;\n      acc.totalWeight += cur.ftWeight;\n      acc.totalQuantity += cur.ftQuantity;\n      acc.totalIncome += cur.ftIncome;\n      return acc;\n    }, {\n      title: '',\n      totalShips: 0,\n      totalTeu: 0,\n      totalWeight: 0,\n      totalQuantity: 0,\n      totalIncome: 0\n    });\n    let sum = dtlData.reduce((acc, cur) => {\n      acc.title = '全汇总信息';\n      acc.totalShips += cur.dtShips + cur.ftShips;\n      acc.totalTeu += cur.dtTeu + cur.ftTeu;\n      acc.totalWeight += cur.dtWeight + cur.ftWeight;\n      acc.totalQuantity += cur.dtQuantity + cur.ftQuantity;\n      acc.totalIncome += cur.dtIncome + cur.ftIncome;\n      return acc;\n    }, {\n      title: '',\n      totalShips: 0,\n      totalTeu: 0,\n      totalWeight: 0,\n      totalQuantity: 0,\n      totalIncome: 0\n    });\n    this.sumInfo = this.sumInfo.concat(dt).concat(ft).concat(sum);\n  }\n  newDtlData() {\n    // if(this.id == null || this.id === ''){\n    //   this.message.warning('请先保存业务主表', {\n    //           nzDuration: 3000\n    //   });\n    //   return;\n    // }\n    this.dtlListOfData.push({\n      id: null,\n      businessId: this.id,\n      partnerCd: null,\n      partnerNm: null,\n      dtShips: null,\n      dtImportTeu: null,\n      dtExportTeu: null,\n      dtIncome: null,\n      ftShips: null,\n      ftImportTeu: null,\n      ftExportTeu: null,\n      ftIncome: null,\n      version: null,\n      editFlag: true,\n      isEditing: true\n    });\n  }\n  delDtl(info, index) {\n    this.dtlListOfData.splice(index, 1);\n    // if(info.id == null) {\n    //   this.dtlListOfData.splice(index, 1);\n    //   return;\n    // }\n    // const requestData = [];\n    // requestData.push(info.id);\n    // this.cwfRestfulService.delete('/saBusinessDtl/batch', this.gol.serviceName['tas'].en, { body: requestData }).then((rps: responseInterface) => {\n    //   this.loading = false;\n    //   if (rps.ok) {\n    //     this.showState(ModalTypeEnum.success, '删除成功！');\n    //     this.queryDtlList();\n    //   } else {\n    //     this.showState(ModalTypeEnum.error, rps.msg);\n    //   }\n    // });\n  }\n  getPartnerData(selectedValue) {\n    let requestData = {\n      page: 1,\n      size: 100,\n      param: selectedValue,\n      partyType: ['I', 'B']\n    };\n    this.cwfRestfulService.post('/partner/getPartnerInfo', requestData, this.gol.serviceName['bc'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 partnerData 数组\n        this.partnerData = rps.data.map(item => ({\n          label: item.partnerNm,\n          value: item.partnerCd,\n          partnerNm: item.partnerNm,\n          partnerTypeCd: item.partnerTypeCd,\n          partnerTypeNm: item.partnerTypeNm,\n          groupLevel2OrgCd: item.groupLevel2OrgCd,\n          groupLevel2OrgNm: item.groupLevel2OrgNm,\n          kaTag: item.kaTag\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  onPartnerChange(value, info, i) {\n    const isDuplicate = this.dtlListOfData.some(item => item !== info && item.partnerCd === value);\n    if (isDuplicate) {\n      info.partnerCd = null;\n      this.message.warning('船公司不能重复选择', {\n        nzDuration: 3000\n      });\n      this.dtlListOfData.splice(i, 1);\n      this.newDtlData();\n      return;\n    }\n    let model = this.partnerData.find(item => item.value === info.partnerCd);\n    let dtlData = this.dtlListOfData.find(item => item.partnerCd === info.partnerCd);\n    dtlData['partnerNm'] = model.partnerNm;\n    dtlData['partnerTypeCd'] = model.partnerTypeCd;\n    dtlData['partnerTypeNm'] = model.partnerTypeNm;\n    dtlData['groupLevel2OrgCd'] = model.groupLevel2OrgCd;\n    dtlData['groupLevel2OrgNm'] = model.groupLevel2OrgNm;\n    dtlData['kaTag'] = model.kaTag;\n  }\n  saveDtl(blurValues) {\n    if (blurValues.partnerCd == null || blurValues.partnerCd.trim() == '') {\n      this.message.warning('船公司不能为空', {\n        nzDuration: 3000\n      });\n      return;\n    }\n    const url = '/saBusinessDtl';\n    this.loading = true;\n    if (blurValues.id == null) {\n      this.cwfRestfulService.post(url, blurValues, this.gol.serviceName['tas'].en).then(rps => {\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.queryDtlList(true);\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    } else {\n      this.cwfRestfulService.put(url, blurValues, this.gol.serviceName['tas'].en).then(rps => {\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.queryDtlList(true);\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    }\n    setTimeout(() => {\n      this.isEditing = false;\n      this.sumDtl([]);\n    }, 100);\n  }\n  updateDtl(info) {\n    info.isEditing = !info.isEditing;\n    info.editFlag = !info.editFlag;\n  }\n  saveFront(info) {\n    if (info.partnerCd == null || info.partnerCd.trim() == '') {\n      this.message.warning('船公司不能为空', {\n        nzDuration: 3000\n      });\n      return;\n    }\n    this.updateDtl(info);\n  }\n  static {\n    this.ɵfac = function SuperviseEditComponent_Factory(t) {\n      return new (t || SuperviseEditComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.NzMessageService), i0.ɵɵdirectiveInject(i4.CwfRestfulService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuperviseEditComponent,\n      selectors: [[\"supervise-edit\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 77,\n      vars: 41,\n      consts: [[\"table\", \"\"], [1, \"system-index-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"10\", 1, \"list-title\"], [3, \"src\"], [1, \"title\"], [\"nzSpan\", \"14\", \"class\", \"list-button\", 4, \"ngIf\"], [\"nz-form\", \"\", 2, \"margin-top\", \"20px\", 3, \"formGroup\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"12\", 2, \"margin-top\", \"5px\"], [\"nzRequired\", \"\", 2, \"width\", \"120px\"], [\"formControlName\", \"reportMoonId\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"nzPlaceHolder\", \"nzShowSearch\"], [3, \"nzLabel\", \"nzValue\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"reportOrgCd\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"nzPlaceHolder\", \"nzShowSearch\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [2, \"width\", \"120px\"], [\"nz-input\", \"\", \"formControlName\", \"remark\", 2, \"height\", \"100px\", \"resize\", \"none\", 3, \"placeholder\", \"readonly\"], [1, \"list-button\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"primary\", 3, \"click\"], [3, \"nzBordered\", \"nzFrontPagination\", \"nzShowPagination\", \"nzData\", \"nzScroll\"], [2, \"text-align\", \"center\", \"background-color\", \"rgb(172, 174, 237)\"], [\"nzWidth\", \"50px\", \"nzLeft\", \"\"], [\"nzWidth\", \"200px\", \"nzAlign\", \"center\"], [\"nzAlign\", \"center\"], [\"nzAlign\", \"center\", \"nzWidth\", \"75px\", \"nzRight\", \"\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"nzSpan\", \"14\", 1, \"list-button\"], [\"nz-button\", \"\", 3, \"click\", \"nzLoading\", \"nzType\"], [\"nz-button\", \"\", 3, \"click\"], [3, \"nzLabel\", \"nzValue\"], [\"style\", \"width: 180px;\", \"nzAllowClear\", \"\", 3, \"ngModel\", \"nzPlaceHolder\", \"nzShowSearch\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nzRight\", \"\"], [3, \"click\", 4, \"ngIf\"], [\"nz-popconfirm\", \"\", 3, \"nzOnConfirm\", \"nzPopconfirmTitle\"], [\"nz-icon\", \"\", \"nzIconfont\", \"icon-delete-tb\", 2, \"font-size\", \"16px\"], [\"nzAllowClear\", \"\", 2, \"width\", \"180px\", 3, \"ngModelChange\", \"ngModel\", \"nzPlaceHolder\", \"nzShowSearch\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5185\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nz-input\", \"\", \"placeholder\", \"\\u5916\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [\"nz-icon\", \"\", \"nzIconfont\", \"icon-edit-tb\", 2, \"font-size\", \"16px\", \"margin-right\", \"20px\"], [\"nz-icon\", \"\", \"nzIconfont\", \"icon-save-tb\", 2, \"font-size\", \"16px\", \"margin-right\", \"20px\"], [\"style\", \"border: 1px solid rgb(73, 73, 244);width: 100%; height: 60px;margin-top: 10px;\", 4, \"ngFor\", \"ngForOf\"], [2, \"border\", \"1px solid rgb(73, 73, 244)\", \"width\", \"100%\", \"height\", \"60px\", \"margin-top\", \"10px\"], [2, \"margin-left\", \"3px\", \"width\", \"10px\", \"height\", \"10px\", \"background-color\", \"rgb(237, 142, 70)\", \"display\", \"inline-block\"], [2, \"color\", \"rgb(237, 142, 70)\", \"margin-left\", \"3px\"], [2, \"margin-left\", \"5px\", \"font-size\", \"12px\"], [2, \"display\", \"inline-block\", \"width\", \"20%\"], [2, \"margin-right\", \"50px\"]],\n      template: function SuperviseEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 1)(1, \"nz-row\")(2, \"nz-col\", 2);\n          i0.ɵɵelement(3, \"svg-icon\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, SuperviseEditComponent_nz_col_7_Template, 7, 8, \"nz-col\", 5)(8, SuperviseEditComponent_nz_col_8_Template, 4, 3, \"nz-col\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"form\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"nz-form-item\")(13, \"nz-form-label\", 9);\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nz-form-control\")(17, \"nz-select\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function SuperviseEditComponent_Template_nz_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReportMonthChange($event));\n          });\n          i0.ɵɵtemplate(18, SuperviseEditComponent_nz_option_18_Template, 1, 2, \"nz-option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"nz-form-item\")(21, \"nz-form-label\", 9);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nz-form-control\")(25, \"nz-select\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function SuperviseEditComponent_Template_nz_select_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOrgChange($event));\n          });\n          i0.ɵɵtemplate(26, SuperviseEditComponent_nz_option_26_Template, 1, 2, \"nz-option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 13)(28, \"nz-form-item\")(29, \"nz-form-label\", 14);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"nz-form-control\");\n          i0.ɵɵelement(33, \"textarea\", 15);\n          i0.ɵɵpipe(34, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(35, \"h4\")(36, \"div\", 16)(37, \"div\")(38, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SuperviseEditComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.newDtlData());\n          });\n          i0.ɵɵelementStart(39, \"span\");\n          i0.ɵɵtext(40, \"\\u65B0\\u589E\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(41, \"nz-table\", 18, 0)(43, \"thead\", 19)(44, \"tr\")(45, \"th\", 20);\n          i0.ɵɵtext(46);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 21);\n          i0.ɵɵtext(49, \"\\u8239\\u516C\\u53F8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 22);\n          i0.ɵɵtext(51, \"\\u8239\\u516C\\u53F8\\u4EE3\\u7801\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"th\", 22);\n          i0.ɵɵtext(53, \"\\u5185\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"th\", 22);\n          i0.ɵɵtext(55, \"\\u5185\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"th\", 22);\n          i0.ɵɵtext(57, \"\\u5185\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\", 22);\n          i0.ɵɵtext(59, \"\\u5185\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\", 22);\n          i0.ɵɵtext(61, \"\\u5185\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\", 22);\n          i0.ɵɵtext(63, \"\\u5916\\u8D38\\u8239\\u8236(\\u8258\\u6B21)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"th\", 22);\n          i0.ɵɵtext(65, \"\\u5916\\u8D38\\u96C6\\u88C5\\u7BB1\\u6570(TEU)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"th\", 22);\n          i0.ɵɵtext(67, \"\\u5916\\u8D38\\u8D27\\u7269\\u91CD\\u91CF(\\u4E07\\u5428)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\", 22);\n          i0.ɵɵtext(69, \"\\u5916\\u8D38\\u8D27\\u7269\\u4EF6\\u6570(\\u4E07\\u4EF6)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"th\", 22);\n          i0.ɵɵtext(71, \"\\u5916\\u8D38\\u6536\\u5165(\\u4E07\\u5143)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"th\", 23);\n          i0.ɵɵtext(73, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"tbody\");\n          i0.ɵɵtemplate(75, SuperviseEditComponent_tr_75_Template, 46, 29, \"tr\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(76, SuperviseEditComponent_div_76_Template, 2, 1, \"div\", 25);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(38, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", \"assets/img/icons/filter.svg\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 26, \"TAS.SUPERVISE_EDIT\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(39, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 28, \"TAS.REPORT_YM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.reportMonthData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 30, \"TAS.REPORT_ORG\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.initData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 32, \"TAS.REMARK\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(34, 34, \"TAS.REMARK\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"nzBordered\", true)(\"nzFrontPagination\", false)(\"nzShowPagination\", false)(\"nzData\", ctx.mainStore.getDatas())(\"nzScroll\", i0.ɵɵpureFunction0(40, _c2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(47, 36, \"DB.SEQ\"));\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.dtlListOfData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDiv);\n        }\n      },\n      dependencies: [i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i6.NgForOf, i6.NgIf, i5.FormGroupDirective, i5.FormControlName, i7.SvgIconComponent, i8.NzColDirective, i8.NzRowDirective, i9.NzFormDirective, i9.NzFormItemComponent, i9.NzFormLabelComponent, i9.NzFormControlComponent, i10.NzButtonComponent, i11.ɵNzTransitionPatchDirective, i12.NzWaveDirective, i13.NzInputDirective, i14.NzOptionComponent, i14.NzSelectComponent, i15.NzCardComponent, i16.NzPopconfirmDirective, i17.NzTableComponent, i17.NzTableCellDirective, i17.NzThMeasureDirective, i17.NzTheadComponent, i17.NzTbodyComponent, i17.NzTrDirective, i17.NzCellFixedDirective, i17.NzCellAlignDirective, i18.NzIconDirective, i19.DecimalPipe, i20.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CwfBaseCrud", "DialogResultEnum", "ModalTypeEnum", "PageModeEnum", "FormControl", "Validators", "TAS_T_SA_BUSINESS", "i0", "ɵɵelementStart", "ɵɵlistener", "SuperviseEditComponent_nz_col_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "saveData", "ɵɵtext", "ɵɵelementEnd", "SuperviseEditComponent_nz_col_7_Template_button_click_4_listener", "onClose", "ɵɵadvance", "ɵɵproperty", "loading", "ɵɵtextInterpolate", "ɵɵpipeBind1", "SuperviseEditComponent_nz_col_8_Template_button_click_1_listener", "_r4", "ɵɵelement", "option_r5", "label", "value", "option_r6", "option_r12", "ɵɵtwoWayListener", "SuperviseEditComponent_tr_75_nz_select_4_Template_nz_select_ngModelChange_0_listener", "$event", "_r8", "info_r9", "$implicit", "ɵɵtwoWayBindingSet", "partnerCd", "ctx_r9", "i_r11", "index", "onPartnerChange", "ɵɵtemplate", "SuperviseEditComponent_tr_75_nz_select_4_nz_option_1_Template", "ɵɵtwoWayProperty", "partnerData", "partnerNm", "SuperviseEditComponent_tr_75_input_10_Template_input_ngModelChange_0_listener", "_r13", "dtShips", "SuperviseEditComponent_tr_75_input_13_Template_input_ngModelChange_0_listener", "_r14", "dtTeu", "SuperviseEditComponent_tr_75_input_16_Template_input_ngModelChange_0_listener", "_r15", "dtWeight", "SuperviseEditComponent_tr_75_input_19_Template_input_ngModelChange_0_listener", "_r16", "dtQuantity", "SuperviseEditComponent_tr_75_input_22_Template_input_ngModelChange_0_listener", "_r17", "dt<PERSON>ncome", "SuperviseEditComponent_tr_75_input_25_Template_input_ngModelChange_0_listener", "_r18", "ftShips", "SuperviseEditComponent_tr_75_input_28_Template_input_ngModelChange_0_listener", "_r19", "ftTeu", "SuperviseEditComponent_tr_75_input_31_Template_input_ngModelChange_0_listener", "_r20", "ftWeight", "SuperviseEditComponent_tr_75_input_34_Template_input_ngModelChange_0_listener", "_r21", "ftQuantity", "SuperviseEditComponent_tr_75_input_37_Template_input_ngModelChange_0_listener", "_r22", "ftIncome", "SuperviseEditComponent_tr_75_a_41_Template_a_click_0_listener", "_r23", "updateDtl", "SuperviseEditComponent_tr_75_a_42_Template_a_click_0_listener", "_r24", "saveFront", "SuperviseEditComponent_tr_75_nz_select_4_Template", "SuperviseEditComponent_tr_75_span_5_Template", "SuperviseEditComponent_tr_75_input_10_Template", "SuperviseEditComponent_tr_75_span_11_Template", "SuperviseEditComponent_tr_75_input_13_Template", "SuperviseEditComponent_tr_75_span_14_Template", "SuperviseEditComponent_tr_75_input_16_Template", "SuperviseEditComponent_tr_75_span_17_Template", "SuperviseEditComponent_tr_75_input_19_Template", "SuperviseEditComponent_tr_75_span_20_Template", "SuperviseEditComponent_tr_75_input_22_Template", "SuperviseEditComponent_tr_75_span_23_Template", "SuperviseEditComponent_tr_75_input_25_Template", "SuperviseEditComponent_tr_75_span_26_Template", "SuperviseEditComponent_tr_75_input_28_Template", "SuperviseEditComponent_tr_75_span_29_Template", "SuperviseEditComponent_tr_75_input_31_Template", "SuperviseEditComponent_tr_75_span_32_Template", "SuperviseEditComponent_tr_75_input_34_Template", "SuperviseEditComponent_tr_75_span_35_Template", "SuperviseEditComponent_tr_75_input_37_Template", "SuperviseEditComponent_tr_75_span_38_Template", "SuperviseEditComponent_tr_75_a_41_Template", "SuperviseEditComponent_tr_75_a_42_Template", "SuperviseEditComponent_tr_75_Template_a_nzOnConfirm_43_listener", "ctx_r24", "_r7", "delDtl", "isEditing", "editFlag", "info_r26", "title", "ɵɵtextInterpolate1", "totalShips", "ɵɵpipeBind2", "totalTeu", "totalWeight", "totalQuantity", "totalIncome", "SuperviseEditComponent_div_76_div_1_Template", "sumInfo", "SuperviseEditComponent", "constructor", "cwfBusContextService", "gol", "message", "cwfRestfulService", "mainStore", "editStores", "reportMonthData", "initData", "id", "showDiv", "dtlListOfData", "disabledEditForm", "ALL", "initEdit", "nullValidator", "reportMoonId", "required", "reportYm", "reportDt", "reportOrgCd", "reportOrgNm", "businessTypeCd", "businessTypeNm", "remark", "max<PERSON><PERSON><PERSON>", "created<PERSON>ser", "createdTime", "modifiedUser", "modifiedTime", "isDelete", "version", "onShow", "_this", "_asyncToGenerator", "openParam", "Custom", "get", "serviceName", "en", "then", "rps", "ok", "editForm", "patchValue", "data", "showState", "error", "openMainPage", "Add", "controls", "disable", "queryDtlList", "getPartnerData", "getOrgData", "getTimeLimitData", "url", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "Date", "warning", "nzDuration", "month", "getMonth", "setValue", "getFullYear", "cwfBusContext", "getNotify", "showLoading", "removeControl", "post", "saBusiness", "getRawValue", "dtls", "filter", "item", "removeShow", "success", "getMainController", "msg", "checkSave", "showConfirm", "getMsgi18nString", "state", "yes", "no", "cancel", "getEditFromDisabled", "feild<PERSON><PERSON>", "onInputChange", "rdata", "type", "map", "orgCode", "orgName", "companyCode", "companyName", "size", "startDt", "endDt", "pmDt", "onReportMonthChange", "model", "find", "onOrgChange", "reset", "pageing", "PAGE", "requestData", "page", "LIMIT", "sortBy", "businessId", "clearData", "loadDatas", "content", "getDatas", "reduce", "itemNew", "itemOld", "push", "TOTAL", "totalElements", "length", "sumDtl", "dtlData", "dt", "acc", "cur", "ft", "sum", "concat", "newDtlData", "dtImportTeu", "dtExportTeu", "ftImportTeu", "ftExportTeu", "info", "splice", "selected<PERSON><PERSON><PERSON>", "param", "partyType", "partnerTypeCd", "partnerTypeNm", "groupLevel2OrgCd", "groupLevel2OrgNm", "kaTag", "isDuplicate", "some", "saveDtl", "blurValues", "trim", "put", "setTimeout", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "NzMessageService", "i4", "CwfRestfulService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SuperviseEditComponent_Template", "rf", "ctx", "SuperviseEditComponent_nz_col_7_Template", "SuperviseEditComponent_nz_col_8_Template", "SuperviseEditComponent_Template_nz_select_ngModelChange_17_listener", "_r1", "SuperviseEditComponent_nz_option_18_Template", "SuperviseEditComponent_Template_nz_select_ngModelChange_25_listener", "SuperviseEditComponent_nz_option_26_Template", "SuperviseEditComponent_Template_button_click_38_listener", "SuperviseEditComponent_tr_75_Template", "SuperviseEditComponent_div_76_Template", "ɵɵpureFunction0", "_c0", "_c1", "ɵɵpropertyInterpolate", "_c2"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise-edit\\supervise-edit.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise-edit\\supervise-edit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CwfBaseCrud, CwfBusContextService, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { TAS_T_SA_BUSINESS } from '@store/BCD/TAS_T_SA_BUSINESS';\r\nimport {NzMessageService} from \"ng-zorro-antd/message\";\r\nimport { format } from 'date-fns';\r\n\r\n@Component({\r\n  selector: 'supervise-edit',\r\n  templateUrl: './supervise-edit.component.html'\r\n})\r\n\r\nexport class SuperviseEditComponent extends CwfBaseCrud {\r\n  editForm: FormGroup;\r\n  mainStore = new TAS_T_SA_BUSINESS();\r\n  editStores = [this.mainStore];\r\n  reportMonthData = [];\r\n  initData = [];\r\n  partnerData = [];\r\n  id = null;\r\n  showDiv = false;\r\n  isEditing = false;\r\n  dtlListOfData = [];\r\n  sumInfo = [];\r\n  // 定义禁用编辑表单的对象，用于控制表单的禁用状态\r\n  // ALL属性表示是否全局禁用编辑表单的功能\r\n  disabledEditForm: Object = {\r\n    ALL: false,\r\n  };\r\n  constructor(cwfBusContextService: CwfBusContextService,\r\n    private gol: GlobalDataService,\r\n    private message: NzMessageService,\r\n    private cwfRestfulService: CwfRestfulService) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  /**\r\n * 初始化查询条件\r\n */\r\n  initEdit() {\r\n    return {\r\n      id: new FormControl('', Validators.nullValidator), // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\r\n      reportMoonId: new FormControl('', Validators.required),\r\n      reportYm: new FormControl('', Validators.required),\r\n      reportDt: new FormControl('', Validators.required),\r\n      reportOrgCd: new FormControl('', Validators.required),\r\n      reportOrgNm: new FormControl('', Validators.required),\r\n      businessTypeCd: new FormControl('SL', Validators.required),\r\n      businessTypeNm: new FormControl('监装监卸', Validators.required),\r\n      remark: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\r\n      createdUser: new FormControl('', Validators.nullValidator), // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      createdTime: new FormControl('', Validators.nullValidator), // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedUser: new FormControl('', Validators.nullValidator), // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedTime: new FormControl('', Validators.nullValidator), // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      isDelete: new FormControl('', Validators.nullValidator), // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\r\n      version: new FormControl('', Validators.nullValidator), // 版本号，初始值为空，验证规则为nullValidator（允许为空）\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 页面加载完成后出发\r\n   */\r\n  async onShow() {\r\n    this.id = null;\r\n    if (this.openParam['state'] === PageModeEnum.Custom) {\r\n      this.disabledEditForm['ALL'] = true;\r\n      this.cwfRestfulService.get('/saBusiness/' + this.openParam['id'],this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.editForm.patchValue(rps.data);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\r\n          this.openMainPage();\r\n        }\r\n      });\r\n    }\r\n    if(this.openParam['state'] !== PageModeEnum.Add){\r\n      this.editForm.controls['reportMoonId'].disable();\r\n      this.editForm.controls['reportOrgCd'].disable();\r\n      this.queryDtlList(true);\r\n      this.getPartnerData(null);\r\n      this.showDiv = true;\r\n    }\r\n    this.getOrgData();\r\n    this.getTimeLimitData();\r\n  }\r\n\r\n  /**\r\n   * desc:保存数据\r\n   * by:\r\n   */\r\n  saveData() {\r\n    const url = '/saBusiness';\r\n    for (const i in this.editForm.controls) {\r\n      this.editForm.controls[i].markAsDirty();\r\n      this.editForm.controls[i].updateValueAndValidity();\r\n    }\r\n    if (this.editForm.invalid) {\r\n      return;\r\n    }\r\n    if(new Date(this.editForm.controls['endDt']?.value) < new Date(this.editForm.controls['startDt']?.value)){\r\n      this.message.warning('统计截止时间不能小于统计开始时间', {\r\n              nzDuration: 3000\r\n      });\r\n      return;\r\n    }\r\n    if(new Date(this.editForm.controls['pmDt']?.value) <= new Date(this.editForm.controls['endDt']?.value)){\r\n      this.message.warning('禁止修改时间不能小于等于统计截止时间', {\r\n              nzDuration: 3000\r\n      });\r\n      return;\r\n    }\r\n    let reportYm = new Date(this.editForm.controls['reportYm'].value);\r\n    let month = reportYm.getMonth() + 1;\r\n    this.editForm.controls['reportYm'].setValue(reportYm.getFullYear() + \"-\" + (month < 10?\"0\" + month : month));\r\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\r\n    this.loading = true;\r\n    if (this.openParam['state'] === PageModeEnum.Add) {\r\n      this.editForm.removeControl('id');\r\n      this.cwfRestfulService.post(url + '/save', {saBusiness: this.editForm.getRawValue(), dtls: this.dtlListOfData.filter(item =>item.partnerCd != null) }, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    } else {\r\n      this.cwfRestfulService.post(url + '/update', {saBusiness: this.editForm.getRawValue(), dtls: this.dtlListOfData.filter(item =>item.partnerCd != null) }, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    if (this.checkSave()) {\r\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\r\n        switch (state) {\r\n          case DialogResultEnum.yes:   // 是\r\n            this.saveData();\r\n            return true;\r\n          case DialogResultEnum.no:    // 否\r\n            this.openMainPage({});\r\n            return true;\r\n          case DialogResultEnum.cancel: // 取消\r\n            return false;\r\n        }\r\n      });\r\n    } else {\r\n      this.openMainPage({});\r\n      return true;\r\n    }\r\n  }\r\n  getEditFromDisabled(feildName) {\r\n    return this.disabledEditForm[feildName];\r\n  }\r\n\r\n  onInputChange(value: Date) {\r\n    if(this.openParam['state'] === PageModeEnum.Add){\r\n      this.editForm.controls['startDt'].setValue(new Date(value.getFullYear(), value.getMonth(), 1));\r\n      this.editForm.controls['endDt'].setValue(new Date(value.getFullYear(), value.getMonth() + 1, 0));\r\n      this.editForm.controls['pmDt'].setValue(new Date(value.getFullYear(), value.getMonth() + 2, 0));\r\n    }\r\n  }\r\n\r\n  getOrgData() {\r\n    const rdata = { type: 'company' };\r\n    this.cwfRestfulService\r\n        .post(\r\n          '/org/getRelateChildren',\r\n          rdata,\r\n          this.gol.serviceName['main'].en\r\n        )\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            // 更新 initData 数组\r\n            this.initData = rps.data.map((item) => ({\r\n              label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\r\n              value: item.orgCode,\r\n              orgName: item.orgName\r\n            }));\r\n          }else{\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n    })\r\n  }\r\n\r\n  getTimeLimitData() {\r\n    const rdata = { size: 1000 };\r\n    this.cwfRestfulService\r\n        .post(\r\n          '/saTimeLimit/getReportMonthInfo',\r\n          rdata,\r\n          this.gol.serviceName['tas'].en\r\n        )\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            // 更新 initData 数组\r\n            this.reportMonthData = rps.data.map((item) => ({\r\n              label: item.reportYm + ' 范围:' + item.startDt + '到' + item.endDt + ' 不可修改日期:' + item.pmDt,\r\n              value: item.id,\r\n              reportYm: item.reportYm,\r\n            }));\r\n          }else{\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n    })\r\n  }\r\n\r\n  onReportMonthChange(value: any) {\r\n    if (value == null) {\r\n      // 清理数据\r\n      this.editForm.controls['reportYm'].setValue(\"\");\r\n      this.editForm.controls['reportDt'].setValue(\"\");\r\n    } else {\r\n      let model = this.reportMonthData.find(item => item.value === value);\r\n      this.editForm.controls['reportYm'].setValue(model.reportYm);\r\n      this.editForm.controls['reportDt'].setValue(new Date(model.reportYm));\r\n    }\r\n  }\r\n\r\n  onOrgChange(value: any) {\r\n    if (value == null) {\r\n      // 清理数据\r\n      this.editForm.controls['reportOrgNm'].setValue(\"\");\r\n    } else {\r\n      let model = this.initData.find(item => item.value === value);\r\n      this.editForm.controls['reportOrgNm'].setValue(model.orgName);\r\n    }\r\n  }\r\n\r\n  queryDtlList(reset?: boolean) {\r\n    if (reset) {\r\n      this.mainStore.pageing.PAGE = 1;\r\n    }\r\n    const requestData = {\r\n      page: this.mainStore.pageing.PAGE,\r\n      size: this.mainStore.pageing.LIMIT,\r\n      sortBy: {\r\n        createdTime: 'DESC'\r\n      }\r\n    };\r\n    requestData['data'] = {businessId: this.editForm.controls['id']?.value}\r\n    if(requestData['data'].businessId == null) {\r\n      requestData['data'].businessId = this.id;\r\n    }\r\n    if(this.id == null) {\r\n      this.id = this.editForm.controls['id']?.value;\r\n    }\r\n    this.mainStore.clearData();\r\n    this.cwfRestfulService.post('/saBusinessDtl/list/page', requestData, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n      if (rps.ok === true) {\r\n        this.mainStore.loadDatas(rps.data.content);\r\n        this.dtlListOfData = this.mainStore.getDatas()?.reduce((itemNew, itemOld) => {\r\n          itemNew.push({\r\n            id: itemOld.id,\r\n            businessId: itemOld.businessId,\r\n            partnerCd: itemOld.partnerCd,\r\n            partnerNm: itemOld.partnerNm, \r\n            dtShips: itemOld.dtShips == null ? 0 : itemOld.dtShips, \r\n            dtTeu: itemOld.dtTeu == null ? 0 : itemOld.dtTeu, \r\n            dtWeight: itemOld.dtWeight == null ? 0 : itemOld.dtWeight,\r\n            dtQuantity: itemOld.dtQuantity == null ? 0 : itemOld.dtQuantity,\r\n            dtIncome: itemOld.dtIncome == null ? 0 : itemOld.dtIncome,\r\n            ftShips: itemOld.ftShips == null ? 0 : itemOld.ftShips,\r\n            ftTeu: itemOld.ftTeu == null ? 0 : itemOld.ftTeu,\r\n            ftWeight: itemOld.ftWeight == null ? 0 : itemOld.ftWeight,\r\n            ftQuantity: itemOld.ftQuantity == null ? 0 : itemOld.ftQuantity,\r\n            ftIncome: itemOld.ftIncome == null ? 0 : itemOld.ftIncome,\r\n            version: itemOld.version,\r\n            isEditing: false,\r\n            editFlag: false\r\n          });\r\n          return itemNew;\r\n        }, []);\r\n        this.mainStore.pageing.TOTAL = rps.data.totalElements; // 总条数\r\n        if(this.dtlListOfData.length > 0){\r\n          this.showDiv = true;\r\n        } else {\r\n          this.showDiv = false;\r\n        }\r\n        this.sumDtl(this.dtlListOfData);\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n  sumDtl(dtlData: any[]){\r\n    this.sumInfo = [];\r\n    if(dtlData.length === 0){\r\n      dtlData = this.dtlListOfData;\r\n    }\r\n    // 进行数据汇总\r\n    let dt = dtlData.reduce((acc, cur) =>{\r\n      acc.title = '内贸汇总信息';\r\n      acc.totalShips += cur.dtShips;\r\n      acc.totalTeu += cur.dtTeu;\r\n      acc.totalWeight += cur.dtWeight;\r\n      acc.totalQuantity += cur.dtQuantity;\r\n      acc.totalIncome += cur.dtIncome;\r\n      return acc;\r\n    }, { title: '', totalShips: 0, totalTeu: 0, totalWeight: 0, totalQuantity: 0, totalIncome: 0 });\r\n\r\n    let ft = dtlData.reduce((acc, cur) =>{\r\n      acc.title = '外贸汇总信息';\r\n      acc.totalShips += cur.ftShips;\r\n      acc.totalTeu += cur.ftTeu;\r\n      acc.totalWeight += cur.ftWeight;\r\n      acc.totalQuantity += cur.ftQuantity;\r\n      acc.totalIncome += cur.ftIncome;\r\n      return acc;\r\n    }, { title: '', totalShips: 0, totalTeu: 0, totalWeight: 0, totalQuantity: 0, totalIncome: 0 });\r\n\r\n    let sum = dtlData.reduce((acc, cur) =>{\r\n      acc.title = '全汇总信息';\r\n      acc.totalShips += cur.dtShips + cur.ftShips;\r\n      acc.totalTeu += cur.dtTeu + cur.ftTeu;\r\n      acc.totalWeight += cur.dtWeight + cur.ftWeight;\r\n      acc.totalQuantity += cur.dtQuantity + cur.ftQuantity;\r\n      acc.totalIncome += cur.dtIncome + cur.ftIncome;\r\n      return acc;\r\n    }, { title: '', totalShips: 0, totalTeu: 0, totalWeight: 0, totalQuantity: 0, totalIncome: 0 });\r\n    this.sumInfo = this.sumInfo.concat(dt).concat(ft).concat(sum);\r\n  }\r\n\r\n  newDtlData() {\r\n    // if(this.id == null || this.id === ''){\r\n    //   this.message.warning('请先保存业务主表', {\r\n    //           nzDuration: 3000\r\n    //   });\r\n    //   return;\r\n    // }\r\n    this.dtlListOfData.push(\r\n      {\r\n        id: null, \r\n        businessId: this.id,\r\n        partnerCd: null,\r\n        partnerNm: null, \r\n        dtShips: null, \r\n        dtImportTeu: null, \r\n        dtExportTeu: null,\r\n        dtIncome: null,\r\n        ftShips: null,\r\n        ftImportTeu: null,\r\n        ftExportTeu: null,\r\n        ftIncome: null,\r\n        version: null,\r\n        editFlag: true, \r\n        isEditing: true\r\n      });\r\n  }\r\n\r\n  delDtl(info: any, index: any) {\r\n    this.dtlListOfData.splice(index, 1);\r\n    // if(info.id == null) {\r\n    //   this.dtlListOfData.splice(index, 1);\r\n    //   return;\r\n    // }\r\n    // const requestData = [];\r\n    // requestData.push(info.id);\r\n    // this.cwfRestfulService.delete('/saBusinessDtl/batch', this.gol.serviceName['tas'].en, { body: requestData }).then((rps: responseInterface) => {\r\n    //   this.loading = false;\r\n    //   if (rps.ok) {\r\n    //     this.showState(ModalTypeEnum.success, '删除成功！');\r\n    //     this.queryDtlList();\r\n    //   } else {\r\n    //     this.showState(ModalTypeEnum.error, rps.msg);\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  getPartnerData(selectedValue: any) {\r\n    let requestData = {\r\n      page: 1,\r\n      size: 100,\r\n      param: selectedValue,\r\n      partyType: ['I','B']\r\n    };\r\n    this.cwfRestfulService\r\n      .post(\r\n        '/partner/getPartnerInfo',\r\n        requestData,\r\n        this.gol.serviceName['bc'].en\r\n      )\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          // 更新 partnerData 数组\r\n          this.partnerData = rps.data.map((item) => ({\r\n            label: item.partnerNm,\r\n            value: item.partnerCd,\r\n            partnerNm: item.partnerNm,\r\n            partnerTypeCd: item.partnerTypeCd,\r\n            partnerTypeNm: item.partnerTypeNm,\r\n            groupLevel2OrgCd: item.groupLevel2OrgCd,\r\n            groupLevel2OrgNm: item.groupLevel2OrgNm,\r\n            kaTag: item.kaTag,\r\n          }));\r\n        }else{\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n    })\r\n  }\r\n\r\n  onPartnerChange(value: any, info: any, i: any ) {\r\n    const isDuplicate =  this.dtlListOfData.some(item => item !== info && item.partnerCd === value);\r\n    if(isDuplicate){\r\n      info.partnerCd = null\r\n      this.message.warning('船公司不能重复选择', {\r\n        nzDuration: 3000\r\n      });\r\n      this.dtlListOfData.splice(i, 1);\r\n      this.newDtlData()\r\n      return;\r\n    }\r\n    let model = this.partnerData.find(item => item.value === info.partnerCd);\r\n    let dtlData = this.dtlListOfData.find(item => item.partnerCd === info.partnerCd);\r\n    dtlData['partnerNm'] = model.partnerNm;\r\n    dtlData['partnerTypeCd'] = model.partnerTypeCd;\r\n    dtlData['partnerTypeNm'] = model.partnerTypeNm;\r\n    dtlData['groupLevel2OrgCd'] = model.groupLevel2OrgCd;\r\n    dtlData['groupLevel2OrgNm'] = model.groupLevel2OrgNm;\r\n    dtlData['kaTag'] = model.kaTag;\r\n  }\r\n\r\n  saveDtl(blurValues: any): void {\r\n    if(blurValues.partnerCd == null || blurValues.partnerCd.trim() == '') {\r\n      this.message.warning('船公司不能为空', {\r\n              nzDuration: 3000\r\n      });\r\n      return;\r\n    }\r\n    const url = '/saBusinessDtl';\r\n    this.loading = true;\r\n    if (blurValues.id == null) {\r\n      this.cwfRestfulService.post(url, blurValues, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.queryDtlList(true);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    } else {\r\n      this.cwfRestfulService.put(url, blurValues, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.queryDtlList(true);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n    setTimeout(() => {\r\n      this.isEditing = false;\r\n      this.sumDtl([]);\r\n    }, 100); \r\n  }\r\n\r\n  updateDtl(info: any) {\r\n    info.isEditing = !info.isEditing;\r\n    info.editFlag = !info.editFlag;\r\n  }\r\n\r\n  saveFront(info: any) {\r\n    if(info.partnerCd == null || info.partnerCd.trim() == '') {\r\n      this.message.warning('船公司不能为空', {\r\n              nzDuration: 3000\r\n      });\r\n      return;\r\n    }\r\n    this.updateDtl(info);\r\n  }\r\n}\r\n", "<nz-card class=\"system-index-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"10\" class=\"list-title\">\r\n      <svg-icon [src]=\"'assets/img/icons/filter.svg'\"></svg-icon>\r\n      <div class=\"title\">{{'TAS.SUPERVISE_EDIT' | translate}}</div>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"!getEditFromDisabled('ALL')\">\r\n      <button nz-button [nzLoading]=\"loading\" (click)=\"saveData()\" [nzType]=\"'primary'\">{{'FP.SAVE' | translate}}</button>\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"getEditFromDisabled('ALL')\">\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n  </nz-row>\r\n  <!-- 查询条件表单 -->\r\n  <form style=\"margin-top: 20px;\" nz-form [formGroup]=\"editForm\">\r\n    <div nz-row [nzGutter]=\"[8,10]\">\r\n\r\n      <div nz-col nzSpan=\"12\" style=\"margin-top: 5px;\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px;\">{{ 'TAS.REPORT_YM' | translate }}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"reportMoonId\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onReportMonthChange($event)\" nzAllowClear>\r\n              <nz-option *ngFor=\"let option of reportMonthData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"12\" style=\"margin-top: 5px;\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px;\">{{ 'TAS.REPORT_ORG' | translate }}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"reportOrgCd\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onOrgChange($event)\" nzAllowClear>\r\n              <nz-option *ngFor=\"let option of initData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!--remark :备注 -->\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.REMARK' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <textarea nz-input placeholder=\"{{'TAS.REMARK' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n                      formControlName=\"remark\" style=\"height: 100px;resize: none;\"></textarea>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n    </div>\r\n  </form>\r\n  <h4>  \r\n    <div class=\"list-button\">\r\n      <div>\r\n        <button type=\"button\" nz-button nzType=\"primary\" (click)=\"newDtlData()\">\r\n          <span>新增</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </h4>\r\n  <nz-table\r\n    #table\r\n    [nzBordered]=\"true\"\r\n    [nzFrontPagination]=\"false\"\r\n    [nzShowPagination]=\"false\"\r\n    [nzData]=\"mainStore.getDatas()\"\r\n    [nzScroll]=\"{ x: '1500px' }\"\r\n    >\r\n      <thead style=\"text-align: center;background-color: rgb(172, 174, 237);\">\r\n        <tr>\r\n          <th nzWidth=\"50px\" nzLeft>{{ 'DB.SEQ' | translate }}</th>\r\n          <th nzWidth=\"200px\" nzAlign=\"center\">船公司</th>\r\n          <th nzAlign=\"center\">船公司代码</th>\r\n          <!-- <th nzAlign=\"center\">类型</th> -->\r\n          <th nzAlign=\"center\">内贸船舶(艘次)</th>\r\n          <th nzAlign=\"center\">内贸集装箱数(TEU)</th>\r\n          <th nzAlign=\"center\">内贸货物重量(万吨)</th>\r\n          <th nzAlign=\"center\">内贸货物件数(万件)</th>\r\n          <th nzAlign=\"center\">内贸收入(万元)</th>\r\n          <th nzAlign=\"center\">外贸船舶(艘次)</th>\r\n          <th nzAlign=\"center\">外贸集装箱数(TEU)</th>\r\n          <th nzAlign=\"center\">外贸货物重量(万吨)</th>\r\n          <th nzAlign=\"center\">外贸货物件数(万件)</th>\r\n          <th nzAlign=\"center\">外贸收入(万元)</th>\r\n          <th nzAlign=\"center\" nzWidth=\"75px\" nzRight>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let info of dtlListOfData; let i = index\">\r\n          <!-- 序号 -->\r\n          <td nzAlign=\"center\">{{ i + 1 }}</td>\r\n\r\n          <td>\r\n            <nz-select style=\"width: 180px;\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.partnerCd\" [nzPlaceHolder]=\"'请选择船公司'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onPartnerChange($event, info, i)\" nzAllowClear>\r\n              <nz-option *ngFor=\"let option of partnerData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n            <!-- <input *ngIf=\"info.isEditing\" [(ngModel)]=\"info.parnterNm\" nz-input placeholder=\"船公司\" required> -->\r\n            <span *ngIf=\"!info.isEditing\">{{ info.partnerNm }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <span>{{ info.partnerCd }}</span>\r\n          </td>\r\n\r\n          <!-- <td>\r\n            <input *ngIf=\"info.isEditing\" [(ngModel)]=\"info.parnterTypeNm\" nz-input placeholder=\"类型\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.parnterTypeNm }}</span>\r\n          </td> -->\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.dtShips\" nz-input placeholder=\"内贸船舶(艘次)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.dtShips }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.dtTeu\" nz-input placeholder=\"内贸集装箱数(TEU)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.dtTeu }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.dtWeight\" nz-input placeholder=\"内贸货物重量(万吨)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.dtWeight }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.dtQuantity\" nz-input placeholder=\"内贸货物件数(万件)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.dtQuantity }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.dtIncome\" nz-input placeholder=\"内贸收入(万元)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.dtIncome }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.ftShips\" nz-input placeholder=\"外贸船舶(艘次)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.ftShips }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.ftTeu\" nz-input placeholder=\"外贸集装箱数(TEU)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.ftTeu }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.ftWeight\" nz-input placeholder=\"外贸货物重量(万吨)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.ftWeight }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.ftQuantity\" nz-input placeholder=\"外贸货物件数(万件)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.ftQuantity }}</span>\r\n          </td>\r\n\r\n          <td>\r\n            <input type=\"number\" *ngIf=\"info.isEditing\" [(ngModel)]=\"info.ftIncome\" nz-input placeholder=\"外贸收入(万元)\" required>\r\n            <span *ngIf=\"!info.isEditing\">{{ info.ftIncome }}</span>\r\n          </td>\r\n\r\n          <td nzRight>\r\n            <span>\r\n              <!-- 确认按钮 -->\r\n              <a *ngIf=\"!info.editFlag\" (click)=\"updateDtl(info)\">\r\n                <i nz-icon nzIconfont=\"icon-edit-tb\" style=\"font-size: 16px; margin-right: 20px\"></i>\r\n              </a>\r\n              <!-- 编辑按钮 -->\r\n              <a *ngIf=\"info.editFlag\" (click)=\"saveFront(info)\">\r\n                <i nz-icon nzIconfont=\"icon-save-tb\" style=\"font-size: 16px; margin-right: 20px\"></i>\r\n              </a>\r\n              <a nz-popconfirm (nzOnConfirm)=\"delDtl(info, i)\"\r\n                [nzPopconfirmTitle]=\"'MSG.WEB0020' | translate\">\r\n                <i nz-icon nzIconfont=\"icon-delete-tb\" style=\"font-size: 16px\"></i>\r\n              </a>\r\n            </span>\r\n          </td>       \r\n        </tr>\r\n      </tbody>\r\n    </nz-table>\r\n    <div *ngIf=\"showDiv\">\r\n      <div *ngFor=\"let info of sumInfo; let i = index\" style=\"border: 1px solid rgb(73, 73, 244);width: 100%; height: 60px;margin-top: 10px;\">\r\n        <div>\r\n          <div style=\"margin-left: 3px;width: 10px; height: 10px; background-color: rgb(237, 142, 70);display:inline-block;\"></div><span style=\"color: rgb(237, 142, 70);margin-left: 3px;\">{{ info.title }}</span>\r\n          <div style=\"margin-left: 5px;font-size: 12px;\">\r\n            <div style=\"display: inline-block;width: 20%;\"><span style=\"margin-right: 50px;\">船舶</span> {{ info.totalShips }} 艘次</div>\r\n            <div style=\"display: inline-block;width: 20%;\"><span style=\"margin-right: 50px;\">集装箱数</span> {{ info.totalTeu | decimal: 1  }} TEU</div>\r\n            <div style=\"display: inline-block;width: 20%;\"><span style=\"margin-right: 50px;\">货物重量</span> {{ info.totalWeight | decimal: 4  }} 万吨</div>\r\n            <div style=\"display: inline-block;width: 20%;\"><span style=\"margin-right: 50px;\">货物件数</span> {{ info.totalQuantity | decimal: 4  }} 万件</div>\r\n            <div style=\"display: inline-block;width: 20%;\"><span style=\"margin-right: 50px;\">收入</span> {{ info.totalIncome | decimal: 4  }} 万元</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n</nz-card>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAwBC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB;AACjH,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,iBAAiB,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICC1DC,EADF,CAAAC,cAAA,iBAA4E,iBACQ;IAA1CD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAAsBT,EAAA,CAAAU,MAAA,GAAyB;;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACpHX,EAAA,CAAAC,cAAA,iBAAsC;IAApBD,EAAA,CAAAE,UAAA,mBAAAU,iEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;;IAFWX,EAAA,CAAAc,SAAA,EAAqB;IAAsBd,EAA3C,CAAAe,UAAA,cAAAT,MAAA,CAAAU,OAAA,CAAqB,qBAA0C;IAAChB,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,kBAAyB;IACrElB,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;;IAGpElB,EADF,CAAAC,cAAA,iBAA2E,iBACnC;IAApBD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;IAD+BX,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;IAa5DlB,EAAA,CAAAqB,SAAA,oBACY;;;;IAD+DrB,EAAzB,CAAAe,UAAA,YAAAO,SAAA,CAAAC,KAAA,CAAwB,YAAAD,SAAA,CAAAE,KAAA,CAAyB;;;;;IAanGxB,EAAA,CAAAqB,SAAA,oBACY;;;;IADwDrB,EAAzB,CAAAe,UAAA,YAAAU,SAAA,CAAAF,KAAA,CAAwB,YAAAE,SAAA,CAAAD,KAAA,CAAyB;;;;;IA+D5FxB,EAAA,CAAAqB,SAAA,oBACY;;;;IAD2DrB,EAAzB,CAAAe,UAAA,YAAAW,UAAA,CAAAH,KAAA,CAAwB,YAAAG,UAAA,CAAAF,KAAA,CAAyB;;;;;;IAFjGxB,EAAA,CAAAC,cAAA,oBACkE;IADVD,EAAA,CAAA2B,gBAAA,2BAAAC,qFAAAC,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,GAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAG,SAAA,EAAAL,MAAA,MAAAE,OAAA,CAAAG,SAAA,GAAAL,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA4B;IAClF7B,EAAA,CAAAE,UAAA,2BAAA0B,qFAAAC,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,GAAA;MAAA,MAAAK,MAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,MAAAwB,OAAA,GAAAI,MAAA,CAAAH,SAAA;MAAA,MAAAI,KAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAgC,eAAA,CAAAT,MAAA,EAAAE,OAAA,EAAAK,KAAA,CAAgC;IAAA,EAAC;IAClDpC,EAAA,CAAAuC,UAAA,IAAAC,6DAAA,wBAAgG;IAElGxC,EAAA,CAAAW,YAAA,EAAY;;;;;IAJ4CX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAG,SAAA,CAA4B;IAA4BlC,EAA3B,CAAAe,UAAA,yDAA0B,sBAAsB;IAErGf,EAAA,CAAAc,SAAA,EAAc;IAAdd,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAoC,WAAA,CAAc;;;;;IAI9C1C,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA3BX,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAY,SAAA,CAAoB;;;;;;IAalD3C,EAAA,CAAAC,cAAA,gBAAgH;IAApED,EAAA,CAAA2B,gBAAA,2BAAAiB,8EAAAf,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAd,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAe,OAAA,EAAAjB,MAAA,MAAAE,OAAA,CAAAe,OAAA,GAAAjB,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA0B;IAAtE7B,EAAA,CAAAW,YAAA,EAAgH;;;;IAApEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAe,OAAA,CAA0B;;;;;IACtE9C,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAzBX,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAe,OAAA,CAAkB;;;;;;IAIhD9C,EAAA,CAAAC,cAAA,gBAAiH;IAArED,EAAA,CAAA2B,gBAAA,2BAAAoB,8EAAAlB,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAAjB,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAkB,KAAA,EAAApB,MAAA,MAAAE,OAAA,CAAAkB,KAAA,GAAApB,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAAwB;IAApE7B,EAAA,CAAAW,YAAA,EAAiH;;;;IAArEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAkB,KAAA,CAAwB;;;;;IACpEjD,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAvBX,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAkB,KAAA,CAAgB;;;;;;IAI9CjD,EAAA,CAAAC,cAAA,gBAAmH;IAAvED,EAAA,CAAA2B,gBAAA,2BAAAuB,8EAAArB,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA+C,IAAA;MAAA,MAAApB,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAqB,QAAA,EAAAvB,MAAA,MAAAE,OAAA,CAAAqB,QAAA,GAAAvB,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA2B;IAAvE7B,EAAA,CAAAW,YAAA,EAAmH;;;;IAAvEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAqB,QAAA,CAA2B;;;;;IACvEpD,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA1BX,EAAA,CAAAc,SAAA,EAAmB;IAAnBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAqB,QAAA,CAAmB;;;;;;IAIjDpD,EAAA,CAAAC,cAAA,gBAAqH;IAAzED,EAAA,CAAA2B,gBAAA,2BAAA0B,8EAAAxB,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAvB,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAwB,UAAA,EAAA1B,MAAA,MAAAE,OAAA,CAAAwB,UAAA,GAAA1B,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA6B;IAAzE7B,EAAA,CAAAW,YAAA,EAAqH;;;;IAAzEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAwB,UAAA,CAA6B;;;;;IACzEvD,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA5BX,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAwB,UAAA,CAAqB;;;;;;IAInDvD,EAAA,CAAAC,cAAA,gBAAiH;IAArED,EAAA,CAAA2B,gBAAA,2BAAA6B,8EAAA3B,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAqD,IAAA;MAAA,MAAA1B,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAA2B,QAAA,EAAA7B,MAAA,MAAAE,OAAA,CAAA2B,QAAA,GAAA7B,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA2B;IAAvE7B,EAAA,CAAAW,YAAA,EAAiH;;;;IAArEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAA2B,QAAA,CAA2B;;;;;IACvE1D,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA1BX,EAAA,CAAAc,SAAA,EAAmB;IAAnBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAA2B,QAAA,CAAmB;;;;;;IAIjD1D,EAAA,CAAAC,cAAA,gBAAgH;IAApED,EAAA,CAAA2B,gBAAA,2BAAAgC,8EAAA9B,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAA7B,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAA8B,OAAA,EAAAhC,MAAA,MAAAE,OAAA,CAAA8B,OAAA,GAAAhC,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA0B;IAAtE7B,EAAA,CAAAW,YAAA,EAAgH;;;;IAApEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAA8B,OAAA,CAA0B;;;;;IACtE7D,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAzBX,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAA8B,OAAA,CAAkB;;;;;;IAIhD7D,EAAA,CAAAC,cAAA,gBAAiH;IAArED,EAAA,CAAA2B,gBAAA,2BAAAmC,8EAAAjC,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA2D,IAAA;MAAA,MAAAhC,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAiC,KAAA,EAAAnC,MAAA,MAAAE,OAAA,CAAAiC,KAAA,GAAAnC,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAAwB;IAApE7B,EAAA,CAAAW,YAAA,EAAiH;;;;IAArEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAiC,KAAA,CAAwB;;;;;IACpEhE,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAvBX,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAiC,KAAA,CAAgB;;;;;;IAI9ChE,EAAA,CAAAC,cAAA,gBAAmH;IAAvED,EAAA,CAAA2B,gBAAA,2BAAAsC,8EAAApC,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAAnC,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAoC,QAAA,EAAAtC,MAAA,MAAAE,OAAA,CAAAoC,QAAA,GAAAtC,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA2B;IAAvE7B,EAAA,CAAAW,YAAA,EAAmH;;;;IAAvEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAoC,QAAA,CAA2B;;;;;IACvEnE,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA1BX,EAAA,CAAAc,SAAA,EAAmB;IAAnBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAoC,QAAA,CAAmB;;;;;;IAIjDnE,EAAA,CAAAC,cAAA,gBAAqH;IAAzED,EAAA,CAAA2B,gBAAA,2BAAAyC,8EAAAvC,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAAtC,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAuC,UAAA,EAAAzC,MAAA,MAAAE,OAAA,CAAAuC,UAAA,GAAAzC,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA6B;IAAzE7B,EAAA,CAAAW,YAAA,EAAqH;;;;IAAzEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAAuC,UAAA,CAA6B;;;;;IACzEtE,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA5BX,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAuC,UAAA,CAAqB;;;;;;IAInDtE,EAAA,CAAAC,cAAA,gBAAiH;IAArED,EAAA,CAAA2B,gBAAA,2BAAA4C,8EAAA1C,MAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAzC,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAA0C,QAAA,EAAA5C,MAAA,MAAAE,OAAA,CAAA0C,QAAA,GAAA5C,MAAA;MAAA,OAAA7B,EAAA,CAAAQ,WAAA,CAAAqB,MAAA;IAAA,EAA2B;IAAvE7B,EAAA,CAAAW,YAAA,EAAiH;;;;IAArEX,EAAA,CAAAyC,gBAAA,YAAAV,OAAA,CAAA0C,QAAA,CAA2B;;;;;IACvEzE,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA1BX,EAAA,CAAAc,SAAA,EAAmB;IAAnBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAA0C,QAAA,CAAmB;;;;;;IAM/CzE,EAAA,CAAAC,cAAA,YAAoD;IAA1BD,EAAA,CAAAE,UAAA,mBAAAwE,8DAAA;MAAA1E,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAA5C,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsE,SAAA,CAAA7C,OAAA,CAAe;IAAA,EAAC;IACjD/B,EAAA,CAAAqB,SAAA,YAAqF;IACvFrB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAEJX,EAAA,CAAAC,cAAA,YAAmD;IAA1BD,EAAA,CAAAE,UAAA,mBAAA2E,8DAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,IAAA;MAAA,MAAA/C,OAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,SAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyE,SAAA,CAAAhD,OAAA,CAAe;IAAA,EAAC;IAChD/B,EAAA,CAAAqB,SAAA,YAAqF;IACvFrB,EAAA,CAAAW,YAAA,EAAI;;;;;;IAhFRX,EAFF,CAAAC,cAAA,SAAsD,aAE/B;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAErCX,EAAA,CAAAC,cAAA,SAAI;IAOFD,EANA,CAAAuC,UAAA,IAAAyC,iDAAA,wBACkE,IAAAC,4CAAA,mBAKpC;IAChCjF,EAAA,CAAAW,YAAA,EAAK;IAGHX,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAU,MAAA,GAAoB;IAC5BV,EAD4B,CAAAW,YAAA,EAAO,EAC9B;IAOLX,EAAA,CAAAC,cAAA,SAAI;IAEFD,EADA,CAAAuC,UAAA,KAAA2C,8CAAA,oBAAgH,KAAAC,6CAAA,mBAClF;IAChCnF,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAA6C,8CAAA,oBAAiH,KAAAC,6CAAA,mBACnF;IAChCrF,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAA+C,8CAAA,oBAAmH,KAAAC,6CAAA,mBACrF;IAChCvF,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAAiD,8CAAA,oBAAqH,KAAAC,6CAAA,mBACvF;IAChCzF,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAAmD,8CAAA,oBAAiH,KAAAC,6CAAA,mBACnF;IAChC3F,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAAqD,8CAAA,oBAAgH,KAAAC,6CAAA,mBAClF;IAChC7F,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAAuD,8CAAA,oBAAiH,KAAAC,6CAAA,mBACnF;IAChC/F,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAAyD,8CAAA,oBAAmH,KAAAC,6CAAA,mBACrF;IAChCjG,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAA2D,8CAAA,oBAAqH,KAAAC,6CAAA,mBACvF;IAChCnG,EAAA,CAAAW,YAAA,EAAK;IAELX,EAAA,CAAAC,cAAA,UAAI;IAEFD,EADA,CAAAuC,UAAA,KAAA6D,8CAAA,oBAAiH,KAAAC,6CAAA,mBACnF;IAChCrG,EAAA,CAAAW,YAAA,EAAK;IAGHX,EADF,CAAAC,cAAA,cAAY,YACJ;IAMJD,EAJA,CAAAuC,UAAA,KAAA+D,0CAAA,gBAAoD,KAAAC,0CAAA,gBAID;IAGnDvG,EAAA,CAAAC,cAAA,aACkD;;IADjCD,EAAA,CAAAE,UAAA,yBAAAsG,gEAAA;MAAA,MAAAC,OAAA,GAAAzG,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAA3E,OAAA,GAAA0E,OAAA,CAAAzE,SAAA;MAAA,MAAAI,KAAA,GAAAqE,OAAA,CAAApE,KAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAqG,MAAA,CAAA5E,OAAA,EAAAK,KAAA,CAAe;IAAA,EAAC;IAE9CpC,EAAA,CAAAqB,SAAA,aAAmE;IAI3ErB,EAHM,CAAAW,YAAA,EAAI,EACC,EACJ,EACF;;;;;IAvFkBX,EAAA,CAAAc,SAAA,GAAW;IAAXd,EAAA,CAAAiB,iBAAA,CAAAmB,KAAA,KAAW;IAGIpC,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IAM/C5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAItB5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAiB,iBAAA,CAAAc,OAAA,CAAAG,SAAA,CAAoB;IASJlC,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAIN5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA6E,SAAA,CAAoB;IACnC5G,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA6E,SAAA,CAAqB;IAMtB5G,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,UAAAgB,OAAA,CAAA8E,QAAA,CAAoB;IAIpB7G,EAAA,CAAAc,SAAA,EAAmB;IAAnBd,EAAA,CAAAe,UAAA,SAAAgB,OAAA,CAAA8E,QAAA,CAAmB;IAIrB7G,EAAA,CAAAc,SAAA,EAA+C;IAA/Cd,EAAA,CAAAe,UAAA,sBAAAf,EAAA,CAAAkB,WAAA,wBAA+C;;;;;IAUvDlB,EADF,CAAAC,cAAA,cAAwI,UACjI;IACHD,EAAA,CAAAqB,SAAA,cAAyH;IAAArB,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAU,MAAA,GAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAExJX,EADjD,CAAAC,cAAA,cAA+C,cACE,eAAkC;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC1EX,EAA/C,CAAAC,cAAA,eAA+C,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAU,MAAA,IAAqC;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACzFX,EAA/C,CAAAC,cAAA,eAA+C,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAU,MAAA,IAAuC;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3FX,EAA/C,CAAAC,cAAA,eAA+C,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAU,MAAA,IAAyC;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC7FX,EAA/C,CAAAC,cAAA,eAA+C,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAU,MAAA,IAAuC;;IAGxIV,EAHwI,CAAAW,YAAA,EAAM,EACpI,EACF,EACF;;;;IATgLX,EAAA,CAAAc,SAAA,GAAgB;IAAhBd,EAAA,CAAAiB,iBAAA,CAAA6F,QAAA,CAAAC,KAAA,CAAgB;IAErG/G,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAgH,kBAAA,MAAAF,QAAA,CAAAG,UAAA,kBAAwB;IACtBjH,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAAgH,kBAAA,MAAAhH,EAAA,CAAAkH,WAAA,QAAAJ,QAAA,CAAAK,QAAA,aAAqC;IACrCnH,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAgH,kBAAA,MAAAhH,EAAA,CAAAkH,WAAA,QAAAJ,QAAA,CAAAM,WAAA,sBAAuC;IACvCpH,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAgH,kBAAA,MAAAhH,EAAA,CAAAkH,WAAA,SAAAJ,QAAA,CAAAO,aAAA,sBAAyC;IAC3CrH,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAgH,kBAAA,MAAAhH,EAAA,CAAAkH,WAAA,SAAAJ,QAAA,CAAAQ,WAAA,sBAAuC;;;;;IAT1ItH,EAAA,CAAAC,cAAA,UAAqB;IACnBD,EAAA,CAAAuC,UAAA,IAAAgF,4CAAA,oBAAwI;IAY1IvH,EAAA,CAAAW,YAAA,EAAM;;;;IAZkBX,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAkH,OAAA,CAAY;;;AD3KxC,OAAM,MAAOC,sBAAuB,SAAQhI,WAAW;EAiBrDiI,YAAYC,oBAA0C,EAC5CC,GAAsB,EACtBC,OAAyB,EACzBC,iBAAoC;IAC5C,KAAK,CAACH,oBAAoB,CAAC;IAHnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAlB3B,KAAAC,SAAS,GAAG,IAAIhI,iBAAiB,EAAE;IACnC,KAAAiI,UAAU,GAAG,CAAC,IAAI,CAACD,SAAS,CAAC;IAC7B,KAAAE,eAAe,GAAG,EAAE;IACpB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAxF,WAAW,GAAG,EAAE;IAChB,KAAAyF,EAAE,GAAG,IAAI;IACT,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAxB,SAAS,GAAG,KAAK;IACjB,KAAAyB,aAAa,GAAG,EAAE;IAClB,KAAAb,OAAO,GAAG,EAAE;IACZ;IACA;IACA,KAAAc,gBAAgB,GAAW;MACzBC,GAAG,EAAE;KACN;EAMD;EAEA;;;EAGAC,QAAQA,CAAA;IACN,OAAO;MACLL,EAAE,EAAE,IAAItI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MACnDC,YAAY,EAAE,IAAI7I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MACtDC,QAAQ,EAAE,IAAI/I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MAClDE,QAAQ,EAAE,IAAIhJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MAClDG,WAAW,EAAE,IAAIjJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MACrDI,WAAW,EAAE,IAAIlJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MACrDK,cAAc,EAAE,IAAInJ,WAAW,CAAC,IAAI,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MAC1DM,cAAc,EAAE,IAAIpJ,WAAW,CAAC,MAAM,EAAEC,UAAU,CAAC6I,QAAQ,CAAC;MAC5DO,MAAM,EAAE,IAAIrJ,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAAC2I,aAAa,EAAE3I,UAAU,CAACqJ,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACjFC,WAAW,EAAE,IAAIvJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MAC5DY,WAAW,EAAE,IAAIxJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MAC5Da,YAAY,EAAE,IAAIzJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MAC7Dc,YAAY,EAAE,IAAI1J,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MAC7De,QAAQ,EAAE,IAAI3J,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC;MAAE;MACzDgB,OAAO,EAAE,IAAI5J,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC2I,aAAa,CAAC,CAAE;KACzD;EACH;EAEA;;;EAGMiB,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACVD,KAAI,CAACxB,EAAE,GAAG,IAAI;MACd,IAAIwB,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKjK,YAAY,CAACkK,MAAM,EAAE;QACnDH,KAAI,CAACrB,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;QACnCqB,KAAI,CAAC7B,iBAAiB,CAACiC,GAAG,CAAC,cAAc,GAAGJ,KAAI,CAACE,SAAS,CAAC,IAAI,CAAC,EAACF,KAAI,CAAC/B,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;UAC/H,IAAIA,GAAG,CAACC,EAAE,EAAE;YACVT,KAAI,CAACU,QAAQ,CAACC,UAAU,CAACH,GAAG,CAACI,IAAI,CAAC;UACpC,CAAC,MAAM;YACLZ,KAAI,CAACa,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAE,cAAc,CAAC;YACnDd,KAAI,CAACe,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ;MACA,IAAGf,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKjK,YAAY,CAAC+K,GAAG,EAAC;QAC9ChB,KAAI,CAACU,QAAQ,CAACO,QAAQ,CAAC,cAAc,CAAC,CAACC,OAAO,EAAE;QAChDlB,KAAI,CAACU,QAAQ,CAACO,QAAQ,CAAC,aAAa,CAAC,CAACC,OAAO,EAAE;QAC/ClB,KAAI,CAACmB,YAAY,CAAC,IAAI,CAAC;QACvBnB,KAAI,CAACoB,cAAc,CAAC,IAAI,CAAC;QACzBpB,KAAI,CAACvB,OAAO,GAAG,IAAI;MACrB;MACAuB,KAAI,CAACqB,UAAU,EAAE;MACjBrB,KAAI,CAACsB,gBAAgB,EAAE;IAAC;EAC1B;EAEA;;;;EAIAxK,QAAQA,CAAA;IACN,MAAMyK,GAAG,GAAG,aAAa;IACzB,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACd,QAAQ,CAACO,QAAQ,EAAE;MACtC,IAAI,CAACP,QAAQ,CAACO,QAAQ,CAACO,CAAC,CAAC,CAACC,WAAW,EAAE;MACvC,IAAI,CAACf,QAAQ,CAACO,QAAQ,CAACO,CAAC,CAAC,CAACE,sBAAsB,EAAE;IACpD;IACA,IAAI,IAAI,CAAChB,QAAQ,CAACiB,OAAO,EAAE;MACzB;IACF;IACA,IAAG,IAAIC,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,EAAEpJ,KAAK,CAAC,GAAG,IAAI+J,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAEpJ,KAAK,CAAC,EAAC;MACvG,IAAI,CAACqG,OAAO,CAAC2D,OAAO,CAAC,kBAAkB,EAAE;QACjCC,UAAU,EAAE;OACnB,CAAC;MACF;IACF;IACA,IAAG,IAAIF,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACO,QAAQ,CAAC,MAAM,CAAC,EAAEpJ,KAAK,CAAC,IAAI,IAAI+J,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,EAAEpJ,KAAK,CAAC,EAAC;MACrG,IAAI,CAACqG,OAAO,CAAC2D,OAAO,CAAC,oBAAoB,EAAE;QACnCC,UAAU,EAAE;OACnB,CAAC;MACF;IACF;IACA,IAAI7C,QAAQ,GAAG,IAAI2C,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACpJ,KAAK,CAAC;IACjE,IAAIkK,KAAK,GAAG9C,QAAQ,CAAC+C,QAAQ,EAAE,GAAG,CAAC;IACnC,IAAI,CAACtB,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACgB,QAAQ,CAAChD,QAAQ,CAACiD,WAAW,EAAE,GAAG,GAAG,IAAIH,KAAK,GAAG,EAAE,GAAC,GAAG,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC;IAC5G,MAAMvD,EAAE,GAAG,IAAI,CAAC2D,aAAa,CAACC,SAAS,EAAE,CAACC,WAAW,CAAC,WAAW,CAAC;IAClE,IAAI,CAAChL,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAAC6I,SAAS,CAAC,OAAO,CAAC,KAAKjK,YAAY,CAAC+K,GAAG,EAAE;MAChD,IAAI,CAACN,QAAQ,CAAC4B,aAAa,CAAC,IAAI,CAAC;MACjC,IAAI,CAACnE,iBAAiB,CAACoE,IAAI,CAAChB,GAAG,GAAG,OAAO,EAAE;QAACiB,UAAU,EAAE,IAAI,CAAC9B,QAAQ,CAAC+B,WAAW,EAAE;QAAEC,IAAI,EAAE,IAAI,CAAChE,aAAa,CAACiE,MAAM,CAACC,IAAI,IAAGA,IAAI,CAACrK,SAAS,IAAI,IAAI;MAAC,CAAE,EAAE,IAAI,CAAC0F,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;QACrN,IAAI,CAAC2B,aAAa,CAACC,SAAS,EAAE,CAACS,UAAU,CAACrE,EAAE,CAAC;QAC7C,IAAI,CAACnH,OAAO,GAAG,KAAK;QACpB,IAAImJ,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACI,SAAS,CAAC7K,aAAa,CAAC8M,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAAC/B,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAACgC,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAClC,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC7E,iBAAiB,CAACoE,IAAI,CAAChB,GAAG,GAAG,SAAS,EAAE;QAACiB,UAAU,EAAE,IAAI,CAAC9B,QAAQ,CAAC+B,WAAW,EAAE;QAAEC,IAAI,EAAE,IAAI,CAAChE,aAAa,CAACiE,MAAM,CAACC,IAAI,IAAGA,IAAI,CAACrK,SAAS,IAAI,IAAI;MAAC,CAAE,EAAE,IAAI,CAAC0F,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;QACvN,IAAI,CAAC2B,aAAa,CAACC,SAAS,EAAE,CAACS,UAAU,CAACrE,EAAE,CAAC;QAC7C,IAAI,CAACnH,OAAO,GAAG,KAAK;QACpB,IAAImJ,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACI,SAAS,CAAC7K,aAAa,CAAC8M,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAAC/B,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAACgC,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAClC,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ;EACF;EAEA9L,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC+L,SAAS,EAAE,EAAE;MACpB,OAAO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC5C,IAAI,CAAC6C,KAAK,IAAG;QACxG,QAAQA,KAAK;UACX,KAAKrN,gBAAgB,CAACsN,GAAG;YAAI;YAC3B,IAAI,CAACvM,QAAQ,EAAE;YACf,OAAO,IAAI;UACb,KAAKf,gBAAgB,CAACuN,EAAE;YAAK;YAC3B,IAAI,CAACvC,YAAY,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI;UACb,KAAKhL,gBAAgB,CAACwN,MAAM;YAAE;YAC5B,OAAO,KAAK;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACxC,YAAY,CAAC,EAAE,CAAC;MACrB,OAAO,IAAI;IACb;EACF;EACAyC,mBAAmBA,CAACC,SAAS;IAC3B,OAAO,IAAI,CAAC9E,gBAAgB,CAAC8E,SAAS,CAAC;EACzC;EAEAC,aAAaA,CAAC7L,KAAW;IACvB,IAAG,IAAI,CAACqI,SAAS,CAAC,OAAO,CAAC,KAAKjK,YAAY,CAAC+K,GAAG,EAAC;MAC9C,IAAI,CAACN,QAAQ,CAACO,QAAQ,CAAC,SAAS,CAAC,CAACgB,QAAQ,CAAC,IAAIL,IAAI,CAAC/J,KAAK,CAACqK,WAAW,EAAE,EAAErK,KAAK,CAACmK,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;MAC9F,IAAI,CAACtB,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,CAACgB,QAAQ,CAAC,IAAIL,IAAI,CAAC/J,KAAK,CAACqK,WAAW,EAAE,EAAErK,KAAK,CAACmK,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAChG,IAAI,CAACtB,QAAQ,CAACO,QAAQ,CAAC,MAAM,CAAC,CAACgB,QAAQ,CAAC,IAAIL,IAAI,CAAC/J,KAAK,CAACqK,WAAW,EAAE,EAAErK,KAAK,CAACmK,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACjG;EACF;EAEAX,UAAUA,CAAA;IACR,MAAMsC,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAS,CAAE;IACjC,IAAI,CAACzF,iBAAiB,CACjBoE,IAAI,CACH,wBAAwB,EACxBoB,KAAK,EACL,IAAI,CAAC1F,GAAG,CAACoC,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAChC,CACAC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAAClC,QAAQ,GAAGiC,GAAG,CAACI,IAAI,CAACiD,GAAG,CAAEjB,IAAI,KAAM;UACtChL,KAAK,EAAEgL,IAAI,CAACkB,OAAO,GAAG,GAAG,GAAGlB,IAAI,CAACmB,OAAO,GAAG,GAAG,GAAGnB,IAAI,CAACoB,WAAW,GAAG,GAAG,GAAGpB,IAAI,CAACqB,WAAW;UAC1FpM,KAAK,EAAE+K,IAAI,CAACkB,OAAO;UACnBC,OAAO,EAAEnB,IAAI,CAACmB;SACf,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAAClD,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;MAC9C;IACN,CAAC,CAAC;EACJ;EAEA1B,gBAAgBA,CAAA;IACd,MAAMqC,KAAK,GAAG;MAAEO,IAAI,EAAE;IAAI,CAAE;IAC5B,IAAI,CAAC/F,iBAAiB,CACjBoE,IAAI,CACH,iCAAiC,EACjCoB,KAAK,EACL,IAAI,CAAC1F,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAC/B,CACAC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAACnC,eAAe,GAAGkC,GAAG,CAACI,IAAI,CAACiD,GAAG,CAAEjB,IAAI,KAAM;UAC7ChL,KAAK,EAAEgL,IAAI,CAAC3D,QAAQ,GAAG,MAAM,GAAG2D,IAAI,CAACuB,OAAO,GAAG,GAAG,GAAGvB,IAAI,CAACwB,KAAK,GAAG,UAAU,GAAGxB,IAAI,CAACyB,IAAI;UACxFxM,KAAK,EAAE+K,IAAI,CAACpE,EAAE;UACdS,QAAQ,EAAE2D,IAAI,CAAC3D;SAChB,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAAC4B,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;MAC9C;IACN,CAAC,CAAC;EACJ;EAEAsB,mBAAmBA,CAACzM,KAAU;IAC5B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB;MACA,IAAI,CAAC6I,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACgB,QAAQ,CAAC,EAAE,CAAC;MAC/C,IAAI,CAACvB,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACgB,QAAQ,CAAC,EAAE,CAAC;IACjD,CAAC,MAAM;MACL,IAAIsC,KAAK,GAAG,IAAI,CAACjG,eAAe,CAACkG,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAAC/K,KAAK,KAAKA,KAAK,CAAC;MACnE,IAAI,CAAC6I,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACgB,QAAQ,CAACsC,KAAK,CAACtF,QAAQ,CAAC;MAC3D,IAAI,CAACyB,QAAQ,CAACO,QAAQ,CAAC,UAAU,CAAC,CAACgB,QAAQ,CAAC,IAAIL,IAAI,CAAC2C,KAAK,CAACtF,QAAQ,CAAC,CAAC;IACvE;EACF;EAEAwF,WAAWA,CAAC5M,KAAU;IACpB,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB;MACA,IAAI,CAAC6I,QAAQ,CAACO,QAAQ,CAAC,aAAa,CAAC,CAACgB,QAAQ,CAAC,EAAE,CAAC;IACpD,CAAC,MAAM;MACL,IAAIsC,KAAK,GAAG,IAAI,CAAChG,QAAQ,CAACiG,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAAC/K,KAAK,KAAKA,KAAK,CAAC;MAC5D,IAAI,CAAC6I,QAAQ,CAACO,QAAQ,CAAC,aAAa,CAAC,CAACgB,QAAQ,CAACsC,KAAK,CAACR,OAAO,CAAC;IAC/D;EACF;EAEA5C,YAAYA,CAACuD,KAAe;IAC1B,IAAIA,KAAK,EAAE;MACT,IAAI,CAACtG,SAAS,CAACuG,OAAO,CAACC,IAAI,GAAG,CAAC;IACjC;IACA,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAE,IAAI,CAAC1G,SAAS,CAACuG,OAAO,CAACC,IAAI;MACjCV,IAAI,EAAE,IAAI,CAAC9F,SAAS,CAACuG,OAAO,CAACI,KAAK;MAClCC,MAAM,EAAE;QACNtF,WAAW,EAAE;;KAEhB;IACDmF,WAAW,CAAC,MAAM,CAAC,GAAG;MAACI,UAAU,EAAE,IAAI,CAACvE,QAAQ,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAEpJ;IAAK,CAAC;IACvE,IAAGgN,WAAW,CAAC,MAAM,CAAC,CAACI,UAAU,IAAI,IAAI,EAAE;MACzCJ,WAAW,CAAC,MAAM,CAAC,CAACI,UAAU,GAAG,IAAI,CAACzG,EAAE;IAC1C;IACA,IAAG,IAAI,CAACA,EAAE,IAAI,IAAI,EAAE;MAClB,IAAI,CAACA,EAAE,GAAG,IAAI,CAACkC,QAAQ,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAEpJ,KAAK;IAC/C;IACA,IAAI,CAACuG,SAAS,CAAC8G,SAAS,EAAE;IAC1B,IAAI,CAAC/G,iBAAiB,CAACoE,IAAI,CAAC,0BAA0B,EAAEsC,WAAW,EAAE,IAAI,CAAC5G,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;MACnI,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;QACnB,IAAI,CAACrC,SAAS,CAAC+G,SAAS,CAAC3E,GAAG,CAACI,IAAI,CAACwE,OAAO,CAAC;QAC1C,IAAI,CAAC1G,aAAa,GAAG,IAAI,CAACN,SAAS,CAACiH,QAAQ,EAAE,EAAEC,MAAM,CAAC,CAACC,OAAO,EAAEC,OAAO,KAAI;UAC1ED,OAAO,CAACE,IAAI,CAAC;YACXjH,EAAE,EAAEgH,OAAO,CAAChH,EAAE;YACdyG,UAAU,EAAEO,OAAO,CAACP,UAAU;YAC9B1M,SAAS,EAAEiN,OAAO,CAACjN,SAAS;YAC5BS,SAAS,EAAEwM,OAAO,CAACxM,SAAS;YAC5BG,OAAO,EAAEqM,OAAO,CAACrM,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGqM,OAAO,CAACrM,OAAO;YACtDG,KAAK,EAAEkM,OAAO,CAAClM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGkM,OAAO,CAAClM,KAAK;YAChDG,QAAQ,EAAE+L,OAAO,CAAC/L,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG+L,OAAO,CAAC/L,QAAQ;YACzDG,UAAU,EAAE4L,OAAO,CAAC5L,UAAU,IAAI,IAAI,GAAG,CAAC,GAAG4L,OAAO,CAAC5L,UAAU;YAC/DG,QAAQ,EAAEyL,OAAO,CAACzL,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGyL,OAAO,CAACzL,QAAQ;YACzDG,OAAO,EAAEsL,OAAO,CAACtL,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGsL,OAAO,CAACtL,OAAO;YACtDG,KAAK,EAAEmL,OAAO,CAACnL,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGmL,OAAO,CAACnL,KAAK;YAChDG,QAAQ,EAAEgL,OAAO,CAAChL,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGgL,OAAO,CAAChL,QAAQ;YACzDG,UAAU,EAAE6K,OAAO,CAAC7K,UAAU,IAAI,IAAI,GAAG,CAAC,GAAG6K,OAAO,CAAC7K,UAAU;YAC/DG,QAAQ,EAAE0K,OAAO,CAAC1K,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG0K,OAAO,CAAC1K,QAAQ;YACzDgF,OAAO,EAAE0F,OAAO,CAAC1F,OAAO;YACxB7C,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,OAAOqI,OAAO;QAChB,CAAC,EAAE,EAAE,CAAC;QACN,IAAI,CAACnH,SAAS,CAACuG,OAAO,CAACe,KAAK,GAAGlF,GAAG,CAACI,IAAI,CAAC+E,aAAa,CAAC,CAAC;QACvD,IAAG,IAAI,CAACjH,aAAa,CAACkH,MAAM,GAAG,CAAC,EAAC;UAC/B,IAAI,CAACnH,OAAO,GAAG,IAAI;QACrB,CAAC,MAAM;UACL,IAAI,CAACA,OAAO,GAAG,KAAK;QACtB;QACA,IAAI,CAACoH,MAAM,CAAC,IAAI,CAACnH,aAAa,CAAC;MACjC,CAAC,MAAM;QACL,IAAI,CAACmC,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;EAEA6C,MAAMA,CAACC,OAAc;IACnB,IAAI,CAACjI,OAAO,GAAG,EAAE;IACjB,IAAGiI,OAAO,CAACF,MAAM,KAAK,CAAC,EAAC;MACtBE,OAAO,GAAG,IAAI,CAACpH,aAAa;IAC9B;IACA;IACA,IAAIqH,EAAE,GAAGD,OAAO,CAACR,MAAM,CAAC,CAACU,GAAG,EAAEC,GAAG,KAAI;MACnCD,GAAG,CAAC5I,KAAK,GAAG,QAAQ;MACpB4I,GAAG,CAAC1I,UAAU,IAAI2I,GAAG,CAAC9M,OAAO;MAC7B6M,GAAG,CAACxI,QAAQ,IAAIyI,GAAG,CAAC3M,KAAK;MACzB0M,GAAG,CAACvI,WAAW,IAAIwI,GAAG,CAACxM,QAAQ;MAC/BuM,GAAG,CAACtI,aAAa,IAAIuI,GAAG,CAACrM,UAAU;MACnCoM,GAAG,CAACrI,WAAW,IAAIsI,GAAG,CAAClM,QAAQ;MAC/B,OAAOiM,GAAG;IACZ,CAAC,EAAE;MAAE5I,KAAK,EAAE,EAAE;MAAEE,UAAU,EAAE,CAAC;MAAEE,QAAQ,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,CAAC;IAE/F,IAAIuI,EAAE,GAAGJ,OAAO,CAACR,MAAM,CAAC,CAACU,GAAG,EAAEC,GAAG,KAAI;MACnCD,GAAG,CAAC5I,KAAK,GAAG,QAAQ;MACpB4I,GAAG,CAAC1I,UAAU,IAAI2I,GAAG,CAAC/L,OAAO;MAC7B8L,GAAG,CAACxI,QAAQ,IAAIyI,GAAG,CAAC5L,KAAK;MACzB2L,GAAG,CAACvI,WAAW,IAAIwI,GAAG,CAACzL,QAAQ;MAC/BwL,GAAG,CAACtI,aAAa,IAAIuI,GAAG,CAACtL,UAAU;MACnCqL,GAAG,CAACrI,WAAW,IAAIsI,GAAG,CAACnL,QAAQ;MAC/B,OAAOkL,GAAG;IACZ,CAAC,EAAE;MAAE5I,KAAK,EAAE,EAAE;MAAEE,UAAU,EAAE,CAAC;MAAEE,QAAQ,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,CAAC;IAE/F,IAAIwI,GAAG,GAAGL,OAAO,CAACR,MAAM,CAAC,CAACU,GAAG,EAAEC,GAAG,KAAI;MACpCD,GAAG,CAAC5I,KAAK,GAAG,OAAO;MACnB4I,GAAG,CAAC1I,UAAU,IAAI2I,GAAG,CAAC9M,OAAO,GAAG8M,GAAG,CAAC/L,OAAO;MAC3C8L,GAAG,CAACxI,QAAQ,IAAIyI,GAAG,CAAC3M,KAAK,GAAG2M,GAAG,CAAC5L,KAAK;MACrC2L,GAAG,CAACvI,WAAW,IAAIwI,GAAG,CAACxM,QAAQ,GAAGwM,GAAG,CAACzL,QAAQ;MAC9CwL,GAAG,CAACtI,aAAa,IAAIuI,GAAG,CAACrM,UAAU,GAAGqM,GAAG,CAACtL,UAAU;MACpDqL,GAAG,CAACrI,WAAW,IAAIsI,GAAG,CAAClM,QAAQ,GAAGkM,GAAG,CAACnL,QAAQ;MAC9C,OAAOkL,GAAG;IACZ,CAAC,EAAE;MAAE5I,KAAK,EAAE,EAAE;MAAEE,UAAU,EAAE,CAAC;MAAEE,QAAQ,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,CAAC;IAC/F,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuI,MAAM,CAACL,EAAE,CAAC,CAACK,MAAM,CAACF,EAAE,CAAC,CAACE,MAAM,CAACD,GAAG,CAAC;EAC/D;EAEAE,UAAUA,CAAA;IACR;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC3H,aAAa,CAAC+G,IAAI,CACrB;MACEjH,EAAE,EAAE,IAAI;MACRyG,UAAU,EAAE,IAAI,CAACzG,EAAE;MACnBjG,SAAS,EAAE,IAAI;MACfS,SAAS,EAAE,IAAI;MACfG,OAAO,EAAE,IAAI;MACbmN,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBxM,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAE,IAAI;MACbsM,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjB3L,QAAQ,EAAE,IAAI;MACdgF,OAAO,EAAE,IAAI;MACb5C,QAAQ,EAAE,IAAI;MACdD,SAAS,EAAE;KACZ,CAAC;EACN;EAEAD,MAAMA,CAAC0J,IAAS,EAAEhO,KAAU;IAC1B,IAAI,CAACgG,aAAa,CAACiI,MAAM,CAACjO,KAAK,EAAE,CAAC,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA0I,cAAcA,CAACwF,aAAkB;IAC/B,IAAI/B,WAAW,GAAG;MAChBC,IAAI,EAAE,CAAC;MACPZ,IAAI,EAAE,GAAG;MACT2C,KAAK,EAAED,aAAa;MACpBE,SAAS,EAAE,CAAC,GAAG,EAAC,GAAG;KACpB;IACD,IAAI,CAAC3I,iBAAiB,CACnBoE,IAAI,CACH,yBAAyB,EACzBsC,WAAW,EACX,IAAI,CAAC5G,GAAG,CAACoC,WAAW,CAAC,IAAI,CAAC,CAACC,EAAE,CAC9B,CACAC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAAC1H,WAAW,GAAGyH,GAAG,CAACI,IAAI,CAACiD,GAAG,CAAEjB,IAAI,KAAM;UACzChL,KAAK,EAAEgL,IAAI,CAAC5J,SAAS;UACrBnB,KAAK,EAAE+K,IAAI,CAACrK,SAAS;UACrBS,SAAS,EAAE4J,IAAI,CAAC5J,SAAS;UACzB+N,aAAa,EAAEnE,IAAI,CAACmE,aAAa;UACjCC,aAAa,EAAEpE,IAAI,CAACoE,aAAa;UACjCC,gBAAgB,EAAErE,IAAI,CAACqE,gBAAgB;UACvCC,gBAAgB,EAAEtE,IAAI,CAACsE,gBAAgB;UACvCC,KAAK,EAAEvE,IAAI,CAACuE;SACb,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAACtG,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;MAC9C;IACJ,CAAC,CAAC;EACJ;EAEArK,eAAeA,CAACd,KAAU,EAAE6O,IAAS,EAAElF,CAAM;IAC3C,MAAM4F,WAAW,GAAI,IAAI,CAAC1I,aAAa,CAAC2I,IAAI,CAACzE,IAAI,IAAIA,IAAI,KAAK8D,IAAI,IAAI9D,IAAI,CAACrK,SAAS,KAAKV,KAAK,CAAC;IAC/F,IAAGuP,WAAW,EAAC;MACbV,IAAI,CAACnO,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC2F,OAAO,CAAC2D,OAAO,CAAC,WAAW,EAAE;QAChCC,UAAU,EAAE;OACb,CAAC;MACF,IAAI,CAACpD,aAAa,CAACiI,MAAM,CAACnF,CAAC,EAAE,CAAC,CAAC;MAC/B,IAAI,CAAC6E,UAAU,EAAE;MACjB;IACF;IACA,IAAI9B,KAAK,GAAG,IAAI,CAACxL,WAAW,CAACyL,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAAC/K,KAAK,KAAK6O,IAAI,CAACnO,SAAS,CAAC;IACxE,IAAIuN,OAAO,GAAG,IAAI,CAACpH,aAAa,CAAC8F,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAACrK,SAAS,KAAKmO,IAAI,CAACnO,SAAS,CAAC;IAChFuN,OAAO,CAAC,WAAW,CAAC,GAAGvB,KAAK,CAACvL,SAAS;IACtC8M,OAAO,CAAC,eAAe,CAAC,GAAGvB,KAAK,CAACwC,aAAa;IAC9CjB,OAAO,CAAC,eAAe,CAAC,GAAGvB,KAAK,CAACyC,aAAa;IAC9ClB,OAAO,CAAC,kBAAkB,CAAC,GAAGvB,KAAK,CAAC0C,gBAAgB;IACpDnB,OAAO,CAAC,kBAAkB,CAAC,GAAGvB,KAAK,CAAC2C,gBAAgB;IACpDpB,OAAO,CAAC,OAAO,CAAC,GAAGvB,KAAK,CAAC4C,KAAK;EAChC;EAEAG,OAAOA,CAACC,UAAe;IACrB,IAAGA,UAAU,CAAChP,SAAS,IAAI,IAAI,IAAIgP,UAAU,CAAChP,SAAS,CAACiP,IAAI,EAAE,IAAI,EAAE,EAAE;MACpE,IAAI,CAACtJ,OAAO,CAAC2D,OAAO,CAAC,SAAS,EAAE;QACxBC,UAAU,EAAE;OACnB,CAAC;MACF;IACF;IACA,MAAMP,GAAG,GAAG,gBAAgB;IAC5B,IAAI,CAAClK,OAAO,GAAG,IAAI;IACnB,IAAIkQ,UAAU,CAAC/I,EAAE,IAAI,IAAI,EAAE;MACzB,IAAI,CAACL,iBAAiB,CAACoE,IAAI,CAAChB,GAAG,EAAEgG,UAAU,EAAE,IAAI,CAACtJ,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;QAC3G,IAAI,CAACnJ,OAAO,GAAG,KAAK;QACpB,IAAImJ,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACI,SAAS,CAAC7K,aAAa,CAAC8M,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAAC3B,YAAY,CAAC,IAAI,CAAC;QACzB,CAAC,MAAM;UACL,IAAI,CAACN,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC7E,iBAAiB,CAACsJ,GAAG,CAAClG,GAAG,EAAEgG,UAAU,EAAE,IAAI,CAACtJ,GAAG,CAACoC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;QAC1G,IAAI,CAACnJ,OAAO,GAAG,KAAK;QACpB,IAAImJ,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACI,SAAS,CAAC7K,aAAa,CAAC8M,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAAC3B,YAAY,CAAC,IAAI,CAAC;QACzB,CAAC,MAAM;UACL,IAAI,CAACN,SAAS,CAAC7K,aAAa,CAAC8K,KAAK,EAAEN,GAAG,CAACwC,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ;IACA0E,UAAU,CAAC,MAAK;MACd,IAAI,CAACzK,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC4I,MAAM,CAAC,EAAE,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5K,SAASA,CAACyL,IAAS;IACjBA,IAAI,CAACzJ,SAAS,GAAG,CAACyJ,IAAI,CAACzJ,SAAS;IAChCyJ,IAAI,CAACxJ,QAAQ,GAAG,CAACwJ,IAAI,CAACxJ,QAAQ;EAChC;EAEA9B,SAASA,CAACsL,IAAS;IACjB,IAAGA,IAAI,CAACnO,SAAS,IAAI,IAAI,IAAImO,IAAI,CAACnO,SAAS,CAACiP,IAAI,EAAE,IAAI,EAAE,EAAE;MACxD,IAAI,CAACtJ,OAAO,CAAC2D,OAAO,CAAC,SAAS,EAAE;QACxBC,UAAU,EAAE;OACnB,CAAC;MACF;IACF;IACA,IAAI,CAAC7G,SAAS,CAACyL,IAAI,CAAC;EACtB;;;uBAxdW5I,sBAAsB,EAAAzH,EAAA,CAAAsR,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAxR,EAAA,CAAAsR,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA1R,EAAA,CAAAsR,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA5R,EAAA,CAAAsR,iBAAA,CAAAO,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAtBrK,sBAAsB;MAAAsK,SAAA;MAAAC,QAAA,GAAAhS,EAAA,CAAAiS,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCb/BvS,EAFJ,CAAAC,cAAA,iBAAwE,aAC9D,gBACiC;UACrCD,EAAA,CAAAqB,SAAA,kBAA2D;UAC3DrB,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAU,MAAA,GAAoC;;UACzDV,EADyD,CAAAW,YAAA,EAAM,EACtD;UAKTX,EAJA,CAAAuC,UAAA,IAAAkQ,wCAAA,oBAA4E,IAAAC,wCAAA,oBAID;UAG7E1S,EAAA,CAAAW,YAAA,EAAS;UAODX,EALR,CAAAC,cAAA,cAA+D,cAC7B,cAEmB,oBACjC,wBACoC;UAAAD,EAAA,CAAAU,MAAA,IAAiC;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAE/FX,EADF,CAAAC,cAAA,uBAAiB,qBAE8C;UAA3DD,EAAA,CAAAE,UAAA,2BAAAyS,oEAAA9Q,MAAA;YAAA7B,EAAA,CAAAI,aAAA,CAAAwS,GAAA;YAAA,OAAA5S,EAAA,CAAAQ,WAAA,CAAiBgS,GAAA,CAAAvE,mBAAA,CAAApM,MAAA,CAA2B;UAAA,EAAC;UAC7C7B,EAAA,CAAAuC,UAAA,KAAAsQ,4CAAA,wBAAoG;UAK5G7S,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,cAAiD,oBACjC,wBACoC;UAAAD,EAAA,CAAAU,MAAA,IAAkC;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAEhGX,EADF,CAAAC,cAAA,uBAAiB,qBAEsC;UAAnDD,EAAA,CAAAE,UAAA,2BAAA4S,oEAAAjR,MAAA;YAAA7B,EAAA,CAAAI,aAAA,CAAAwS,GAAA;YAAA,OAAA5S,EAAA,CAAAQ,WAAA,CAAiBgS,GAAA,CAAApE,WAAA,CAAAvM,MAAA,CAAmB;UAAA,EAAC;UACrC7B,EAAA,CAAAuC,UAAA,KAAAwQ,4CAAA,wBAA6F;UAKrG/S,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAwB,oBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAA4B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAChFX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,oBACkF;;UAK5FrB,EAJQ,CAAAW,YAAA,EAAkB,EACL,EACX,EACF,EACD;UAIDX,EAHN,CAAAC,cAAA,UAAI,eACuB,WAClB,kBACqE;UAAvBD,EAAA,CAAAE,UAAA,mBAAA8S,yDAAA;YAAAhT,EAAA,CAAAI,aAAA,CAAAwS,GAAA;YAAA,OAAA5S,EAAA,CAAAQ,WAAA,CAASgS,GAAA,CAAAxC,UAAA,EAAY;UAAA,EAAC;UACrEhQ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAIhBV,EAJgB,CAAAW,YAAA,EAAO,EACR,EACL,EACF,EACH;UAWGX,EAVR,CAAAC,cAAA,uBAOG,iBACyE,UAClE,cACwB;UAAAD,EAAA,CAAAU,MAAA,IAA0B;;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACzDX,EAAA,CAAAC,cAAA,cAAqC;UAAAD,EAAA,CAAAU,MAAA,0BAAG;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAC7CX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,sCAAK;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAE/BX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,8CAAQ;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,iDAAW;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACrCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,0DAAU;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACpCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,0DAAU;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACpCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,8CAAQ;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,8CAAQ;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,iDAAW;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACrCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,0DAAU;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACpCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,0DAAU;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACpCX,EAAA,CAAAC,cAAA,cAAqB;UAAAD,EAAA,CAAAU,MAAA,8CAAQ;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClCX,EAAA,CAAAC,cAAA,cAA4C;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAElDV,EAFkD,CAAAW,YAAA,EAAK,EAChD,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuC,UAAA,KAAA0Q,qCAAA,mBAAsD;UA2F1DjT,EADE,CAAAW,YAAA,EAAQ,EACC;UACXX,EAAA,CAAAuC,UAAA,KAAA2Q,sCAAA,kBAAqB;UAczBlT,EAAA,CAAAW,YAAA,EAAU;;;UAvMyBX,EAAA,CAAAe,UAAA,gBAAAf,EAAA,CAAAmT,eAAA,KAAAC,GAAA,EAAoC;UAGvDpT,EAAA,CAAAc,SAAA,GAAqC;UAArCd,EAAA,CAAAe,UAAA,sCAAqC;UAC5Bf,EAAA,CAAAc,SAAA,GAAoC;UAApCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,8BAAoC;UAEhBlB,EAAA,CAAAc,SAAA,GAAiC;UAAjCd,EAAA,CAAAe,UAAA,UAAAyR,GAAA,CAAArF,mBAAA,QAAiC;UAIjCnN,EAAA,CAAAc,SAAA,EAAgC;UAAhCd,EAAA,CAAAe,UAAA,SAAAyR,GAAA,CAAArF,mBAAA,QAAgC;UAKnCnN,EAAA,CAAAc,SAAA,EAAsB;UAAtBd,EAAA,CAAAe,UAAA,cAAAyR,GAAA,CAAAnI,QAAA,CAAsB;UAChDrK,EAAA,CAAAc,SAAA,EAAmB;UAAnBd,EAAA,CAAAe,UAAA,aAAAf,EAAA,CAAAmT,eAAA,KAAAE,GAAA,EAAmB;UAIuBrT,EAAA,CAAAc,SAAA,GAAiC;UAAjCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,0BAAiC;UAErClB,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,sBAAsB;UAEvDf,EAAA,CAAAc,SAAA,EAAkB;UAAlBd,EAAA,CAAAe,UAAA,YAAAyR,GAAA,CAAAvK,eAAA,CAAkB;UASJjI,EAAA,CAAAc,SAAA,GAAkC;UAAlCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,2BAAkC;UAEvClB,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,sBAAsB;UAEtDf,EAAA,CAAAc,SAAA,EAAW;UAAXd,EAAA,CAAAe,UAAA,YAAAyR,GAAA,CAAAtK,QAAA,CAAW;UAUTlI,EAAA,CAAAc,SAAA,GAA4B;UAA5Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA4B;UAE3ClB,EAAA,CAAAc,SAAA,GAA0C;UAA1Cd,EAAA,CAAAsT,qBAAA,gBAAAtT,EAAA,CAAAkB,WAAA,uBAA0C;UAAClB,EAAA,CAAAe,UAAA,aAAAyR,GAAA,CAAArF,mBAAA,QAAuC;UAkB7GnN,EAAA,CAAAc,SAAA,GAAmB;UAInBd,EAJA,CAAAe,UAAA,oBAAmB,4BACQ,2BACD,WAAAyR,GAAA,CAAAzK,SAAA,CAAAiH,QAAA,GACK,aAAAhP,EAAA,CAAAmT,eAAA,KAAAI,GAAA,EACH;UAIIvT,EAAA,CAAAc,SAAA,GAA0B;UAA1Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,mBAA0B;UAkBjClB,EAAA,CAAAc,SAAA,IAAkB;UAAlBd,EAAA,CAAAe,UAAA,YAAAyR,GAAA,CAAAnK,aAAA,CAAkB;UA4FrCrI,EAAA,CAAAc,SAAA,EAAa;UAAbd,EAAA,CAAAe,UAAA,SAAAyR,GAAA,CAAApK,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}