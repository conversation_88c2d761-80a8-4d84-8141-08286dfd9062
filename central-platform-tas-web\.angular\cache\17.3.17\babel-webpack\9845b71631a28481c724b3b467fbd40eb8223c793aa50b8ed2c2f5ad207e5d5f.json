{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['πΧ', 'μΧ'],\n  abbreviated: ['π.Χ.', 'μ.Χ.'],\n  wide: ['προ Χριστού', 'μετά Χριστόν']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Τ1', 'Τ2', 'Τ3', 'Τ4'],\n  wide: ['1ο τρίμηνο', '2ο τρίμηνο', '3ο τρίμηνο', '4ο τρίμηνο']\n};\nvar monthValues = {\n  narrow: ['Ι', 'Φ', 'Μ', 'Α', 'Μ', 'Ι', 'Ι', 'Α', 'Σ', 'Ο', 'Ν', 'Δ'],\n  abbreviated: ['Ιαν', 'Φεβ', 'Μάρ', 'Απρ', 'Μάι', 'Ιούν', 'Ιούλ', 'Αύγ', 'Σεπ', 'Οκτ', 'Νοέ', 'Δεκ'],\n  wide: ['Ιανουάριος', 'Φεβρουάριος', 'Μάρτιος', 'Απρίλιος', 'Μάιος', 'Ιούνιος', 'Ιούλιος', 'Αύγουστος', 'Σεπτέμβριος', 'Οκτώβριος', 'Νοέμβριος', 'Δεκέμβριος']\n};\nvar formattingMonthValues = {\n  narrow: ['Ι', 'Φ', 'Μ', 'Α', 'Μ', 'Ι', 'Ι', 'Α', 'Σ', 'Ο', 'Ν', 'Δ'],\n  abbreviated: ['Ιαν', 'Φεβ', 'Μαρ', 'Απρ', 'Μαΐ', 'Ιουν', 'Ιουλ', 'Αυγ', 'Σεπ', 'Οκτ', 'Νοε', 'Δεκ'],\n  wide: ['Ιανουαρίου', 'Φεβρουαρίου', 'Μαρτίου', 'Απριλίου', 'Μαΐου', 'Ιουνίου', 'Ιουλίου', 'Αυγούστου', 'Σεπτεμβρίου', 'Οκτωβρίου', 'Νοεμβρίου', 'Δεκεμβρίου']\n};\nvar dayValues = {\n  narrow: ['Κ', 'Δ', 'T', 'Τ', 'Π', 'Π', 'Σ'],\n  short: ['Κυ', 'Δε', 'Τρ', 'Τε', 'Πέ', 'Πα', 'Σά'],\n  abbreviated: ['Κυρ', 'Δευ', 'Τρί', 'Τετ', 'Πέμ', 'Παρ', 'Σάβ'],\n  wide: ['Κυριακή', 'Δευτέρα', 'Τρίτη', 'Τετάρτη', 'Πέμπτη', 'Παρασκευή', 'Σάββατο']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'πμ',\n    pm: 'μμ',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  },\n  abbreviated: {\n    am: 'π.μ.',\n    pm: 'μ.μ.',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  },\n  wide: {\n    am: 'π.μ.',\n    pm: 'μ.μ.',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n  if (unit === 'year' || unit === 'month') {\n    suffix = 'ος';\n  } else if (unit === 'week' || unit === 'dayOfYear' || unit === 'day' || unit === 'hour' || unit === 'date') {\n    suffix = 'η';\n  } else {\n    suffix = 'ο';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/el/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['πΧ', 'μΧ'],\n  abbreviated: ['π.Χ.', 'μ.Χ.'],\n  wide: ['προ Χριστού', 'μετά Χριστόν']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Τ1', 'Τ2', 'Τ3', 'Τ4'],\n  wide: ['1ο τρίμηνο', '2ο τρίμηνο', '3ο τρίμηνο', '4ο τρίμηνο']\n};\nvar monthValues = {\n  narrow: ['Ι', 'Φ', 'Μ', 'Α', 'Μ', 'Ι', 'Ι', 'Α', 'Σ', 'Ο', 'Ν', 'Δ'],\n  abbreviated: ['Ιαν', 'Φεβ', 'Μάρ', 'Απρ', 'Μάι', 'Ιούν', 'Ιούλ', 'Αύγ', 'Σεπ', 'Οκτ', 'Νοέ', 'Δεκ'],\n  wide: ['Ιανουάριος', 'Φεβρουάριος', 'Μάρτιος', 'Απρίλιος', 'Μάιος', 'Ιούνιος', 'Ιούλιος', 'Αύγουστος', 'Σεπτέμβριος', 'Οκτώβριος', 'Νοέμβριος', 'Δεκέμβριος']\n};\nvar formattingMonthValues = {\n  narrow: ['Ι', 'Φ', 'Μ', 'Α', 'Μ', 'Ι', 'Ι', 'Α', 'Σ', 'Ο', 'Ν', 'Δ'],\n  abbreviated: ['Ιαν', 'Φεβ', 'Μαρ', 'Απρ', 'Μαΐ', 'Ιουν', 'Ιουλ', 'Αυγ', 'Σεπ', 'Οκτ', 'Νοε', 'Δεκ'],\n  wide: ['Ιανουαρίου', 'Φεβρουαρίου', 'Μαρτίου', 'Απριλίου', 'Μαΐου', 'Ιουνίου', 'Ιουλίου', 'Αυγούστου', 'Σεπτεμβρίου', 'Οκτωβρίου', 'Νοεμβρίου', 'Δεκεμβρίου']\n};\nvar dayValues = {\n  narrow: ['Κ', 'Δ', 'T', 'Τ', 'Π', 'Π', 'Σ'],\n  short: ['Κυ', 'Δε', 'Τρ', 'Τε', 'Πέ', 'Πα', 'Σά'],\n  abbreviated: ['Κυρ', 'Δευ', 'Τρί', 'Τετ', 'Πέμ', 'Παρ', 'Σάβ'],\n  wide: ['Κυριακή', 'Δευτέρα', 'Τρίτη', 'Τετάρτη', 'Πέμπτη', 'Παρασκευή', 'Σάββατο']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'πμ',\n    pm: 'μμ',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  },\n  abbreviated: {\n    am: 'π.μ.',\n    pm: 'μ.μ.',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  },\n  wide: {\n    am: 'π.μ.',\n    pm: 'μ.μ.',\n    midnight: 'μεσάνυχτα',\n    noon: 'μεσημέρι',\n    morning: 'πρωί',\n    afternoon: 'απόγευμα',\n    evening: 'βράδυ',\n    night: 'νύχτα'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n  if (unit === 'year' || unit === 'month') {\n    suffix = 'ος';\n  } else if (unit === 'week' || unit === 'dayOfYear' || unit === 'day' || unit === 'hour' || unit === 'date') {\n    suffix = 'η';\n  } else {\n    suffix = 'ο';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc;AACtC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACnGC,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;AAC9J,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACnGC,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;AAC9J,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS;AACnF,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,IAAI,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;EACzE,IAAIC,MAAM;EACV,IAAID,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;IACvCC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM,IAAID,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC1GC,MAAM,GAAG,GAAG;EACd,CAAC,MAAM;IACLA,MAAM,GAAG,GAAG;EACd;EACA,OAAOH,MAAM,GAAGG,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbP,aAAa,EAAEA,aAAa;EAC5BQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE1B,qBAAqB;IACvC2B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAEnC,eAAe,CAAC;IACnB4B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAEpC,eAAe,CAAC;IACzB4B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}