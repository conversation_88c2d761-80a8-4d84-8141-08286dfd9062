{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { InformationComponent } from './information.component';\nimport { InformationRoutingModule } from './information-routing.module';\nimport { InformationEditComponent } from '@business/tas/information/information-edit/information-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [InformationComponent, InformationEditComponent];\nexport class InformationModule {\n  static {\n    this.ɵfac = function InformationModule_Factory(t) {\n      return new (t || InformationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: InformationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, InformationRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InformationModule, {\n    declarations: [InformationComponent, InformationEditComponent],\n    imports: [SharedModule, InformationRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "InformationComponent", "InformationRoutingModule", "InformationEditComponent", "COMPONENTS", "InformationModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\information\\information.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { InformationComponent } from './information.component';\r\nimport { InformationRoutingModule } from './information-routing.module';\r\nimport {InformationEditComponent} from '@business/tas/information/information-edit/information-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  InformationComponent,\r\n  InformationEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, InformationRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class InformationModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}