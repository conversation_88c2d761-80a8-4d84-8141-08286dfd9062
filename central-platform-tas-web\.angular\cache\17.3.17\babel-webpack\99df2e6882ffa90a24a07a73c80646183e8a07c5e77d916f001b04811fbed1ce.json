{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VesselMessageComponent } from './vesselmessage.component';\nimport { VesselMessageEditComponent } from '@business/tas/vesselmessage/vesselmessage-edit/vesselmessage-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: VesselMessageComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: VesselMessageEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class VesselMessageRoutingModule {\n  static {\n    this.ɵfac = function VesselMessageRoutingModule_Factory(t) {\n      return new (t || VesselMessageRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselMessageRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselMessageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VesselMessageComponent", "VesselMessageEditComponent", "routes", "path", "component", "data", "cache", "VesselMessageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vesselmessage\\vesselmessage-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VesselMessageComponent } from './vesselmessage.component';\r\nimport { VesselMessageEditComponent } from '@business/tas/vesselmessage/vesselmessage-edit/vesselmessage-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: VesselMessageComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: VesselMessageEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class VesselMessageRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,0BAA0B,QAAQ,6EAA6E;;;AACxH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,sBAAsB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EAC1E;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,0BAA0B;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACpF;AAMD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAF3BZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}