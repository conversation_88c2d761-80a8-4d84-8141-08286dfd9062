{"ast": null, "code": "'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\nvar utils = require('../utils/common');\nvar MAXBITS = 15;\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH = (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\nvar lbase = [/* Length codes 257..285 base */\n3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0];\nvar lext = [/* Length codes 257..285 extra */\n16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78];\nvar dbase = [/* Distance codes 0..29 base */\n1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 0, 0];\nvar dext = [/* Distance codes 0..29 extra */\n16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29, 64, 64];\nmodule.exports = function inflate_table(type, lens, lens_index, codes, table, table_index, work, opts) {\n  var bits = opts.bits;\n  //here = opts.here; /* table entry for duplication */\n\n  var len = 0; /* a code's length in bits */\n  var sym = 0; /* index of code symbols */\n  var min = 0,\n    max = 0; /* minimum and maximum code lengths */\n  var root = 0; /* number of index bits for root table */\n  var curr = 0; /* number of index bits for current table */\n  var drop = 0; /* code bits to drop for sub-table */\n  var left = 0; /* number of prefix codes available */\n  var used = 0; /* code entries in table used */\n  var huff = 0; /* Huffman code */\n  var incr; /* for incrementing code, index */\n  var fill; /* index for replicating entries */\n  var low; /* low bits for current root entry */\n  var mask; /* mask for low root bits */\n  var next; /* next available space in table */\n  var base = null; /* base value table to use */\n  var base_index = 0;\n  //  var shoextra;    /* extra bits table to use */\n  var end; /* use base and extra for symbol > end */\n  var count = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];    /* number of codes of each length */\n  var offs = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];     /* offsets in table for each length */\n  var extra = null;\n  var extra_index = 0;\n  var here_bits, here_op, here_val;\n\n  /*\n   Process a set of code lengths to create a canonical Huffman code.  The\n   code lengths are lens[0..codes-1].  Each length corresponds to the\n   symbols 0..codes-1.  The Huffman code is generated by first sorting the\n   symbols by length from short to long, and retaining the symbol order\n   for codes with equal lengths.  Then the code starts with all zero bits\n   for the first code of the shortest length, and the codes are integer\n   increments for the same length, and zeros are appended as the length\n   increases.  For the deflate format, these bits are stored backwards\n   from their more natural integer increment ordering, and so when the\n   decoding tables are built in the large loop below, the integer codes\n   are incremented backwards.\n    This routine assumes, but does not check, that all of the entries in\n   lens[] are in the range 0..MAXBITS.  The caller must assure this.\n   1..MAXBITS is interpreted as that code length.  zero means that that\n   symbol does not occur in this code.\n    The codes are sorted by computing a count of codes for each length,\n   creating from that a table of starting indices for each length in the\n   sorted table, and then entering the symbols in order in the sorted\n   table.  The sorted table is work[], with that space being provided by\n   the caller.\n    The length counts are used for other purposes as well, i.e. finding\n   the minimum and maximum length codes, determining if there are any\n   codes at all, checking for a valid set of lengths, and looking ahead\n   at length counts to determine sub-table sizes when building the\n   decoding tables.\n   */\n\n  /* accumulate lengths for codes (assumes lens[] all in 0..MAXBITS) */\n  for (len = 0; len <= MAXBITS; len++) {\n    count[len] = 0;\n  }\n  for (sym = 0; sym < codes; sym++) {\n    count[lens[lens_index + sym]]++;\n  }\n\n  /* bound code lengths, force root to be within code lengths */\n  root = bits;\n  for (max = MAXBITS; max >= 1; max--) {\n    if (count[max] !== 0) {\n      break;\n    }\n  }\n  if (root > max) {\n    root = max;\n  }\n  if (max === 0) {\n    /* no symbols to code at all */\n    //table.op[opts.table_index] = 64;  //here.op = (var char)64;    /* invalid code marker */\n    //table.bits[opts.table_index] = 1;   //here.bits = (var char)1;\n    //table.val[opts.table_index++] = 0;   //here.val = (var short)0;\n    table[table_index++] = 1 << 24 | 64 << 16 | 0;\n\n    //table.op[opts.table_index] = 64;\n    //table.bits[opts.table_index] = 1;\n    //table.val[opts.table_index++] = 0;\n    table[table_index++] = 1 << 24 | 64 << 16 | 0;\n    opts.bits = 1;\n    return 0; /* no symbols, but wait for decoding to report error */\n  }\n  for (min = 1; min < max; min++) {\n    if (count[min] !== 0) {\n      break;\n    }\n  }\n  if (root < min) {\n    root = min;\n  }\n\n  /* check for an over-subscribed or incomplete set of lengths */\n  left = 1;\n  for (len = 1; len <= MAXBITS; len++) {\n    left <<= 1;\n    left -= count[len];\n    if (left < 0) {\n      return -1;\n    } /* over-subscribed */\n  }\n  if (left > 0 && (type === CODES || max !== 1)) {\n    return -1; /* incomplete set */\n  }\n\n  /* generate offsets into symbol table for each length for sorting */\n  offs[1] = 0;\n  for (len = 1; len < MAXBITS; len++) {\n    offs[len + 1] = offs[len] + count[len];\n  }\n\n  /* sort symbols by length, by symbol order within each length */\n  for (sym = 0; sym < codes; sym++) {\n    if (lens[lens_index + sym] !== 0) {\n      work[offs[lens[lens_index + sym]]++] = sym;\n    }\n  }\n\n  /*\n   Create and fill in decoding tables.  In this loop, the table being\n   filled is at next and has curr index bits.  The code being used is huff\n   with length len.  That code is converted to an index by dropping drop\n   bits off of the bottom.  For codes where len is less than drop + curr,\n   those top drop + curr - len bits are incremented through all values to\n   fill the table with replicated entries.\n    root is the number of index bits for the root table.  When len exceeds\n   root, sub-tables are created pointed to by the root entry with an index\n   of the low root bits of huff.  This is saved in low to check for when a\n   new sub-table should be started.  drop is zero when the root table is\n   being filled, and drop is root when sub-tables are being filled.\n    When a new sub-table is needed, it is necessary to look ahead in the\n   code lengths to determine what size sub-table is needed.  The length\n   counts are used for this, and so count[] is decremented as codes are\n   entered in the tables.\n    used keeps track of how many table entries have been allocated from the\n   provided *table space.  It is checked for LENS and DIST tables against\n   the constants ENOUGH_LENS and ENOUGH_DISTS to guard against changes in\n   the initial root table size constants.  See the comments in inftrees.h\n   for more information.\n    sym increments through all symbols, and the loop terminates when\n   all codes of length max, i.e. all codes, have been processed.  This\n   routine permits incomplete codes, so another loop after this one fills\n   in the rest of the decoding tables with invalid code markers.\n   */\n\n  /* set up for code type */\n  // poor man optimization - use if-else instead of switch,\n  // to avoid deopts in old v8\n  if (type === CODES) {\n    base = extra = work; /* dummy value--not used */\n    end = 19;\n  } else if (type === LENS) {\n    base = lbase;\n    base_index -= 257;\n    extra = lext;\n    extra_index -= 257;\n    end = 256;\n  } else {\n    /* DISTS */\n    base = dbase;\n    extra = dext;\n    end = -1;\n  }\n\n  /* initialize opts for loop */\n  huff = 0; /* starting code */\n  sym = 0; /* starting code symbol */\n  len = min; /* starting code length */\n  next = table_index; /* current table to fill in */\n  curr = root; /* current table index bits */\n  drop = 0; /* current bits to drop from code for index */\n  low = -1; /* trigger new sub-table when len > root */\n  used = 1 << root; /* use root table entries */\n  mask = used - 1; /* mask for comparing low */\n\n  /* check available table space */\n  if (type === LENS && used > ENOUGH_LENS || type === DISTS && used > ENOUGH_DISTS) {\n    return 1;\n  }\n\n  /* process all codes and make table entries */\n  for (;;) {\n    /* create table entry */\n    here_bits = len - drop;\n    if (work[sym] < end) {\n      here_op = 0;\n      here_val = work[sym];\n    } else if (work[sym] > end) {\n      here_op = extra[extra_index + work[sym]];\n      here_val = base[base_index + work[sym]];\n    } else {\n      here_op = 32 + 64; /* end of block */\n      here_val = 0;\n    }\n\n    /* replicate for those indices with low len bits equal to huff */\n    incr = 1 << len - drop;\n    fill = 1 << curr;\n    min = fill; /* save offset to next table */\n    do {\n      fill -= incr;\n      table[next + (huff >> drop) + fill] = here_bits << 24 | here_op << 16 | here_val | 0;\n    } while (fill !== 0);\n\n    /* backwards increment the len-bit code huff */\n    incr = 1 << len - 1;\n    while (huff & incr) {\n      incr >>= 1;\n    }\n    if (incr !== 0) {\n      huff &= incr - 1;\n      huff += incr;\n    } else {\n      huff = 0;\n    }\n\n    /* go to next symbol, update count, len */\n    sym++;\n    if (--count[len] === 0) {\n      if (len === max) {\n        break;\n      }\n      len = lens[lens_index + work[sym]];\n    }\n\n    /* create new sub-table if needed */\n    if (len > root && (huff & mask) !== low) {\n      /* if first time, transition to sub-tables */\n      if (drop === 0) {\n        drop = root;\n      }\n\n      /* increment past last table */\n      next += min; /* here min is 1 << curr */\n\n      /* determine length of next table */\n      curr = len - drop;\n      left = 1 << curr;\n      while (curr + drop < max) {\n        left -= count[curr + drop];\n        if (left <= 0) {\n          break;\n        }\n        curr++;\n        left <<= 1;\n      }\n\n      /* check for enough space */\n      used += 1 << curr;\n      if (type === LENS && used > ENOUGH_LENS || type === DISTS && used > ENOUGH_DISTS) {\n        return 1;\n      }\n\n      /* point entry in root table to sub-table */\n      low = huff & mask;\n      /*table.op[low] = curr;\n      table.bits[low] = root;\n      table.val[low] = next - opts.table_index;*/\n      table[low] = root << 24 | curr << 16 | next - table_index | 0;\n    }\n  }\n\n  /* fill in remaining table entry if code is incomplete (guaranteed to have\n   at most one remaining entry, since if the code is incomplete, the\n   maximum code length that was allowed to get this far is one bit) */\n  if (huff !== 0) {\n    //table.op[next + huff] = 64;            /* invalid code marker */\n    //table.bits[next + huff] = len - drop;\n    //table.val[next + huff] = 0;\n    table[next + huff] = len - drop << 24 | 64 << 16 | 0;\n  }\n\n  /* set return parameters */\n  //opts.table_index += used;\n  opts.bits = root;\n  return 0;\n};", "map": {"version": 3, "names": ["utils", "require", "MAXBITS", "ENOUGH_LENS", "ENOUGH_DISTS", "CODES", "LENS", "DISTS", "lbase", "lext", "dbase", "dext", "module", "exports", "inflate_table", "type", "lens", "lens_index", "codes", "table", "table_index", "work", "opts", "bits", "len", "sym", "min", "max", "root", "curr", "drop", "left", "used", "huff", "incr", "fill", "low", "mask", "next", "base", "base_index", "end", "count", "Buf16", "offs", "extra", "extra_index", "here_bits", "here_op", "here_val"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/zlib/inftrees.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nvar utils = require('../utils/common');\n\nvar MAXBITS = 15;\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH = (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\n\nvar lbase = [ /* Length codes 257..285 base */\n  3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31,\n  35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0\n];\n\nvar lext = [ /* Length codes 257..285 extra */\n  16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18,\n  19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78\n];\n\nvar dbase = [ /* Distance codes 0..29 base */\n  1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,\n  257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,\n  8193, 12289, 16385, 24577, 0, 0\n];\n\nvar dext = [ /* Distance codes 0..29 extra */\n  16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22,\n  23, 23, 24, 24, 25, 25, 26, 26, 27, 27,\n  28, 28, 29, 29, 64, 64\n];\n\nmodule.exports = function inflate_table(type, lens, lens_index, codes, table, table_index, work, opts)\n{\n  var bits = opts.bits;\n      //here = opts.here; /* table entry for duplication */\n\n  var len = 0;               /* a code's length in bits */\n  var sym = 0;               /* index of code symbols */\n  var min = 0, max = 0;          /* minimum and maximum code lengths */\n  var root = 0;              /* number of index bits for root table */\n  var curr = 0;              /* number of index bits for current table */\n  var drop = 0;              /* code bits to drop for sub-table */\n  var left = 0;                   /* number of prefix codes available */\n  var used = 0;              /* code entries in table used */\n  var huff = 0;              /* Huffman code */\n  var incr;              /* for incrementing code, index */\n  var fill;              /* index for replicating entries */\n  var low;               /* low bits for current root entry */\n  var mask;              /* mask for low root bits */\n  var next;             /* next available space in table */\n  var base = null;     /* base value table to use */\n  var base_index = 0;\n//  var shoextra;    /* extra bits table to use */\n  var end;                    /* use base and extra for symbol > end */\n  var count = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];    /* number of codes of each length */\n  var offs = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];     /* offsets in table for each length */\n  var extra = null;\n  var extra_index = 0;\n\n  var here_bits, here_op, here_val;\n\n  /*\n   Process a set of code lengths to create a canonical Huffman code.  The\n   code lengths are lens[0..codes-1].  Each length corresponds to the\n   symbols 0..codes-1.  The Huffman code is generated by first sorting the\n   symbols by length from short to long, and retaining the symbol order\n   for codes with equal lengths.  Then the code starts with all zero bits\n   for the first code of the shortest length, and the codes are integer\n   increments for the same length, and zeros are appended as the length\n   increases.  For the deflate format, these bits are stored backwards\n   from their more natural integer increment ordering, and so when the\n   decoding tables are built in the large loop below, the integer codes\n   are incremented backwards.\n\n   This routine assumes, but does not check, that all of the entries in\n   lens[] are in the range 0..MAXBITS.  The caller must assure this.\n   1..MAXBITS is interpreted as that code length.  zero means that that\n   symbol does not occur in this code.\n\n   The codes are sorted by computing a count of codes for each length,\n   creating from that a table of starting indices for each length in the\n   sorted table, and then entering the symbols in order in the sorted\n   table.  The sorted table is work[], with that space being provided by\n   the caller.\n\n   The length counts are used for other purposes as well, i.e. finding\n   the minimum and maximum length codes, determining if there are any\n   codes at all, checking for a valid set of lengths, and looking ahead\n   at length counts to determine sub-table sizes when building the\n   decoding tables.\n   */\n\n  /* accumulate lengths for codes (assumes lens[] all in 0..MAXBITS) */\n  for (len = 0; len <= MAXBITS; len++) {\n    count[len] = 0;\n  }\n  for (sym = 0; sym < codes; sym++) {\n    count[lens[lens_index + sym]]++;\n  }\n\n  /* bound code lengths, force root to be within code lengths */\n  root = bits;\n  for (max = MAXBITS; max >= 1; max--) {\n    if (count[max] !== 0) { break; }\n  }\n  if (root > max) {\n    root = max;\n  }\n  if (max === 0) {                     /* no symbols to code at all */\n    //table.op[opts.table_index] = 64;  //here.op = (var char)64;    /* invalid code marker */\n    //table.bits[opts.table_index] = 1;   //here.bits = (var char)1;\n    //table.val[opts.table_index++] = 0;   //here.val = (var short)0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n\n    //table.op[opts.table_index] = 64;\n    //table.bits[opts.table_index] = 1;\n    //table.val[opts.table_index++] = 0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n    opts.bits = 1;\n    return 0;     /* no symbols, but wait for decoding to report error */\n  }\n  for (min = 1; min < max; min++) {\n    if (count[min] !== 0) { break; }\n  }\n  if (root < min) {\n    root = min;\n  }\n\n  /* check for an over-subscribed or incomplete set of lengths */\n  left = 1;\n  for (len = 1; len <= MAXBITS; len++) {\n    left <<= 1;\n    left -= count[len];\n    if (left < 0) {\n      return -1;\n    }        /* over-subscribed */\n  }\n  if (left > 0 && (type === CODES || max !== 1)) {\n    return -1;                      /* incomplete set */\n  }\n\n  /* generate offsets into symbol table for each length for sorting */\n  offs[1] = 0;\n  for (len = 1; len < MAXBITS; len++) {\n    offs[len + 1] = offs[len] + count[len];\n  }\n\n  /* sort symbols by length, by symbol order within each length */\n  for (sym = 0; sym < codes; sym++) {\n    if (lens[lens_index + sym] !== 0) {\n      work[offs[lens[lens_index + sym]]++] = sym;\n    }\n  }\n\n  /*\n   Create and fill in decoding tables.  In this loop, the table being\n   filled is at next and has curr index bits.  The code being used is huff\n   with length len.  That code is converted to an index by dropping drop\n   bits off of the bottom.  For codes where len is less than drop + curr,\n   those top drop + curr - len bits are incremented through all values to\n   fill the table with replicated entries.\n\n   root is the number of index bits for the root table.  When len exceeds\n   root, sub-tables are created pointed to by the root entry with an index\n   of the low root bits of huff.  This is saved in low to check for when a\n   new sub-table should be started.  drop is zero when the root table is\n   being filled, and drop is root when sub-tables are being filled.\n\n   When a new sub-table is needed, it is necessary to look ahead in the\n   code lengths to determine what size sub-table is needed.  The length\n   counts are used for this, and so count[] is decremented as codes are\n   entered in the tables.\n\n   used keeps track of how many table entries have been allocated from the\n   provided *table space.  It is checked for LENS and DIST tables against\n   the constants ENOUGH_LENS and ENOUGH_DISTS to guard against changes in\n   the initial root table size constants.  See the comments in inftrees.h\n   for more information.\n\n   sym increments through all symbols, and the loop terminates when\n   all codes of length max, i.e. all codes, have been processed.  This\n   routine permits incomplete codes, so another loop after this one fills\n   in the rest of the decoding tables with invalid code markers.\n   */\n\n  /* set up for code type */\n  // poor man optimization - use if-else instead of switch,\n  // to avoid deopts in old v8\n  if (type === CODES) {\n    base = extra = work;    /* dummy value--not used */\n    end = 19;\n\n  } else if (type === LENS) {\n    base = lbase;\n    base_index -= 257;\n    extra = lext;\n    extra_index -= 257;\n    end = 256;\n\n  } else {                    /* DISTS */\n    base = dbase;\n    extra = dext;\n    end = -1;\n  }\n\n  /* initialize opts for loop */\n  huff = 0;                   /* starting code */\n  sym = 0;                    /* starting code symbol */\n  len = min;                  /* starting code length */\n  next = table_index;              /* current table to fill in */\n  curr = root;                /* current table index bits */\n  drop = 0;                   /* current bits to drop from code for index */\n  low = -1;                   /* trigger new sub-table when len > root */\n  used = 1 << root;          /* use root table entries */\n  mask = used - 1;            /* mask for comparing low */\n\n  /* check available table space */\n  if ((type === LENS && used > ENOUGH_LENS) ||\n    (type === DISTS && used > ENOUGH_DISTS)) {\n    return 1;\n  }\n\n  /* process all codes and make table entries */\n  for (;;) {\n    /* create table entry */\n    here_bits = len - drop;\n    if (work[sym] < end) {\n      here_op = 0;\n      here_val = work[sym];\n    }\n    else if (work[sym] > end) {\n      here_op = extra[extra_index + work[sym]];\n      here_val = base[base_index + work[sym]];\n    }\n    else {\n      here_op = 32 + 64;         /* end of block */\n      here_val = 0;\n    }\n\n    /* replicate for those indices with low len bits equal to huff */\n    incr = 1 << (len - drop);\n    fill = 1 << curr;\n    min = fill;                 /* save offset to next table */\n    do {\n      fill -= incr;\n      table[next + (huff >> drop) + fill] = (here_bits << 24) | (here_op << 16) | here_val |0;\n    } while (fill !== 0);\n\n    /* backwards increment the len-bit code huff */\n    incr = 1 << (len - 1);\n    while (huff & incr) {\n      incr >>= 1;\n    }\n    if (incr !== 0) {\n      huff &= incr - 1;\n      huff += incr;\n    } else {\n      huff = 0;\n    }\n\n    /* go to next symbol, update count, len */\n    sym++;\n    if (--count[len] === 0) {\n      if (len === max) { break; }\n      len = lens[lens_index + work[sym]];\n    }\n\n    /* create new sub-table if needed */\n    if (len > root && (huff & mask) !== low) {\n      /* if first time, transition to sub-tables */\n      if (drop === 0) {\n        drop = root;\n      }\n\n      /* increment past last table */\n      next += min;            /* here min is 1 << curr */\n\n      /* determine length of next table */\n      curr = len - drop;\n      left = 1 << curr;\n      while (curr + drop < max) {\n        left -= count[curr + drop];\n        if (left <= 0) { break; }\n        curr++;\n        left <<= 1;\n      }\n\n      /* check for enough space */\n      used += 1 << curr;\n      if ((type === LENS && used > ENOUGH_LENS) ||\n        (type === DISTS && used > ENOUGH_DISTS)) {\n        return 1;\n      }\n\n      /* point entry in root table to sub-table */\n      low = huff & mask;\n      /*table.op[low] = curr;\n      table.bits[low] = root;\n      table.val[low] = next - opts.table_index;*/\n      table[low] = (root << 24) | (curr << 16) | (next - table_index) |0;\n    }\n  }\n\n  /* fill in remaining table entry if code is incomplete (guaranteed to have\n   at most one remaining entry, since if the code is incomplete, the\n   maximum code length that was allowed to get this far is one bit) */\n  if (huff !== 0) {\n    //table.op[next + huff] = 64;            /* invalid code marker */\n    //table.bits[next + huff] = len - drop;\n    //table.val[next + huff] = 0;\n    table[next + huff] = ((len - drop) << 24) | (64 << 16) |0;\n  }\n\n  /* set return parameters */\n  //opts.table_index += used;\n  opts.bits = root;\n  return 0;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,KAAK,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAEtC,IAAIC,OAAO,GAAG,EAAE;AAChB,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,YAAY,GAAG,GAAG;AACtB;;AAEA,IAAIC,KAAK,GAAG,CAAC;AACb,IAAIC,IAAI,GAAG,CAAC;AACZ,IAAIC,KAAK,GAAG,CAAC;AAEb,IAAIC,KAAK,GAAG,CAAE;AACZ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACvD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAC/D;AAED,IAAIC,IAAI,GAAG,CAAE;AACX,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9D,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAC3D;AAED,IAAIC,KAAK,GAAG,CAAE;AACZ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EACzD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACtD,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAChC;AAED,IAAIC,IAAI,GAAG,CAAE;AACX,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9D,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACtC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACvB;AAEDC,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAEC,IAAI,EAAEC,IAAI,EACrG;EACE,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;EAChB;;EAEJ,IAAIC,GAAG,GAAG,CAAC,CAAC,CAAe;EAC3B,IAAIC,GAAG,GAAG,CAAC,CAAC,CAAe;EAC3B,IAAIC,GAAG,GAAG,CAAC;IAAEC,GAAG,GAAG,CAAC,CAAC,CAAU;EAC/B,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAmB;EAChC,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIC,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIC,IAAI,CAAC,CAAc;EACvB,IAAIC,IAAI,CAAC,CAAc;EACvB,IAAIC,GAAG,CAAC,CAAe;EACvB,IAAIC,IAAI,CAAC,CAAc;EACvB,IAAIC,IAAI,CAAC,CAAa;EACtB,IAAIC,IAAI,GAAG,IAAI,CAAC,CAAK;EACrB,IAAIC,UAAU,GAAG,CAAC;EACpB;EACE,IAAIC,GAAG,CAAC,CAAoB;EAC5B,IAAIC,KAAK,GAAG,IAAI1C,KAAK,CAAC2C,KAAK,CAACzC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1C,IAAI0C,IAAI,GAAG,IAAI5C,KAAK,CAAC2C,KAAK,CAACzC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,IAAI2C,KAAK,GAAG,IAAI;EAChB,IAAIC,WAAW,GAAG,CAAC;EAEnB,IAAIC,SAAS,EAAEC,OAAO,EAAEC,QAAQ;;EAEhC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAKE;EACA,KAAKzB,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAItB,OAAO,EAAEsB,GAAG,EAAE,EAAE;IACnCkB,KAAK,CAAClB,GAAG,CAAC,GAAG,CAAC;EAChB;EACA,KAAKC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,KAAK,EAAEO,GAAG,EAAE,EAAE;IAChCiB,KAAK,CAAC1B,IAAI,CAACC,UAAU,GAAGQ,GAAG,CAAC,CAAC,EAAE;EACjC;;EAEA;EACAG,IAAI,GAAGL,IAAI;EACX,KAAKI,GAAG,GAAGzB,OAAO,EAAEyB,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IACnC,IAAIe,KAAK,CAACf,GAAG,CAAC,KAAK,CAAC,EAAE;MAAE;IAAO;EACjC;EACA,IAAIC,IAAI,GAAGD,GAAG,EAAE;IACdC,IAAI,GAAGD,GAAG;EACZ;EACA,IAAIA,GAAG,KAAK,CAAC,EAAE;IAAsB;IACnC;IACA;IACA;IACAR,KAAK,CAACC,WAAW,EAAE,CAAC,GAAI,CAAC,IAAI,EAAE,GAAK,EAAE,IAAI,EAAG,GAAG,CAAC;;IAGjD;IACA;IACA;IACAD,KAAK,CAACC,WAAW,EAAE,CAAC,GAAI,CAAC,IAAI,EAAE,GAAK,EAAE,IAAI,EAAG,GAAG,CAAC;IAEjDE,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,OAAO,CAAC,CAAC,CAAK;EAChB;EACA,KAAKG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGC,GAAG,EAAED,GAAG,EAAE,EAAE;IAC9B,IAAIgB,KAAK,CAAChB,GAAG,CAAC,KAAK,CAAC,EAAE;MAAE;IAAO;EACjC;EACA,IAAIE,IAAI,GAAGF,GAAG,EAAE;IACdE,IAAI,GAAGF,GAAG;EACZ;;EAEA;EACAK,IAAI,GAAG,CAAC;EACR,KAAKP,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAItB,OAAO,EAAEsB,GAAG,EAAE,EAAE;IACnCO,IAAI,KAAK,CAAC;IACVA,IAAI,IAAIW,KAAK,CAAClB,GAAG,CAAC;IAClB,IAAIO,IAAI,GAAG,CAAC,EAAE;MACZ,OAAO,CAAC,CAAC;IACX,CAAC,CAAQ;EACX;EACA,IAAIA,IAAI,GAAG,CAAC,KAAKhB,IAAI,KAAKV,KAAK,IAAIsB,GAAG,KAAK,CAAC,CAAC,EAAE;IAC7C,OAAO,CAAC,CAAC,CAAC,CAAsB;EAClC;;EAEA;EACAiB,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACX,KAAKpB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,OAAO,EAAEsB,GAAG,EAAE,EAAE;IAClCoB,IAAI,CAACpB,GAAG,GAAG,CAAC,CAAC,GAAGoB,IAAI,CAACpB,GAAG,CAAC,GAAGkB,KAAK,CAAClB,GAAG,CAAC;EACxC;;EAEA;EACA,KAAKC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,KAAK,EAAEO,GAAG,EAAE,EAAE;IAChC,IAAIT,IAAI,CAACC,UAAU,GAAGQ,GAAG,CAAC,KAAK,CAAC,EAAE;MAChCJ,IAAI,CAACuB,IAAI,CAAC5B,IAAI,CAACC,UAAU,GAAGQ,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGA,GAAG;IAC5C;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAME;EACA;EACA;EACA,IAAIV,IAAI,KAAKV,KAAK,EAAE;IAClBkC,IAAI,GAAGM,KAAK,GAAGxB,IAAI,CAAC,CAAI;IACxBoB,GAAG,GAAG,EAAE;EAEV,CAAC,MAAM,IAAI1B,IAAI,KAAKT,IAAI,EAAE;IACxBiC,IAAI,GAAG/B,KAAK;IACZgC,UAAU,IAAI,GAAG;IACjBK,KAAK,GAAGpC,IAAI;IACZqC,WAAW,IAAI,GAAG;IAClBL,GAAG,GAAG,GAAG;EAEX,CAAC,MAAM;IAAqB;IAC1BF,IAAI,GAAG7B,KAAK;IACZmC,KAAK,GAAGlC,IAAI;IACZ8B,GAAG,GAAG,CAAC,CAAC;EACV;;EAEA;EACAR,IAAI,GAAG,CAAC,CAAC,CAAmB;EAC5BR,GAAG,GAAG,CAAC,CAAC,CAAoB;EAC5BD,GAAG,GAAGE,GAAG,CAAC,CAAkB;EAC5BY,IAAI,GAAGlB,WAAW,CAAC,CAAc;EACjCS,IAAI,GAAGD,IAAI,CAAC,CAAgB;EAC5BE,IAAI,GAAG,CAAC,CAAC,CAAmB;EAC5BM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAmB;EAC5BJ,IAAI,GAAG,CAAC,IAAIJ,IAAI,CAAC,CAAU;EAC3BS,IAAI,GAAGL,IAAI,GAAG,CAAC,CAAC,CAAY;;EAE5B;EACA,IAAKjB,IAAI,KAAKT,IAAI,IAAI0B,IAAI,GAAG7B,WAAW,IACrCY,IAAI,KAAKR,KAAK,IAAIyB,IAAI,GAAG5B,YAAa,EAAE;IACzC,OAAO,CAAC;EACV;;EAEA;EACA,SAAS;IACP;IACA2C,SAAS,GAAGvB,GAAG,GAAGM,IAAI;IACtB,IAAIT,IAAI,CAACI,GAAG,CAAC,GAAGgB,GAAG,EAAE;MACnBO,OAAO,GAAG,CAAC;MACXC,QAAQ,GAAG5B,IAAI,CAACI,GAAG,CAAC;IACtB,CAAC,MACI,IAAIJ,IAAI,CAACI,GAAG,CAAC,GAAGgB,GAAG,EAAE;MACxBO,OAAO,GAAGH,KAAK,CAACC,WAAW,GAAGzB,IAAI,CAACI,GAAG,CAAC,CAAC;MACxCwB,QAAQ,GAAGV,IAAI,CAACC,UAAU,GAAGnB,IAAI,CAACI,GAAG,CAAC,CAAC;IACzC,CAAC,MACI;MACHuB,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC,CAAS;MAC3BC,QAAQ,GAAG,CAAC;IACd;;IAEA;IACAf,IAAI,GAAG,CAAC,IAAKV,GAAG,GAAGM,IAAK;IACxBK,IAAI,GAAG,CAAC,IAAIN,IAAI;IAChBH,GAAG,GAAGS,IAAI,CAAC,CAAiB;IAC5B,GAAG;MACDA,IAAI,IAAID,IAAI;MACZf,KAAK,CAACmB,IAAI,IAAIL,IAAI,IAAIH,IAAI,CAAC,GAAGK,IAAI,CAAC,GAAIY,SAAS,IAAI,EAAE,GAAKC,OAAO,IAAI,EAAG,GAAGC,QAAQ,GAAE,CAAC;IACzF,CAAC,QAAQd,IAAI,KAAK,CAAC;;IAEnB;IACAD,IAAI,GAAG,CAAC,IAAKV,GAAG,GAAG,CAAE;IACrB,OAAOS,IAAI,GAAGC,IAAI,EAAE;MAClBA,IAAI,KAAK,CAAC;IACZ;IACA,IAAIA,IAAI,KAAK,CAAC,EAAE;MACdD,IAAI,IAAIC,IAAI,GAAG,CAAC;MAChBD,IAAI,IAAIC,IAAI;IACd,CAAC,MAAM;MACLD,IAAI,GAAG,CAAC;IACV;;IAEA;IACAR,GAAG,EAAE;IACL,IAAI,EAAEiB,KAAK,CAAClB,GAAG,CAAC,KAAK,CAAC,EAAE;MACtB,IAAIA,GAAG,KAAKG,GAAG,EAAE;QAAE;MAAO;MAC1BH,GAAG,GAAGR,IAAI,CAACC,UAAU,GAAGI,IAAI,CAACI,GAAG,CAAC,CAAC;IACpC;;IAEA;IACA,IAAID,GAAG,GAAGI,IAAI,IAAI,CAACK,IAAI,GAAGI,IAAI,MAAMD,GAAG,EAAE;MACvC;MACA,IAAIN,IAAI,KAAK,CAAC,EAAE;QACdA,IAAI,GAAGF,IAAI;MACb;;MAEA;MACAU,IAAI,IAAIZ,GAAG,CAAC,CAAY;;MAExB;MACAG,IAAI,GAAGL,GAAG,GAAGM,IAAI;MACjBC,IAAI,GAAG,CAAC,IAAIF,IAAI;MAChB,OAAOA,IAAI,GAAGC,IAAI,GAAGH,GAAG,EAAE;QACxBI,IAAI,IAAIW,KAAK,CAACb,IAAI,GAAGC,IAAI,CAAC;QAC1B,IAAIC,IAAI,IAAI,CAAC,EAAE;UAAE;QAAO;QACxBF,IAAI,EAAE;QACNE,IAAI,KAAK,CAAC;MACZ;;MAEA;MACAC,IAAI,IAAI,CAAC,IAAIH,IAAI;MACjB,IAAKd,IAAI,KAAKT,IAAI,IAAI0B,IAAI,GAAG7B,WAAW,IACrCY,IAAI,KAAKR,KAAK,IAAIyB,IAAI,GAAG5B,YAAa,EAAE;QACzC,OAAO,CAAC;MACV;;MAEA;MACAgC,GAAG,GAAGH,IAAI,GAAGI,IAAI;MACjB;AACN;AACA;MACMlB,KAAK,CAACiB,GAAG,CAAC,GAAIR,IAAI,IAAI,EAAE,GAAKC,IAAI,IAAI,EAAG,GAAIS,IAAI,GAAGlB,WAAY,GAAE,CAAC;IACpE;EACF;;EAEA;AACF;AACA;EACE,IAAIa,IAAI,KAAK,CAAC,EAAE;IACd;IACA;IACA;IACAd,KAAK,CAACmB,IAAI,GAAGL,IAAI,CAAC,GAAKT,GAAG,GAAGM,IAAI,IAAK,EAAE,GAAK,EAAE,IAAI,EAAG,GAAE,CAAC;EAC3D;;EAEA;EACA;EACAR,IAAI,CAACC,IAAI,GAAGK,IAAI;EAChB,OAAO,CAAC;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}