{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { SaTimeLimitComponent } from './saTimeLimit.component';\nimport { SaTimeLimitRoutingModule } from './saTimeLimit-routing.module';\nimport { SaTimeLimitEditComponent } from '@business/tas/saTimeLimit/saTimeLimit-edit/saTimeLimit-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [SaTimeLimitComponent, SaTimeLimitEditComponent];\nexport class SaTimeLimitModule {\n  static {\n    this.ɵfac = function SaTimeLimitModule_Factory(t) {\n      return new (t || SaTimeLimitModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SaTimeLimitModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, SaTimeLimitRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SaTimeLimitModule, {\n    declarations: [SaTimeLimitComponent, SaTimeLimitEditComponent],\n    imports: [SharedModule, SaTimeLimitRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "SaTimeLimitComponent", "SaTimeLimitRoutingModule", "SaTimeLimitEditComponent", "COMPONENTS", "SaTimeLimitModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\saTimeLimit\\saTimeLimit.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { SaTimeLimitComponent } from './saTimeLimit.component';\r\nimport { SaTimeLimitRoutingModule } from './saTimeLimit-routing.module';\r\nimport {SaTimeLimitEditComponent} from '@business/tas/saTimeLimit/saTimeLimit-edit/saTimeLimit-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  SaTimeLimitComponent,\r\n  SaTimeLimitEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, SaTimeLimitRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class SaTimeLimitModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}