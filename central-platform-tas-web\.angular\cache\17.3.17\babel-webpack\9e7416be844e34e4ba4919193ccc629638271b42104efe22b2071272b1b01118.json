{"ast": null, "code": "import { int2char } from \"./util\";\nvar b64map = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nvar b64pad = \"=\";\nexport function hex2b64(h) {\n  var i;\n  var c;\n  var ret = \"\";\n  for (i = 0; i + 3 <= h.length; i += 3) {\n    c = parseInt(h.substring(i, i + 3), 16);\n    ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);\n  }\n  if (i + 1 == h.length) {\n    c = parseInt(h.substring(i, i + 1), 16);\n    ret += b64map.charAt(c << 2);\n  } else if (i + 2 == h.length) {\n    c = parseInt(h.substring(i, i + 2), 16);\n    ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);\n  }\n  while ((ret.length & 3) > 0) {\n    ret += b64pad;\n  }\n  return ret;\n}\n// convert a base64 string to hex\nexport function b64tohex(s) {\n  var ret = \"\";\n  var i;\n  var k = 0; // b64 state, 0-3\n  var slop = 0;\n  for (i = 0; i < s.length; ++i) {\n    if (s.charAt(i) == b64pad) {\n      break;\n    }\n    var v = b64map.indexOf(s.charAt(i));\n    if (v < 0) {\n      continue;\n    }\n    if (k == 0) {\n      ret += int2char(v >> 2);\n      slop = v & 3;\n      k = 1;\n    } else if (k == 1) {\n      ret += int2char(slop << 2 | v >> 4);\n      slop = v & 0xf;\n      k = 2;\n    } else if (k == 2) {\n      ret += int2char(slop);\n      ret += int2char(v >> 2);\n      slop = v & 3;\n      k = 3;\n    } else {\n      ret += int2char(slop << 2 | v >> 4);\n      ret += int2char(v & 0xf);\n      k = 0;\n    }\n  }\n  if (k == 1) {\n    ret += int2char(slop << 2);\n  }\n  return ret;\n}\n// convert a base64 string to a byte/number array\nexport function b64toBA(s) {\n  // piggyback on b64tohex for now, optimize later\n  var h = b64tohex(s);\n  var i;\n  var a = [];\n  for (i = 0; 2 * i < h.length; ++i) {\n    a[i] = parseInt(h.substring(2 * i, 2 * i + 2), 16);\n  }\n  return a;\n}", "map": {"version": 3, "names": ["int2char", "b64map", "b64pad", "hex2b64", "h", "i", "c", "ret", "length", "parseInt", "substring", "char<PERSON>t", "b64tohex", "s", "k", "slop", "v", "indexOf", "b64toBA", "a"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/jsbn/base64.js"], "sourcesContent": ["import { int2char } from \"./util\";\nvar b64map = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nvar b64pad = \"=\";\nexport function hex2b64(h) {\n    var i;\n    var c;\n    var ret = \"\";\n    for (i = 0; i + 3 <= h.length; i += 3) {\n        c = parseInt(h.substring(i, i + 3), 16);\n        ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);\n    }\n    if (i + 1 == h.length) {\n        c = parseInt(h.substring(i, i + 1), 16);\n        ret += b64map.charAt(c << 2);\n    }\n    else if (i + 2 == h.length) {\n        c = parseInt(h.substring(i, i + 2), 16);\n        ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);\n    }\n    while ((ret.length & 3) > 0) {\n        ret += b64pad;\n    }\n    return ret;\n}\n// convert a base64 string to hex\nexport function b64tohex(s) {\n    var ret = \"\";\n    var i;\n    var k = 0; // b64 state, 0-3\n    var slop = 0;\n    for (i = 0; i < s.length; ++i) {\n        if (s.charAt(i) == b64pad) {\n            break;\n        }\n        var v = b64map.indexOf(s.charAt(i));\n        if (v < 0) {\n            continue;\n        }\n        if (k == 0) {\n            ret += int2char(v >> 2);\n            slop = v & 3;\n            k = 1;\n        }\n        else if (k == 1) {\n            ret += int2char((slop << 2) | (v >> 4));\n            slop = v & 0xf;\n            k = 2;\n        }\n        else if (k == 2) {\n            ret += int2char(slop);\n            ret += int2char(v >> 2);\n            slop = v & 3;\n            k = 3;\n        }\n        else {\n            ret += int2char((slop << 2) | (v >> 4));\n            ret += int2char(v & 0xf);\n            k = 0;\n        }\n    }\n    if (k == 1) {\n        ret += int2char(slop << 2);\n    }\n    return ret;\n}\n// convert a base64 string to a byte/number array\nexport function b64toBA(s) {\n    // piggyback on b64tohex for now, optimize later\n    var h = b64tohex(s);\n    var i;\n    var a = [];\n    for (i = 0; 2 * i < h.length; ++i) {\n        a[i] = parseInt(h.substring(2 * i, 2 * i + 2), 16);\n    }\n    return a;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAQ;AACjC,IAAIC,MAAM,GAAG,kEAAkE;AAC/E,IAAIC,MAAM,GAAG,GAAG;AAChB,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAE;EACvB,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAID,CAAC,CAACI,MAAM,EAAEH,CAAC,IAAI,CAAC,EAAE;IACnCC,CAAC,GAAGG,QAAQ,CAACL,CAAC,CAACM,SAAS,CAACL,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACvCE,GAAG,IAAIN,MAAM,CAACU,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC,GAAGL,MAAM,CAACU,MAAM,CAACL,CAAC,GAAG,EAAE,CAAC;EACxD;EACA,IAAID,CAAC,GAAG,CAAC,IAAID,CAAC,CAACI,MAAM,EAAE;IACnBF,CAAC,GAAGG,QAAQ,CAACL,CAAC,CAACM,SAAS,CAACL,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACvCE,GAAG,IAAIN,MAAM,CAACU,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC;EAChC,CAAC,MACI,IAAID,CAAC,GAAG,CAAC,IAAID,CAAC,CAACI,MAAM,EAAE;IACxBF,CAAC,GAAGG,QAAQ,CAACL,CAAC,CAACM,SAAS,CAACL,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACvCE,GAAG,IAAIN,MAAM,CAACU,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC,GAAGL,MAAM,CAACU,MAAM,CAAC,CAACL,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAC9D;EACA,OAAO,CAACC,GAAG,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;IACzBD,GAAG,IAAIL,MAAM;EACjB;EACA,OAAOK,GAAG;AACd;AACA;AACA,OAAO,SAASK,QAAQA,CAACC,CAAC,EAAE;EACxB,IAAIN,GAAG,GAAG,EAAE;EACZ,IAAIF,CAAC;EACL,IAAIS,CAAC,GAAG,CAAC,CAAC,CAAC;EACX,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAKV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,CAACL,MAAM,EAAE,EAAEH,CAAC,EAAE;IAC3B,IAAIQ,CAAC,CAACF,MAAM,CAACN,CAAC,CAAC,IAAIH,MAAM,EAAE;MACvB;IACJ;IACA,IAAIc,CAAC,GAAGf,MAAM,CAACgB,OAAO,CAACJ,CAAC,CAACF,MAAM,CAACN,CAAC,CAAC,CAAC;IACnC,IAAIW,CAAC,GAAG,CAAC,EAAE;MACP;IACJ;IACA,IAAIF,CAAC,IAAI,CAAC,EAAE;MACRP,GAAG,IAAIP,QAAQ,CAACgB,CAAC,IAAI,CAAC,CAAC;MACvBD,IAAI,GAAGC,CAAC,GAAG,CAAC;MACZF,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIA,CAAC,IAAI,CAAC,EAAE;MACbP,GAAG,IAAIP,QAAQ,CAAEe,IAAI,IAAI,CAAC,GAAKC,CAAC,IAAI,CAAE,CAAC;MACvCD,IAAI,GAAGC,CAAC,GAAG,GAAG;MACdF,CAAC,GAAG,CAAC;IACT,CAAC,MACI,IAAIA,CAAC,IAAI,CAAC,EAAE;MACbP,GAAG,IAAIP,QAAQ,CAACe,IAAI,CAAC;MACrBR,GAAG,IAAIP,QAAQ,CAACgB,CAAC,IAAI,CAAC,CAAC;MACvBD,IAAI,GAAGC,CAAC,GAAG,CAAC;MACZF,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACDP,GAAG,IAAIP,QAAQ,CAAEe,IAAI,IAAI,CAAC,GAAKC,CAAC,IAAI,CAAE,CAAC;MACvCT,GAAG,IAAIP,QAAQ,CAACgB,CAAC,GAAG,GAAG,CAAC;MACxBF,CAAC,GAAG,CAAC;IACT;EACJ;EACA,IAAIA,CAAC,IAAI,CAAC,EAAE;IACRP,GAAG,IAAIP,QAAQ,CAACe,IAAI,IAAI,CAAC,CAAC;EAC9B;EACA,OAAOR,GAAG;AACd;AACA;AACA,OAAO,SAASW,OAAOA,CAACL,CAAC,EAAE;EACvB;EACA,IAAIT,CAAC,GAAGQ,QAAQ,CAACC,CAAC,CAAC;EACnB,IAAIR,CAAC;EACL,IAAIc,CAAC,GAAG,EAAE;EACV,KAAKd,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAGD,CAAC,CAACI,MAAM,EAAE,EAAEH,CAAC,EAAE;IAC/Bc,CAAC,CAACd,CAAC,CAAC,GAAGI,QAAQ,CAACL,CAAC,CAACM,SAAS,CAAC,CAAC,GAAGL,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EACtD;EACA,OAAOc,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}