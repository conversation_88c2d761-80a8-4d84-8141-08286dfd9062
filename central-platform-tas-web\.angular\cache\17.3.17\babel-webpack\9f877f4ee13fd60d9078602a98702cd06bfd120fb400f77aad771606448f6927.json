{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'o' eeee 'pasado á' LT\",\n  yesterday: \"'onte á' p\",\n  today: \"'hoxe á' p\",\n  tomorrow: \"'mañá á' p\",\n  nextWeek: \"eeee 'á' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'o' eeee 'pasado ás' p\",\n  yesterday: \"'onte ás' p\",\n  today: \"'hoxe ás' p\",\n  tomorrow: \"'mañá ás' p\",\n  nextWeek: \"eeee 'ás' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getUTCHours"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/gl/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'o' eeee 'pasado á' LT\",\n  yesterday: \"'onte á' p\",\n  today: \"'hoxe á' p\",\n  tomorrow: \"'mañá á' p\",\n  nextWeek: \"eeee 'á' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'o' eeee 'pasado ás' p\",\n  yesterday: \"'onte ás' p\",\n  today: \"'hoxe ás' p\",\n  tomorrow: \"'mañá ás' p\",\n  nextWeek: \"eeee 'ás' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,0BAA0B,GAAG;EAC/BN,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIF,IAAI,CAACG,WAAW,CAAC,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}