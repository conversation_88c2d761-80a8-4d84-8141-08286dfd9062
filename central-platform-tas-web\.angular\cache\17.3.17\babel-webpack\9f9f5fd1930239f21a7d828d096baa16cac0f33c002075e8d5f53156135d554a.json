{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['p.m.ē', 'm.ē'],\n  abbreviated: ['p. m. ē.', 'm. ē.'],\n  wide: ['pirms mūsu ēras', 'mūsu ērā']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. cet.', '2. cet.', '3. cet.', '4. cet.'],\n  wide: ['pirmais ceturksnis', 'otrais ceturksnis', 'trešais ceturksnis', 'ceturtais ceturksnis']\n};\nvar formattingQuarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. cet.', '2. cet.', '3. cet.', '4. cet.'],\n  wide: ['pirmajā ceturksnī', 'otrajā ceturksnī', 'trešajā ceturksnī', 'ceturtajā ceturksnī']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'febr.', 'marts', 'apr.', 'maijs', 'jūn.', 'jūl.', 'aug.', 'sept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['janvāris', 'februāris', 'marts', 'aprīlis', 'maijs', 'jūnijs', 'jūlijs', 'augusts', 'septembris', 'oktobris', 'novembris', 'decembris']\n};\nvar formattingMonthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'febr.', 'martā', 'apr.', 'maijs', 'jūn.', 'jūl.', 'aug.', 'sept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['janvārī', 'februārī', 'martā', 'aprīlī', 'maijā', 'jūnijā', 'jūlijā', 'augustā', 'septembrī', 'oktobrī', 'novembrī', 'decembrī']\n};\nvar dayValues = {\n  narrow: ['S', 'P', 'O', 'T', 'C', 'P', 'S'],\n  short: ['Sv', 'P', 'O', 'T', 'C', 'Pk', 'S'],\n  abbreviated: ['svētd.', 'pirmd.', 'otrd.', 'trešd.', 'ceturtd.', 'piektd.', 'sestd.'],\n  wide: ['svētdiena', 'pirmdiena', 'otrdiena', 'trešdiena', 'ceturtdiena', 'piektdiena', 'sestdiena']\n};\nvar formattingDayValues = {\n  narrow: ['S', 'P', 'O', 'T', 'C', 'P', 'S'],\n  short: ['Sv', 'P', 'O', 'T', 'C', 'Pk', 'S'],\n  abbreviated: ['svētd.', 'pirmd.', 'otrd.', 'trešd.', 'ceturtd.', 'piektd.', 'sestd.'],\n  wide: ['svētdienā', 'pirmdienā', 'otrdienā', 'trešdienā', 'ceturtdienā', 'piektdienā', 'sestdienā']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rīts',\n    afternoon: 'diena',\n    evening: 'vakars',\n    night: 'nakts'\n  },\n  abbreviated: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rīts',\n    afternoon: 'pēcpusd.',\n    evening: 'vakars',\n    night: 'nakts'\n  },\n  wide: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusnakts',\n    noon: 'pusdienlaiks',\n    morning: 'rīts',\n    afternoon: 'pēcpusdiena',\n    evening: 'vakars',\n    night: 'nakts'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rītā',\n    afternoon: 'dienā',\n    evening: 'vakarā',\n    night: 'naktī'\n  },\n  abbreviated: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rītā',\n    afternoon: 'pēcpusd.',\n    evening: 'vakarā',\n    night: 'naktī'\n  },\n  wide: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusnaktī',\n    noon: 'pusdienlaikā',\n    morning: 'rītā',\n    afternoon: 'pēcpusdienā',\n    evening: 'vakarā',\n    night: 'naktī'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "formattingQuarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "formattingDayValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "formattingValues", "defaultFormattingWidth", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/lv/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['p.m.ē', 'm.ē'],\n  abbreviated: ['p. m. ē.', 'm. ē.'],\n  wide: ['pirms mūsu ēras', 'mūsu ērā']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. cet.', '2. cet.', '3. cet.', '4. cet.'],\n  wide: ['pirmais ceturksnis', 'otrais ceturksnis', 'trešais ceturksnis', 'ceturtais ceturksnis']\n};\nvar formattingQuarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. cet.', '2. cet.', '3. cet.', '4. cet.'],\n  wide: ['pirmajā ceturksnī', 'otrajā ceturksnī', 'trešajā ceturksnī', 'ceturtajā ceturksnī']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'febr.', 'marts', 'apr.', 'maijs', 'jūn.', 'jūl.', 'aug.', 'sept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['janvāris', 'februāris', 'marts', 'aprīlis', 'maijs', 'jūnijs', 'jūlijs', 'augusts', 'septembris', 'oktobris', 'novembris', 'decembris']\n};\nvar formattingMonthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'febr.', 'martā', 'apr.', 'maijs', 'jūn.', 'jūl.', 'aug.', 'sept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['janvārī', 'februārī', 'martā', 'aprīlī', 'maijā', 'jūnijā', 'jūlijā', 'augustā', 'septembrī', 'oktobrī', 'novembrī', 'decembrī']\n};\nvar dayValues = {\n  narrow: ['S', 'P', 'O', 'T', 'C', 'P', 'S'],\n  short: ['Sv', 'P', 'O', 'T', 'C', 'Pk', 'S'],\n  abbreviated: ['svētd.', 'pirmd.', 'otrd.', 'trešd.', 'ceturtd.', 'piektd.', 'sestd.'],\n  wide: ['svētdiena', 'pirmdiena', 'otrdiena', 'trešdiena', 'ceturtdiena', 'piektdiena', 'sestdiena']\n};\nvar formattingDayValues = {\n  narrow: ['S', 'P', 'O', 'T', 'C', 'P', 'S'],\n  short: ['Sv', 'P', 'O', 'T', 'C', 'Pk', 'S'],\n  abbreviated: ['svētd.', 'pirmd.', 'otrd.', 'trešd.', 'ceturtd.', 'piektd.', 'sestd.'],\n  wide: ['svētdienā', 'pirmdienā', 'otrdienā', 'trešdienā', 'ceturtdienā', 'piektdienā', 'sestdienā']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rīts',\n    afternoon: 'diena',\n    evening: 'vakars',\n    night: 'nakts'\n  },\n  abbreviated: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rīts',\n    afternoon: 'pēcpusd.',\n    evening: 'vakars',\n    night: 'nakts'\n  },\n  wide: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusnakts',\n    noon: 'pusdienlaiks',\n    morning: 'rīts',\n    afternoon: 'pēcpusdiena',\n    evening: 'vakars',\n    night: 'nakts'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rītā',\n    afternoon: 'dienā',\n    evening: 'vakarā',\n    night: 'naktī'\n  },\n  abbreviated: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusn.',\n    noon: 'pusd.',\n    morning: 'rītā',\n    afternoon: 'pēcpusd.',\n    evening: 'vakarā',\n    night: 'naktī'\n  },\n  wide: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'pusnaktī',\n    noon: 'pusdienlaikā',\n    morning: 'rītā',\n    afternoon: 'pēcpusdienā',\n    evening: 'vakarā',\n    night: 'naktī'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;EACxBC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,UAAU;AACtC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,sBAAsB;AAChG,CAAC;AACD,IAAIE,uBAAuB,GAAG;EAC5BJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,qBAAqB;AAC5F,CAAC;AACD,IAAIG,WAAW,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAClHC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW;AAChJ,CAAC;AACD,IAAII,qBAAqB,GAAG;EAC1BN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAClHC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AACzI,CAAC;AACD,IAAIK,SAAS,GAAG;EACdP,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CQ,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5CP,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;EACrFC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW;AACpG,CAAC;AACD,IAAIO,mBAAmB,GAAG;EACxBT,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CQ,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5CP,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;EACrFC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW;AACpG,CAAC;AACD,IAAIQ,eAAe,GAAG;EACpBV,MAAM,EAAE;IACNW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDjB,WAAW,EAAE;IACXU,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,IAAI,EAAE;IACJS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BnB,MAAM,EAAE;IACNW,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDjB,WAAW,EAAE;IACXU,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,IAAI,EAAE;IACJS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE1B,uBAAuB;IACzC2B,sBAAsB,EAAE,MAAM;IAC9BC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACH,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFI,KAAK,EAAEnC,eAAe,CAAC;IACrB6B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAExB,qBAAqB;IACvCyB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFG,GAAG,EAAEpC,eAAe,CAAC;IACnB6B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAErB,mBAAmB;IACrCsB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFI,SAAS,EAAErC,eAAe,CAAC;IACzB6B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAEX,yBAAyB;IAC3CY,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}