{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['НТӨ', 'НТ'],\n  abbreviated: ['НТӨ', 'НТ'],\n  wide: ['нийтийн тооллын өмнөх', 'нийтийн тооллын']\n};\nvar quarterValues = {\n  narrow: ['I', 'II', 'III', 'IV'],\n  abbreviated: ['I улирал', 'II улирал', 'III улирал', 'IV улирал'],\n  wide: ['1-р улирал', '2-р улирал', '3-р улирал', '4-р улирал']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['Нэгдүгээр сар', 'Хоёрдугаар сар', 'Гуравдугаар сар', 'Дөрөвдүгээр сар', 'Тавдугаар сар', 'Зургаадугаар сар', 'Долоодугаар сар', 'Наймдугаар сар', 'Есдүгээр сар', 'Аравдугаар сар', 'Арваннэгдүгээр сар', 'Арван хоёрдугаар сар']\n};\nvar formattingMonthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['нэгдүгээр сар', 'хоёрдугаар сар', 'гуравдугаар сар', 'дөрөвдүгээр сар', 'тавдугаар сар', 'зургаадугаар сар', 'долоодугаар сар', 'наймдугаар сар', 'есдүгээр сар', 'аравдугаар сар', 'арваннэгдүгээр сар', 'арван хоёрдугаар сар']\n};\nvar dayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['Ням', 'Даваа', 'Мягмар', 'Лхагва', 'Пүрэв', 'Баасан', 'Бямба']\n};\nvar formattingDayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['ням', 'даваа', 'мягмар', 'лхагва', 'пүрэв', 'баасан', 'бямба']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  abbreviated: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  wide: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "formattingDayValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/mn/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['НТӨ', 'НТ'],\n  abbreviated: ['НТӨ', 'НТ'],\n  wide: ['нийтийн тооллын өмнөх', 'нийтийн тооллын']\n};\nvar quarterValues = {\n  narrow: ['I', 'II', 'III', 'IV'],\n  abbreviated: ['I улирал', 'II улирал', 'III улирал', 'IV улирал'],\n  wide: ['1-р улирал', '2-р улирал', '3-р улирал', '4-р улирал']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['Нэгдүгээр сар', 'Хоёрдугаар сар', 'Гуравдугаар сар', 'Дөрөвдүгээр сар', 'Тавдугаар сар', 'Зургаадугаар сар', 'Долоодугаар сар', 'Наймдугаар сар', 'Есдүгээр сар', 'Аравдугаар сар', 'Арваннэгдүгээр сар', 'Арван хоёрдугаар сар']\n};\nvar formattingMonthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['нэгдүгээр сар', 'хоёрдугаар сар', 'гуравдугаар сар', 'дөрөвдүгээр сар', 'тавдугаар сар', 'зургаадугаар сар', 'долоодугаар сар', 'наймдугаар сар', 'есдүгээр сар', 'аравдугаар сар', 'арваннэгдүгээр сар', 'арван хоёрдугаар сар']\n};\nvar dayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['Ням', 'Даваа', 'Мягмар', 'Лхагва', 'Пүрэв', 'Баасан', 'Бямба']\n};\nvar formattingDayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['ням', 'даваа', 'мягмар', 'лхагва', 'пүрэв', 'баасан', 'бямба']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  abbreviated: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  wide: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EACrBC,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAC1BC,IAAI,EAAE,CAAC,uBAAuB,EAAE,iBAAiB;AACnD,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;EACjEC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;EAClFC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACpJC,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,sBAAsB;AAC1O,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;EAClFC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACpJC,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,sBAAsB;AAC1O,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;AACvE,CAAC;AACD,IAAIM,mBAAmB,GAAG;EACxBR,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;AACvE,CAAC;AACD,IAAIO,eAAe,GAAG;EACpBT,MAAM,EAAE;IACNU,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAExB,qBAAqB;IACvCyB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAEjC,eAAe,CAAC;IACnB0B,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAErB,mBAAmB;IACrCsB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFE,SAAS,EAAElC,eAAe,CAAC;IACzB0B,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}