{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_DAMAGE_LEVEL extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"残损程度表主键\",\n      \"damage_level_cd\": \"残损程度代码\",\n      \"damage_level_nm\": \"残损程度名称\",\n      \"damage_level_nm_en\": \"残损程度英文名称\",\n      \"bs_cd\": \"业务场景\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_DAMAGE_LEVEL'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_DAMAGE_LEVEL", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_DAMAGELEVEL.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_DAMAGE_LEVEL extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_DAMAGE_LEVEL'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"残损程度表主键\",\r\n      \"damage_level_cd\":\"残损程度代码\",\r\n      \"damage_level_nm\":\"残损程度名称\",\r\n      \"damage_level_nm_en\":\"残损程度英文名称\",\r\n      \"bs_cd\":\"业务场景\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,mBAAoB,SAAQD,QAAQ;EAQ/CE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,iBAAiB,EAAC,QAAQ;MAC1B,iBAAiB,EAAC,QAAQ;MAC1B,oBAAoB,EAAC,UAAU;MAC/B,OAAO,EAAC,MAAM;MACd,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IApBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,qBAAqB,CAAC,CAAC;IACnC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAiBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}