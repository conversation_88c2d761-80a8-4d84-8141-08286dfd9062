{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { GeneralcargoComponent } from './generalcargo.component';\nimport { GeneralcargoRoutingModule } from './generalcargo-routing.module';\nimport { GeneralcargoEditComponent } from '@business/tas/generalcargo/generalcargo-edit/generalcargo-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [GeneralcargoComponent, GeneralcargoEditComponent];\nexport class GeneralcargoModule {\n  static {\n    this.ɵfac = function GeneralcargoModule_Factory(t) {\n      return new (t || GeneralcargoModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GeneralcargoModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, GeneralcargoRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GeneralcargoModule, {\n    declarations: [GeneralcargoComponent, GeneralcargoEditComponent],\n    imports: [SharedModule, GeneralcargoRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "GeneralcargoComponent", "GeneralcargoRoutingModule", "GeneralcargoEditComponent", "COMPONENTS", "GeneralcargoModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\generalcargo\\generalcargo.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { GeneralcargoComponent } from './generalcargo.component';\r\nimport { GeneralcargoRoutingModule } from './generalcargo-routing.module';\r\nimport {GeneralcargoEditComponent} from '@business/tas/generalcargo/generalcargo-edit/generalcargo-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  GeneralcargoComponent,\r\n  GeneralcargoEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, GeneralcargoRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class GeneralcargoModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAAQC,yBAAyB,QAAO,0EAA0E;;AAElH,MAAMC,UAAU,GAAG,CACjBH,qBAAqB,EACrBE,yBAAyB,CAC1B;AAMD,OAAM,MAAOE,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBN,YAAY,EAAEG,yBAAyB,EAAEF,YAAY;IAAA;EAAA;;;2EAGpDK,kBAAkB;IAAAC,YAAA,GAR7BL,qBAAqB,EACrBE,yBAAyB;IAAAI,OAAA,GAIfR,YAAY,EAAEG,yBAAyB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}