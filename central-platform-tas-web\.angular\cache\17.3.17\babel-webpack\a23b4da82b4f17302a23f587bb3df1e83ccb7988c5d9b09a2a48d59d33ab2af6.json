{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_BUSINESS_LINE extends CwfStore {\n  constructor() {\n    super({\n      \"id\": \"业务种类表主键\",\n      \"business_line_cd\": \"业务种类代码\",\n      \"business_line_nm\": \"业务种类名称\",\n      \"business_line_nm_en\": \"业务种类英文名称\",\n      \"business_category_cd\": \"纬度大类代码\",\n      \"business_category_nm\": \"纬度大类名称\",\n      \"business_category_nm_en\": \"纬度大类英文名称\",\n      \"bs_cd\": \"业务场景代码\",\n      \"bs_nm\": \"业务场景名称\",\n      \"bs_nm_en\": \"业务场景英文名称\",\n      \"remark\": \"备注\",\n      \"created_user\": \"创建人\",\n      \"created_time\": \"创建时间\",\n      \"modified_user\": \"修改人\",\n      \"modified_time\": \"修改时间\",\n      \"version\": \"乐观锁\",\n      \"is_delete\": \"逻辑删除\",\n      \"tenant_id\": \"租户ID\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'BASE_T_BUSINESS_LINE'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_BUSINESS_LINE", "constructor", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_BUSINESSLINE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_BUSINESS_LINE extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'BASE_T_BUSINESS_LINE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      \"id\":\"业务种类表主键\",\r\n      \"business_line_cd\":\"业务种类代码\",\r\n      \"business_line_nm\":\"业务种类名称\",\r\n      \"business_line_nm_en\":\"业务种类英文名称\",\r\n      \"business_category_cd\":\"纬度大类代码\",\r\n      \"business_category_nm\":\"纬度大类名称\",\r\n      \"business_category_nm_en\":\"纬度大类英文名称\",\r\n      \"bs_cd\":\"业务场景代码\",\r\n      \"bs_nm\":\"业务场景名称\",\r\n      \"bs_nm_en\":\"业务场景英文名称\",\r\n      \"remark\":\"备注\",\r\n      \"created_user\":\"创建人\",\r\n      \"created_time\":\"创建时间\",\r\n      \"modified_user\":\"修改人\",\r\n      \"modified_time\":\"修改时间\",\r\n      \"version\":\"乐观锁\",\r\n      \"is_delete\":\"逻辑删除\",\r\n      \"tenant_id\":\"租户ID\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,oBAAqB,SAAQD,QAAQ;EAQhDE,YAAA;IACE,KAAK,CAAC;MACJ,IAAI,EAAC,SAAS;MACd,kBAAkB,EAAC,QAAQ;MAC3B,kBAAkB,EAAC,QAAQ;MAC3B,qBAAqB,EAAC,UAAU;MAChC,sBAAsB,EAAC,QAAQ;MAC/B,sBAAsB,EAAC,QAAQ;MAC/B,yBAAyB,EAAC,UAAU;MACpC,OAAO,EAAC,QAAQ;MAChB,OAAO,EAAC,QAAQ;MAChB,UAAU,EAAC,UAAU;MACrB,QAAQ,EAAC,IAAI;MACb,cAAc,EAAC,KAAK;MACpB,cAAc,EAAC,MAAM;MACrB,eAAe,EAAC,KAAK;MACrB,eAAe,EAAC,MAAM;MACtB,SAAS,EAAC,KAAK;MACf,WAAW,EAAC,MAAM;MAClB,WAAW,EAAC;KACb,CAAC;IA1BJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,sBAAsB,CAAC,CAAC;IACpC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAuBnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}