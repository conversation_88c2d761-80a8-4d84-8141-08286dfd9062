{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { WorkclassesComponent } from './workclasses.component';\nimport { WorkclassesEditComponent } from '@business/tas/workclasses/workclasses-edit/workclasses-edit.component';\nimport { WorkclassesRoutingModule } from './workclasses-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [WorkclassesComponent, WorkclassesEditComponent];\nexport class WorkclassesModule {\n  static {\n    this.ɵfac = function WorkclassesModule_Factory(t) {\n      return new (t || WorkclassesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: WorkclassesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, WorkclassesRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WorkclassesModule, {\n    declarations: [WorkclassesComponent, WorkclassesEditComponent],\n    imports: [SharedModule, WorkclassesRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "WorkclassesComponent", "WorkclassesEditComponent", "WorkclassesRoutingModule", "COMPONENTS", "WorkclassesModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\workclasses\\workclasses.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { WorkclassesComponent } from './workclasses.component';\r\nimport { WorkclassesEditComponent } from '@business/tas/workclasses/workclasses-edit/workclasses-edit.component';\r\nimport { WorkclassesRoutingModule } from './workclasses-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  WorkclassesComponent,\r\n  WorkclassesEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, WorkclassesRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class WorkclassesModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,uEAAuE;AAChH,SAASC,wBAAwB,QAAQ,8BAA8B;;AAEvE,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBC,wBAAwB,CACzB;AAMD,OAAM,MAAOG,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEI,wBAAwB,EAAEH,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBC,wBAAwB;IAAAK,OAAA,GAIdR,YAAY,EAAEI,wBAAwB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}