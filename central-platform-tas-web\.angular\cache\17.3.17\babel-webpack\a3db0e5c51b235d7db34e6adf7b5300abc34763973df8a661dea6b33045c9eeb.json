{"ast": null, "code": "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern = /\\d+(\\.)?/i;\nvar matchEraPatterns = {\n  narrow: /^(f\\.Kr\\.|e\\.Kr\\.)/i,\n  abbreviated: /^(f\\.Kr\\.|e\\.Kr\\.)/i,\n  wide: /^(fyrir Krist|eftir Krist)/i\n};\nvar parseEraPatterns = {\n  any: [/^(f\\.Kr\\.)/i, /^(e\\.Kr\\.)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]\\.?/i,\n  abbreviated: /^q[1234]\\.?/i,\n  wide: /^[1234]\\.? fjórðungur/i\n};\nvar parseQuarterPatterns = {\n  any: [/1\\.?/i, /2\\.?/i, /3\\.?/i, /4\\.?/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmásónd]/i,\n  abbreviated: /^(jan\\.|feb\\.|mars\\.|apríl\\.|maí|júní|júlí|águst|sep\\.|oct\\.|nov\\.|dec\\.)/i,\n  wide: /^(januar|febrúar|mars|apríl|maí|júní|júlí|águst|september|október|nóvember|desember)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^á/i, /^s/i, /^ó/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^maí/i, /^jún/i, /^júl/i, /^áu/i, /^s/i, /^ó/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|má|þr|mi|fi|fö|la)/i,\n  abbreviated: /^(sun|mán|þri|mið|fim|fös|lau)\\.?/i,\n  wide: /^(sunnudagur|mánudagur|þriðjudagur|miðvikudagur|fimmtudagur|föstudagur|laugardagur)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^þ/i, /^m/i, /^f/i, /^f/i, /^l/i],\n  any: [/^su/i, /^má/i, /^þr/i, /^mi/i, /^fi/i, /^fö/i, /^la/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(f|e|síðdegis|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i,\n  any: /^(fyrir hádegi|eftir hádegi|[ef]\\.?h\\.?|síðdegis|morgunn|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^f/i,\n    pm: /^e/i,\n    midnight: /^mi/i,\n    noon: /^há/i,\n    morning: /morgunn/i,\n    afternoon: /síðdegi/i,\n    evening: /kvöld/i,\n    night: /nótt/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/is/_lib/match/index.js"], "sourcesContent": ["import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern = /\\d+(\\.)?/i;\nvar matchEraPatterns = {\n  narrow: /^(f\\.Kr\\.|e\\.Kr\\.)/i,\n  abbreviated: /^(f\\.Kr\\.|e\\.Kr\\.)/i,\n  wide: /^(fyrir Krist|eftir Krist)/i\n};\nvar parseEraPatterns = {\n  any: [/^(f\\.Kr\\.)/i, /^(e\\.Kr\\.)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]\\.?/i,\n  abbreviated: /^q[1234]\\.?/i,\n  wide: /^[1234]\\.? fjórðungur/i\n};\nvar parseQuarterPatterns = {\n  any: [/1\\.?/i, /2\\.?/i, /3\\.?/i, /4\\.?/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmásónd]/i,\n  abbreviated: /^(jan\\.|feb\\.|mars\\.|apríl\\.|maí|júní|júlí|águst|sep\\.|oct\\.|nov\\.|dec\\.)/i,\n  wide: /^(januar|febrúar|mars|apríl|maí|júní|júlí|águst|september|október|nóvember|desember)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^á/i, /^s/i, /^ó/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^maí/i, /^jún/i, /^júl/i, /^áu/i, /^s/i, /^ó/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|má|þr|mi|fi|fö|la)/i,\n  abbreviated: /^(sun|mán|þri|mið|fim|fös|lau)\\.?/i,\n  wide: /^(sunnudagur|mánudagur|þriðjudagur|miðvikudagur|fimmtudagur|föstudagur|laugardagur)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^þ/i, /^m/i, /^f/i, /^f/i, /^l/i],\n  any: [/^su/i, /^má/i, /^þr/i, /^mi/i, /^fi/i, /^fö/i, /^la/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(f|e|síðdegis|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i,\n  any: /^(fyrir hádegi|eftir hádegi|[ef]\\.?h\\.?|síðdegis|morgunn|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^f/i,\n    pm: /^e/i,\n    midnight: /^mi/i,\n    noon: /^há/i,\n    morning: /morgunn/i,\n    afternoon: /síðdegi/i,\n    evening: /kvöld/i,\n    night: /nótt/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,IAAIC,yBAAyB,GAAG,cAAc;AAC9C,IAAIC,yBAAyB,GAAG,WAAW;AAC3C,IAAIC,gBAAgB,GAAG;EACrBC,MAAM,EAAE,qBAAqB;EAC7BC,WAAW,EAAE,qBAAqB;EAClCC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,gBAAgB,GAAG;EACrBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa;AACpC,CAAC;AACD,IAAIC,oBAAoB,GAAG;EACzBL,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE;AACR,CAAC;AACD,IAAII,oBAAoB,GAAG;EACzBF,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;AAC1C,CAAC;AACD,IAAIG,kBAAkB,GAAG;EACvBP,MAAM,EAAE,cAAc;EACtBC,WAAW,EAAE,4EAA4E;EACzFC,IAAI,EAAE;AACR,CAAC;AACD,IAAIM,kBAAkB,GAAG;EACvBR,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5FI,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACrG,CAAC;AACD,IAAIK,gBAAgB,GAAG;EACrBT,MAAM,EAAE,WAAW;EACnBU,KAAK,EAAE,0BAA0B;EACjCT,WAAW,EAAE,oCAAoC;EACjDC,IAAI,EAAE;AACR,CAAC;AACD,IAAIS,gBAAgB,GAAG;EACrBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAC9D,CAAC;AACD,IAAIQ,sBAAsB,GAAG;EAC3BZ,MAAM,EAAE,yDAAyD;EACjEI,GAAG,EAAE;AACP,CAAC;AACD,IAAIS,sBAAsB,GAAG;EAC3BT,GAAG,EAAE;IACHU,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,KAAK,GAAG;EACVC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAE,SAASA,aAAaA,CAACC,KAAK,EAAE;MAC3C,OAAOC,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;IAC5B;EACF,CAAC,CAAC;EACFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAE,SAASA,aAAaA,CAACS,KAAK,EAAE;MAC3C,OAAOA,KAAK,GAAG,CAAC;IAClB;EACF,CAAC,CAAC;EACFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC;AACD,eAAeX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}