{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م', 'ب.م'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['ينا', 'فبر', 'مارس', 'أبريل', 'مايو', 'يونـ', 'يولـ', 'أغسـ', 'سبتـ', 'أكتـ', 'نوفـ', 'ديسـ'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    morning: 'في الصباح',\n    noon: 'ظهراً',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ar-EG/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م', 'ب.م'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['ينا', 'فبر', 'مارس', 'أبريل', 'مايو', 'يونـ', 'يولـ', 'أغسـ', 'سبتـ', 'أكتـ', 'نوفـ', 'ديسـ'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    morning: 'في الصباح',\n    noon: 'ظهراً',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACtE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACtH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAClEL,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EACxEC,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;AAChF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBE,OAAO,EAAE,WAAW;IACpBD,IAAI,EAAE,OAAO;IACbE,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}