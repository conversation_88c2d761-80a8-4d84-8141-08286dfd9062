{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DamageTypeComponent } from './damagetype.component';\nimport { DamageTypeEditComponent } from '@business/tas/damagetype/damagetype-edit/damagetype-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: DamageTypeComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: DamageTypeEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class DamageTypeRoutingModule {\n  static {\n    this.ɵfac = function DamageTypeRoutingModule_Factory(t) {\n      return new (t || DamageTypeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DamageTypeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DamageTypeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DamageTypeComponent", "DamageTypeEditComponent", "routes", "path", "component", "data", "cache", "DamageTypeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\damagetype\\damagetype-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { DamageTypeComponent } from './damagetype.component';\r\nimport {DamageTypeEditComponent} from '@business/tas/damagetype/damagetype-edit/damagetype-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: DamageTypeComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: DamageTypeEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class DamageTypeRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAAQC,uBAAuB,QAAO,oEAAoE;;;AAC1G,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,mBAAmB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACvE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,uBAAuB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CACjF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFxBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}