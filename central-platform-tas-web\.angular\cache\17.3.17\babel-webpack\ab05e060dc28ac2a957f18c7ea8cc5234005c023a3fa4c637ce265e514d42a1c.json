{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormControl, Validators } from '@angular/forms';\nimport { CwfBaseCrud, CwfOpenParam, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\nimport { TAS_T_SA_BUSINESS } from '@store/BCD/TAS_T_SA_BUSINESS';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@service/cwfRestful.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ng-zorro-antd/grid\";\nimport * as i7 from \"ng-zorro-antd/form\";\nimport * as i8 from \"ng-zorro-antd/button\";\nimport * as i9 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i10 from \"ng-zorro-antd/core/wave\";\nimport * as i11 from \"ng-zorro-antd/select\";\nimport * as i12 from \"ng-zorro-antd/card\";\nimport * as i13 from \"ng-zorro-antd/popconfirm\";\nimport * as i14 from \"ng-zorro-antd/table\";\nimport * as i15 from \"ng-zorro-antd/icon\";\nimport * as i16 from \"ng-zorro-antd/date-picker\";\nimport * as i17 from \"../../../pipe/authPipe.pipe\";\nimport * as i18 from \"@ngx-translate/core\";\nconst _c0 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c1 = () => [8, 10];\nconst _c2 = () => ({\n  x: \"1000px\"\n});\nfunction SuperviseComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function SuperviseComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAdd());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", \"primary\")(\"nzLoading\", ctx_r2.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 3, \"FP.ADD\"), \" \");\n  }\n}\nfunction SuperviseComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function SuperviseComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onModify());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", \"primary\")(\"nzLoading\", ctx_r2.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 3, \"FP.MODIFY\"), \" \");\n  }\n}\nfunction SuperviseComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function SuperviseComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.OnDel());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", \"primary\")(\"nzLoading\", ctx_r2.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 3, \"FP.DELETE\"), \" \");\n  }\n}\nfunction SuperviseComponent_nz_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 31);\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r6.label)(\"nzValue\", option_r6.value);\n  }\n}\nfunction SuperviseComponent_tr_77_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 32);\n    i0.ɵɵlistener(\"click\", function SuperviseComponent_tr_77_Template_tr_click_0_listener() {\n      const info_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkData_V(info_r8));\n    });\n    i0.ɵɵelementStart(1, \"td\", 33);\n    i0.ɵɵlistener(\"nzCheckedChange\", function SuperviseComponent_tr_77_Template_td_nzCheckedChange_1_listener() {\n      const info_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheck(info_r8));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"td\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 35)(24, \"span\")(25, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function SuperviseComponent_tr_77_Template_a_click_25_listener() {\n      const info_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.modifyBoxType(info_r8));\n    });\n    i0.ɵɵelement(26, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"a\", 37);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵlistener(\"nzOnConfirm\", function SuperviseComponent_tr_77_Template_a_nzOnConfirm_27_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.OnDel());\n    });\n    i0.ɵɵelement(29, \"i\", 38);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const info_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzChecked\", info_r8.SELECTED);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 11, info_r8.reportYm, \"yyyy-MM\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r8.reportOrgCd);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(info_r8.reportOrgNm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(info_r8.remark);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(info_r8.createdUserName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 14, info_r8.createdTime, \"yyyy-MM-dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r8.modifiedUserName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 17, info_r8.modifiedTime, \"yyyy-MM-dd HH:mm:ss\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzPopconfirmTitle\", i0.ɵɵpipeBind1(28, 20, \"MSG.WEB0020\"));\n  }\n}\nfunction SuperviseComponent_ng_template_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n  }\n  if (rf & 2) {\n    const range_r10 = ctx.range;\n    const total_r11 = ctx.$implicit;\n    i0.ɵɵtextInterpolate7(\" \", i0.ɵɵpipeBind1(1, 7, \"OTH.FROM\"), \" \", range_r10[0], \" - \", range_r10[1], \" \", i0.ɵɵpipeBind1(2, 9, \"OTH.ITEM\"), \" \", i0.ɵɵpipeBind1(3, 11, \"OTH.TOTAL\"), \" \", total_r11, \" \", i0.ɵɵpipeBind1(4, 13, \"OTH.ITEM\"), \" \");\n  }\n}\nexport class SuperviseComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, cwfRestfulService) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.cwfRestfulService = cwfRestfulService;\n    this.mainStore = new TAS_T_SA_BUSINESS();\n    this.initData = [];\n    this.nzPageSizeOptions = [20, 30, 40, 50, 100];\n  }\n  /**\n  * desc:初始化查询条件\n  */\n  initCondtion() {\n    return {\n      id: new FormControl('', Validators.nullValidator),\n      reportDtStart: new FormControl('', Validators.nullValidator),\n      reportDtEnd: new FormControl('', Validators.nullValidator),\n      reportOrgCds: new FormControl([], Validators.nullValidator),\n      businessTypeCd: new FormControl('SL', Validators.nullValidator)\n    };\n  }\n  /**\n   * desc:页面加载后处理\n   */\n  onShow() {\n    this.queryList(true);\n    this.getOrgData();\n  }\n  afterClearData() {\n    this.conditionForm.reset();\n    this.conditionForm.controls['businessTypeCd'].setValue('SL');\n    this.queryList(true);\n  }\n  /**\n   * desc:查询列表\n   */\n  queryList(reset) {\n    for (const i in this.conditionForm.controls) {\n      this.conditionForm.controls[i].markAsDirty();\n      this.conditionForm.controls[i].updateValueAndValidity();\n    }\n    if (this.conditionForm.invalid) {\n      return;\n    }\n    setTimeout(() => {\n      if (reset) {\n        this.mainStore.pageing.PAGE = 1;\n      }\n      const requestData = {\n        page: this.mainStore.pageing.PAGE,\n        size: this.mainStore.pageing.LIMIT,\n        sortBy: {\n          reportYm: 'DESC',\n          createdTime: 'DESC'\n        }\n      };\n      const conditionData = {};\n      for (const form in this.conditionForm.controls) {\n        if (this.conditionForm.controls[form].value !== '' && this.conditionForm.controls[form].value !== null) {\n          conditionData[form] = this.conditionForm.controls[form].value;\n        }\n      }\n      if (Object.keys(conditionData).length > 0) {\n        // 对象至少有一个自有属性\n        requestData['data'] = conditionData;\n      }\n      this.mainStore.clearData();\n      this.cwfRestfulService.post('/saBusiness/list/page', requestData, this.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok === true) {\n          this.mainStore.loadDatas(rps.data.content);\n          this.mainStore.pageing.TOTAL = rps.data.totalElements; // 总条数\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    });\n  }\n  checkData_V(info) {\n    this.mainStore.getDatas().forEach(item => item.SELECTED = false);\n    this.onCheck(info);\n  }\n  beforeModify() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let records = _this.mainStore.getSelectedDatas();\n      if (records.length == 0) {\n        _this.showAlert(_this.getMsgi18nString(\"FK0018\"), _this.getMsgi18nString(\"FK7003\"));\n        return false;\n      } else if (records.length > 1) {\n        _this.showAlert(_this.getMsgi18nString(\"FK0018\"), _this.getMsgi18nString(\"FK8024\"));\n        return false;\n      }\n      if (records[0][\"isDelete\"] == \"1\") {\n        _this.showAlert(_this.getMsgi18nString(\"FK0018\"), _this.getMsgi18nString(\"FK8025\"));\n        return false;\n      }\n      return true;\n    })();\n  }\n  modifyBoxType(info) {\n    for (const storeData of this.mainStore.getDatas()) {\n      storeData.SELECTED = false;\n    }\n    info.SELECTED = true;\n    this.onModify();\n  }\n  //删除\n  OnDel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const sld = _this2.mainStore.getSelectedDatas();\n      if (sld.length == 0) {\n        _this2.showAlert(_this2.getMsgi18nString(\"FK0018\"), _this2.getMsgi18nString(\"FK7003\"));\n        return false;\n      }\n      let f = false;\n      const requestData = [];\n      sld.forEach(item => {\n        requestData.push(item['id']);\n      });\n      if (f) {\n        _this2.showAlert(_this2.getMsgi18nString(\"FK0018\"), _this2.getMsgi18nString(\"FK8032\"));\n        return false;\n      }\n      let state = yield _this2.showConfirm(_this2.getMsgi18nString('FK0018'), _this2.getMsgi18nString('FK0024'));\n      if (!(state === DialogResultEnum.yes)) {\n        return false;\n      }\n      _this2.loading = true;\n      _this2.cwfRestfulService.delete('/saBusiness/batch', _this2.gol.serviceName['tas'].en, {\n        body: requestData\n      }).then(rps => {\n        _this2.loading = false;\n        if (rps.ok) {\n          _this2.showState(ModalTypeEnum.success, '删除成功！');\n          _this2.queryList();\n        } else {\n          _this2.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n  * desc: 查看\n  */\n  OnView() {\n    let records = this.mainStore.getSelectedDatas();\n    if (records.length == 0) {\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK7003\"));\n      return false;\n    } else if (records.length > 1) {\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK8024\"));\n      return false;\n    }\n    let item = this.mainStore.getSelectedDatas();\n    const param = new CwfOpenParam();\n    // 设置页面操作状态\n    const objparam = {};\n    objparam[this.PAGE_STATE] = PageModeEnum.Custom;\n    this.openPage('/tas/supervise/list-edit', {\n      id: item[0]['id'],\n      state: \"custom\"\n    });\n  }\n  getOrgData() {\n    const rdata = {\n      type: 'company'\n    };\n    this.cwfRestfulService.post('/org/getRelateChildren', rdata, this.gol.serviceName['main'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 initData 数组\n        this.initData = rps.data.map(item => ({\n          label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\n          value: item.orgCode,\n          orgCode: item.orgCode,\n          orgName: item.orgName\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SuperviseComponent_Factory(t) {\n      return new (t || SuperviseComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.CwfRestfulService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuperviseComponent,\n      selectors: [[\"tas-supervise-app\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 80,\n      vars: 75,\n      consts: [[\"table\", \"\"], [\"rangeTemplate\", \"\"], [1, \"system-index-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"24\"], [\"nz-button\", \"\", 3, \"nzType\", \"nzLoading\", \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: 10px;\", \"nz-button\", \"\", 3, \"nzType\", \"nzLoading\", \"click\", 4, \"ngIf\"], [\"nz-button\", \"\", 1, \"mx-sm\", 2, \"float\", \"right\", \"margin-left\", \"10px\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"redo\"], [\"nz-button\", \"\", \"id\", \"query\", 2, \"float\", \"right\", 3, \"click\", \"nzType\", \"nzLoading\"], [\"nz-icon\", \"\", \"nzType\", \"search\"], [\"nz-form\", \"\", 2, \"margin-top\", \"20px\", 3, \"formGroup\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"6\"], [2, \"width\", \"120px\"], [\"nzMode\", \"month\", \"formControlName\", \"reportDtStart\", \"nzFormat\", \"yyyy-MM\"], [\"nzMode\", \"month\", \"formControlName\", \"reportDtEnd\", \"nzFormat\", \"yyyy-MM\"], [\"nz-col\", \"\", \"nzSpan\", \"18\", 2, \"margin-top\", \"5px\"], [\"formControlName\", \"reportOrgCds\", \"nzMode\", \"multiple\", 3, \"nzPlaceHolder\", \"nzShowSearch\"], [3, \"nzLabel\", \"nzValue\", 4, \"ngFor\", \"ngForOf\"], [\"nzShowPagination\", \"\", \"nzShowSizeChanger\", \"\", 3, \"nzPageIndexChange\", \"nzPageSizeChange\", \"nzLoading\", \"nzSize\", \"nzScroll\", \"nzFrontPagination\", \"nzShowTotal\", \"nzData\", \"nzPageSizeOptions\", \"nzTotal\", \"nzPageIndex\", \"nzPageSize\"], [\"nzShowCheckbox\", \"\", \"nzWidth\", \"40px\", 3, \"nzCheckedChange\", \"nzChecked\", \"nzIndeterminate\"], [\"nzWidth\", \"40px\"], [\"nzWidth\", \"120px\"], [\"nzWidth\", \"180px\"], [\"nzWidth\", \"220px\"], [\"nzWidth\", \"200px\"], [\"nzRight\", \"\", \"nzWidth\", \"110px\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\", \"nzLoading\"], [\"nz-icon\", \"\", \"nzType\", \"plus\"], [\"nz-button\", \"\", 2, \"margin-left\", \"10px\", 3, \"click\", \"nzType\", \"nzLoading\"], [3, \"nzLabel\", \"nzValue\"], [3, \"click\"], [\"nzShowCheckbox\", \"\", 3, \"nzCheckedChange\", \"nzChecked\"], [\"nzAlign\", \"center\"], [\"nzRight\", \"\", \"nzWidth\", \"75px\"], [\"nz-icon\", \"\", \"nzIconfont\", \"icon-edit-tb\", 2, \"font-size\", \"16px\", \"margin-right\", \"20px\"], [\"nz-popconfirm\", \"\", 3, \"nzOnConfirm\", \"nzPopconfirmTitle\"], [\"nz-icon\", \"\", \"nzIconfont\", \"icon-delete-tb\", 2, \"font-size\", \"16px\"]],\n      template: function SuperviseComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 2)(1, \"nz-row\")(2, \"nz-col\", 3)(3, \"div\");\n          i0.ɵɵtemplate(4, SuperviseComponent_button_4_Template, 4, 5, \"button\", 4);\n          i0.ɵɵpipe(5, \"auth\");\n          i0.ɵɵtemplate(6, SuperviseComponent_button_6_Template, 4, 5, \"button\", 5);\n          i0.ɵɵpipe(7, \"auth\");\n          i0.ɵɵtemplate(8, SuperviseComponent_button_8_Template, 4, 5, \"button\", 5);\n          i0.ɵɵpipe(9, \"auth\");\n          i0.ɵɵelementStart(10, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SuperviseComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.afterClearData());\n          });\n          i0.ɵɵelement(11, \"i\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function SuperviseComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.queryList(true));\n          });\n          i0.ɵɵelement(15, \"i\", 9);\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"form\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"nz-form-item\")(22, \"nz-form-label\", 13);\n          i0.ɵɵtext(23);\n          i0.ɵɵpipe(24, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nz-form-control\");\n          i0.ɵɵelement(26, \"nz-date-picker\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 12)(28, \"nz-form-item\")(29, \"nz-form-label\", 13);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"nz-form-control\");\n          i0.ɵɵelement(33, \"nz-date-picker\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"nz-form-item\")(36, \"nz-form-label\", 13);\n          i0.ɵɵtext(37);\n          i0.ɵɵpipe(38, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nz-form-control\")(40, \"nz-select\", 17);\n          i0.ɵɵtemplate(41, SuperviseComponent_nz_option_41_Template, 1, 2, \"nz-option\", 18);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(42, \"nz-table\", 19, 0);\n          i0.ɵɵlistener(\"nzPageIndexChange\", function SuperviseComponent_Template_nz_table_nzPageIndexChange_42_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.queryList());\n          })(\"nzPageSizeChange\", function SuperviseComponent_Template_nz_table_nzPageSizeChange_42_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.queryList(true));\n          });\n          i0.ɵɵtwoWayListener(\"nzPageIndexChange\", function SuperviseComponent_Template_nz_table_nzPageIndexChange_42_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.mainStore.pageing.PAGE, $event) || (ctx.mainStore.pageing.PAGE = $event);\n            return i0.ɵɵresetView($event);\n          })(\"nzPageSizeChange\", function SuperviseComponent_Template_nz_table_nzPageSizeChange_42_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.mainStore.pageing.LIMIT, $event) || (ctx.mainStore.pageing.LIMIT = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(44, \"thead\")(45, \"tr\")(46, \"th\", 20);\n          i0.ɵɵlistener(\"nzCheckedChange\", function SuperviseComponent_Template_th_nzCheckedChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.checkAll($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 21);\n          i0.ɵɵtext(48);\n          i0.ɵɵpipe(49, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 22);\n          i0.ɵɵtext(51);\n          i0.ɵɵpipe(52, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 23);\n          i0.ɵɵtext(54);\n          i0.ɵɵpipe(55, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"th\", 24);\n          i0.ɵɵtext(57);\n          i0.ɵɵpipe(58, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 22);\n          i0.ɵɵtext(60);\n          i0.ɵɵpipe(61, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\", 25);\n          i0.ɵɵtext(63);\n          i0.ɵɵpipe(64, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 25);\n          i0.ɵɵtext(66);\n          i0.ɵɵpipe(67, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\", 25);\n          i0.ɵɵtext(69);\n          i0.ɵɵpipe(70, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 25);\n          i0.ɵɵtext(72);\n          i0.ɵɵpipe(73, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"th\", 26);\n          i0.ɵɵtext(75, \" \\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"tbody\");\n          i0.ɵɵtemplate(77, SuperviseComponent_tr_77_Template, 30, 22, \"tr\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(78, SuperviseComponent_ng_template_78_Template, 5, 15, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const table_r12 = i0.ɵɵreference(43);\n          const rangeTemplate_r13 = i0.ɵɵreference(79);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(72, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 38, \"draftsurvey:add\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(7, 40, \"draftsurvey:modify\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(9, 42, \"draftsurvey:del\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(13, 44, \"FP.CLEAR\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"primary\")(\"nzLoading\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(17, 46, \"FP.QUERY\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.conditionForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(73, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 48, \"TAS.REPORT_DT_START\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 50, \"TAS.REPORT_DT_END\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(38, 52, \"TAS.REPORT_ORG\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.initData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzLoading\", ctx.loading)(\"nzSize\", \"middle\")(\"nzScroll\", i0.ɵɵpureFunction0(74, _c2))(\"nzFrontPagination\", false)(\"nzShowTotal\", rangeTemplate_r13)(\"nzData\", ctx.mainStore.getDatas())(\"nzPageSizeOptions\", ctx.nzPageSizeOptions)(\"nzTotal\", ctx.mainStore.pageing.TOTAL);\n          i0.ɵɵtwoWayProperty(\"nzPageIndex\", ctx.mainStore.pageing.PAGE)(\"nzPageSize\", ctx.mainStore.pageing.LIMIT);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzChecked\", ctx.isAllDisplayDataChecked)(\"nzIndeterminate\", ctx.isIndeterminate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(49, 54, \"DB.SEQ\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(52, 56, \"TAS.REPORT_YM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 58, \"TAS.REPORT_ORG_CD\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(58, 60, \"TAS.REPORT_ORG_NM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(61, 62, \"TAS.REMARK\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(64, 64, \"TAS.CREAT_OPER_NM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(67, 66, \"TAS.CREAT_DT\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(70, 68, \"TAS.MODIFIER_NM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(73, 70, \"TAS.MODIFIED_DT\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", table_r12.data);\n        }\n      },\n      dependencies: [i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i5.NgForOf, i5.NgIf, i4.FormGroupDirective, i4.FormControlName, i6.NzColDirective, i6.NzRowDirective, i7.NzFormDirective, i7.NzFormItemComponent, i7.NzFormLabelComponent, i7.NzFormControlComponent, i8.NzButtonComponent, i9.ɵNzTransitionPatchDirective, i10.NzWaveDirective, i11.NzOptionComponent, i11.NzSelectComponent, i12.NzCardComponent, i13.NzPopconfirmDirective, i14.NzTableComponent, i14.NzTableCellDirective, i14.NzThMeasureDirective, i14.NzTdAddOnComponent, i14.NzTheadComponent, i14.NzTbodyComponent, i14.NzTrDirective, i14.NzCellFixedDirective, i14.NzCellAlignDirective, i14.NzThSelectionComponent, i15.NzIconDirective, i16.NzDatePickerComponent, i17.AuthPipe, i5.DatePipe, i18.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "Validators", "CwfBaseCrud", "CwfOpenParam", "DialogResultEnum", "ModalTypeEnum", "PageModeEnum", "TAS_T_SA_BUSINESS", "i0", "ɵɵelementStart", "ɵɵlistener", "SuperviseComponent_button_4_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onAdd", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "loading", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "SuperviseComponent_button_6_Template_button_click_0_listener", "_r4", "onModify", "SuperviseComponent_button_8_Template_button_click_0_listener", "_r5", "OnDel", "option_r6", "label", "value", "SuperviseComponent_tr_77_Template_tr_click_0_listener", "info_r8", "_r7", "$implicit", "checkData_V", "SuperviseComponent_tr_77_Template_td_nzCheckedChange_1_listener", "onCheck", "SuperviseComponent_tr_77_Template_a_click_25_listener", "modifyBoxType", "SuperviseComponent_tr_77_Template_a_nzOnConfirm_27_listener", "SELECTED", "ɵɵtextInterpolate", "i_r9", "ɵɵpipeBind2", "reportYm", "reportOrgCd", "reportOrgNm", "remark", "createdUserName", "createdTime", "modifiedUserName", "modifiedTime", "ɵɵtextInterpolate7", "range_r10", "total_r11", "SuperviseComponent", "constructor", "cwfBusContextService", "gol", "cwfRestfulService", "mainStore", "initData", "nzPageSizeOptions", "initCondtion", "id", "nullValidator", "reportDtStart", "reportDtEnd", "reportOrgCds", "businessTypeCd", "onShow", "queryList", "getOrgData", "afterClearData", "conditionForm", "reset", "controls", "setValue", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "setTimeout", "pageing", "PAGE", "requestData", "page", "size", "LIMIT", "sortBy", "conditionData", "form", "Object", "keys", "length", "clearData", "post", "serviceName", "en", "then", "rps", "ok", "loadDatas", "data", "content", "TOTAL", "totalElements", "showState", "error", "msg", "info", "getDatas", "for<PERSON>ach", "item", "beforeModify", "_this", "_asyncToGenerator", "records", "getSelectedDatas", "show<PERSON><PERSON><PERSON>", "getMsgi18nString", "storeData", "_this2", "sld", "f", "push", "state", "showConfirm", "yes", "delete", "body", "success", "OnView", "param", "obj<PERSON><PERSON>", "PAGE_STATE", "Custom", "openPage", "rdata", "type", "map", "orgCode", "orgName", "companyCode", "companyName", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "CwfRestfulService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SuperviseComponent_Template", "rf", "ctx", "ɵɵtemplate", "SuperviseComponent_button_4_Template", "SuperviseComponent_button_6_Template", "SuperviseComponent_button_8_Template", "SuperviseComponent_Template_button_click_10_listener", "_r1", "SuperviseComponent_Template_button_click_14_listener", "SuperviseComponent_nz_option_41_Template", "SuperviseComponent_Template_nz_table_nzPageIndexChange_42_listener", "SuperviseComponent_Template_nz_table_nzPageSizeChange_42_listener", "ɵɵtwoWayListener", "$event", "ɵɵtwoWayBindingSet", "SuperviseComponent_Template_th_nzCheckedChange_46_listener", "checkAll", "SuperviseComponent_tr_77_Template", "SuperviseComponent_ng_template_78_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction0", "_c0", "_c1", "_c2", "rangeTemplate_r13", "ɵɵtwoWayProperty", "isAllDisplayDataChecked", "isIndeterminate", "table_r12"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\supervise\\supervise.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport {CwfBaseCrud, CwfBusContextService, CwfModel, CwfOpenParam, DialogResultEnum, ModalTypeEnum, PageModeEnum} from 'cwf-ng-library';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { TAS_T_SA_BUSINESS } from '@store/BCD/TAS_T_SA_BUSINESS';\r\n\r\n@Component({\r\n  selector: 'tas-supervise-app',\r\n  templateUrl: './supervise.component.html'\r\n})\r\nexport class SuperviseComponent extends CwfBaseCrud {\r\n  mainStore = new TAS_T_SA_BUSINESS();\r\n  initData = [];\r\n  constructor(\r\n    cwfBusContextService: CwfBusContextService,\r\n    private gol: GlobalDataService,\r\n    private cwfRestfulService: CwfRestfulService\r\n  ) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  nzPageSizeOptions = [20, 30, 40, 50, 100];\r\n  conditionForm: FormGroup;\r\n  /**\r\n * desc:初始化查询条件\r\n */\r\n  initCondtion() {\r\n    return {\r\n      id: new FormControl('', Validators.nullValidator),\r\n      reportDtStart: new FormControl('', Validators.nullValidator),\r\n      reportDtEnd: new FormControl('', Validators.nullValidator),\r\n      reportOrgCds: new FormControl([], Validators.nullValidator),\r\n      businessTypeCd: new FormControl('SL', Validators.nullValidator)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * desc:页面加载后处理\r\n   */\r\n  onShow() {\r\n    this.queryList(true);\r\n    this.getOrgData();\r\n  }\r\n\r\n  afterClearData() {\r\n    this.conditionForm.reset();\r\n    this.conditionForm.controls['businessTypeCd'].setValue('SL')\r\n    this.queryList(true);\r\n  }\r\n\r\n  /**\r\n   * desc:查询列表\r\n   */\r\n  queryList(reset?: boolean) {\r\n    for (const i in this.conditionForm.controls) {\r\n      this.conditionForm.controls[i].markAsDirty();\r\n      this.conditionForm.controls[i].updateValueAndValidity();\r\n    }\r\n    if (this.conditionForm.invalid) {\r\n      return;\r\n    }\r\n    setTimeout(() => {\r\n      if (reset) {\r\n        this.mainStore.pageing.PAGE = 1;\r\n      }\r\n      const requestData = {\r\n        page: this.mainStore.pageing.PAGE,\r\n        size: this.mainStore.pageing.LIMIT,\r\n        sortBy: {\r\n          reportYm: 'DESC',\r\n          createdTime: 'DESC'\r\n        }\r\n      };\r\n      const conditionData = {};\r\n      for (const form in this.conditionForm.controls) {\r\n        if (this.conditionForm.controls[form].value !== '' && this.conditionForm.controls[form].value !== null) {\r\n          conditionData[form] = this.conditionForm.controls[form].value;\r\n        }\r\n      }\r\n      if (Object.keys(conditionData).length > 0) {\r\n        // 对象至少有一个自有属性\r\n        requestData['data'] = conditionData;\r\n      }\r\n      this.mainStore.clearData();\r\n      this.cwfRestfulService.post('/saBusiness/list/page', requestData, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok === true) {\r\n          this.mainStore.loadDatas(rps.data.content);\r\n          this.mainStore.pageing.TOTAL = rps.data.totalElements; // 总条数\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  checkData_V(info: any) {\r\n    this.mainStore.getDatas().forEach(item => item.SELECTED = false);\r\n    this.onCheck(info);\r\n  }\r\n\r\n  async beforeModify(): Promise<boolean> {\r\n    let records = this.mainStore.getSelectedDatas();\r\n    if (records.length == 0) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK7003\"));\r\n      return false;\r\n    } else if (records.length > 1) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK8024\"));\r\n      return false;\r\n    }\r\n    if (records[0][\"isDelete\"] == \"1\") {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK8025\"));\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  modifyBoxType(info: any) {\r\n    for (const storeData of this.mainStore.getDatas()) {\r\n      storeData.SELECTED = false;\r\n    }\r\n    info.SELECTED = true;\r\n    this.onModify();\r\n  }\r\n\r\n\r\n  //删除\r\n  async OnDel() {\r\n    const sld: CwfModel[] = this.mainStore.getSelectedDatas();\r\n    if (sld.length == 0) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK7003\"));\r\n      return false;\r\n    }\r\n    let f = false;\r\n    const requestData = []\r\n    sld.forEach(item => {\r\n      requestData.push(item['id']);\r\n    });\r\n    if (f) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK8032\"));\r\n      return false;\r\n    }\r\n    let state = await this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0024'));\r\n\r\n    if (!(state === DialogResultEnum.yes)) {\r\n      return false;\r\n    }\r\n    this.loading = true;\r\n    this.cwfRestfulService.delete('/saBusiness/batch', this.gol.serviceName['tas'].en, { body: requestData }).then((rps: responseInterface) => {\r\n      this.loading = false;\r\n      if (rps.ok) {\r\n        this.showState(ModalTypeEnum.success, '删除成功！');\r\n        this.queryList();\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n  * desc: 查看\r\n  */\r\n  OnView() {\r\n    let records = this.mainStore.getSelectedDatas();\r\n    if (records.length == 0) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK7003\"));\r\n      return false;\r\n    } else if (records.length > 1) {\r\n      this.showAlert(this.getMsgi18nString(\"FK0018\"), this.getMsgi18nString(\"FK8024\"));\r\n      return false;\r\n    }\r\n    let item = this.mainStore.getSelectedDatas();\r\n    const param = new CwfOpenParam();\r\n    // 设置页面操作状态\r\n    const objparam: object = {};\r\n    objparam[this.PAGE_STATE] = PageModeEnum.Custom;\r\n    this.openPage('/tas/supervise/list-edit', { id: item[0]['id'], state: \"custom\" });\r\n  }\r\n\r\n  getOrgData() {\r\n    const rdata = { type: 'company' };\r\n    this.cwfRestfulService\r\n        .post(\r\n          '/org/getRelateChildren',\r\n          rdata,\r\n          this.gol.serviceName['main'].en\r\n        )\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            // 更新 initData 数组\r\n            this.initData = rps.data.map((item) => ({\r\n              label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\r\n              value: item.orgCode,\r\n              orgCode: item.orgCode,\r\n              orgName: item.orgName\r\n            }));\r\n          }else{\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n    })\r\n  }\r\n\r\n}\r\n", "<nz-card class=\"system-index-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <!-- <nz-row>\r\n    <nz-col nzSpan=\"6\" class=\"list-title\">\r\n      <svg-icon [src]=\"'assets/img/icons/filter.svg'\"></svg-icon>\r\n      <div class=\"title\">{{ 'DB.BOXTYPE' | translate }}</div>\r\n    </nz-col>\r\n  </nz-row> -->\r\n\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div>\r\n        <!-- 添加按钮 -->\r\n        <button nz-button [nzType]=\"'primary'\" (click)=\"onAdd()\" [nzLoading]=\"loading\" *ngIf=\"'draftsurvey:add' | auth\">\r\n          <i nz-icon nzType=\"plus\"></i>{{ 'FP.ADD' | translate }}\r\n        </button>\r\n\r\n        <!-- 修改按钮 -->\r\n        <button style=\"margin-left: 10px;\" nz-button [nzType]=\"'primary'\" (click)=\"onModify()\" [nzLoading]=\"loading\"\r\n                *ngIf=\"'draftsurvey:modify' | auth\">\r\n          <i nz-icon nzType=\"plus\"></i>{{ 'FP.MODIFY' | translate }}\r\n        </button>\r\n\r\n        <!-- 删除按钮 -->\r\n        <button style=\"margin-left: 10px;\" nz-button [nzType]=\"'primary'\" (click)=\"OnDel()\" [nzLoading]=\"loading\"\r\n                *ngIf=\"'draftsurvey:del' | auth\">\r\n          <i nz-icon nzType=\"plus\"></i>{{ 'FP.DELETE' | translate }}\r\n        </button>\r\n\r\n        <!-- 清空 -->\r\n        <button nz-button (click)=\"afterClearData()\" class=\"mx-sm\" style=\"float: right;margin-left: 10px;\">\r\n          <i nz-icon nzType=\"redo\"></i>{{ 'FP.CLEAR' | translate }}\r\n        </button>\r\n        <!-- 查询 -->\r\n        <button nz-button [nzType]=\"'primary'\" (click)=\"queryList(true)\" id=\"query\" [nzLoading]=\"loading\"\r\n                style=\"float: right;\">\r\n          <i nz-icon nzType=\"search\"></i>{{ 'FP.QUERY' | translate }}\r\n        </button>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n\r\n  <!-- 查询条件表单 -->\r\n  <form style=\"margin-top: 20px;\" nz-form [formGroup]=\"conditionForm\">\r\n    <div nz-row [nzGutter]=\"[8,10]\">\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.REPORT_DT_START' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker nzMode=\"month\" formControlName=\"reportDtStart\" nzFormat=\"yyyy-MM\"></nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.REPORT_DT_END' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker nzMode=\"month\" formControlName=\"reportDtEnd\" nzFormat=\"yyyy-MM\"></nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"18\" style=\"margin-top: 5px;\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px;\">{{ 'TAS.REPORT_ORG' | translate }}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"reportOrgCds\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              nzMode=\"multiple\">\r\n              <nz-option *ngFor=\"let option of initData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n    </div>\r\n  </form>\r\n\r\n  <!-- 数据表格 -->\r\n  <nz-table #table nzShowPagination nzShowSizeChanger [nzLoading]=\"loading\" [nzSize]=\"'middle'\" [nzScroll]=\"{x:'1000px'}\"\r\n            [nzFrontPagination]=\"false\" [nzShowTotal]=\"rangeTemplate\" [nzData]=\"mainStore.getDatas()\" [nzPageSizeOptions]=\"nzPageSizeOptions\"\r\n            (nzPageIndexChange)=\"queryList()\" [nzTotal]=\"mainStore.pageing.TOTAL\" (nzPageSizeChange)=\"queryList(true)\"\r\n            [(nzPageIndex)]=\"mainStore.pageing.PAGE\" [(nzPageSize)]=\"mainStore.pageing.LIMIT\">\r\n    <thead>\r\n    <tr>\r\n      <!-- 多选列 -->\r\n      <th nzShowCheckbox nzWidth=\"40px\" [nzChecked]=\"isAllDisplayDataChecked\" [nzIndeterminate]=\"isIndeterminate\"\r\n          (nzCheckedChange)=\"checkAll($event)\">\r\n      </th>\r\n      <!-- 序号 -->\r\n      <th nzWidth=\"40px\">{{ 'DB.SEQ' | translate }}</th>\r\n\r\n      <th nzWidth=\"120px\">{{ 'TAS.REPORT_YM' | translate }}</th>\r\n\r\n      <th nzWidth=\"180px\">{{ 'TAS.REPORT_ORG_CD' | translate }}</th>\r\n\r\n      <th nzWidth=\"220px\">{{ 'TAS.REPORT_ORG_NM' | translate }}</th>\r\n\r\n      <th nzWidth=\"120px\">{{ 'TAS.REMARK' | translate }}</th>\r\n\r\n      <th nzWidth=\"200px\">{{ 'TAS.CREAT_OPER_NM' | translate }}</th>\r\n\r\n      <th nzWidth=\"200px\">{{ 'TAS.CREAT_DT' | translate }}</th>\r\n\r\n      <th nzWidth=\"200px\">{{ 'TAS.MODIFIER_NM' | translate }}</th>\r\n\r\n      <th nzWidth=\"200px\">{{ 'TAS.MODIFIED_DT' | translate }}</th>\r\n      <th nzRight nzWidth=\"110px\">\r\n        操作\r\n      </th>\r\n    </tr>\r\n    </thead>\r\n\r\n    <tbody>\r\n    <tr *ngFor=\"let info of table.data; let i = index\" (click)=\"checkData_V(info)\">\r\n\r\n      <!-- 多选框 -->\r\n      <td nzShowCheckbox [nzChecked]=\"info.SELECTED\" (nzCheckedChange)=\"onCheck(info)\"></td>\r\n\r\n      <!-- 序号 -->\r\n      <td nzAlign=\"center\">{{ i + 1 }}</td>\r\n\r\n      <td>{{ info.reportYm | date:'yyyy-MM' }}</td>\r\n\r\n      <td>{{ info.reportOrgCd }}</td>\r\n\r\n      <td>{{ info.reportOrgNm }}</td>\r\n\r\n      <td>{{ info.remark }}</td>\r\n\r\n      <!-- 创建人单元格 -->\r\n      <td>{{ info.createdUserName }}</td>\r\n      <!-- 创建时间单元格 -->\r\n      <td>{{ info.createdTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>\r\n      <!-- 修改人单元格 -->\r\n      <td>{{ info.modifiedUserName }}</td>\r\n      <!-- 修改时间单元格 -->\r\n      <td>{{ info.modifiedTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>\r\n\r\n      <td nzRight nzWidth=\"75px\">\r\n        <span>\r\n          <a (click)=\"modifyBoxType(info)\">\r\n            <i nz-icon nzIconfont=\"icon-edit-tb\" style=\"font-size: 16px; margin-right: 20px\"></i>\r\n          </a>\r\n          <a nz-popconfirm (nzOnConfirm)=\"OnDel()\"\r\n            [nzPopconfirmTitle]=\"'MSG.WEB0020' | translate\">\r\n            <i nz-icon nzIconfont=\"icon-delete-tb\" style=\"font-size: 16px\"></i>\r\n          </a>\r\n        </span>\r\n      </td>       \r\n    </tr>\r\n    </tbody>\r\n  </nz-table>\r\n\r\n  <!-- 分页模板 -->\r\n  <ng-template #rangeTemplate let-range=\"range\" let-total>\r\n    {{ 'OTH.FROM' | translate }} {{ range[0] }} - {{ range[1] }} {{ 'OTH.ITEM' | translate }}\r\n    {{ 'OTH.TOTAL' | translate }} {{ total }} {{ 'OTH.ITEM' | translate }}\r\n  </ng-template>\r\n</nz-card>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AACnE,SAAQC,WAAW,EAAkCC,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,QAAO,gBAAgB;AAIvI,SAASC,iBAAiB,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICMxDC,EAAA,CAAAC,cAAA,iBAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IACtDT,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,GAC/B;;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;IAFgDZ,EAAvC,CAAAa,UAAA,qBAAoB,cAAAP,MAAA,CAAAQ,OAAA,CAAwC;IAC/Cd,EAAA,CAAAe,SAAA,GAC/B;IAD+Bf,EAAA,CAAAgB,kBAAA,KAAAhB,EAAA,CAAAiB,WAAA,sBAC/B;;;;;;IAGAjB,EAAA,CAAAC,cAAA,iBAC4C;IADsBD,EAAA,CAAAE,UAAA,mBAAAgB,6DAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IAEpFpB,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,GAC/B;;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;IAH8EZ,EAA1C,CAAAa,UAAA,qBAAoB,cAAAP,MAAA,CAAAQ,OAAA,CAA2C;IAE7Ed,EAAA,CAAAe,SAAA,GAC/B;IAD+Bf,EAAA,CAAAgB,kBAAA,KAAAhB,EAAA,CAAAiB,WAAA,yBAC/B;;;;;;IAGAjB,EAAA,CAAAC,cAAA,iBACyC;IADyBD,EAAA,CAAAE,UAAA,mBAAAmB,6DAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,KAAA,EAAO;IAAA,EAAC;IAEjFvB,EAAA,CAAAU,SAAA,YAA6B;IAAAV,EAAA,CAAAW,MAAA,GAC/B;;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;IAH2EZ,EAAvC,CAAAa,UAAA,qBAAoB,cAAAP,MAAA,CAAAQ,OAAA,CAAwC;IAE1Ed,EAAA,CAAAe,SAAA,GAC/B;IAD+Bf,EAAA,CAAAgB,kBAAA,KAAAhB,EAAA,CAAAiB,WAAA,yBAC/B;;;;;IA2CMjB,EAAA,CAAAU,SAAA,oBACY;;;;IADwDV,EAAzB,CAAAa,UAAA,YAAAW,SAAA,CAAAC,KAAA,CAAwB,YAAAD,SAAA,CAAAE,KAAA,CAAyB;;;;;;IA8CtG1B,EAAA,CAAAC,cAAA,aAA+E;IAA5BD,EAAA,CAAAE,UAAA,mBAAAyB,sDAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,WAAA,CAAAH,OAAA,CAAiB;IAAA,EAAC;IAG5E5B,EAAA,CAAAC,cAAA,aAAiF;IAAlCD,EAAA,CAAAE,UAAA,6BAAA8B,gEAAA;MAAA,MAAAJ,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAmBF,MAAA,CAAA2B,OAAA,CAAAL,OAAA,CAAa;IAAA,EAAC;IAAC5B,EAAA,CAAAY,YAAA,EAAK;IAGtFZ,EAAA,CAAAC,cAAA,aAAqB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAErCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,GAAoC;;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE7CZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE/BZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,IAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE/BZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAG1BZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAEnCZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAAmD;;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE5DZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAEpCZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAAoD;;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAIzDZ,EAFJ,CAAAC,cAAA,cAA2B,YACnB,aAC6B;IAA9BD,EAAA,CAAAE,UAAA,mBAAAgC,sDAAA;MAAA,MAAAN,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6B,aAAA,CAAAP,OAAA,CAAmB;IAAA,EAAC;IAC9B5B,EAAA,CAAAU,SAAA,aAAqF;IACvFV,EAAA,CAAAY,YAAA,EAAI;IACJZ,EAAA,CAAAC,cAAA,aACkD;;IADjCD,EAAA,CAAAE,UAAA,yBAAAkC,4DAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAiB,KAAA,EAAO;IAAA,EAAC;IAEtCvB,EAAA,CAAAU,SAAA,aAAmE;IAI3EV,EAHM,CAAAY,YAAA,EAAI,EACC,EACJ,EACF;;;;;IAjCgBZ,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAa,UAAA,cAAAe,OAAA,CAAAS,QAAA,CAA2B;IAGzBrC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAsC,iBAAA,CAAAC,IAAA,KAAW;IAE5BvC,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAwC,WAAA,QAAAZ,OAAA,CAAAa,QAAA,aAAoC;IAEpCzC,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAsC,iBAAA,CAAAV,OAAA,CAAAc,WAAA,CAAsB;IAEtB1C,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAsC,iBAAA,CAAAV,OAAA,CAAAe,WAAA,CAAsB;IAEtB3C,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAsC,iBAAA,CAAAV,OAAA,CAAAgB,MAAA,CAAiB;IAGjB5C,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAsC,iBAAA,CAAAV,OAAA,CAAAiB,eAAA,CAA0B;IAE1B7C,EAAA,CAAAe,SAAA,GAAmD;IAAnDf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAwC,WAAA,SAAAZ,OAAA,CAAAkB,WAAA,yBAAmD;IAEnD9C,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAsC,iBAAA,CAAAV,OAAA,CAAAmB,gBAAA,CAA2B;IAE3B/C,EAAA,CAAAe,SAAA,GAAoD;IAApDf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAwC,WAAA,SAAAZ,OAAA,CAAAoB,YAAA,yBAAoD;IAQlDhD,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAa,UAAA,sBAAAb,EAAA,CAAAiB,WAAA,wBAA+C;;;;;IAWvDjB,EAAA,CAAAW,MAAA,GAEF;;;;;;;;;IAFEX,EAAA,CAAAiD,kBAAA,MAAAjD,EAAA,CAAAiB,WAAA,yBAAAiC,SAAA,YAAAA,SAAA,UAAAlD,EAAA,CAAAiB,WAAA,yBAAAjB,EAAA,CAAAiB,WAAA,2BAAAkC,SAAA,OAAAnD,EAAA,CAAAiB,WAAA,yBAEF;;;ADnJF,OAAM,MAAOmC,kBAAmB,SAAQ1D,WAAW;EAGjD2D,YACEC,oBAA0C,EAClCC,GAAsB,EACtBC,iBAAoC;IAE5C,KAAK,CAACF,oBAAoB,CAAC;IAHnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAL3B,KAAAC,SAAS,GAAG,IAAI1D,iBAAiB,EAAE;IACnC,KAAA2D,QAAQ,GAAG,EAAE;IASb,KAAAC,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAFzC;EAIA;;;EAGAC,YAAYA,CAAA;IACV,OAAO;MACLC,EAAE,EAAE,IAAIrE,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqE,aAAa,CAAC;MACjDC,aAAa,EAAE,IAAIvE,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqE,aAAa,CAAC;MAC5DE,WAAW,EAAE,IAAIxE,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqE,aAAa,CAAC;MAC1DG,YAAY,EAAE,IAAIzE,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqE,aAAa,CAAC;MAC3DI,cAAc,EAAE,IAAI1E,WAAW,CAAC,IAAI,EAAEC,UAAU,CAACqE,aAAa;KAC/D;EACH;EAEA;;;EAGAK,MAAMA,CAAA;IACJ,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;IACpB,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACC,aAAa,CAACC,KAAK,EAAE;IAC1B,IAAI,CAACD,aAAa,CAACE,QAAQ,CAAC,gBAAgB,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC5D,IAAI,CAACN,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGAA,SAASA,CAACI,KAAe;IACvB,KAAK,MAAMG,CAAC,IAAI,IAAI,CAACJ,aAAa,CAACE,QAAQ,EAAE;MAC3C,IAAI,CAACF,aAAa,CAACE,QAAQ,CAACE,CAAC,CAAC,CAACC,WAAW,EAAE;MAC5C,IAAI,CAACL,aAAa,CAACE,QAAQ,CAACE,CAAC,CAAC,CAACE,sBAAsB,EAAE;IACzD;IACA,IAAI,IAAI,CAACN,aAAa,CAACO,OAAO,EAAE;MAC9B;IACF;IACAC,UAAU,CAAC,MAAK;MACd,IAAIP,KAAK,EAAE;QACT,IAAI,CAACf,SAAS,CAACuB,OAAO,CAACC,IAAI,GAAG,CAAC;MACjC;MACA,MAAMC,WAAW,GAAG;QAClBC,IAAI,EAAE,IAAI,CAAC1B,SAAS,CAACuB,OAAO,CAACC,IAAI;QACjCG,IAAI,EAAE,IAAI,CAAC3B,SAAS,CAACuB,OAAO,CAACK,KAAK;QAClCC,MAAM,EAAE;UACN7C,QAAQ,EAAE,MAAM;UAChBK,WAAW,EAAE;;OAEhB;MACD,MAAMyC,aAAa,GAAG,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACjB,aAAa,CAACE,QAAQ,EAAE;QAC9C,IAAI,IAAI,CAACF,aAAa,CAACE,QAAQ,CAACe,IAAI,CAAC,CAAC9D,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC6C,aAAa,CAACE,QAAQ,CAACe,IAAI,CAAC,CAAC9D,KAAK,KAAK,IAAI,EAAE;UACtG6D,aAAa,CAACC,IAAI,CAAC,GAAG,IAAI,CAACjB,aAAa,CAACE,QAAQ,CAACe,IAAI,CAAC,CAAC9D,KAAK;QAC/D;MACF;MACA,IAAI+D,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;QACzC;QACAT,WAAW,CAAC,MAAM,CAAC,GAAGK,aAAa;MACrC;MACA,IAAI,CAAC9B,SAAS,CAACmC,SAAS,EAAE;MAC1B,IAAI,CAACpC,iBAAiB,CAACqC,IAAI,CAAC,uBAAuB,EAAEX,WAAW,EAAE,IAAI,CAAC3B,GAAG,CAACuC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACC,IAAI,CAAEC,GAAsB,IAAI;QAChI,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;UACnB,IAAI,CAACzC,SAAS,CAAC0C,SAAS,CAACF,GAAG,CAACG,IAAI,CAACC,OAAO,CAAC;UAC1C,IAAI,CAAC5C,SAAS,CAACuB,OAAO,CAACsB,KAAK,GAAGL,GAAG,CAACG,IAAI,CAACG,aAAa,CAAC,CAAC;QACzD,CAAC,MAAM;UACL,IAAI,CAACC,SAAS,CAAC3G,aAAa,CAAC4G,KAAK,EAAER,GAAG,CAACS,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA3E,WAAWA,CAAC4E,IAAS;IACnB,IAAI,CAAClD,SAAS,CAACmD,QAAQ,EAAE,CAACC,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACzE,QAAQ,GAAG,KAAK,CAAC;IAChE,IAAI,CAACJ,OAAO,CAAC0E,IAAI,CAAC;EACpB;EAEMI,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAIC,OAAO,GAAGF,KAAI,CAACvD,SAAS,CAAC0D,gBAAgB,EAAE;MAC/C,IAAID,OAAO,CAACvB,MAAM,IAAI,CAAC,EAAE;QACvBqB,KAAI,CAACI,SAAS,CAACJ,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,EAAEL,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChF,OAAO,KAAK;MACd,CAAC,MAAM,IAAIH,OAAO,CAACvB,MAAM,GAAG,CAAC,EAAE;QAC7BqB,KAAI,CAACI,SAAS,CAACJ,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,EAAEL,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChF,OAAO,KAAK;MACd;MACA,IAAIH,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,GAAG,EAAE;QACjCF,KAAI,CAACI,SAAS,CAACJ,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,EAAEL,KAAI,CAACK,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChF,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IAAC;EACd;EAEAlF,aAAaA,CAACwE,IAAS;IACrB,KAAK,MAAMW,SAAS,IAAI,IAAI,CAAC7D,SAAS,CAACmD,QAAQ,EAAE,EAAE;MACjDU,SAAS,CAACjF,QAAQ,GAAG,KAAK;IAC5B;IACAsE,IAAI,CAACtE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACjB,QAAQ,EAAE;EACjB;EAGA;EACMG,KAAKA,CAAA;IAAA,IAAAgG,MAAA;IAAA,OAAAN,iBAAA;MACT,MAAMO,GAAG,GAAeD,MAAI,CAAC9D,SAAS,CAAC0D,gBAAgB,EAAE;MACzD,IAAIK,GAAG,CAAC7B,MAAM,IAAI,CAAC,EAAE;QACnB4B,MAAI,CAACH,SAAS,CAACG,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,EAAEE,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChF,OAAO,KAAK;MACd;MACA,IAAII,CAAC,GAAG,KAAK;MACb,MAAMvC,WAAW,GAAG,EAAE;MACtBsC,GAAG,CAACX,OAAO,CAACC,IAAI,IAAG;QACjB5B,WAAW,CAACwC,IAAI,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF,IAAIW,CAAC,EAAE;QACLF,MAAI,CAACH,SAAS,CAACG,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,EAAEE,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChF,OAAO,KAAK;MACd;MACA,IAAIM,KAAK,SAASJ,MAAI,CAACK,WAAW,CAACL,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,EAAEE,MAAI,CAACF,gBAAgB,CAAC,QAAQ,CAAC,CAAC;MAEpG,IAAI,EAAEM,KAAK,KAAK/H,gBAAgB,CAACiI,GAAG,CAAC,EAAE;QACrC,OAAO,KAAK;MACd;MACAN,MAAI,CAACzG,OAAO,GAAG,IAAI;MACnByG,MAAI,CAAC/D,iBAAiB,CAACsE,MAAM,CAAC,mBAAmB,EAAEP,MAAI,CAAChE,GAAG,CAACuC,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,EAAE;QAAEgC,IAAI,EAAE7C;MAAW,CAAE,CAAC,CAACc,IAAI,CAAEC,GAAsB,IAAI;QACxIsB,MAAI,CAACzG,OAAO,GAAG,KAAK;QACpB,IAAImF,GAAG,CAACC,EAAE,EAAE;UACVqB,MAAI,CAACf,SAAS,CAAC3G,aAAa,CAACmI,OAAO,EAAE,OAAO,CAAC;UAC9CT,MAAI,CAACnD,SAAS,EAAE;QAClB,CAAC,MAAM;UACLmD,MAAI,CAACf,SAAS,CAAC3G,aAAa,CAAC4G,KAAK,EAAER,GAAG,CAACS,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACL;EAEA;;;EAGAuB,MAAMA,CAAA;IACJ,IAAIf,OAAO,GAAG,IAAI,CAACzD,SAAS,CAAC0D,gBAAgB,EAAE;IAC/C,IAAID,OAAO,CAACvB,MAAM,IAAI,CAAC,EAAE;MACvB,IAAI,CAACyB,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,CAAC;MAChF,OAAO,KAAK;IACd,CAAC,MAAM,IAAIH,OAAO,CAACvB,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACyB,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,CAAC;MAChF,OAAO,KAAK;IACd;IACA,IAAIP,IAAI,GAAG,IAAI,CAACrD,SAAS,CAAC0D,gBAAgB,EAAE;IAC5C,MAAMe,KAAK,GAAG,IAAIvI,YAAY,EAAE;IAChC;IACA,MAAMwI,QAAQ,GAAW,EAAE;IAC3BA,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,GAAGtI,YAAY,CAACuI,MAAM;IAC/C,IAAI,CAACC,QAAQ,CAAC,0BAA0B,EAAE;MAAEzE,EAAE,EAAEiD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MAAEa,KAAK,EAAE;IAAQ,CAAE,CAAC;EACnF;EAEAtD,UAAUA,CAAA;IACR,MAAMkE,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAS,CAAE;IACjC,IAAI,CAAChF,iBAAiB,CACjBqC,IAAI,CACH,wBAAwB,EACxB0C,KAAK,EACL,IAAI,CAAChF,GAAG,CAACuC,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAChC,CACAC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAACxC,QAAQ,GAAGuC,GAAG,CAACG,IAAI,CAACqC,GAAG,CAAE3B,IAAI,KAAM;UACtCrF,KAAK,EAAEqF,IAAI,CAAC4B,OAAO,GAAG,GAAG,GAAG5B,IAAI,CAAC6B,OAAO,GAAG,GAAG,GAAG7B,IAAI,CAAC8B,WAAW,GAAG,GAAG,GAAG9B,IAAI,CAAC+B,WAAW;UAC1FnH,KAAK,EAAEoF,IAAI,CAAC4B,OAAO;UACnBA,OAAO,EAAE5B,IAAI,CAAC4B,OAAO;UACrBC,OAAO,EAAE7B,IAAI,CAAC6B;SACf,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAACnC,SAAS,CAAC3G,aAAa,CAAC4G,KAAK,EAAER,GAAG,CAACS,GAAG,CAAC;MAC9C;IACN,CAAC,CAAC;EACJ;;;uBA9LWtD,kBAAkB,EAAApD,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlJ,EAAA,CAAA8I,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAlBhG,kBAAkB;MAAAiG,SAAA;MAAAC,QAAA,GAAAtJ,EAAA,CAAAuJ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCFzB7J,EAVN,CAAAC,cAAA,iBAAwE,aAQ9D,gBACc,UACb;UAEHD,EAAA,CAAA+J,UAAA,IAAAC,oCAAA,oBAAgH;;UAKhHhK,EAAA,CAAA+J,UAAA,IAAAE,oCAAA,oBAC4C;;UAK5CjK,EAAA,CAAA+J,UAAA,IAAAG,oCAAA,oBACyC;;UAKzClK,EAAA,CAAAC,cAAA,iBAAmG;UAAjFD,EAAA,CAAAE,UAAA,mBAAAiK,qDAAA;YAAAnK,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAQ,WAAA,CAASsJ,GAAA,CAAAxF,cAAA,EAAgB;UAAA,EAAC;UAC1CtE,EAAA,CAAAU,SAAA,YAA6B;UAAAV,EAAA,CAAAW,MAAA,IAC/B;;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAETZ,EAAA,CAAAC,cAAA,iBAC8B;UADSD,EAAA,CAAAE,UAAA,mBAAAmK,qDAAA;YAAArK,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAQ,WAAA,CAASsJ,GAAA,CAAA1F,SAAA,CAAU,IAAI,CAAC;UAAA,EAAC;UAE9DpE,EAAA,CAAAU,SAAA,YAA+B;UAAAV,EAAA,CAAAW,MAAA,IACjC;;UAGNX,EAHM,CAAAY,YAAA,EAAS,EACL,EACC,EACF;UAQDZ,EALR,CAAAC,cAAA,gBAAoE,eAClC,eAEP,oBACP,yBACwB;UAAAD,EAAA,CAAAW,MAAA,IAAqC;;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACzFZ,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAU,SAAA,0BAAmG;UAGzGV,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACwB;UAAAD,EAAA,CAAAW,MAAA,IAAmC;;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACvFZ,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAU,SAAA,0BAAiG;UAGvGV,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAC,cAAA,eAAiD,oBACjC,yBACyB;UAAAD,EAAA,CAAAW,MAAA,IAAkC;;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAErFZ,EADF,CAAAC,cAAA,uBAAiB,qBAEK;UAClBD,EAAA,CAAA+J,UAAA,KAAAO,wCAAA,wBAA6F;UAQzGtK,EANU,CAAAY,YAAA,EAAY,EACI,EACL,EACX,EAEF,EACD;UAGPZ,EAAA,CAAAC,cAAA,uBAG4F;UADZD,EAAtE,CAAAE,UAAA,+BAAAqK,mEAAA;YAAAvK,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAQ,WAAA,CAAqBsJ,GAAA,CAAA1F,SAAA,EAAW;UAAA,EAAC,8BAAAoG,kEAAA;YAAAxK,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAQ,WAAA,CAAyDsJ,GAAA,CAAA1F,SAAA,CAAU,IAAI,CAAC;UAAA,EAAC;UACjEpE,EAAzC,CAAAyK,gBAAA,+BAAAF,mEAAAG,MAAA;YAAA1K,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAApK,EAAA,CAAA2K,kBAAA,CAAAb,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAC,IAAA,EAAAyF,MAAA,MAAAZ,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAC,IAAA,GAAAyF,MAAA;YAAA,OAAA1K,EAAA,CAAAQ,WAAA,CAAAkK,MAAA;UAAA,EAAwC,8BAAAF,kEAAAE,MAAA;YAAA1K,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAApK,EAAA,CAAA2K,kBAAA,CAAAb,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAK,KAAA,EAAAqF,MAAA,MAAAZ,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAK,KAAA,GAAAqF,MAAA;YAAA,OAAA1K,EAAA,CAAAQ,WAAA,CAAAkK,MAAA;UAAA,EAAyC;UAIvF1K,EAHF,CAAAC,cAAA,aAAO,UACH,cAGuC;UAArCD,EAAA,CAAAE,UAAA,6BAAA0K,2DAAAF,MAAA;YAAA1K,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAQ,WAAA,CAAmBsJ,GAAA,CAAAe,QAAA,CAAAH,MAAA,CAAgB;UAAA,EAAC;UACxC1K,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAW,MAAA,IAA0B;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAElDZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAiC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE1DZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAqC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE9DZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAqC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE9DZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAA8B;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAEvDZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAqC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE9DZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAgC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAEzDZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAmC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE5DZ,EAAA,CAAAC,cAAA,cAAoB;UAAAD,EAAA,CAAAW,MAAA,IAAmC;;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAC5DZ,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAW,MAAA,sBACF;UAEFX,EAFE,CAAAY,YAAA,EAAK,EACF,EACG;UAERZ,EAAA,CAAAC,cAAA,aAAO;UACPD,EAAA,CAAA+J,UAAA,KAAAe,iCAAA,mBAA+E;UAsCjF9K,EADE,CAAAY,YAAA,EAAQ,EACC;UAGXZ,EAAA,CAAA+J,UAAA,KAAAgB,0CAAA,iCAAA/K,EAAA,CAAAgL,sBAAA,CAAwD;UAI1DhL,EAAA,CAAAY,YAAA,EAAU;;;;;UAhKyBZ,EAAA,CAAAa,UAAA,gBAAAb,EAAA,CAAAiL,eAAA,KAAAC,GAAA,EAAoC;UAYiBlL,EAAA,CAAAe,SAAA,GAA8B;UAA9Bf,EAAA,CAAAa,UAAA,SAAAb,EAAA,CAAAiB,WAAA,2BAA8B;UAMrGjB,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAa,UAAA,SAAAb,EAAA,CAAAiB,WAAA,8BAAiC;UAMjCjB,EAAA,CAAAe,SAAA,GAA8B;UAA9Bf,EAAA,CAAAa,UAAA,SAAAb,EAAA,CAAAiB,WAAA,2BAA8B;UAMRjB,EAAA,CAAAe,SAAA,GAC/B;UAD+Bf,EAAA,CAAAgB,kBAAA,KAAAhB,EAAA,CAAAiB,WAAA,0BAC/B;UAEkBjB,EAAA,CAAAe,SAAA,GAAoB;UAAsCf,EAA1D,CAAAa,UAAA,qBAAoB,cAAAiJ,GAAA,CAAAhJ,OAAA,CAA2D;UAEhEd,EAAA,CAAAe,SAAA,GACjC;UADiCf,EAAA,CAAAgB,kBAAA,KAAAhB,EAAA,CAAAiB,WAAA,0BACjC;UAMkCjB,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAa,UAAA,cAAAiJ,GAAA,CAAAvF,aAAA,CAA2B;UACrDvE,EAAA,CAAAe,SAAA,EAAmB;UAAnBf,EAAA,CAAAa,UAAA,aAAAb,EAAA,CAAAiL,eAAA,KAAAE,GAAA,EAAmB;UAIWnL,EAAA,CAAAe,SAAA,GAAqC;UAArCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,gCAAqC;UASrCjB,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,8BAAmC;UASlCjB,EAAA,CAAAe,SAAA,GAAkC;UAAlCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,2BAAkC;UAE3BjB,EAAA,CAAAe,SAAA,GAAuB;UAACf,EAAxB,CAAAa,UAAA,uCAAuB,sBAAsB;UAEvDb,EAAA,CAAAe,SAAA,EAAW;UAAXf,EAAA,CAAAa,UAAA,YAAAiJ,GAAA,CAAApG,QAAA,CAAW;UAWD1D,EAAA,CAAAe,SAAA,EAAqB;UAE7Bf,EAFQ,CAAAa,UAAA,cAAAiJ,GAAA,CAAAhJ,OAAA,CAAqB,oBAAoB,aAAAd,EAAA,CAAAiL,eAAA,KAAAG,GAAA,EAA0B,4BAClF,gBAAAC,iBAAA,CAA8B,WAAAvB,GAAA,CAAArG,SAAA,CAAAmD,QAAA,GAAgC,sBAAAkD,GAAA,CAAAnG,iBAAA,CAAwC,YAAAmG,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAsB,KAAA,CAC5D;UAC5BtG,EAAzC,CAAAsL,gBAAA,gBAAAxB,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAC,IAAA,CAAwC,eAAA6E,GAAA,CAAArG,SAAA,CAAAuB,OAAA,CAAAK,KAAA,CAAyC;UAIrDrF,EAAA,CAAAe,SAAA,GAAqC;UAACf,EAAtC,CAAAa,UAAA,cAAAiJ,GAAA,CAAAyB,uBAAA,CAAqC,oBAAAzB,GAAA,CAAA0B,eAAA,CAAoC;UAIxFxL,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,mBAA0B;UAEzBjB,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,0BAAiC;UAEjCjB,EAAA,CAAAe,SAAA,GAAqC;UAArCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,8BAAqC;UAErCjB,EAAA,CAAAe,SAAA,GAAqC;UAArCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,8BAAqC;UAErCjB,EAAA,CAAAe,SAAA,GAA8B;UAA9Bf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,uBAA8B;UAE9BjB,EAAA,CAAAe,SAAA,GAAqC;UAArCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,8BAAqC;UAErCjB,EAAA,CAAAe,SAAA,GAAgC;UAAhCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,yBAAgC;UAEhCjB,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,4BAAmC;UAEnCjB,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAiB,WAAA,4BAAmC;UAQpCjB,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAa,UAAA,YAAA4K,SAAA,CAAArF,IAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}