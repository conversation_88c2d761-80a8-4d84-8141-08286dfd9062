{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'moins d’une seconde',\n    other: 'moins de {{count}} secondes'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} secondes'\n  },\n  halfAMinute: '30 secondes',\n  lessThanXMinutes: {\n    one: 'moins d’une minute',\n    other: 'moins de {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'environ 1 heure',\n    other: 'environ {{count}} heures'\n  },\n  xHours: {\n    one: '1 heure',\n    other: '{{count}} heures'\n  },\n  xDays: {\n    one: '1 jour',\n    other: '{{count}} jours'\n  },\n  aboutXWeeks: {\n    one: 'environ 1 semaine',\n    other: 'environ {{count}} semaines'\n  },\n  xWeeks: {\n    one: '1 semaine',\n    other: '{{count}} semaines'\n  },\n  aboutXMonths: {\n    one: 'environ 1 mois',\n    other: 'environ {{count}} mois'\n  },\n  xMonths: {\n    one: '1 mois',\n    other: '{{count}} mois'\n  },\n  aboutXYears: {\n    one: 'environ 1 an',\n    other: 'environ {{count}} ans'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ans'\n  },\n  overXYears: {\n    one: 'plus d’un an',\n    other: 'plus de {{count}} ans'\n  },\n  almostXYears: {\n    one: 'presqu’un an',\n    other: 'presque {{count}} ans'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var form = formatDistanceLocale[token];\n  if (typeof form === 'string') {\n    result = form;\n  } else if (count === 1) {\n    result = form.one;\n  } else {\n    result = form.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dans ' + result;\n    } else {\n      return 'il y a ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "form", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/fr/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'moins d’une seconde',\n    other: 'moins de {{count}} secondes'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} secondes'\n  },\n  halfAMinute: '30 secondes',\n  lessThanXMinutes: {\n    one: 'moins d’une minute',\n    other: 'moins de {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'environ 1 heure',\n    other: 'environ {{count}} heures'\n  },\n  xHours: {\n    one: '1 heure',\n    other: '{{count}} heures'\n  },\n  xDays: {\n    one: '1 jour',\n    other: '{{count}} jours'\n  },\n  aboutXWeeks: {\n    one: 'environ 1 semaine',\n    other: 'environ {{count}} semaines'\n  },\n  xWeeks: {\n    one: '1 semaine',\n    other: '{{count}} semaines'\n  },\n  aboutXMonths: {\n    one: 'environ 1 mois',\n    other: 'environ {{count}} mois'\n  },\n  xMonths: {\n    one: '1 mois',\n    other: '{{count}} mois'\n  },\n  aboutXYears: {\n    one: 'environ 1 an',\n    other: 'environ {{count}} ans'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ans'\n  },\n  overXYears: {\n    one: 'plus d’un an',\n    other: 'plus de {{count}} ans'\n  },\n  almostXYears: {\n    one: 'presqu’un an',\n    other: 'presque {{count}} ans'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var form = formatDistanceLocale[token];\n  if (typeof form === 'string') {\n    result = form;\n  } else if (count === 1) {\n    result = form.one;\n  } else {\n    result = form.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dans ' + result;\n    } else {\n      return 'il y a ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,IAAI,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EACtC,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;IAC5BD,MAAM,GAAGC,IAAI;EACf,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,IAAI,CAACtB,GAAG;EACnB,CAAC,MAAM;IACLqB,MAAM,GAAGC,IAAI,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACzD;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAO,SAAS,GAAGA,MAAM;IAC3B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}