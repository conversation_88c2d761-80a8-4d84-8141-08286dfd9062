{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar weekdays = ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота'];\nfunction _lastWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "weekdays", "_lastWeek", "day", "weekday", "thisWeek", "_nextWeek", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/mk/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar weekdays = ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота'];\nfunction _lastWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,IAAIC,QAAQ,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC1F,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,QAAQ;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGA,OAAO,GAAG,QAAQ;EAC5C;AACF;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,OAAO,GAAGC,OAAO,GAAG,QAAQ;IACrC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,QAAQ,GAAGA,OAAO,GAAG,QAAQ;EACxC;AACF;AACA,SAASE,SAASA,CAACH,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAC3B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,QAAQ;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGA,OAAO,GAAG,QAAQ;EAC5C;AACF;AACA,IAAIG,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,SAAS,CAACC,GAAG,CAAC;IACvB;EACF,CAAC;EACDU,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,SAASA,QAAQA,CAACP,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,SAAS,CAACH,GAAG,CAAC;IACvB;EACF,CAAC;EACDc,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIS,MAAM,GAAGb,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOS,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}