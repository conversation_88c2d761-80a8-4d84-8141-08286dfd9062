{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { DamageTypeComponent } from './damagetype.component';\nimport { DamageTypeRoutingModule } from './damagetype-routing.module';\nimport { DamageTypeEditComponent } from '@business/tas/damagetype/damagetype-edit/damagetype-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [DamageTypeComponent, DamageTypeEditComponent];\nexport class DamageTypeModule {\n  static {\n    this.ɵfac = function DamageTypeModule_Factory(t) {\n      return new (t || DamageTypeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DamageTypeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, DamageTypeRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DamageTypeModule, {\n    declarations: [DamageTypeComponent, DamageTypeEditComponent],\n    imports: [SharedModule, DamageTypeRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "DamageTypeComponent", "DamageTypeRoutingModule", "DamageTypeEditComponent", "COMPONENTS", "DamageTypeModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\damagetype\\damagetype.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { DamageTypeComponent } from './damagetype.component';\r\nimport { DamageTypeRoutingModule } from './damagetype-routing.module';\r\nimport {DamageTypeEditComponent} from '@business/tas/damagetype/damagetype-edit/damagetype-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  DamageTypeComponent,\r\n  DamageTypeEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, DamageTypeRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class DamageTypeModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAAQC,uBAAuB,QAAO,oEAAoE;;AAE1G,MAAMC,UAAU,GAAG,CACjBH,mBAAmB,EACnBE,uBAAuB,CACxB;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBN,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;IAAA;EAAA;;;2EAGlDK,gBAAgB;IAAAC,YAAA,GAR3BL,mBAAmB,EACnBE,uBAAuB;IAAAI,OAAA,GAIbR,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}