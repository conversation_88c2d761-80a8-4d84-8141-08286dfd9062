{"ast": null, "code": "'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\nvar utils = require('../utils/common');\nvar trees = require('./trees');\nvar adler32 = require('./adler32');\nvar crc32 = require('./crc32');\nvar msg = require('./messages');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n/* Allowed flush values; see deflate() and inflate() below for details */\nvar Z_NO_FLUSH = 0;\nvar Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\nvar Z_FULL_FLUSH = 3;\nvar Z_FINISH = 4;\nvar Z_BLOCK = 5;\n//var Z_TREES         = 6;\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK = 0;\nvar Z_STREAM_END = 1;\n//var Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR = -2;\nvar Z_DATA_ERROR = -3;\n//var Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR = -5;\n//var Z_VERSION_ERROR = -6;\n\n/* compression levels */\n//var Z_NO_COMPRESSION      = 0;\n//var Z_BEST_SPEED          = 1;\n//var Z_BEST_COMPRESSION    = 9;\nvar Z_DEFAULT_COMPRESSION = -1;\nvar Z_FILTERED = 1;\nvar Z_HUFFMAN_ONLY = 2;\nvar Z_RLE = 3;\nvar Z_FIXED = 4;\nvar Z_DEFAULT_STRATEGY = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\n//var Z_BINARY              = 0;\n//var Z_TEXT                = 1;\n//var Z_ASCII               = 1; // = Z_TEXT\nvar Z_UNKNOWN = 2;\n\n/* The deflate compression method */\nvar Z_DEFLATED = 8;\n\n/*============================================================================*/\n\nvar MAX_MEM_LEVEL = 9;\n/* Maximum value for memLevel in deflateInit2 */\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_MEM_LEVEL = 8;\nvar LENGTH_CODES = 29;\n/* number of length codes, not counting the special END_BLOCK code */\nvar LITERALS = 256;\n/* number of literal bytes 0..255 */\nvar L_CODES = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\nvar D_CODES = 30;\n/* number of distance codes */\nvar BL_CODES = 19;\n/* number of codes used to transfer the bit lengths */\nvar HEAP_SIZE = 2 * L_CODES + 1;\n/* maximum heap size */\nvar MAX_BITS = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar MIN_MATCH = 3;\nvar MAX_MATCH = 258;\nvar MIN_LOOKAHEAD = MAX_MATCH + MIN_MATCH + 1;\nvar PRESET_DICT = 0x20;\nvar INIT_STATE = 42;\nvar EXTRA_STATE = 69;\nvar NAME_STATE = 73;\nvar COMMENT_STATE = 91;\nvar HCRC_STATE = 103;\nvar BUSY_STATE = 113;\nvar FINISH_STATE = 666;\nvar BS_NEED_MORE = 1; /* block not completed, need more input or more output */\nvar BS_BLOCK_DONE = 2; /* block flush performed */\nvar BS_FINISH_STARTED = 3; /* finish started, need only more output at next deflate */\nvar BS_FINISH_DONE = 4; /* finish done, accept no more input or output */\n\nvar OS_CODE = 0x03; // Unix :) . Don't detect, use this default.\n\nfunction err(strm, errorCode) {\n  strm.msg = msg[errorCode];\n  return errorCode;\n}\nfunction rank(f) {\n  return (f << 1) - (f > 4 ? 9 : 0);\n}\nfunction zero(buf) {\n  var len = buf.length;\n  while (--len >= 0) {\n    buf[len] = 0;\n  }\n}\n\n/* =========================================================================\n * Flush as much pending output as possible. All deflate() output goes\n * through this function so some applications may wish to modify it\n * to avoid allocating a large strm->output buffer and copying into it.\n * (See also read_buf()).\n */\nfunction flush_pending(strm) {\n  var s = strm.state;\n\n  //_tr_flush_bits(s);\n  var len = s.pending;\n  if (len > strm.avail_out) {\n    len = strm.avail_out;\n  }\n  if (len === 0) {\n    return;\n  }\n  utils.arraySet(strm.output, s.pending_buf, s.pending_out, len, strm.next_out);\n  strm.next_out += len;\n  s.pending_out += len;\n  strm.total_out += len;\n  strm.avail_out -= len;\n  s.pending -= len;\n  if (s.pending === 0) {\n    s.pending_out = 0;\n  }\n}\nfunction flush_block_only(s, last) {\n  trees._tr_flush_block(s, s.block_start >= 0 ? s.block_start : -1, s.strstart - s.block_start, last);\n  s.block_start = s.strstart;\n  flush_pending(s.strm);\n}\nfunction put_byte(s, b) {\n  s.pending_buf[s.pending++] = b;\n}\n\n/* =========================================================================\n * Put a short in the pending buffer. The 16-bit value is put in MSB order.\n * IN assertion: the stream state is correct and there is enough room in\n * pending_buf.\n */\nfunction putShortMSB(s, b) {\n  //  put_byte(s, (Byte)(b >> 8));\n  //  put_byte(s, (Byte)(b & 0xff));\n  s.pending_buf[s.pending++] = b >>> 8 & 0xff;\n  s.pending_buf[s.pending++] = b & 0xff;\n}\n\n/* ===========================================================================\n * Read a new buffer from the current input stream, update the adler32\n * and total number of bytes read.  All deflate() input goes through\n * this function so some applications may wish to modify it to avoid\n * allocating a large strm->input buffer and copying from it.\n * (See also flush_pending()).\n */\nfunction read_buf(strm, buf, start, size) {\n  var len = strm.avail_in;\n  if (len > size) {\n    len = size;\n  }\n  if (len === 0) {\n    return 0;\n  }\n  strm.avail_in -= len;\n\n  // zmemcpy(buf, strm->next_in, len);\n  utils.arraySet(buf, strm.input, strm.next_in, len, start);\n  if (strm.state.wrap === 1) {\n    strm.adler = adler32(strm.adler, buf, len, start);\n  } else if (strm.state.wrap === 2) {\n    strm.adler = crc32(strm.adler, buf, len, start);\n  }\n  strm.next_in += len;\n  strm.total_in += len;\n  return len;\n}\n\n/* ===========================================================================\n * Set match_start to the longest match starting at the given string and\n * return its length. Matches shorter or equal to prev_length are discarded,\n * in which case the result is equal to prev_length and match_start is\n * garbage.\n * IN assertions: cur_match is the head of the hash chain for the current\n *   string (strstart) and its distance is <= MAX_DIST, and prev_length >= 1\n * OUT assertion: the match length is not greater than s->lookahead.\n */\nfunction longest_match(s, cur_match) {\n  var chain_length = s.max_chain_length; /* max hash chain length */\n  var scan = s.strstart; /* current string */\n  var match; /* matched string */\n  var len; /* length of current match */\n  var best_len = s.prev_length; /* best match length so far */\n  var nice_match = s.nice_match; /* stop if match long enough */\n  var limit = s.strstart > s.w_size - MIN_LOOKAHEAD ? s.strstart - (s.w_size - MIN_LOOKAHEAD) : 0 /*NIL*/;\n  var _win = s.window; // shortcut\n\n  var wmask = s.w_mask;\n  var prev = s.prev;\n\n  /* Stop when cur_match becomes <= limit. To simplify the code,\n   * we prevent matches with the string of window index 0.\n   */\n\n  var strend = s.strstart + MAX_MATCH;\n  var scan_end1 = _win[scan + best_len - 1];\n  var scan_end = _win[scan + best_len];\n\n  /* The code is optimized for HASH_BITS >= 8 and MAX_MATCH-2 multiple of 16.\n   * It is easy to get rid of this optimization if necessary.\n   */\n  // Assert(s->hash_bits >= 8 && MAX_MATCH == 258, \"Code too clever\");\n\n  /* Do not waste too much time if we already have a good match: */\n  if (s.prev_length >= s.good_match) {\n    chain_length >>= 2;\n  }\n  /* Do not look for matches beyond the end of the input. This is necessary\n   * to make deflate deterministic.\n   */\n  if (nice_match > s.lookahead) {\n    nice_match = s.lookahead;\n  }\n\n  // Assert((ulg)s->strstart <= s->window_size-MIN_LOOKAHEAD, \"need lookahead\");\n\n  do {\n    // Assert(cur_match < s->strstart, \"no future\");\n    match = cur_match;\n\n    /* Skip to next match if the match length cannot increase\n     * or if the match length is less than 2.  Note that the checks below\n     * for insufficient lookahead only occur occasionally for performance\n     * reasons.  Therefore uninitialized memory will be accessed, and\n     * conditional jumps will be made that depend on those values.\n     * However the length of the match is limited to the lookahead, so\n     * the output of deflate is not affected by the uninitialized values.\n     */\n\n    if (_win[match + best_len] !== scan_end || _win[match + best_len - 1] !== scan_end1 || _win[match] !== _win[scan] || _win[++match] !== _win[scan + 1]) {\n      continue;\n    }\n\n    /* The check at best_len-1 can be removed because it will be made\n     * again later. (This heuristic is not always a win.)\n     * It is not necessary to compare scan[2] and match[2] since they\n     * are always equal when the other bytes match, given that\n     * the hash keys are equal and that HASH_BITS >= 8.\n     */\n    scan += 2;\n    match++;\n    // Assert(*scan == *match, \"match[2]?\");\n\n    /* We check for insufficient lookahead only every 8th comparison;\n     * the 256th check will be made at strstart+258.\n     */\n    do {\n      /*jshint noempty:false*/\n    } while (_win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && _win[++scan] === _win[++match] && scan < strend);\n\n    // Assert(scan <= s->window+(unsigned)(s->window_size-1), \"wild scan\");\n\n    len = MAX_MATCH - (strend - scan);\n    scan = strend - MAX_MATCH;\n    if (len > best_len) {\n      s.match_start = cur_match;\n      best_len = len;\n      if (len >= nice_match) {\n        break;\n      }\n      scan_end1 = _win[scan + best_len - 1];\n      scan_end = _win[scan + best_len];\n    }\n  } while ((cur_match = prev[cur_match & wmask]) > limit && --chain_length !== 0);\n  if (best_len <= s.lookahead) {\n    return best_len;\n  }\n  return s.lookahead;\n}\n\n/* ===========================================================================\n * Fill the window when the lookahead becomes insufficient.\n * Updates strstart and lookahead.\n *\n * IN assertion: lookahead < MIN_LOOKAHEAD\n * OUT assertions: strstart <= window_size-MIN_LOOKAHEAD\n *    At least one byte has been read, or avail_in == 0; reads are\n *    performed for at least two bytes (required for the zip translate_eol\n *    option -- not supported here).\n */\nfunction fill_window(s) {\n  var _w_size = s.w_size;\n  var p, n, m, more, str;\n\n  //Assert(s->lookahead < MIN_LOOKAHEAD, \"already enough lookahead\");\n\n  do {\n    more = s.window_size - s.lookahead - s.strstart;\n\n    // JS ints have 32 bit, block below not needed\n    /* Deal with !@#$% 64K limit: */\n    //if (sizeof(int) <= 2) {\n    //    if (more == 0 && s->strstart == 0 && s->lookahead == 0) {\n    //        more = wsize;\n    //\n    //  } else if (more == (unsigned)(-1)) {\n    //        /* Very unlikely, but possible on 16 bit machine if\n    //         * strstart == 0 && lookahead == 1 (input done a byte at time)\n    //         */\n    //        more--;\n    //    }\n    //}\n\n    /* If the window is almost full and there is insufficient lookahead,\n     * move the upper half to the lower one to make room in the upper half.\n     */\n    if (s.strstart >= _w_size + (_w_size - MIN_LOOKAHEAD)) {\n      utils.arraySet(s.window, s.window, _w_size, _w_size, 0);\n      s.match_start -= _w_size;\n      s.strstart -= _w_size;\n      /* we now have strstart >= MAX_DIST */\n      s.block_start -= _w_size;\n\n      /* Slide the hash table (could be avoided with 32 bit values\n       at the expense of memory usage). We slide even when level == 0\n       to keep the hash table consistent if we switch back to level > 0\n       later. (Using level 0 permanently is not an optimal usage of\n       zlib, so we don't care about this pathological case.)\n       */\n\n      n = s.hash_size;\n      p = n;\n      do {\n        m = s.head[--p];\n        s.head[p] = m >= _w_size ? m - _w_size : 0;\n      } while (--n);\n      n = _w_size;\n      p = n;\n      do {\n        m = s.prev[--p];\n        s.prev[p] = m >= _w_size ? m - _w_size : 0;\n        /* If n is not on any hash chain, prev[n] is garbage but\n         * its value will never be used.\n         */\n      } while (--n);\n      more += _w_size;\n    }\n    if (s.strm.avail_in === 0) {\n      break;\n    }\n\n    /* If there was no sliding:\n     *    strstart <= WSIZE+MAX_DIST-1 && lookahead <= MIN_LOOKAHEAD - 1 &&\n     *    more == window_size - lookahead - strstart\n     * => more >= window_size - (MIN_LOOKAHEAD-1 + WSIZE + MAX_DIST-1)\n     * => more >= window_size - 2*WSIZE + 2\n     * In the BIG_MEM or MMAP case (not yet supported),\n     *   window_size == input_size + MIN_LOOKAHEAD  &&\n     *   strstart + s->lookahead <= input_size => more >= MIN_LOOKAHEAD.\n     * Otherwise, window_size == 2*WSIZE so more >= 2.\n     * If there was sliding, more >= WSIZE. So in all cases, more >= 2.\n     */\n    //Assert(more >= 2, \"more < 2\");\n    n = read_buf(s.strm, s.window, s.strstart + s.lookahead, more);\n    s.lookahead += n;\n\n    /* Initialize the hash value now that we have some input: */\n    if (s.lookahead + s.insert >= MIN_MATCH) {\n      str = s.strstart - s.insert;\n      s.ins_h = s.window[str];\n\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + 1]); */\n      s.ins_h = (s.ins_h << s.hash_shift ^ s.window[str + 1]) & s.hash_mask;\n      //#if MIN_MATCH != 3\n      //        Call update_hash() MIN_MATCH-3 more times\n      //#endif\n      while (s.insert) {\n        /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n        s.ins_h = (s.ins_h << s.hash_shift ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n        s.prev[str & s.w_mask] = s.head[s.ins_h];\n        s.head[s.ins_h] = str;\n        str++;\n        s.insert--;\n        if (s.lookahead + s.insert < MIN_MATCH) {\n          break;\n        }\n      }\n    }\n    /* If the whole input has less than MIN_MATCH bytes, ins_h is garbage,\n     * but this is not important since only literal bytes will be emitted.\n     */\n  } while (s.lookahead < MIN_LOOKAHEAD && s.strm.avail_in !== 0);\n\n  /* If the WIN_INIT bytes after the end of the current data have never been\n   * written, then zero those bytes in order to avoid memory check reports of\n   * the use of uninitialized (or uninitialised as Julian writes) bytes by\n   * the longest match routines.  Update the high water mark for the next\n   * time through here.  WIN_INIT is set to MAX_MATCH since the longest match\n   * routines allow scanning to strstart + MAX_MATCH, ignoring lookahead.\n   */\n  //  if (s.high_water < s.window_size) {\n  //    var curr = s.strstart + s.lookahead;\n  //    var init = 0;\n  //\n  //    if (s.high_water < curr) {\n  //      /* Previous high water mark below current data -- zero WIN_INIT\n  //       * bytes or up to end of window, whichever is less.\n  //       */\n  //      init = s.window_size - curr;\n  //      if (init > WIN_INIT)\n  //        init = WIN_INIT;\n  //      zmemzero(s->window + curr, (unsigned)init);\n  //      s->high_water = curr + init;\n  //    }\n  //    else if (s->high_water < (ulg)curr + WIN_INIT) {\n  //      /* High water mark at or above current data, but below current data\n  //       * plus WIN_INIT -- zero out to current data plus WIN_INIT, or up\n  //       * to end of window, whichever is less.\n  //       */\n  //      init = (ulg)curr + WIN_INIT - s->high_water;\n  //      if (init > s->window_size - s->high_water)\n  //        init = s->window_size - s->high_water;\n  //      zmemzero(s->window + s->high_water, (unsigned)init);\n  //      s->high_water += init;\n  //    }\n  //  }\n  //\n  //  Assert((ulg)s->strstart <= s->window_size - MIN_LOOKAHEAD,\n  //    \"not enough room for search\");\n}\n\n/* ===========================================================================\n * Copy without compression as much as possible from the input stream, return\n * the current block state.\n * This function does not insert new strings in the dictionary since\n * uncompressible data is probably not useful. This function is used\n * only for the level=0 compression option.\n * NOTE: this function should be optimized to avoid extra copying from\n * window to pending_buf.\n */\nfunction deflate_stored(s, flush) {\n  /* Stored blocks are limited to 0xffff bytes, pending_buf is limited\n   * to pending_buf_size, and each stored block has a 5 byte header:\n   */\n  var max_block_size = 0xffff;\n  if (max_block_size > s.pending_buf_size - 5) {\n    max_block_size = s.pending_buf_size - 5;\n  }\n\n  /* Copy as much as possible from input to output: */\n  for (;;) {\n    /* Fill the window as much as possible: */\n    if (s.lookahead <= 1) {\n      //Assert(s->strstart < s->w_size+MAX_DIST(s) ||\n      //  s->block_start >= (long)s->w_size, \"slide too late\");\n      //      if (!(s.strstart < s.w_size + (s.w_size - MIN_LOOKAHEAD) ||\n      //        s.block_start >= s.w_size)) {\n      //        throw  new Error(\"slide too late\");\n      //      }\n\n      fill_window(s);\n      if (s.lookahead === 0 && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break;\n      }\n      /* flush the current block */\n    }\n    //Assert(s->block_start >= 0L, \"block gone\");\n    //    if (s.block_start < 0) throw new Error(\"block gone\");\n\n    s.strstart += s.lookahead;\n    s.lookahead = 0;\n\n    /* Emit a stored block if pending_buf will be full: */\n    var max_start = s.block_start + max_block_size;\n    if (s.strstart === 0 || s.strstart >= max_start) {\n      /* strstart == 0 is possible when wraparound on 16-bit machine */\n      s.lookahead = s.strstart - max_start;\n      s.strstart = max_start;\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n    /* Flush if we may have to slide, otherwise block_start may become\n     * negative and the data will be gone:\n     */\n    if (s.strstart - s.block_start >= s.w_size - MIN_LOOKAHEAD) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.strstart > s.block_start) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_NEED_MORE;\n}\n\n/* ===========================================================================\n * Compress as much as possible from the input stream, return the current\n * block state.\n * This function does not perform lazy evaluation of matches and inserts\n * new strings in the dictionary only for unmatched strings or for short\n * matches. It is used only for the fast compression options.\n */\nfunction deflate_fast(s, flush) {\n  var hash_head; /* head of the hash chain */\n  var bflush; /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break; /* flush the current block */\n      }\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0 /*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     * At this point we have always match_length < MIN_MATCH\n     */\n    if (hash_head !== 0 /*NIL*/ && s.strstart - hash_head <= s.w_size - MIN_LOOKAHEAD) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n    }\n    if (s.match_length >= MIN_MATCH) {\n      // check_match(s, s.strstart, s.match_start, s.match_length); // for debug only\n\n      /*** _tr_tally_dist(s, s.strstart - s.match_start,\n                     s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, s.strstart - s.match_start, s.match_length - MIN_MATCH);\n      s.lookahead -= s.match_length;\n\n      /* Insert new strings in the hash table only if the match length\n       * is not too large. This saves time but degrades compression.\n       */\n      if (s.match_length <= s.max_lazy_match /*max_insert_length*/ && s.lookahead >= MIN_MATCH) {\n        s.match_length--; /* string at strstart already in table */\n        do {\n          s.strstart++;\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n          /* strstart never exceeds WSIZE-MAX_MATCH, so there are\n           * always MIN_MATCH bytes ahead.\n           */\n        } while (--s.match_length !== 0);\n        s.strstart++;\n      } else {\n        s.strstart += s.match_length;\n        s.match_length = 0;\n        s.ins_h = s.window[s.strstart];\n        /* UPDATE_HASH(s, s.ins_h, s.window[s.strstart+1]); */\n        s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + 1]) & s.hash_mask;\n\n        //#if MIN_MATCH != 3\n        //                Call UPDATE_HASH() MIN_MATCH-3 more times\n        //#endif\n        /* If lookahead < MIN_MATCH, ins_h is garbage, but it does not\n         * matter since it will be recomputed at next deflate call.\n         */\n      }\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s.window[s.strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = s.strstart < MIN_MATCH - 1 ? s.strstart : MIN_MATCH - 1;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * Same as above, but achieves better compression. We use a lazy\n * evaluation for matches: a match is finally adopted only if there is\n * no better match at the next window position.\n */\nfunction deflate_slow(s, flush) {\n  var hash_head; /* head of hash chain */\n  var bflush; /* set if current block must be flushed */\n\n  var max_insert;\n\n  /* Process the input block. */\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break;\n      } /* flush the current block */\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0 /*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     */\n    s.prev_length = s.match_length;\n    s.prev_match = s.match_start;\n    s.match_length = MIN_MATCH - 1;\n    if (hash_head !== 0 /*NIL*/ && s.prev_length < s.max_lazy_match && s.strstart - hash_head <= s.w_size - MIN_LOOKAHEAD /*MAX_DIST(s)*/) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n\n      if (s.match_length <= 5 && (s.strategy === Z_FILTERED || s.match_length === MIN_MATCH && s.strstart - s.match_start > 4096 /*TOO_FAR*/)) {\n        /* If prev_match is also MIN_MATCH, match_start is garbage\n         * but we will ignore the current match anyway.\n         */\n        s.match_length = MIN_MATCH - 1;\n      }\n    }\n    /* If there was a match at the previous step and the current\n     * match is not better, output the previous match:\n     */\n    if (s.prev_length >= MIN_MATCH && s.match_length <= s.prev_length) {\n      max_insert = s.strstart + s.lookahead - MIN_MATCH;\n      /* Do not insert strings in hash table beyond this. */\n\n      //check_match(s, s.strstart-1, s.prev_match, s.prev_length);\n\n      /***_tr_tally_dist(s, s.strstart - 1 - s.prev_match,\n                     s.prev_length - MIN_MATCH, bflush);***/\n      bflush = trees._tr_tally(s, s.strstart - 1 - s.prev_match, s.prev_length - MIN_MATCH);\n      /* Insert in hash table all strings up to the end of the match.\n       * strstart-1 and strstart are already inserted. If there is not\n       * enough lookahead, the last two strings are not inserted in\n       * the hash table.\n       */\n      s.lookahead -= s.prev_length - 1;\n      s.prev_length -= 2;\n      do {\n        if (++s.strstart <= max_insert) {\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n        }\n      } while (--s.prev_length !== 0);\n      s.match_available = 0;\n      s.match_length = MIN_MATCH - 1;\n      s.strstart++;\n      if (bflush) {\n        /*** FLUSH_BLOCK(s, 0); ***/\n        flush_block_only(s, false);\n        if (s.strm.avail_out === 0) {\n          return BS_NEED_MORE;\n        }\n        /***/\n      }\n    } else if (s.match_available) {\n      /* If there was no match at the previous position, output a\n       * single literal. If there was a match but the current match\n       * is longer, truncate the previous match to a single literal.\n       */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n      /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n      if (bflush) {\n        /*** FLUSH_BLOCK_ONLY(s, 0) ***/\n        flush_block_only(s, false);\n        /***/\n      }\n      s.strstart++;\n      s.lookahead--;\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n    } else {\n      /* There is no previous match to compare with, wait for\n       * the next step to decide.\n       */\n      s.match_available = 1;\n      s.strstart++;\n      s.lookahead--;\n    }\n  }\n  //Assert (flush != Z_NO_FLUSH, \"no flush?\");\n  if (s.match_available) {\n    //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n    /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n    s.match_available = 0;\n  }\n  s.insert = s.strstart < MIN_MATCH - 1 ? s.strstart : MIN_MATCH - 1;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * For Z_RLE, simply look for runs of bytes, generate matches only of distance\n * one.  Do not maintain a hash table.  (It will be regenerated if this run of\n * deflate switches away from Z_RLE.)\n */\nfunction deflate_rle(s, flush) {\n  var bflush; /* set if current block must be flushed */\n  var prev; /* byte at distance one to match */\n  var scan, strend; /* scan goes up to strend for length of run */\n\n  var _win = s.window;\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the longest run, plus one for the unrolled loop.\n     */\n    if (s.lookahead <= MAX_MATCH) {\n      fill_window(s);\n      if (s.lookahead <= MAX_MATCH && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break;\n      } /* flush the current block */\n    }\n\n    /* See how many times the previous byte repeats */\n    s.match_length = 0;\n    if (s.lookahead >= MIN_MATCH && s.strstart > 0) {\n      scan = s.strstart - 1;\n      prev = _win[scan];\n      if (prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan]) {\n        strend = s.strstart + MAX_MATCH;\n        do {\n          /*jshint noempty:false*/\n        } while (prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan] && scan < strend);\n        s.match_length = MAX_MATCH - (strend - scan);\n        if (s.match_length > s.lookahead) {\n          s.match_length = s.lookahead;\n        }\n      }\n      //Assert(scan <= s->window+(uInt)(s->window_size-1), \"wild scan\");\n    }\n\n    /* Emit match if have run of MIN_MATCH or longer, else emit literal */\n    if (s.match_length >= MIN_MATCH) {\n      //check_match(s, s.strstart, s.strstart - 1, s.match_length);\n\n      /*** _tr_tally_dist(s, 1, s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, 1, s.match_length - MIN_MATCH);\n      s.lookahead -= s.match_length;\n      s.strstart += s.match_length;\n      s.match_length = 0;\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * For Z_HUFFMAN_ONLY, do not look for matches.  Do not maintain a hash table.\n * (It will be regenerated if this run of deflate switches away from Huffman.)\n */\nfunction deflate_huff(s, flush) {\n  var bflush; /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we have a literal to write. */\n    if (s.lookahead === 0) {\n      fill_window(s);\n      if (s.lookahead === 0) {\n        if (flush === Z_NO_FLUSH) {\n          return BS_NEED_MORE;\n        }\n        break; /* flush the current block */\n      }\n    }\n\n    /* Output a literal byte */\n    s.match_length = 0;\n    //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n    /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n    s.lookahead--;\n    s.strstart++;\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* Values for max_lazy_match, good_match and max_chain_length, depending on\n * the desired pack level (0..9). The values given below have been tuned to\n * exclude worst case performance for pathological files. Better values may be\n * found for specific files.\n */\nfunction Config(good_length, max_lazy, nice_length, max_chain, func) {\n  this.good_length = good_length;\n  this.max_lazy = max_lazy;\n  this.nice_length = nice_length;\n  this.max_chain = max_chain;\n  this.func = func;\n}\nvar configuration_table;\nconfiguration_table = [/*      good lazy nice chain */\nnew Config(0, 0, 0, 0, deflate_stored), /* 0 store only */\nnew Config(4, 4, 8, 4, deflate_fast), /* 1 max speed, no lazy matches */\nnew Config(4, 5, 16, 8, deflate_fast), /* 2 */\nnew Config(4, 6, 32, 32, deflate_fast), /* 3 */\n\nnew Config(4, 4, 16, 16, deflate_slow), /* 4 lazy matches */\nnew Config(8, 16, 32, 32, deflate_slow), /* 5 */\nnew Config(8, 16, 128, 128, deflate_slow), /* 6 */\nnew Config(8, 32, 128, 256, deflate_slow), /* 7 */\nnew Config(32, 128, 258, 1024, deflate_slow), /* 8 */\nnew Config(32, 258, 258, 4096, deflate_slow) /* 9 max compression */];\n\n/* ===========================================================================\n * Initialize the \"longest match\" routines for a new zlib stream\n */\nfunction lm_init(s) {\n  s.window_size = 2 * s.w_size;\n\n  /*** CLEAR_HASH(s); ***/\n  zero(s.head); // Fill with NIL (= 0);\n\n  /* Set the default configuration parameters:\n   */\n  s.max_lazy_match = configuration_table[s.level].max_lazy;\n  s.good_match = configuration_table[s.level].good_length;\n  s.nice_match = configuration_table[s.level].nice_length;\n  s.max_chain_length = configuration_table[s.level].max_chain;\n  s.strstart = 0;\n  s.block_start = 0;\n  s.lookahead = 0;\n  s.insert = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  s.ins_h = 0;\n}\nfunction DeflateState() {\n  this.strm = null; /* pointer back to this zlib stream */\n  this.status = 0; /* as the name implies */\n  this.pending_buf = null; /* output still pending */\n  this.pending_buf_size = 0; /* size of pending_buf */\n  this.pending_out = 0; /* next pending byte to output to the stream */\n  this.pending = 0; /* nb of bytes in the pending buffer */\n  this.wrap = 0; /* bit 0 true for zlib, bit 1 true for gzip */\n  this.gzhead = null; /* gzip header information to write */\n  this.gzindex = 0; /* where in extra, name, or comment */\n  this.method = Z_DEFLATED; /* can only be DEFLATED */\n  this.last_flush = -1; /* value of flush param for previous deflate call */\n\n  this.w_size = 0; /* LZ77 window size (32K by default) */\n  this.w_bits = 0; /* log2(w_size)  (8..16) */\n  this.w_mask = 0; /* w_size - 1 */\n\n  this.window = null;\n  /* Sliding window. Input bytes are read into the second half of the window,\n   * and move to the first half later to keep a dictionary of at least wSize\n   * bytes. With this organization, matches are limited to a distance of\n   * wSize-MAX_MATCH bytes, but this ensures that IO is always\n   * performed with a length multiple of the block size.\n   */\n\n  this.window_size = 0;\n  /* Actual size of window: 2*wSize, except when the user input buffer\n   * is directly used as sliding window.\n   */\n\n  this.prev = null;\n  /* Link to older string with same hash index. To limit the size of this\n   * array to 64K, this link is maintained only for the last 32K strings.\n   * An index in this array is thus a window index modulo 32K.\n   */\n\n  this.head = null; /* Heads of the hash chains or NIL. */\n\n  this.ins_h = 0; /* hash index of string to be inserted */\n  this.hash_size = 0; /* number of elements in hash table */\n  this.hash_bits = 0; /* log2(hash_size) */\n  this.hash_mask = 0; /* hash_size-1 */\n\n  this.hash_shift = 0;\n  /* Number of bits by which ins_h must be shifted at each input\n   * step. It must be such that after MIN_MATCH steps, the oldest\n   * byte no longer takes part in the hash key, that is:\n   *   hash_shift * MIN_MATCH >= hash_bits\n   */\n\n  this.block_start = 0;\n  /* Window position at the beginning of the current output block. Gets\n   * negative when the window is moved backwards.\n   */\n\n  this.match_length = 0; /* length of best match */\n  this.prev_match = 0; /* previous match */\n  this.match_available = 0; /* set if previous match exists */\n  this.strstart = 0; /* start of string to insert */\n  this.match_start = 0; /* start of matching string */\n  this.lookahead = 0; /* number of valid bytes ahead in window */\n\n  this.prev_length = 0;\n  /* Length of the best match at previous step. Matches not greater than this\n   * are discarded. This is used in the lazy match evaluation.\n   */\n\n  this.max_chain_length = 0;\n  /* To speed up deflation, hash chains are never searched beyond this\n   * length.  A higher limit improves compression ratio but degrades the\n   * speed.\n   */\n\n  this.max_lazy_match = 0;\n  /* Attempt to find a better match only when the current match is strictly\n   * smaller than this value. This mechanism is used only for compression\n   * levels >= 4.\n   */\n  // That's alias to max_lazy_match, don't use directly\n  //this.max_insert_length = 0;\n  /* Insert new strings in the hash table only if the match length is not\n   * greater than this length. This saves time but degrades compression.\n   * max_insert_length is used only for compression levels <= 3.\n   */\n\n  this.level = 0; /* compression level (1..9) */\n  this.strategy = 0; /* favor or force Huffman coding*/\n\n  this.good_match = 0;\n  /* Use a faster search when the previous match is longer than this */\n\n  this.nice_match = 0; /* Stop searching when current match exceeds this */\n\n  /* used by trees.c: */\n\n  /* Didn't use ct_data typedef below to suppress compiler warning */\n\n  // struct ct_data_s dyn_ltree[HEAP_SIZE];   /* literal and length tree */\n  // struct ct_data_s dyn_dtree[2*D_CODES+1]; /* distance tree */\n  // struct ct_data_s bl_tree[2*BL_CODES+1];  /* Huffman tree for bit lengths */\n\n  // Use flat array of DOUBLE size, with interleaved fata,\n  // because JS does not support effective\n  this.dyn_ltree = new utils.Buf16(HEAP_SIZE * 2);\n  this.dyn_dtree = new utils.Buf16((2 * D_CODES + 1) * 2);\n  this.bl_tree = new utils.Buf16((2 * BL_CODES + 1) * 2);\n  zero(this.dyn_ltree);\n  zero(this.dyn_dtree);\n  zero(this.bl_tree);\n  this.l_desc = null; /* desc. for literal tree */\n  this.d_desc = null; /* desc. for distance tree */\n  this.bl_desc = null; /* desc. for bit length tree */\n\n  //ush bl_count[MAX_BITS+1];\n  this.bl_count = new utils.Buf16(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  //int heap[2*L_CODES+1];      /* heap used to build the Huffman trees */\n  this.heap = new utils.Buf16(2 * L_CODES + 1); /* heap used to build the Huffman trees */\n  zero(this.heap);\n  this.heap_len = 0; /* number of elements in the heap */\n  this.heap_max = 0; /* element of largest frequency */\n  /* The sons of heap[n] are heap[2*n] and heap[2*n+1]. heap[0] is not used.\n   * The same heap array is used to build all trees.\n   */\n\n  this.depth = new utils.Buf16(2 * L_CODES + 1); //uch depth[2*L_CODES+1];\n  zero(this.depth);\n  /* Depth of each subtree used as tie breaker for trees of equal frequency\n   */\n\n  this.l_buf = 0; /* buffer index for literals or lengths */\n\n  this.lit_bufsize = 0;\n  /* Size of match buffer for literals/lengths.  There are 4 reasons for\n   * limiting lit_bufsize to 64K:\n   *   - frequencies can be kept in 16 bit counters\n   *   - if compression is not successful for the first block, all input\n   *     data is still in the window so we can still emit a stored block even\n   *     when input comes from standard input.  (This can also be done for\n   *     all blocks if lit_bufsize is not greater than 32K.)\n   *   - if compression is not successful for a file smaller than 64K, we can\n   *     even emit a stored file instead of a stored block (saving 5 bytes).\n   *     This is applicable only for zip (not gzip or zlib).\n   *   - creating new Huffman trees less frequently may not provide fast\n   *     adaptation to changes in the input data statistics. (Take for\n   *     example a binary file with poorly compressible code followed by\n   *     a highly compressible string table.) Smaller buffer sizes give\n   *     fast adaptation but have of course the overhead of transmitting\n   *     trees more frequently.\n   *   - I can't count above 4\n   */\n\n  this.last_lit = 0; /* running index in l_buf */\n\n  this.d_buf = 0;\n  /* Buffer index for distances. To simplify the code, d_buf and l_buf have\n   * the same number of elements. To use different lengths, an extra flag\n   * array would be necessary.\n   */\n\n  this.opt_len = 0; /* bit length of current block with optimal trees */\n  this.static_len = 0; /* bit length of current block with static trees */\n  this.matches = 0; /* number of string matches in current block */\n  this.insert = 0; /* bytes at end of window left to insert */\n\n  this.bi_buf = 0;\n  /* Output buffer. bits are inserted starting at the bottom (least\n   * significant bits).\n   */\n  this.bi_valid = 0;\n  /* Number of valid bits in bi_buf.  All bits above the last valid bit\n   * are always zero.\n   */\n\n  // Used for window memory init. We safely ignore it for JS. That makes\n  // sense only for pointers and memory check tools.\n  //this.high_water = 0;\n  /* High water mark offset in window for initialized bytes -- bytes above\n   * this are set to zero in order to avoid memory check warnings when\n   * longest match routines access bytes past the input.  This is then\n   * updated to the new high water mark.\n   */\n}\nfunction deflateResetKeep(strm) {\n  var s;\n  if (!strm || !strm.state) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n  strm.total_in = strm.total_out = 0;\n  strm.data_type = Z_UNKNOWN;\n  s = strm.state;\n  s.pending = 0;\n  s.pending_out = 0;\n  if (s.wrap < 0) {\n    s.wrap = -s.wrap;\n    /* was made negative by deflate(..., Z_FINISH); */\n  }\n  s.status = s.wrap ? INIT_STATE : BUSY_STATE;\n  strm.adler = s.wrap === 2 ? 0 // crc32(0, Z_NULL, 0)\n  : 1; // adler32(0, Z_NULL, 0)\n  s.last_flush = Z_NO_FLUSH;\n  trees._tr_init(s);\n  return Z_OK;\n}\nfunction deflateReset(strm) {\n  var ret = deflateResetKeep(strm);\n  if (ret === Z_OK) {\n    lm_init(strm.state);\n  }\n  return ret;\n}\nfunction deflateSetHeader(strm, head) {\n  if (!strm || !strm.state) {\n    return Z_STREAM_ERROR;\n  }\n  if (strm.state.wrap !== 2) {\n    return Z_STREAM_ERROR;\n  }\n  strm.state.gzhead = head;\n  return Z_OK;\n}\nfunction deflateInit2(strm, level, method, windowBits, memLevel, strategy) {\n  if (!strm) {\n    // === Z_NULL\n    return Z_STREAM_ERROR;\n  }\n  var wrap = 1;\n  if (level === Z_DEFAULT_COMPRESSION) {\n    level = 6;\n  }\n  if (windowBits < 0) {\n    /* suppress zlib wrapper */\n    wrap = 0;\n    windowBits = -windowBits;\n  } else if (windowBits > 15) {\n    wrap = 2; /* write gzip wrapper instead */\n    windowBits -= 16;\n  }\n  if (memLevel < 1 || memLevel > MAX_MEM_LEVEL || method !== Z_DEFLATED || windowBits < 8 || windowBits > 15 || level < 0 || level > 9 || strategy < 0 || strategy > Z_FIXED) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n  if (windowBits === 8) {\n    windowBits = 9;\n  }\n  /* until 256-byte window bug fixed */\n\n  var s = new DeflateState();\n  strm.state = s;\n  s.strm = strm;\n  s.wrap = wrap;\n  s.gzhead = null;\n  s.w_bits = windowBits;\n  s.w_size = 1 << s.w_bits;\n  s.w_mask = s.w_size - 1;\n  s.hash_bits = memLevel + 7;\n  s.hash_size = 1 << s.hash_bits;\n  s.hash_mask = s.hash_size - 1;\n  s.hash_shift = ~~((s.hash_bits + MIN_MATCH - 1) / MIN_MATCH);\n  s.window = new utils.Buf8(s.w_size * 2);\n  s.head = new utils.Buf16(s.hash_size);\n  s.prev = new utils.Buf16(s.w_size);\n\n  // Don't need mem init magic for JS.\n  //s.high_water = 0;  /* nothing written to s->window yet */\n\n  s.lit_bufsize = 1 << memLevel + 6; /* 16K elements by default */\n\n  s.pending_buf_size = s.lit_bufsize * 4;\n\n  //overlay = (ushf *) ZALLOC(strm, s->lit_bufsize, sizeof(ush)+2);\n  //s->pending_buf = (uchf *) overlay;\n  s.pending_buf = new utils.Buf8(s.pending_buf_size);\n\n  // It is offset from `s.pending_buf` (size is `s.lit_bufsize * 2`)\n  //s->d_buf = overlay + s->lit_bufsize/sizeof(ush);\n  s.d_buf = 1 * s.lit_bufsize;\n\n  //s->l_buf = s->pending_buf + (1+sizeof(ush))*s->lit_bufsize;\n  s.l_buf = (1 + 2) * s.lit_bufsize;\n  s.level = level;\n  s.strategy = strategy;\n  s.method = method;\n  return deflateReset(strm);\n}\nfunction deflateInit(strm, level) {\n  return deflateInit2(strm, level, Z_DEFLATED, MAX_WBITS, DEF_MEM_LEVEL, Z_DEFAULT_STRATEGY);\n}\nfunction deflate(strm, flush) {\n  var old_flush, s;\n  var beg, val; // for gzip header write only\n\n  if (!strm || !strm.state || flush > Z_BLOCK || flush < 0) {\n    return strm ? err(strm, Z_STREAM_ERROR) : Z_STREAM_ERROR;\n  }\n  s = strm.state;\n  if (!strm.output || !strm.input && strm.avail_in !== 0 || s.status === FINISH_STATE && flush !== Z_FINISH) {\n    return err(strm, strm.avail_out === 0 ? Z_BUF_ERROR : Z_STREAM_ERROR);\n  }\n  s.strm = strm; /* just in case */\n  old_flush = s.last_flush;\n  s.last_flush = flush;\n\n  /* Write the header */\n  if (s.status === INIT_STATE) {\n    if (s.wrap === 2) {\n      // GZIP header\n      strm.adler = 0; //crc32(0L, Z_NULL, 0);\n      put_byte(s, 31);\n      put_byte(s, 139);\n      put_byte(s, 8);\n      if (!s.gzhead) {\n        // s->gzhead == Z_NULL\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, s.level === 9 ? 2 : s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ? 4 : 0);\n        put_byte(s, OS_CODE);\n        s.status = BUSY_STATE;\n      } else {\n        put_byte(s, (s.gzhead.text ? 1 : 0) + (s.gzhead.hcrc ? 2 : 0) + (!s.gzhead.extra ? 0 : 4) + (!s.gzhead.name ? 0 : 8) + (!s.gzhead.comment ? 0 : 16));\n        put_byte(s, s.gzhead.time & 0xff);\n        put_byte(s, s.gzhead.time >> 8 & 0xff);\n        put_byte(s, s.gzhead.time >> 16 & 0xff);\n        put_byte(s, s.gzhead.time >> 24 & 0xff);\n        put_byte(s, s.level === 9 ? 2 : s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ? 4 : 0);\n        put_byte(s, s.gzhead.os & 0xff);\n        if (s.gzhead.extra && s.gzhead.extra.length) {\n          put_byte(s, s.gzhead.extra.length & 0xff);\n          put_byte(s, s.gzhead.extra.length >> 8 & 0xff);\n        }\n        if (s.gzhead.hcrc) {\n          strm.adler = crc32(strm.adler, s.pending_buf, s.pending, 0);\n        }\n        s.gzindex = 0;\n        s.status = EXTRA_STATE;\n      }\n    } else\n      // DEFLATE header\n      {\n        var header = Z_DEFLATED + (s.w_bits - 8 << 4) << 8;\n        var level_flags = -1;\n        if (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2) {\n          level_flags = 0;\n        } else if (s.level < 6) {\n          level_flags = 1;\n        } else if (s.level === 6) {\n          level_flags = 2;\n        } else {\n          level_flags = 3;\n        }\n        header |= level_flags << 6;\n        if (s.strstart !== 0) {\n          header |= PRESET_DICT;\n        }\n        header += 31 - header % 31;\n        s.status = BUSY_STATE;\n        putShortMSB(s, header);\n\n        /* Save the adler32 of the preset dictionary: */\n        if (s.strstart !== 0) {\n          putShortMSB(s, strm.adler >>> 16);\n          putShortMSB(s, strm.adler & 0xffff);\n        }\n        strm.adler = 1; // adler32(0L, Z_NULL, 0);\n      }\n  }\n\n  //#ifdef GZIP\n  if (s.status === EXTRA_STATE) {\n    if (s.gzhead.extra /* != Z_NULL*/) {\n      beg = s.pending; /* start of bytes to update crc */\n\n      while (s.gzindex < (s.gzhead.extra.length & 0xffff)) {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            break;\n          }\n        }\n        put_byte(s, s.gzhead.extra[s.gzindex] & 0xff);\n        s.gzindex++;\n      }\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (s.gzindex === s.gzhead.extra.length) {\n        s.gzindex = 0;\n        s.status = NAME_STATE;\n      }\n    } else {\n      s.status = NAME_STATE;\n    }\n  }\n  if (s.status === NAME_STATE) {\n    if (s.gzhead.name /* != Z_NULL*/) {\n      beg = s.pending; /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.name.length) {\n          val = s.gzhead.name.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.gzindex = 0;\n        s.status = COMMENT_STATE;\n      }\n    } else {\n      s.status = COMMENT_STATE;\n    }\n  }\n  if (s.status === COMMENT_STATE) {\n    if (s.gzhead.comment /* != Z_NULL*/) {\n      beg = s.pending; /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.comment.length) {\n          val = s.gzhead.comment.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.status = HCRC_STATE;\n      }\n    } else {\n      s.status = HCRC_STATE;\n    }\n  }\n  if (s.status === HCRC_STATE) {\n    if (s.gzhead.hcrc) {\n      if (s.pending + 2 > s.pending_buf_size) {\n        flush_pending(strm);\n      }\n      if (s.pending + 2 <= s.pending_buf_size) {\n        put_byte(s, strm.adler & 0xff);\n        put_byte(s, strm.adler >> 8 & 0xff);\n        strm.adler = 0; //crc32(0L, Z_NULL, 0);\n        s.status = BUSY_STATE;\n      }\n    } else {\n      s.status = BUSY_STATE;\n    }\n  }\n  //#endif\n\n  /* Flush as much pending output as possible */\n  if (s.pending !== 0) {\n    flush_pending(strm);\n    if (strm.avail_out === 0) {\n      /* Since avail_out is 0, deflate will be called again with\n       * more output space, but possibly with both pending and\n       * avail_in equal to zero. There won't be anything to do,\n       * but this is not an error situation so make sure we\n       * return OK instead of BUF_ERROR at next call of deflate:\n       */\n      s.last_flush = -1;\n      return Z_OK;\n    }\n\n    /* Make sure there is something to do and avoid duplicate consecutive\n     * flushes. For repeated and useless calls with Z_FINISH, we keep\n     * returning Z_STREAM_END instead of Z_BUF_ERROR.\n     */\n  } else if (strm.avail_in === 0 && rank(flush) <= rank(old_flush) && flush !== Z_FINISH) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* User must not provide more input after the first FINISH: */\n  if (s.status === FINISH_STATE && strm.avail_in !== 0) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* Start a new block or continue the current one.\n   */\n  if (strm.avail_in !== 0 || s.lookahead !== 0 || flush !== Z_NO_FLUSH && s.status !== FINISH_STATE) {\n    var bstate = s.strategy === Z_HUFFMAN_ONLY ? deflate_huff(s, flush) : s.strategy === Z_RLE ? deflate_rle(s, flush) : configuration_table[s.level].func(s, flush);\n    if (bstate === BS_FINISH_STARTED || bstate === BS_FINISH_DONE) {\n      s.status = FINISH_STATE;\n    }\n    if (bstate === BS_NEED_MORE || bstate === BS_FINISH_STARTED) {\n      if (strm.avail_out === 0) {\n        s.last_flush = -1;\n        /* avoid BUF_ERROR next call, see above */\n      }\n      return Z_OK;\n      /* If flush != Z_NO_FLUSH && avail_out == 0, the next call\n       * of deflate should use the same flush parameter to make sure\n       * that the flush is complete. So we don't have to output an\n       * empty block here, this will be done at next call. This also\n       * ensures that for a very small output buffer, we emit at most\n       * one empty block.\n       */\n    }\n    if (bstate === BS_BLOCK_DONE) {\n      if (flush === Z_PARTIAL_FLUSH) {\n        trees._tr_align(s);\n      } else if (flush !== Z_BLOCK) {\n        /* FULL_FLUSH or SYNC_FLUSH */\n\n        trees._tr_stored_block(s, 0, 0, false);\n        /* For a full flush, this empty block will be recognized\n         * as a special marker by inflate_sync().\n         */\n        if (flush === Z_FULL_FLUSH) {\n          /*** CLEAR_HASH(s); ***/ /* forget history */\n          zero(s.head); // Fill with NIL (= 0);\n\n          if (s.lookahead === 0) {\n            s.strstart = 0;\n            s.block_start = 0;\n            s.insert = 0;\n          }\n        }\n      }\n      flush_pending(strm);\n      if (strm.avail_out === 0) {\n        s.last_flush = -1; /* avoid BUF_ERROR at next call, see above */\n        return Z_OK;\n      }\n    }\n  }\n  //Assert(strm->avail_out > 0, \"bug2\");\n  //if (strm.avail_out <= 0) { throw new Error(\"bug2\");}\n\n  if (flush !== Z_FINISH) {\n    return Z_OK;\n  }\n  if (s.wrap <= 0) {\n    return Z_STREAM_END;\n  }\n\n  /* Write the trailer */\n  if (s.wrap === 2) {\n    put_byte(s, strm.adler & 0xff);\n    put_byte(s, strm.adler >> 8 & 0xff);\n    put_byte(s, strm.adler >> 16 & 0xff);\n    put_byte(s, strm.adler >> 24 & 0xff);\n    put_byte(s, strm.total_in & 0xff);\n    put_byte(s, strm.total_in >> 8 & 0xff);\n    put_byte(s, strm.total_in >> 16 & 0xff);\n    put_byte(s, strm.total_in >> 24 & 0xff);\n  } else {\n    putShortMSB(s, strm.adler >>> 16);\n    putShortMSB(s, strm.adler & 0xffff);\n  }\n  flush_pending(strm);\n  /* If avail_out is zero, the application will call deflate again\n   * to flush the rest.\n   */\n  if (s.wrap > 0) {\n    s.wrap = -s.wrap;\n  }\n  /* write the trailer only once! */\n  return s.pending !== 0 ? Z_OK : Z_STREAM_END;\n}\nfunction deflateEnd(strm) {\n  var status;\n  if (!strm /*== Z_NULL*/ || !strm.state /*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n  status = strm.state.status;\n  if (status !== INIT_STATE && status !== EXTRA_STATE && status !== NAME_STATE && status !== COMMENT_STATE && status !== HCRC_STATE && status !== BUSY_STATE && status !== FINISH_STATE) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n  strm.state = null;\n  return status === BUSY_STATE ? err(strm, Z_DATA_ERROR) : Z_OK;\n}\n\n/* =========================================================================\n * Initializes the compression dictionary from the given byte\n * sequence without producing any compressed output.\n */\nfunction deflateSetDictionary(strm, dictionary) {\n  var dictLength = dictionary.length;\n  var s;\n  var str, n;\n  var wrap;\n  var avail;\n  var next;\n  var input;\n  var tmpDict;\n  if (!strm /*== Z_NULL*/ || !strm.state /*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n  s = strm.state;\n  wrap = s.wrap;\n  if (wrap === 2 || wrap === 1 && s.status !== INIT_STATE || s.lookahead) {\n    return Z_STREAM_ERROR;\n  }\n\n  /* when using zlib wrappers, compute Adler-32 for provided dictionary */\n  if (wrap === 1) {\n    /* adler32(strm->adler, dictionary, dictLength); */\n    strm.adler = adler32(strm.adler, dictionary, dictLength, 0);\n  }\n  s.wrap = 0; /* avoid computing Adler-32 in read_buf */\n\n  /* if dictionary would fill window, just replace the history */\n  if (dictLength >= s.w_size) {\n    if (wrap === 0) {\n      /* already empty otherwise */\n      /*** CLEAR_HASH(s); ***/\n      zero(s.head); // Fill with NIL (= 0);\n      s.strstart = 0;\n      s.block_start = 0;\n      s.insert = 0;\n    }\n    /* use the tail */\n    // dictionary = dictionary.slice(dictLength - s.w_size);\n    tmpDict = new utils.Buf8(s.w_size);\n    utils.arraySet(tmpDict, dictionary, dictLength - s.w_size, s.w_size, 0);\n    dictionary = tmpDict;\n    dictLength = s.w_size;\n  }\n  /* insert dictionary into window and hash */\n  avail = strm.avail_in;\n  next = strm.next_in;\n  input = strm.input;\n  strm.avail_in = dictLength;\n  strm.next_in = 0;\n  strm.input = dictionary;\n  fill_window(s);\n  while (s.lookahead >= MIN_MATCH) {\n    str = s.strstart;\n    n = s.lookahead - (MIN_MATCH - 1);\n    do {\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n      s.ins_h = (s.ins_h << s.hash_shift ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n      s.prev[str & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = str;\n      str++;\n    } while (--n);\n    s.strstart = str;\n    s.lookahead = MIN_MATCH - 1;\n    fill_window(s);\n  }\n  s.strstart += s.lookahead;\n  s.block_start = s.strstart;\n  s.insert = s.lookahead;\n  s.lookahead = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  strm.next_in = next;\n  strm.input = input;\n  strm.avail_in = avail;\n  s.wrap = wrap;\n  return Z_OK;\n}\nexports.deflateInit = deflateInit;\nexports.deflateInit2 = deflateInit2;\nexports.deflateReset = deflateReset;\nexports.deflateResetKeep = deflateResetKeep;\nexports.deflateSetHeader = deflateSetHeader;\nexports.deflate = deflate;\nexports.deflateEnd = deflateEnd;\nexports.deflateSetDictionary = deflateSetDictionary;\nexports.deflateInfo = 'pako deflate (from Nodeca project)';\n\n/* Not implemented\nexports.deflateBound = deflateBound;\nexports.deflateCopy = deflateCopy;\nexports.deflateParams = deflateParams;\nexports.deflatePending = deflatePending;\nexports.deflatePrime = deflatePrime;\nexports.deflateTune = deflateTune;\n*/", "map": {"version": 3, "names": ["utils", "require", "trees", "adler32", "crc32", "msg", "Z_NO_FLUSH", "Z_PARTIAL_FLUSH", "Z_FULL_FLUSH", "Z_FINISH", "Z_BLOCK", "Z_OK", "Z_STREAM_END", "Z_STREAM_ERROR", "Z_DATA_ERROR", "Z_BUF_ERROR", "Z_DEFAULT_COMPRESSION", "Z_FILTERED", "Z_HUFFMAN_ONLY", "Z_RLE", "Z_FIXED", "Z_DEFAULT_STRATEGY", "Z_UNKNOWN", "Z_DEFLATED", "MAX_MEM_LEVEL", "MAX_WBITS", "DEF_MEM_LEVEL", "LENGTH_CODES", "LITERALS", "L_CODES", "D_CODES", "BL_CODES", "HEAP_SIZE", "MAX_BITS", "MIN_MATCH", "MAX_MATCH", "MIN_LOOKAHEAD", "PRESET_DICT", "INIT_STATE", "EXTRA_STATE", "NAME_STATE", "COMMENT_STATE", "HCRC_STATE", "BUSY_STATE", "FINISH_STATE", "BS_NEED_MORE", "BS_BLOCK_DONE", "BS_FINISH_STARTED", "BS_FINISH_DONE", "OS_CODE", "err", "strm", "errorCode", "rank", "f", "zero", "buf", "len", "length", "flush_pending", "s", "state", "pending", "avail_out", "arraySet", "output", "pending_buf", "pending_out", "next_out", "total_out", "flush_block_only", "last", "_tr_flush_block", "block_start", "strstart", "put_byte", "b", "putShortMSB", "read_buf", "start", "size", "avail_in", "input", "next_in", "wrap", "<PERSON><PERSON>", "total_in", "longest_match", "cur_match", "chain_length", "max_chain_length", "scan", "match", "best_len", "prev_length", "nice_match", "limit", "w_size", "_win", "window", "wmask", "w_mask", "prev", "strend", "scan_end1", "scan_end", "good_match", "<PERSON><PERSON><PERSON>", "match_start", "fill_window", "_w_size", "p", "n", "m", "more", "str", "window_size", "hash_size", "head", "insert", "ins_h", "hash_shift", "hash_mask", "deflate_stored", "flush", "max_block_size", "pending_buf_size", "max_start", "deflate_fast", "hash_head", "bflush", "match_length", "_tr_tally", "max_lazy_match", "last_lit", "deflate_slow", "max_insert", "prev_match", "strategy", "match_available", "deflate_rle", "deflate_huff", "Config", "good_length", "max_lazy", "nice_length", "max_chain", "func", "configuration_table", "lm_init", "level", "DeflateState", "status", "gzhead", "gzindex", "method", "last_flush", "w_bits", "hash_bits", "dyn_ltree", "Buf16", "dyn_dtree", "bl_tree", "l_desc", "d_desc", "bl_desc", "bl_count", "heap", "heap_len", "heap_max", "depth", "l_buf", "lit_bufsize", "d_buf", "opt_len", "static_len", "matches", "bi_buf", "bi_valid", "deflateResetKeep", "data_type", "_tr_init", "deflateReset", "ret", "deflateSetHeader", "deflateInit2", "windowBits", "memLevel", "Buf8", "deflateInit", "deflate", "old_flush", "beg", "val", "text", "hcrc", "extra", "name", "comment", "time", "os", "header", "level_flags", "charCodeAt", "bstate", "_tr_align", "_tr_stored_block", "deflateEnd", "deflateSetDictionary", "dictionary", "dict<PERSON>ength", "avail", "next", "tmpDict", "exports", "deflateInfo"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/zlib/deflate.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nvar utils   = require('../utils/common');\nvar trees   = require('./trees');\nvar adler32 = require('./adler32');\nvar crc32   = require('./crc32');\nvar msg     = require('./messages');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n/* Allowed flush values; see deflate() and inflate() below for details */\nvar Z_NO_FLUSH      = 0;\nvar Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\nvar Z_FULL_FLUSH    = 3;\nvar Z_FINISH        = 4;\nvar Z_BLOCK         = 5;\n//var Z_TREES         = 6;\n\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\n//var Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR  = -2;\nvar Z_DATA_ERROR    = -3;\n//var Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR     = -5;\n//var Z_VERSION_ERROR = -6;\n\n\n/* compression levels */\n//var Z_NO_COMPRESSION      = 0;\n//var Z_BEST_SPEED          = 1;\n//var Z_BEST_COMPRESSION    = 9;\nvar Z_DEFAULT_COMPRESSION = -1;\n\n\nvar Z_FILTERED            = 1;\nvar Z_HUFFMAN_ONLY        = 2;\nvar Z_RLE                 = 3;\nvar Z_FIXED               = 4;\nvar Z_DEFAULT_STRATEGY    = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\n//var Z_BINARY              = 0;\n//var Z_TEXT                = 1;\n//var Z_ASCII               = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n\n/* The deflate compression method */\nvar Z_DEFLATED  = 8;\n\n/*============================================================================*/\n\n\nvar MAX_MEM_LEVEL = 9;\n/* Maximum value for memLevel in deflateInit2 */\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_MEM_LEVEL = 8;\n\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\nvar D_CODES       = 30;\n/* number of distance codes */\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\nvar HEAP_SIZE     = 2 * L_CODES + 1;\n/* maximum heap size */\nvar MAX_BITS  = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar MIN_MATCH = 3;\nvar MAX_MATCH = 258;\nvar MIN_LOOKAHEAD = (MAX_MATCH + MIN_MATCH + 1);\n\nvar PRESET_DICT = 0x20;\n\nvar INIT_STATE = 42;\nvar EXTRA_STATE = 69;\nvar NAME_STATE = 73;\nvar COMMENT_STATE = 91;\nvar HCRC_STATE = 103;\nvar BUSY_STATE = 113;\nvar FINISH_STATE = 666;\n\nvar BS_NEED_MORE      = 1; /* block not completed, need more input or more output */\nvar BS_BLOCK_DONE     = 2; /* block flush performed */\nvar BS_FINISH_STARTED = 3; /* finish started, need only more output at next deflate */\nvar BS_FINISH_DONE    = 4; /* finish done, accept no more input or output */\n\nvar OS_CODE = 0x03; // Unix :) . Don't detect, use this default.\n\nfunction err(strm, errorCode) {\n  strm.msg = msg[errorCode];\n  return errorCode;\n}\n\nfunction rank(f) {\n  return ((f) << 1) - ((f) > 4 ? 9 : 0);\n}\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n\n/* =========================================================================\n * Flush as much pending output as possible. All deflate() output goes\n * through this function so some applications may wish to modify it\n * to avoid allocating a large strm->output buffer and copying into it.\n * (See also read_buf()).\n */\nfunction flush_pending(strm) {\n  var s = strm.state;\n\n  //_tr_flush_bits(s);\n  var len = s.pending;\n  if (len > strm.avail_out) {\n    len = strm.avail_out;\n  }\n  if (len === 0) { return; }\n\n  utils.arraySet(strm.output, s.pending_buf, s.pending_out, len, strm.next_out);\n  strm.next_out += len;\n  s.pending_out += len;\n  strm.total_out += len;\n  strm.avail_out -= len;\n  s.pending -= len;\n  if (s.pending === 0) {\n    s.pending_out = 0;\n  }\n}\n\n\nfunction flush_block_only(s, last) {\n  trees._tr_flush_block(s, (s.block_start >= 0 ? s.block_start : -1), s.strstart - s.block_start, last);\n  s.block_start = s.strstart;\n  flush_pending(s.strm);\n}\n\n\nfunction put_byte(s, b) {\n  s.pending_buf[s.pending++] = b;\n}\n\n\n/* =========================================================================\n * Put a short in the pending buffer. The 16-bit value is put in MSB order.\n * IN assertion: the stream state is correct and there is enough room in\n * pending_buf.\n */\nfunction putShortMSB(s, b) {\n//  put_byte(s, (Byte)(b >> 8));\n//  put_byte(s, (Byte)(b & 0xff));\n  s.pending_buf[s.pending++] = (b >>> 8) & 0xff;\n  s.pending_buf[s.pending++] = b & 0xff;\n}\n\n\n/* ===========================================================================\n * Read a new buffer from the current input stream, update the adler32\n * and total number of bytes read.  All deflate() input goes through\n * this function so some applications may wish to modify it to avoid\n * allocating a large strm->input buffer and copying from it.\n * (See also flush_pending()).\n */\nfunction read_buf(strm, buf, start, size) {\n  var len = strm.avail_in;\n\n  if (len > size) { len = size; }\n  if (len === 0) { return 0; }\n\n  strm.avail_in -= len;\n\n  // zmemcpy(buf, strm->next_in, len);\n  utils.arraySet(buf, strm.input, strm.next_in, len, start);\n  if (strm.state.wrap === 1) {\n    strm.adler = adler32(strm.adler, buf, len, start);\n  }\n\n  else if (strm.state.wrap === 2) {\n    strm.adler = crc32(strm.adler, buf, len, start);\n  }\n\n  strm.next_in += len;\n  strm.total_in += len;\n\n  return len;\n}\n\n\n/* ===========================================================================\n * Set match_start to the longest match starting at the given string and\n * return its length. Matches shorter or equal to prev_length are discarded,\n * in which case the result is equal to prev_length and match_start is\n * garbage.\n * IN assertions: cur_match is the head of the hash chain for the current\n *   string (strstart) and its distance is <= MAX_DIST, and prev_length >= 1\n * OUT assertion: the match length is not greater than s->lookahead.\n */\nfunction longest_match(s, cur_match) {\n  var chain_length = s.max_chain_length;      /* max hash chain length */\n  var scan = s.strstart; /* current string */\n  var match;                       /* matched string */\n  var len;                           /* length of current match */\n  var best_len = s.prev_length;              /* best match length so far */\n  var nice_match = s.nice_match;             /* stop if match long enough */\n  var limit = (s.strstart > (s.w_size - MIN_LOOKAHEAD)) ?\n      s.strstart - (s.w_size - MIN_LOOKAHEAD) : 0/*NIL*/;\n\n  var _win = s.window; // shortcut\n\n  var wmask = s.w_mask;\n  var prev  = s.prev;\n\n  /* Stop when cur_match becomes <= limit. To simplify the code,\n   * we prevent matches with the string of window index 0.\n   */\n\n  var strend = s.strstart + MAX_MATCH;\n  var scan_end1  = _win[scan + best_len - 1];\n  var scan_end   = _win[scan + best_len];\n\n  /* The code is optimized for HASH_BITS >= 8 and MAX_MATCH-2 multiple of 16.\n   * It is easy to get rid of this optimization if necessary.\n   */\n  // Assert(s->hash_bits >= 8 && MAX_MATCH == 258, \"Code too clever\");\n\n  /* Do not waste too much time if we already have a good match: */\n  if (s.prev_length >= s.good_match) {\n    chain_length >>= 2;\n  }\n  /* Do not look for matches beyond the end of the input. This is necessary\n   * to make deflate deterministic.\n   */\n  if (nice_match > s.lookahead) { nice_match = s.lookahead; }\n\n  // Assert((ulg)s->strstart <= s->window_size-MIN_LOOKAHEAD, \"need lookahead\");\n\n  do {\n    // Assert(cur_match < s->strstart, \"no future\");\n    match = cur_match;\n\n    /* Skip to next match if the match length cannot increase\n     * or if the match length is less than 2.  Note that the checks below\n     * for insufficient lookahead only occur occasionally for performance\n     * reasons.  Therefore uninitialized memory will be accessed, and\n     * conditional jumps will be made that depend on those values.\n     * However the length of the match is limited to the lookahead, so\n     * the output of deflate is not affected by the uninitialized values.\n     */\n\n    if (_win[match + best_len]     !== scan_end  ||\n        _win[match + best_len - 1] !== scan_end1 ||\n        _win[match]                !== _win[scan] ||\n        _win[++match]              !== _win[scan + 1]) {\n      continue;\n    }\n\n    /* The check at best_len-1 can be removed because it will be made\n     * again later. (This heuristic is not always a win.)\n     * It is not necessary to compare scan[2] and match[2] since they\n     * are always equal when the other bytes match, given that\n     * the hash keys are equal and that HASH_BITS >= 8.\n     */\n    scan += 2;\n    match++;\n    // Assert(*scan == *match, \"match[2]?\");\n\n    /* We check for insufficient lookahead only every 8th comparison;\n     * the 256th check will be made at strstart+258.\n     */\n    do {\n      /*jshint noempty:false*/\n    } while (_win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             scan < strend);\n\n    // Assert(scan <= s->window+(unsigned)(s->window_size-1), \"wild scan\");\n\n    len = MAX_MATCH - (strend - scan);\n    scan = strend - MAX_MATCH;\n\n    if (len > best_len) {\n      s.match_start = cur_match;\n      best_len = len;\n      if (len >= nice_match) {\n        break;\n      }\n      scan_end1  = _win[scan + best_len - 1];\n      scan_end   = _win[scan + best_len];\n    }\n  } while ((cur_match = prev[cur_match & wmask]) > limit && --chain_length !== 0);\n\n  if (best_len <= s.lookahead) {\n    return best_len;\n  }\n  return s.lookahead;\n}\n\n\n/* ===========================================================================\n * Fill the window when the lookahead becomes insufficient.\n * Updates strstart and lookahead.\n *\n * IN assertion: lookahead < MIN_LOOKAHEAD\n * OUT assertions: strstart <= window_size-MIN_LOOKAHEAD\n *    At least one byte has been read, or avail_in == 0; reads are\n *    performed for at least two bytes (required for the zip translate_eol\n *    option -- not supported here).\n */\nfunction fill_window(s) {\n  var _w_size = s.w_size;\n  var p, n, m, more, str;\n\n  //Assert(s->lookahead < MIN_LOOKAHEAD, \"already enough lookahead\");\n\n  do {\n    more = s.window_size - s.lookahead - s.strstart;\n\n    // JS ints have 32 bit, block below not needed\n    /* Deal with !@#$% 64K limit: */\n    //if (sizeof(int) <= 2) {\n    //    if (more == 0 && s->strstart == 0 && s->lookahead == 0) {\n    //        more = wsize;\n    //\n    //  } else if (more == (unsigned)(-1)) {\n    //        /* Very unlikely, but possible on 16 bit machine if\n    //         * strstart == 0 && lookahead == 1 (input done a byte at time)\n    //         */\n    //        more--;\n    //    }\n    //}\n\n\n    /* If the window is almost full and there is insufficient lookahead,\n     * move the upper half to the lower one to make room in the upper half.\n     */\n    if (s.strstart >= _w_size + (_w_size - MIN_LOOKAHEAD)) {\n\n      utils.arraySet(s.window, s.window, _w_size, _w_size, 0);\n      s.match_start -= _w_size;\n      s.strstart -= _w_size;\n      /* we now have strstart >= MAX_DIST */\n      s.block_start -= _w_size;\n\n      /* Slide the hash table (could be avoided with 32 bit values\n       at the expense of memory usage). We slide even when level == 0\n       to keep the hash table consistent if we switch back to level > 0\n       later. (Using level 0 permanently is not an optimal usage of\n       zlib, so we don't care about this pathological case.)\n       */\n\n      n = s.hash_size;\n      p = n;\n      do {\n        m = s.head[--p];\n        s.head[p] = (m >= _w_size ? m - _w_size : 0);\n      } while (--n);\n\n      n = _w_size;\n      p = n;\n      do {\n        m = s.prev[--p];\n        s.prev[p] = (m >= _w_size ? m - _w_size : 0);\n        /* If n is not on any hash chain, prev[n] is garbage but\n         * its value will never be used.\n         */\n      } while (--n);\n\n      more += _w_size;\n    }\n    if (s.strm.avail_in === 0) {\n      break;\n    }\n\n    /* If there was no sliding:\n     *    strstart <= WSIZE+MAX_DIST-1 && lookahead <= MIN_LOOKAHEAD - 1 &&\n     *    more == window_size - lookahead - strstart\n     * => more >= window_size - (MIN_LOOKAHEAD-1 + WSIZE + MAX_DIST-1)\n     * => more >= window_size - 2*WSIZE + 2\n     * In the BIG_MEM or MMAP case (not yet supported),\n     *   window_size == input_size + MIN_LOOKAHEAD  &&\n     *   strstart + s->lookahead <= input_size => more >= MIN_LOOKAHEAD.\n     * Otherwise, window_size == 2*WSIZE so more >= 2.\n     * If there was sliding, more >= WSIZE. So in all cases, more >= 2.\n     */\n    //Assert(more >= 2, \"more < 2\");\n    n = read_buf(s.strm, s.window, s.strstart + s.lookahead, more);\n    s.lookahead += n;\n\n    /* Initialize the hash value now that we have some input: */\n    if (s.lookahead + s.insert >= MIN_MATCH) {\n      str = s.strstart - s.insert;\n      s.ins_h = s.window[str];\n\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + 1]); */\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + 1]) & s.hash_mask;\n//#if MIN_MATCH != 3\n//        Call update_hash() MIN_MATCH-3 more times\n//#endif\n      while (s.insert) {\n        /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n\n        s.prev[str & s.w_mask] = s.head[s.ins_h];\n        s.head[s.ins_h] = str;\n        str++;\n        s.insert--;\n        if (s.lookahead + s.insert < MIN_MATCH) {\n          break;\n        }\n      }\n    }\n    /* If the whole input has less than MIN_MATCH bytes, ins_h is garbage,\n     * but this is not important since only literal bytes will be emitted.\n     */\n\n  } while (s.lookahead < MIN_LOOKAHEAD && s.strm.avail_in !== 0);\n\n  /* If the WIN_INIT bytes after the end of the current data have never been\n   * written, then zero those bytes in order to avoid memory check reports of\n   * the use of uninitialized (or uninitialised as Julian writes) bytes by\n   * the longest match routines.  Update the high water mark for the next\n   * time through here.  WIN_INIT is set to MAX_MATCH since the longest match\n   * routines allow scanning to strstart + MAX_MATCH, ignoring lookahead.\n   */\n//  if (s.high_water < s.window_size) {\n//    var curr = s.strstart + s.lookahead;\n//    var init = 0;\n//\n//    if (s.high_water < curr) {\n//      /* Previous high water mark below current data -- zero WIN_INIT\n//       * bytes or up to end of window, whichever is less.\n//       */\n//      init = s.window_size - curr;\n//      if (init > WIN_INIT)\n//        init = WIN_INIT;\n//      zmemzero(s->window + curr, (unsigned)init);\n//      s->high_water = curr + init;\n//    }\n//    else if (s->high_water < (ulg)curr + WIN_INIT) {\n//      /* High water mark at or above current data, but below current data\n//       * plus WIN_INIT -- zero out to current data plus WIN_INIT, or up\n//       * to end of window, whichever is less.\n//       */\n//      init = (ulg)curr + WIN_INIT - s->high_water;\n//      if (init > s->window_size - s->high_water)\n//        init = s->window_size - s->high_water;\n//      zmemzero(s->window + s->high_water, (unsigned)init);\n//      s->high_water += init;\n//    }\n//  }\n//\n//  Assert((ulg)s->strstart <= s->window_size - MIN_LOOKAHEAD,\n//    \"not enough room for search\");\n}\n\n/* ===========================================================================\n * Copy without compression as much as possible from the input stream, return\n * the current block state.\n * This function does not insert new strings in the dictionary since\n * uncompressible data is probably not useful. This function is used\n * only for the level=0 compression option.\n * NOTE: this function should be optimized to avoid extra copying from\n * window to pending_buf.\n */\nfunction deflate_stored(s, flush) {\n  /* Stored blocks are limited to 0xffff bytes, pending_buf is limited\n   * to pending_buf_size, and each stored block has a 5 byte header:\n   */\n  var max_block_size = 0xffff;\n\n  if (max_block_size > s.pending_buf_size - 5) {\n    max_block_size = s.pending_buf_size - 5;\n  }\n\n  /* Copy as much as possible from input to output: */\n  for (;;) {\n    /* Fill the window as much as possible: */\n    if (s.lookahead <= 1) {\n\n      //Assert(s->strstart < s->w_size+MAX_DIST(s) ||\n      //  s->block_start >= (long)s->w_size, \"slide too late\");\n//      if (!(s.strstart < s.w_size + (s.w_size - MIN_LOOKAHEAD) ||\n//        s.block_start >= s.w_size)) {\n//        throw  new Error(\"slide too late\");\n//      }\n\n      fill_window(s);\n      if (s.lookahead === 0 && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n\n      if (s.lookahead === 0) {\n        break;\n      }\n      /* flush the current block */\n    }\n    //Assert(s->block_start >= 0L, \"block gone\");\n//    if (s.block_start < 0) throw new Error(\"block gone\");\n\n    s.strstart += s.lookahead;\n    s.lookahead = 0;\n\n    /* Emit a stored block if pending_buf will be full: */\n    var max_start = s.block_start + max_block_size;\n\n    if (s.strstart === 0 || s.strstart >= max_start) {\n      /* strstart == 0 is possible when wraparound on 16-bit machine */\n      s.lookahead = s.strstart - max_start;\n      s.strstart = max_start;\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n\n\n    }\n    /* Flush if we may have to slide, otherwise block_start may become\n     * negative and the data will be gone:\n     */\n    if (s.strstart - s.block_start >= (s.w_size - MIN_LOOKAHEAD)) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n\n  s.insert = 0;\n\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n\n  if (s.strstart > s.block_start) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_NEED_MORE;\n}\n\n/* ===========================================================================\n * Compress as much as possible from the input stream, return the current\n * block state.\n * This function does not perform lazy evaluation of matches and inserts\n * new strings in the dictionary only for unmatched strings or for short\n * matches. It is used only for the fast compression options.\n */\nfunction deflate_fast(s, flush) {\n  var hash_head;        /* head of the hash chain */\n  var bflush;           /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break; /* flush the current block */\n      }\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     * At this point we have always match_length < MIN_MATCH\n     */\n    if (hash_head !== 0/*NIL*/ && ((s.strstart - hash_head) <= (s.w_size - MIN_LOOKAHEAD))) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n    }\n    if (s.match_length >= MIN_MATCH) {\n      // check_match(s, s.strstart, s.match_start, s.match_length); // for debug only\n\n      /*** _tr_tally_dist(s, s.strstart - s.match_start,\n                     s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, s.strstart - s.match_start, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n\n      /* Insert new strings in the hash table only if the match length\n       * is not too large. This saves time but degrades compression.\n       */\n      if (s.match_length <= s.max_lazy_match/*max_insert_length*/ && s.lookahead >= MIN_MATCH) {\n        s.match_length--; /* string at strstart already in table */\n        do {\n          s.strstart++;\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n          /* strstart never exceeds WSIZE-MAX_MATCH, so there are\n           * always MIN_MATCH bytes ahead.\n           */\n        } while (--s.match_length !== 0);\n        s.strstart++;\n      } else\n      {\n        s.strstart += s.match_length;\n        s.match_length = 0;\n        s.ins_h = s.window[s.strstart];\n        /* UPDATE_HASH(s, s.ins_h, s.window[s.strstart+1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + 1]) & s.hash_mask;\n\n//#if MIN_MATCH != 3\n//                Call UPDATE_HASH() MIN_MATCH-3 more times\n//#endif\n        /* If lookahead < MIN_MATCH, ins_h is garbage, but it does not\n         * matter since it will be recomputed at next deflate call.\n         */\n      }\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s.window[s.strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = ((s.strstart < (MIN_MATCH - 1)) ? s.strstart : MIN_MATCH - 1);\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * Same as above, but achieves better compression. We use a lazy\n * evaluation for matches: a match is finally adopted only if there is\n * no better match at the next window position.\n */\nfunction deflate_slow(s, flush) {\n  var hash_head;          /* head of hash chain */\n  var bflush;              /* set if current block must be flushed */\n\n  var max_insert;\n\n  /* Process the input block. */\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     */\n    s.prev_length = s.match_length;\n    s.prev_match = s.match_start;\n    s.match_length = MIN_MATCH - 1;\n\n    if (hash_head !== 0/*NIL*/ && s.prev_length < s.max_lazy_match &&\n        s.strstart - hash_head <= (s.w_size - MIN_LOOKAHEAD)/*MAX_DIST(s)*/) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n\n      if (s.match_length <= 5 &&\n         (s.strategy === Z_FILTERED || (s.match_length === MIN_MATCH && s.strstart - s.match_start > 4096/*TOO_FAR*/))) {\n\n        /* If prev_match is also MIN_MATCH, match_start is garbage\n         * but we will ignore the current match anyway.\n         */\n        s.match_length = MIN_MATCH - 1;\n      }\n    }\n    /* If there was a match at the previous step and the current\n     * match is not better, output the previous match:\n     */\n    if (s.prev_length >= MIN_MATCH && s.match_length <= s.prev_length) {\n      max_insert = s.strstart + s.lookahead - MIN_MATCH;\n      /* Do not insert strings in hash table beyond this. */\n\n      //check_match(s, s.strstart-1, s.prev_match, s.prev_length);\n\n      /***_tr_tally_dist(s, s.strstart - 1 - s.prev_match,\n                     s.prev_length - MIN_MATCH, bflush);***/\n      bflush = trees._tr_tally(s, s.strstart - 1 - s.prev_match, s.prev_length - MIN_MATCH);\n      /* Insert in hash table all strings up to the end of the match.\n       * strstart-1 and strstart are already inserted. If there is not\n       * enough lookahead, the last two strings are not inserted in\n       * the hash table.\n       */\n      s.lookahead -= s.prev_length - 1;\n      s.prev_length -= 2;\n      do {\n        if (++s.strstart <= max_insert) {\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n        }\n      } while (--s.prev_length !== 0);\n      s.match_available = 0;\n      s.match_length = MIN_MATCH - 1;\n      s.strstart++;\n\n      if (bflush) {\n        /*** FLUSH_BLOCK(s, 0); ***/\n        flush_block_only(s, false);\n        if (s.strm.avail_out === 0) {\n          return BS_NEED_MORE;\n        }\n        /***/\n      }\n\n    } else if (s.match_available) {\n      /* If there was no match at the previous position, output a\n       * single literal. If there was a match but the current match\n       * is longer, truncate the previous match to a single literal.\n       */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n      /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n\n      if (bflush) {\n        /*** FLUSH_BLOCK_ONLY(s, 0) ***/\n        flush_block_only(s, false);\n        /***/\n      }\n      s.strstart++;\n      s.lookahead--;\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n    } else {\n      /* There is no previous match to compare with, wait for\n       * the next step to decide.\n       */\n      s.match_available = 1;\n      s.strstart++;\n      s.lookahead--;\n    }\n  }\n  //Assert (flush != Z_NO_FLUSH, \"no flush?\");\n  if (s.match_available) {\n    //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n    /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n\n    s.match_available = 0;\n  }\n  s.insert = s.strstart < MIN_MATCH - 1 ? s.strstart : MIN_MATCH - 1;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_BLOCK_DONE;\n}\n\n\n/* ===========================================================================\n * For Z_RLE, simply look for runs of bytes, generate matches only of distance\n * one.  Do not maintain a hash table.  (It will be regenerated if this run of\n * deflate switches away from Z_RLE.)\n */\nfunction deflate_rle(s, flush) {\n  var bflush;            /* set if current block must be flushed */\n  var prev;              /* byte at distance one to match */\n  var scan, strend;      /* scan goes up to strend for length of run */\n\n  var _win = s.window;\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the longest run, plus one for the unrolled loop.\n     */\n    if (s.lookahead <= MAX_MATCH) {\n      fill_window(s);\n      if (s.lookahead <= MAX_MATCH && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* See how many times the previous byte repeats */\n    s.match_length = 0;\n    if (s.lookahead >= MIN_MATCH && s.strstart > 0) {\n      scan = s.strstart - 1;\n      prev = _win[scan];\n      if (prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan]) {\n        strend = s.strstart + MAX_MATCH;\n        do {\n          /*jshint noempty:false*/\n        } while (prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 scan < strend);\n        s.match_length = MAX_MATCH - (strend - scan);\n        if (s.match_length > s.lookahead) {\n          s.match_length = s.lookahead;\n        }\n      }\n      //Assert(scan <= s->window+(uInt)(s->window_size-1), \"wild scan\");\n    }\n\n    /* Emit match if have run of MIN_MATCH or longer, else emit literal */\n    if (s.match_length >= MIN_MATCH) {\n      //check_match(s, s.strstart, s.strstart - 1, s.match_length);\n\n      /*** _tr_tally_dist(s, 1, s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, 1, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n      s.strstart += s.match_length;\n      s.match_length = 0;\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * For Z_HUFFMAN_ONLY, do not look for matches.  Do not maintain a hash table.\n * (It will be regenerated if this run of deflate switches away from Huffman.)\n */\nfunction deflate_huff(s, flush) {\n  var bflush;             /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we have a literal to write. */\n    if (s.lookahead === 0) {\n      fill_window(s);\n      if (s.lookahead === 0) {\n        if (flush === Z_NO_FLUSH) {\n          return BS_NEED_MORE;\n        }\n        break;      /* flush the current block */\n      }\n    }\n\n    /* Output a literal byte */\n    s.match_length = 0;\n    //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n    /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n    s.lookahead--;\n    s.strstart++;\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* Values for max_lazy_match, good_match and max_chain_length, depending on\n * the desired pack level (0..9). The values given below have been tuned to\n * exclude worst case performance for pathological files. Better values may be\n * found for specific files.\n */\nfunction Config(good_length, max_lazy, nice_length, max_chain, func) {\n  this.good_length = good_length;\n  this.max_lazy = max_lazy;\n  this.nice_length = nice_length;\n  this.max_chain = max_chain;\n  this.func = func;\n}\n\nvar configuration_table;\n\nconfiguration_table = [\n  /*      good lazy nice chain */\n  new Config(0, 0, 0, 0, deflate_stored),          /* 0 store only */\n  new Config(4, 4, 8, 4, deflate_fast),            /* 1 max speed, no lazy matches */\n  new Config(4, 5, 16, 8, deflate_fast),           /* 2 */\n  new Config(4, 6, 32, 32, deflate_fast),          /* 3 */\n\n  new Config(4, 4, 16, 16, deflate_slow),          /* 4 lazy matches */\n  new Config(8, 16, 32, 32, deflate_slow),         /* 5 */\n  new Config(8, 16, 128, 128, deflate_slow),       /* 6 */\n  new Config(8, 32, 128, 256, deflate_slow),       /* 7 */\n  new Config(32, 128, 258, 1024, deflate_slow),    /* 8 */\n  new Config(32, 258, 258, 4096, deflate_slow)     /* 9 max compression */\n];\n\n\n/* ===========================================================================\n * Initialize the \"longest match\" routines for a new zlib stream\n */\nfunction lm_init(s) {\n  s.window_size = 2 * s.w_size;\n\n  /*** CLEAR_HASH(s); ***/\n  zero(s.head); // Fill with NIL (= 0);\n\n  /* Set the default configuration parameters:\n   */\n  s.max_lazy_match = configuration_table[s.level].max_lazy;\n  s.good_match = configuration_table[s.level].good_length;\n  s.nice_match = configuration_table[s.level].nice_length;\n  s.max_chain_length = configuration_table[s.level].max_chain;\n\n  s.strstart = 0;\n  s.block_start = 0;\n  s.lookahead = 0;\n  s.insert = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  s.ins_h = 0;\n}\n\n\nfunction DeflateState() {\n  this.strm = null;            /* pointer back to this zlib stream */\n  this.status = 0;            /* as the name implies */\n  this.pending_buf = null;      /* output still pending */\n  this.pending_buf_size = 0;  /* size of pending_buf */\n  this.pending_out = 0;       /* next pending byte to output to the stream */\n  this.pending = 0;           /* nb of bytes in the pending buffer */\n  this.wrap = 0;              /* bit 0 true for zlib, bit 1 true for gzip */\n  this.gzhead = null;         /* gzip header information to write */\n  this.gzindex = 0;           /* where in extra, name, or comment */\n  this.method = Z_DEFLATED; /* can only be DEFLATED */\n  this.last_flush = -1;   /* value of flush param for previous deflate call */\n\n  this.w_size = 0;  /* LZ77 window size (32K by default) */\n  this.w_bits = 0;  /* log2(w_size)  (8..16) */\n  this.w_mask = 0;  /* w_size - 1 */\n\n  this.window = null;\n  /* Sliding window. Input bytes are read into the second half of the window,\n   * and move to the first half later to keep a dictionary of at least wSize\n   * bytes. With this organization, matches are limited to a distance of\n   * wSize-MAX_MATCH bytes, but this ensures that IO is always\n   * performed with a length multiple of the block size.\n   */\n\n  this.window_size = 0;\n  /* Actual size of window: 2*wSize, except when the user input buffer\n   * is directly used as sliding window.\n   */\n\n  this.prev = null;\n  /* Link to older string with same hash index. To limit the size of this\n   * array to 64K, this link is maintained only for the last 32K strings.\n   * An index in this array is thus a window index modulo 32K.\n   */\n\n  this.head = null;   /* Heads of the hash chains or NIL. */\n\n  this.ins_h = 0;       /* hash index of string to be inserted */\n  this.hash_size = 0;   /* number of elements in hash table */\n  this.hash_bits = 0;   /* log2(hash_size) */\n  this.hash_mask = 0;   /* hash_size-1 */\n\n  this.hash_shift = 0;\n  /* Number of bits by which ins_h must be shifted at each input\n   * step. It must be such that after MIN_MATCH steps, the oldest\n   * byte no longer takes part in the hash key, that is:\n   *   hash_shift * MIN_MATCH >= hash_bits\n   */\n\n  this.block_start = 0;\n  /* Window position at the beginning of the current output block. Gets\n   * negative when the window is moved backwards.\n   */\n\n  this.match_length = 0;      /* length of best match */\n  this.prev_match = 0;        /* previous match */\n  this.match_available = 0;   /* set if previous match exists */\n  this.strstart = 0;          /* start of string to insert */\n  this.match_start = 0;       /* start of matching string */\n  this.lookahead = 0;         /* number of valid bytes ahead in window */\n\n  this.prev_length = 0;\n  /* Length of the best match at previous step. Matches not greater than this\n   * are discarded. This is used in the lazy match evaluation.\n   */\n\n  this.max_chain_length = 0;\n  /* To speed up deflation, hash chains are never searched beyond this\n   * length.  A higher limit improves compression ratio but degrades the\n   * speed.\n   */\n\n  this.max_lazy_match = 0;\n  /* Attempt to find a better match only when the current match is strictly\n   * smaller than this value. This mechanism is used only for compression\n   * levels >= 4.\n   */\n  // That's alias to max_lazy_match, don't use directly\n  //this.max_insert_length = 0;\n  /* Insert new strings in the hash table only if the match length is not\n   * greater than this length. This saves time but degrades compression.\n   * max_insert_length is used only for compression levels <= 3.\n   */\n\n  this.level = 0;     /* compression level (1..9) */\n  this.strategy = 0;  /* favor or force Huffman coding*/\n\n  this.good_match = 0;\n  /* Use a faster search when the previous match is longer than this */\n\n  this.nice_match = 0; /* Stop searching when current match exceeds this */\n\n              /* used by trees.c: */\n\n  /* Didn't use ct_data typedef below to suppress compiler warning */\n\n  // struct ct_data_s dyn_ltree[HEAP_SIZE];   /* literal and length tree */\n  // struct ct_data_s dyn_dtree[2*D_CODES+1]; /* distance tree */\n  // struct ct_data_s bl_tree[2*BL_CODES+1];  /* Huffman tree for bit lengths */\n\n  // Use flat array of DOUBLE size, with interleaved fata,\n  // because JS does not support effective\n  this.dyn_ltree  = new utils.Buf16(HEAP_SIZE * 2);\n  this.dyn_dtree  = new utils.Buf16((2 * D_CODES + 1) * 2);\n  this.bl_tree    = new utils.Buf16((2 * BL_CODES + 1) * 2);\n  zero(this.dyn_ltree);\n  zero(this.dyn_dtree);\n  zero(this.bl_tree);\n\n  this.l_desc   = null;         /* desc. for literal tree */\n  this.d_desc   = null;         /* desc. for distance tree */\n  this.bl_desc  = null;         /* desc. for bit length tree */\n\n  //ush bl_count[MAX_BITS+1];\n  this.bl_count = new utils.Buf16(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  //int heap[2*L_CODES+1];      /* heap used to build the Huffman trees */\n  this.heap = new utils.Buf16(2 * L_CODES + 1);  /* heap used to build the Huffman trees */\n  zero(this.heap);\n\n  this.heap_len = 0;               /* number of elements in the heap */\n  this.heap_max = 0;               /* element of largest frequency */\n  /* The sons of heap[n] are heap[2*n] and heap[2*n+1]. heap[0] is not used.\n   * The same heap array is used to build all trees.\n   */\n\n  this.depth = new utils.Buf16(2 * L_CODES + 1); //uch depth[2*L_CODES+1];\n  zero(this.depth);\n  /* Depth of each subtree used as tie breaker for trees of equal frequency\n   */\n\n  this.l_buf = 0;          /* buffer index for literals or lengths */\n\n  this.lit_bufsize = 0;\n  /* Size of match buffer for literals/lengths.  There are 4 reasons for\n   * limiting lit_bufsize to 64K:\n   *   - frequencies can be kept in 16 bit counters\n   *   - if compression is not successful for the first block, all input\n   *     data is still in the window so we can still emit a stored block even\n   *     when input comes from standard input.  (This can also be done for\n   *     all blocks if lit_bufsize is not greater than 32K.)\n   *   - if compression is not successful for a file smaller than 64K, we can\n   *     even emit a stored file instead of a stored block (saving 5 bytes).\n   *     This is applicable only for zip (not gzip or zlib).\n   *   - creating new Huffman trees less frequently may not provide fast\n   *     adaptation to changes in the input data statistics. (Take for\n   *     example a binary file with poorly compressible code followed by\n   *     a highly compressible string table.) Smaller buffer sizes give\n   *     fast adaptation but have of course the overhead of transmitting\n   *     trees more frequently.\n   *   - I can't count above 4\n   */\n\n  this.last_lit = 0;      /* running index in l_buf */\n\n  this.d_buf = 0;\n  /* Buffer index for distances. To simplify the code, d_buf and l_buf have\n   * the same number of elements. To use different lengths, an extra flag\n   * array would be necessary.\n   */\n\n  this.opt_len = 0;       /* bit length of current block with optimal trees */\n  this.static_len = 0;    /* bit length of current block with static trees */\n  this.matches = 0;       /* number of string matches in current block */\n  this.insert = 0;        /* bytes at end of window left to insert */\n\n\n  this.bi_buf = 0;\n  /* Output buffer. bits are inserted starting at the bottom (least\n   * significant bits).\n   */\n  this.bi_valid = 0;\n  /* Number of valid bits in bi_buf.  All bits above the last valid bit\n   * are always zero.\n   */\n\n  // Used for window memory init. We safely ignore it for JS. That makes\n  // sense only for pointers and memory check tools.\n  //this.high_water = 0;\n  /* High water mark offset in window for initialized bytes -- bytes above\n   * this are set to zero in order to avoid memory check warnings when\n   * longest match routines access bytes past the input.  This is then\n   * updated to the new high water mark.\n   */\n}\n\n\nfunction deflateResetKeep(strm) {\n  var s;\n\n  if (!strm || !strm.state) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.total_in = strm.total_out = 0;\n  strm.data_type = Z_UNKNOWN;\n\n  s = strm.state;\n  s.pending = 0;\n  s.pending_out = 0;\n\n  if (s.wrap < 0) {\n    s.wrap = -s.wrap;\n    /* was made negative by deflate(..., Z_FINISH); */\n  }\n  s.status = (s.wrap ? INIT_STATE : BUSY_STATE);\n  strm.adler = (s.wrap === 2) ?\n    0  // crc32(0, Z_NULL, 0)\n  :\n    1; // adler32(0, Z_NULL, 0)\n  s.last_flush = Z_NO_FLUSH;\n  trees._tr_init(s);\n  return Z_OK;\n}\n\n\nfunction deflateReset(strm) {\n  var ret = deflateResetKeep(strm);\n  if (ret === Z_OK) {\n    lm_init(strm.state);\n  }\n  return ret;\n}\n\n\nfunction deflateSetHeader(strm, head) {\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  if (strm.state.wrap !== 2) { return Z_STREAM_ERROR; }\n  strm.state.gzhead = head;\n  return Z_OK;\n}\n\n\nfunction deflateInit2(strm, level, method, windowBits, memLevel, strategy) {\n  if (!strm) { // === Z_NULL\n    return Z_STREAM_ERROR;\n  }\n  var wrap = 1;\n\n  if (level === Z_DEFAULT_COMPRESSION) {\n    level = 6;\n  }\n\n  if (windowBits < 0) { /* suppress zlib wrapper */\n    wrap = 0;\n    windowBits = -windowBits;\n  }\n\n  else if (windowBits > 15) {\n    wrap = 2;           /* write gzip wrapper instead */\n    windowBits -= 16;\n  }\n\n\n  if (memLevel < 1 || memLevel > MAX_MEM_LEVEL || method !== Z_DEFLATED ||\n    windowBits < 8 || windowBits > 15 || level < 0 || level > 9 ||\n    strategy < 0 || strategy > Z_FIXED) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n\n  if (windowBits === 8) {\n    windowBits = 9;\n  }\n  /* until 256-byte window bug fixed */\n\n  var s = new DeflateState();\n\n  strm.state = s;\n  s.strm = strm;\n\n  s.wrap = wrap;\n  s.gzhead = null;\n  s.w_bits = windowBits;\n  s.w_size = 1 << s.w_bits;\n  s.w_mask = s.w_size - 1;\n\n  s.hash_bits = memLevel + 7;\n  s.hash_size = 1 << s.hash_bits;\n  s.hash_mask = s.hash_size - 1;\n  s.hash_shift = ~~((s.hash_bits + MIN_MATCH - 1) / MIN_MATCH);\n\n  s.window = new utils.Buf8(s.w_size * 2);\n  s.head = new utils.Buf16(s.hash_size);\n  s.prev = new utils.Buf16(s.w_size);\n\n  // Don't need mem init magic for JS.\n  //s.high_water = 0;  /* nothing written to s->window yet */\n\n  s.lit_bufsize = 1 << (memLevel + 6); /* 16K elements by default */\n\n  s.pending_buf_size = s.lit_bufsize * 4;\n\n  //overlay = (ushf *) ZALLOC(strm, s->lit_bufsize, sizeof(ush)+2);\n  //s->pending_buf = (uchf *) overlay;\n  s.pending_buf = new utils.Buf8(s.pending_buf_size);\n\n  // It is offset from `s.pending_buf` (size is `s.lit_bufsize * 2`)\n  //s->d_buf = overlay + s->lit_bufsize/sizeof(ush);\n  s.d_buf = 1 * s.lit_bufsize;\n\n  //s->l_buf = s->pending_buf + (1+sizeof(ush))*s->lit_bufsize;\n  s.l_buf = (1 + 2) * s.lit_bufsize;\n\n  s.level = level;\n  s.strategy = strategy;\n  s.method = method;\n\n  return deflateReset(strm);\n}\n\nfunction deflateInit(strm, level) {\n  return deflateInit2(strm, level, Z_DEFLATED, MAX_WBITS, DEF_MEM_LEVEL, Z_DEFAULT_STRATEGY);\n}\n\n\nfunction deflate(strm, flush) {\n  var old_flush, s;\n  var beg, val; // for gzip header write only\n\n  if (!strm || !strm.state ||\n    flush > Z_BLOCK || flush < 0) {\n    return strm ? err(strm, Z_STREAM_ERROR) : Z_STREAM_ERROR;\n  }\n\n  s = strm.state;\n\n  if (!strm.output ||\n      (!strm.input && strm.avail_in !== 0) ||\n      (s.status === FINISH_STATE && flush !== Z_FINISH)) {\n    return err(strm, (strm.avail_out === 0) ? Z_BUF_ERROR : Z_STREAM_ERROR);\n  }\n\n  s.strm = strm; /* just in case */\n  old_flush = s.last_flush;\n  s.last_flush = flush;\n\n  /* Write the header */\n  if (s.status === INIT_STATE) {\n\n    if (s.wrap === 2) { // GZIP header\n      strm.adler = 0;  //crc32(0L, Z_NULL, 0);\n      put_byte(s, 31);\n      put_byte(s, 139);\n      put_byte(s, 8);\n      if (!s.gzhead) { // s->gzhead == Z_NULL\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, OS_CODE);\n        s.status = BUSY_STATE;\n      }\n      else {\n        put_byte(s, (s.gzhead.text ? 1 : 0) +\n                    (s.gzhead.hcrc ? 2 : 0) +\n                    (!s.gzhead.extra ? 0 : 4) +\n                    (!s.gzhead.name ? 0 : 8) +\n                    (!s.gzhead.comment ? 0 : 16)\n        );\n        put_byte(s, s.gzhead.time & 0xff);\n        put_byte(s, (s.gzhead.time >> 8) & 0xff);\n        put_byte(s, (s.gzhead.time >> 16) & 0xff);\n        put_byte(s, (s.gzhead.time >> 24) & 0xff);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, s.gzhead.os & 0xff);\n        if (s.gzhead.extra && s.gzhead.extra.length) {\n          put_byte(s, s.gzhead.extra.length & 0xff);\n          put_byte(s, (s.gzhead.extra.length >> 8) & 0xff);\n        }\n        if (s.gzhead.hcrc) {\n          strm.adler = crc32(strm.adler, s.pending_buf, s.pending, 0);\n        }\n        s.gzindex = 0;\n        s.status = EXTRA_STATE;\n      }\n    }\n    else // DEFLATE header\n    {\n      var header = (Z_DEFLATED + ((s.w_bits - 8) << 4)) << 8;\n      var level_flags = -1;\n\n      if (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2) {\n        level_flags = 0;\n      } else if (s.level < 6) {\n        level_flags = 1;\n      } else if (s.level === 6) {\n        level_flags = 2;\n      } else {\n        level_flags = 3;\n      }\n      header |= (level_flags << 6);\n      if (s.strstart !== 0) { header |= PRESET_DICT; }\n      header += 31 - (header % 31);\n\n      s.status = BUSY_STATE;\n      putShortMSB(s, header);\n\n      /* Save the adler32 of the preset dictionary: */\n      if (s.strstart !== 0) {\n        putShortMSB(s, strm.adler >>> 16);\n        putShortMSB(s, strm.adler & 0xffff);\n      }\n      strm.adler = 1; // adler32(0L, Z_NULL, 0);\n    }\n  }\n\n//#ifdef GZIP\n  if (s.status === EXTRA_STATE) {\n    if (s.gzhead.extra/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n\n      while (s.gzindex < (s.gzhead.extra.length & 0xffff)) {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            break;\n          }\n        }\n        put_byte(s, s.gzhead.extra[s.gzindex] & 0xff);\n        s.gzindex++;\n      }\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (s.gzindex === s.gzhead.extra.length) {\n        s.gzindex = 0;\n        s.status = NAME_STATE;\n      }\n    }\n    else {\n      s.status = NAME_STATE;\n    }\n  }\n  if (s.status === NAME_STATE) {\n    if (s.gzhead.name/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.name.length) {\n          val = s.gzhead.name.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.gzindex = 0;\n        s.status = COMMENT_STATE;\n      }\n    }\n    else {\n      s.status = COMMENT_STATE;\n    }\n  }\n  if (s.status === COMMENT_STATE) {\n    if (s.gzhead.comment/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.comment.length) {\n          val = s.gzhead.comment.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.status = HCRC_STATE;\n      }\n    }\n    else {\n      s.status = HCRC_STATE;\n    }\n  }\n  if (s.status === HCRC_STATE) {\n    if (s.gzhead.hcrc) {\n      if (s.pending + 2 > s.pending_buf_size) {\n        flush_pending(strm);\n      }\n      if (s.pending + 2 <= s.pending_buf_size) {\n        put_byte(s, strm.adler & 0xff);\n        put_byte(s, (strm.adler >> 8) & 0xff);\n        strm.adler = 0; //crc32(0L, Z_NULL, 0);\n        s.status = BUSY_STATE;\n      }\n    }\n    else {\n      s.status = BUSY_STATE;\n    }\n  }\n//#endif\n\n  /* Flush as much pending output as possible */\n  if (s.pending !== 0) {\n    flush_pending(strm);\n    if (strm.avail_out === 0) {\n      /* Since avail_out is 0, deflate will be called again with\n       * more output space, but possibly with both pending and\n       * avail_in equal to zero. There won't be anything to do,\n       * but this is not an error situation so make sure we\n       * return OK instead of BUF_ERROR at next call of deflate:\n       */\n      s.last_flush = -1;\n      return Z_OK;\n    }\n\n    /* Make sure there is something to do and avoid duplicate consecutive\n     * flushes. For repeated and useless calls with Z_FINISH, we keep\n     * returning Z_STREAM_END instead of Z_BUF_ERROR.\n     */\n  } else if (strm.avail_in === 0 && rank(flush) <= rank(old_flush) &&\n    flush !== Z_FINISH) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* User must not provide more input after the first FINISH: */\n  if (s.status === FINISH_STATE && strm.avail_in !== 0) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* Start a new block or continue the current one.\n   */\n  if (strm.avail_in !== 0 || s.lookahead !== 0 ||\n    (flush !== Z_NO_FLUSH && s.status !== FINISH_STATE)) {\n    var bstate = (s.strategy === Z_HUFFMAN_ONLY) ? deflate_huff(s, flush) :\n      (s.strategy === Z_RLE ? deflate_rle(s, flush) :\n        configuration_table[s.level].func(s, flush));\n\n    if (bstate === BS_FINISH_STARTED || bstate === BS_FINISH_DONE) {\n      s.status = FINISH_STATE;\n    }\n    if (bstate === BS_NEED_MORE || bstate === BS_FINISH_STARTED) {\n      if (strm.avail_out === 0) {\n        s.last_flush = -1;\n        /* avoid BUF_ERROR next call, see above */\n      }\n      return Z_OK;\n      /* If flush != Z_NO_FLUSH && avail_out == 0, the next call\n       * of deflate should use the same flush parameter to make sure\n       * that the flush is complete. So we don't have to output an\n       * empty block here, this will be done at next call. This also\n       * ensures that for a very small output buffer, we emit at most\n       * one empty block.\n       */\n    }\n    if (bstate === BS_BLOCK_DONE) {\n      if (flush === Z_PARTIAL_FLUSH) {\n        trees._tr_align(s);\n      }\n      else if (flush !== Z_BLOCK) { /* FULL_FLUSH or SYNC_FLUSH */\n\n        trees._tr_stored_block(s, 0, 0, false);\n        /* For a full flush, this empty block will be recognized\n         * as a special marker by inflate_sync().\n         */\n        if (flush === Z_FULL_FLUSH) {\n          /*** CLEAR_HASH(s); ***/             /* forget history */\n          zero(s.head); // Fill with NIL (= 0);\n\n          if (s.lookahead === 0) {\n            s.strstart = 0;\n            s.block_start = 0;\n            s.insert = 0;\n          }\n        }\n      }\n      flush_pending(strm);\n      if (strm.avail_out === 0) {\n        s.last_flush = -1; /* avoid BUF_ERROR at next call, see above */\n        return Z_OK;\n      }\n    }\n  }\n  //Assert(strm->avail_out > 0, \"bug2\");\n  //if (strm.avail_out <= 0) { throw new Error(\"bug2\");}\n\n  if (flush !== Z_FINISH) { return Z_OK; }\n  if (s.wrap <= 0) { return Z_STREAM_END; }\n\n  /* Write the trailer */\n  if (s.wrap === 2) {\n    put_byte(s, strm.adler & 0xff);\n    put_byte(s, (strm.adler >> 8) & 0xff);\n    put_byte(s, (strm.adler >> 16) & 0xff);\n    put_byte(s, (strm.adler >> 24) & 0xff);\n    put_byte(s, strm.total_in & 0xff);\n    put_byte(s, (strm.total_in >> 8) & 0xff);\n    put_byte(s, (strm.total_in >> 16) & 0xff);\n    put_byte(s, (strm.total_in >> 24) & 0xff);\n  }\n  else\n  {\n    putShortMSB(s, strm.adler >>> 16);\n    putShortMSB(s, strm.adler & 0xffff);\n  }\n\n  flush_pending(strm);\n  /* If avail_out is zero, the application will call deflate again\n   * to flush the rest.\n   */\n  if (s.wrap > 0) { s.wrap = -s.wrap; }\n  /* write the trailer only once! */\n  return s.pending !== 0 ? Z_OK : Z_STREAM_END;\n}\n\nfunction deflateEnd(strm) {\n  var status;\n\n  if (!strm/*== Z_NULL*/ || !strm.state/*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  status = strm.state.status;\n  if (status !== INIT_STATE &&\n    status !== EXTRA_STATE &&\n    status !== NAME_STATE &&\n    status !== COMMENT_STATE &&\n    status !== HCRC_STATE &&\n    status !== BUSY_STATE &&\n    status !== FINISH_STATE\n  ) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.state = null;\n\n  return status === BUSY_STATE ? err(strm, Z_DATA_ERROR) : Z_OK;\n}\n\n\n/* =========================================================================\n * Initializes the compression dictionary from the given byte\n * sequence without producing any compressed output.\n */\nfunction deflateSetDictionary(strm, dictionary) {\n  var dictLength = dictionary.length;\n\n  var s;\n  var str, n;\n  var wrap;\n  var avail;\n  var next;\n  var input;\n  var tmpDict;\n\n  if (!strm/*== Z_NULL*/ || !strm.state/*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  s = strm.state;\n  wrap = s.wrap;\n\n  if (wrap === 2 || (wrap === 1 && s.status !== INIT_STATE) || s.lookahead) {\n    return Z_STREAM_ERROR;\n  }\n\n  /* when using zlib wrappers, compute Adler-32 for provided dictionary */\n  if (wrap === 1) {\n    /* adler32(strm->adler, dictionary, dictLength); */\n    strm.adler = adler32(strm.adler, dictionary, dictLength, 0);\n  }\n\n  s.wrap = 0;   /* avoid computing Adler-32 in read_buf */\n\n  /* if dictionary would fill window, just replace the history */\n  if (dictLength >= s.w_size) {\n    if (wrap === 0) {            /* already empty otherwise */\n      /*** CLEAR_HASH(s); ***/\n      zero(s.head); // Fill with NIL (= 0);\n      s.strstart = 0;\n      s.block_start = 0;\n      s.insert = 0;\n    }\n    /* use the tail */\n    // dictionary = dictionary.slice(dictLength - s.w_size);\n    tmpDict = new utils.Buf8(s.w_size);\n    utils.arraySet(tmpDict, dictionary, dictLength - s.w_size, s.w_size, 0);\n    dictionary = tmpDict;\n    dictLength = s.w_size;\n  }\n  /* insert dictionary into window and hash */\n  avail = strm.avail_in;\n  next = strm.next_in;\n  input = strm.input;\n  strm.avail_in = dictLength;\n  strm.next_in = 0;\n  strm.input = dictionary;\n  fill_window(s);\n  while (s.lookahead >= MIN_MATCH) {\n    str = s.strstart;\n    n = s.lookahead - (MIN_MATCH - 1);\n    do {\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n\n      s.prev[str & s.w_mask] = s.head[s.ins_h];\n\n      s.head[s.ins_h] = str;\n      str++;\n    } while (--n);\n    s.strstart = str;\n    s.lookahead = MIN_MATCH - 1;\n    fill_window(s);\n  }\n  s.strstart += s.lookahead;\n  s.block_start = s.strstart;\n  s.insert = s.lookahead;\n  s.lookahead = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  strm.next_in = next;\n  strm.input = input;\n  strm.avail_in = avail;\n  s.wrap = wrap;\n  return Z_OK;\n}\n\n\nexports.deflateInit = deflateInit;\nexports.deflateInit2 = deflateInit2;\nexports.deflateReset = deflateReset;\nexports.deflateResetKeep = deflateResetKeep;\nexports.deflateSetHeader = deflateSetHeader;\nexports.deflate = deflate;\nexports.deflateEnd = deflateEnd;\nexports.deflateSetDictionary = deflateSetDictionary;\nexports.deflateInfo = 'pako deflate (from Nodeca project)';\n\n/* Not implemented\nexports.deflateBound = deflateBound;\nexports.deflateCopy = deflateCopy;\nexports.deflateParams = deflateParams;\nexports.deflatePending = deflatePending;\nexports.deflatePrime = deflatePrime;\nexports.deflateTune = deflateTune;\n*/\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,KAAK,GAAKC,OAAO,CAAC,iBAAiB,CAAC;AACxC,IAAIC,KAAK,GAAKD,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIE,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;AAClC,IAAIG,KAAK,GAAKH,OAAO,CAAC,SAAS,CAAC;AAChC,IAAII,GAAG,GAAOJ,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA;;AAGA;AACA,IAAIK,UAAU,GAAQ,CAAC;AACvB,IAAIC,eAAe,GAAG,CAAC;AACvB;AACA,IAAIC,YAAY,GAAM,CAAC;AACvB,IAAIC,QAAQ,GAAU,CAAC;AACvB,IAAIC,OAAO,GAAW,CAAC;AACvB;;AAGA;AACA;AACA;AACA,IAAIC,IAAI,GAAc,CAAC;AACvB,IAAIC,YAAY,GAAM,CAAC;AACvB;AACA;AACA,IAAIC,cAAc,GAAI,CAAC,CAAC;AACxB,IAAIC,YAAY,GAAM,CAAC,CAAC;AACxB;AACA,IAAIC,WAAW,GAAO,CAAC,CAAC;AACxB;;AAGA;AACA;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,CAAC,CAAC;AAG9B,IAAIC,UAAU,GAAc,CAAC;AAC7B,IAAIC,cAAc,GAAU,CAAC;AAC7B,IAAIC,KAAK,GAAmB,CAAC;AAC7B,IAAIC,OAAO,GAAiB,CAAC;AAC7B,IAAIC,kBAAkB,GAAM,CAAC;;AAE7B;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAe,CAAC;;AAG7B;AACA,IAAIC,UAAU,GAAI,CAAC;;AAEnB;;AAGA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA,IAAIC,SAAS,GAAG,EAAE;AAClB;AACA,IAAIC,aAAa,GAAG,CAAC;AAGrB,IAAIC,YAAY,GAAI,EAAE;AACtB;AACA,IAAIC,QAAQ,GAAQ,GAAG;AACvB;AACA,IAAIC,OAAO,GAASD,QAAQ,GAAG,CAAC,GAAGD,YAAY;AAC/C;AACA,IAAIG,OAAO,GAAS,EAAE;AACtB;AACA,IAAIC,QAAQ,GAAQ,EAAE;AACtB;AACA,IAAIC,SAAS,GAAO,CAAC,GAAGH,OAAO,GAAG,CAAC;AACnC;AACA,IAAII,QAAQ,GAAI,EAAE;AAClB;;AAEA,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,SAAS,GAAG,GAAG;AACnB,IAAIC,aAAa,GAAID,SAAS,GAAGD,SAAS,GAAG,CAAE;AAE/C,IAAIG,WAAW,GAAG,IAAI;AAEtB,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,WAAW,GAAG,EAAE;AACpB,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,UAAU,GAAG,GAAG;AACpB,IAAIC,UAAU,GAAG,GAAG;AACpB,IAAIC,YAAY,GAAG,GAAG;AAEtB,IAAIC,YAAY,GAAQ,CAAC,CAAC,CAAC;AAC3B,IAAIC,aAAa,GAAO,CAAC,CAAC,CAAC;AAC3B,IAAIC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAC3B,IAAIC,cAAc,GAAM,CAAC,CAAC,CAAC;;AAE3B,IAAIC,OAAO,GAAG,IAAI,CAAC,CAAC;;AAEpB,SAASC,GAAGA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC5BD,IAAI,CAAC9C,GAAG,GAAGA,GAAG,CAAC+C,SAAS,CAAC;EACzB,OAAOA,SAAS;AAClB;AAEA,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,CAAEA,CAAC,IAAK,CAAC,KAAMA,CAAC,GAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvC;AAEA,SAASC,IAAIA,CAACC,GAAG,EAAE;EAAE,IAAIC,GAAG,GAAGD,GAAG,CAACE,MAAM;EAAE,OAAO,EAAED,GAAG,IAAI,CAAC,EAAE;IAAED,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC;EAAE;AAAE;;AAGhF;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACR,IAAI,EAAE;EAC3B,IAAIS,CAAC,GAAGT,IAAI,CAACU,KAAK;;EAElB;EACA,IAAIJ,GAAG,GAAGG,CAAC,CAACE,OAAO;EACnB,IAAIL,GAAG,GAAGN,IAAI,CAACY,SAAS,EAAE;IACxBN,GAAG,GAAGN,IAAI,CAACY,SAAS;EACtB;EACA,IAAIN,GAAG,KAAK,CAAC,EAAE;IAAE;EAAQ;EAEzBzD,KAAK,CAACgE,QAAQ,CAACb,IAAI,CAACc,MAAM,EAAEL,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACO,WAAW,EAAEV,GAAG,EAAEN,IAAI,CAACiB,QAAQ,CAAC;EAC7EjB,IAAI,CAACiB,QAAQ,IAAIX,GAAG;EACpBG,CAAC,CAACO,WAAW,IAAIV,GAAG;EACpBN,IAAI,CAACkB,SAAS,IAAIZ,GAAG;EACrBN,IAAI,CAACY,SAAS,IAAIN,GAAG;EACrBG,CAAC,CAACE,OAAO,IAAIL,GAAG;EAChB,IAAIG,CAAC,CAACE,OAAO,KAAK,CAAC,EAAE;IACnBF,CAAC,CAACO,WAAW,GAAG,CAAC;EACnB;AACF;AAGA,SAASG,gBAAgBA,CAACV,CAAC,EAAEW,IAAI,EAAE;EACjCrE,KAAK,CAACsE,eAAe,CAACZ,CAAC,EAAGA,CAAC,CAACa,WAAW,IAAI,CAAC,GAAGb,CAAC,CAACa,WAAW,GAAG,CAAC,CAAC,EAAGb,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACa,WAAW,EAAEF,IAAI,CAAC;EACrGX,CAAC,CAACa,WAAW,GAAGb,CAAC,CAACc,QAAQ;EAC1Bf,aAAa,CAACC,CAAC,CAACT,IAAI,CAAC;AACvB;AAGA,SAASwB,QAAQA,CAACf,CAAC,EAAEgB,CAAC,EAAE;EACtBhB,CAAC,CAACM,WAAW,CAACN,CAAC,CAACE,OAAO,EAAE,CAAC,GAAGc,CAAC;AAChC;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACjB,CAAC,EAAEgB,CAAC,EAAE;EAC3B;EACA;EACEhB,CAAC,CAACM,WAAW,CAACN,CAAC,CAACE,OAAO,EAAE,CAAC,GAAIc,CAAC,KAAK,CAAC,GAAI,IAAI;EAC7ChB,CAAC,CAACM,WAAW,CAACN,CAAC,CAACE,OAAO,EAAE,CAAC,GAAGc,CAAC,GAAG,IAAI;AACvC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAAC3B,IAAI,EAAEK,GAAG,EAAEuB,KAAK,EAAEC,IAAI,EAAE;EACxC,IAAIvB,GAAG,GAAGN,IAAI,CAAC8B,QAAQ;EAEvB,IAAIxB,GAAG,GAAGuB,IAAI,EAAE;IAAEvB,GAAG,GAAGuB,IAAI;EAAE;EAC9B,IAAIvB,GAAG,KAAK,CAAC,EAAE;IAAE,OAAO,CAAC;EAAE;EAE3BN,IAAI,CAAC8B,QAAQ,IAAIxB,GAAG;;EAEpB;EACAzD,KAAK,CAACgE,QAAQ,CAACR,GAAG,EAAEL,IAAI,CAAC+B,KAAK,EAAE/B,IAAI,CAACgC,OAAO,EAAE1B,GAAG,EAAEsB,KAAK,CAAC;EACzD,IAAI5B,IAAI,CAACU,KAAK,CAACuB,IAAI,KAAK,CAAC,EAAE;IACzBjC,IAAI,CAACkC,KAAK,GAAGlF,OAAO,CAACgD,IAAI,CAACkC,KAAK,EAAE7B,GAAG,EAAEC,GAAG,EAAEsB,KAAK,CAAC;EACnD,CAAC,MAEI,IAAI5B,IAAI,CAACU,KAAK,CAACuB,IAAI,KAAK,CAAC,EAAE;IAC9BjC,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAE7B,GAAG,EAAEC,GAAG,EAAEsB,KAAK,CAAC;EACjD;EAEA5B,IAAI,CAACgC,OAAO,IAAI1B,GAAG;EACnBN,IAAI,CAACmC,QAAQ,IAAI7B,GAAG;EAEpB,OAAOA,GAAG;AACZ;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8B,aAAaA,CAAC3B,CAAC,EAAE4B,SAAS,EAAE;EACnC,IAAIC,YAAY,GAAG7B,CAAC,CAAC8B,gBAAgB,CAAC,CAAM;EAC5C,IAAIC,IAAI,GAAG/B,CAAC,CAACc,QAAQ,CAAC,CAAC;EACvB,IAAIkB,KAAK,CAAC,CAAuB;EACjC,IAAInC,GAAG,CAAC,CAA2B;EACnC,IAAIoC,QAAQ,GAAGjC,CAAC,CAACkC,WAAW,CAAC,CAAc;EAC3C,IAAIC,UAAU,GAAGnC,CAAC,CAACmC,UAAU,CAAC,CAAa;EAC3C,IAAIC,KAAK,GAAIpC,CAAC,CAACc,QAAQ,GAAId,CAAC,CAACqC,MAAM,GAAG7D,aAAc,GAChDwB,CAAC,CAACc,QAAQ,IAAId,CAAC,CAACqC,MAAM,GAAG7D,aAAa,CAAC,GAAG,CAAC;EAE/C,IAAI8D,IAAI,GAAGtC,CAAC,CAACuC,MAAM,CAAC,CAAC;;EAErB,IAAIC,KAAK,GAAGxC,CAAC,CAACyC,MAAM;EACpB,IAAIC,IAAI,GAAI1C,CAAC,CAAC0C,IAAI;;EAElB;AACF;AACA;;EAEE,IAAIC,MAAM,GAAG3C,CAAC,CAACc,QAAQ,GAAGvC,SAAS;EACnC,IAAIqE,SAAS,GAAIN,IAAI,CAACP,IAAI,GAAGE,QAAQ,GAAG,CAAC,CAAC;EAC1C,IAAIY,QAAQ,GAAKP,IAAI,CAACP,IAAI,GAAGE,QAAQ,CAAC;;EAEtC;AACF;AACA;EACE;;EAEA;EACA,IAAIjC,CAAC,CAACkC,WAAW,IAAIlC,CAAC,CAAC8C,UAAU,EAAE;IACjCjB,YAAY,KAAK,CAAC;EACpB;EACA;AACF;AACA;EACE,IAAIM,UAAU,GAAGnC,CAAC,CAAC+C,SAAS,EAAE;IAAEZ,UAAU,GAAGnC,CAAC,CAAC+C,SAAS;EAAE;;EAE1D;;EAEA,GAAG;IACD;IACAf,KAAK,GAAGJ,SAAS;;IAEjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI,IAAIU,IAAI,CAACN,KAAK,GAAGC,QAAQ,CAAC,KAASY,QAAQ,IACvCP,IAAI,CAACN,KAAK,GAAGC,QAAQ,GAAG,CAAC,CAAC,KAAKW,SAAS,IACxCN,IAAI,CAACN,KAAK,CAAC,KAAoBM,IAAI,CAACP,IAAI,CAAC,IACzCO,IAAI,CAAC,EAAEN,KAAK,CAAC,KAAkBM,IAAI,CAACP,IAAI,GAAG,CAAC,CAAC,EAAE;MACjD;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACIA,IAAI,IAAI,CAAC;IACTC,KAAK,EAAE;IACP;;IAEA;AACJ;AACA;IACI,GAAG;MACD;IAAA,CACD,QAAQM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAAIM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAChEM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAAIM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAChEM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAAIM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAChEM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAAIM,IAAI,CAAC,EAAEP,IAAI,CAAC,KAAKO,IAAI,CAAC,EAAEN,KAAK,CAAC,IAChED,IAAI,GAAGY,MAAM;;IAEtB;;IAEA9C,GAAG,GAAGtB,SAAS,IAAIoE,MAAM,GAAGZ,IAAI,CAAC;IACjCA,IAAI,GAAGY,MAAM,GAAGpE,SAAS;IAEzB,IAAIsB,GAAG,GAAGoC,QAAQ,EAAE;MAClBjC,CAAC,CAACgD,WAAW,GAAGpB,SAAS;MACzBK,QAAQ,GAAGpC,GAAG;MACd,IAAIA,GAAG,IAAIsC,UAAU,EAAE;QACrB;MACF;MACAS,SAAS,GAAIN,IAAI,CAACP,IAAI,GAAGE,QAAQ,GAAG,CAAC,CAAC;MACtCY,QAAQ,GAAKP,IAAI,CAACP,IAAI,GAAGE,QAAQ,CAAC;IACpC;EACF,CAAC,QAAQ,CAACL,SAAS,GAAGc,IAAI,CAACd,SAAS,GAAGY,KAAK,CAAC,IAAIJ,KAAK,IAAI,EAAEP,YAAY,KAAK,CAAC;EAE9E,IAAII,QAAQ,IAAIjC,CAAC,CAAC+C,SAAS,EAAE;IAC3B,OAAOd,QAAQ;EACjB;EACA,OAAOjC,CAAC,CAAC+C,SAAS;AACpB;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACjD,CAAC,EAAE;EACtB,IAAIkD,OAAO,GAAGlD,CAAC,CAACqC,MAAM;EACtB,IAAIc,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,GAAG;;EAEtB;;EAEA,GAAG;IACDD,IAAI,GAAGtD,CAAC,CAACwD,WAAW,GAAGxD,CAAC,CAAC+C,SAAS,GAAG/C,CAAC,CAACc,QAAQ;;IAE/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAGA;AACJ;AACA;IACI,IAAId,CAAC,CAACc,QAAQ,IAAIoC,OAAO,IAAIA,OAAO,GAAG1E,aAAa,CAAC,EAAE;MAErDpC,KAAK,CAACgE,QAAQ,CAACJ,CAAC,CAACuC,MAAM,EAAEvC,CAAC,CAACuC,MAAM,EAAEW,OAAO,EAAEA,OAAO,EAAE,CAAC,CAAC;MACvDlD,CAAC,CAACgD,WAAW,IAAIE,OAAO;MACxBlD,CAAC,CAACc,QAAQ,IAAIoC,OAAO;MACrB;MACAlD,CAAC,CAACa,WAAW,IAAIqC,OAAO;;MAExB;AACN;AACA;AACA;AACA;AACA;;MAEME,CAAC,GAAGpD,CAAC,CAACyD,SAAS;MACfN,CAAC,GAAGC,CAAC;MACL,GAAG;QACDC,CAAC,GAAGrD,CAAC,CAAC0D,IAAI,CAAC,EAAEP,CAAC,CAAC;QACfnD,CAAC,CAAC0D,IAAI,CAACP,CAAC,CAAC,GAAIE,CAAC,IAAIH,OAAO,GAAGG,CAAC,GAAGH,OAAO,GAAG,CAAE;MAC9C,CAAC,QAAQ,EAAEE,CAAC;MAEZA,CAAC,GAAGF,OAAO;MACXC,CAAC,GAAGC,CAAC;MACL,GAAG;QACDC,CAAC,GAAGrD,CAAC,CAAC0C,IAAI,CAAC,EAAES,CAAC,CAAC;QACfnD,CAAC,CAAC0C,IAAI,CAACS,CAAC,CAAC,GAAIE,CAAC,IAAIH,OAAO,GAAGG,CAAC,GAAGH,OAAO,GAAG,CAAE;QAC5C;AACR;AACA;MACM,CAAC,QAAQ,EAAEE,CAAC;MAEZE,IAAI,IAAIJ,OAAO;IACjB;IACA,IAAIlD,CAAC,CAACT,IAAI,CAAC8B,QAAQ,KAAK,CAAC,EAAE;MACzB;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI;IACA+B,CAAC,GAAGlC,QAAQ,CAAClB,CAAC,CAACT,IAAI,EAAES,CAAC,CAACuC,MAAM,EAAEvC,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAAC+C,SAAS,EAAEO,IAAI,CAAC;IAC9DtD,CAAC,CAAC+C,SAAS,IAAIK,CAAC;;IAEhB;IACA,IAAIpD,CAAC,CAAC+C,SAAS,GAAG/C,CAAC,CAAC2D,MAAM,IAAIrF,SAAS,EAAE;MACvCiF,GAAG,GAAGvD,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAAC2D,MAAM;MAC3B3D,CAAC,CAAC4D,KAAK,GAAG5D,CAAC,CAACuC,MAAM,CAACgB,GAAG,CAAC;;MAEvB;MACAvD,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACgB,GAAG,GAAG,CAAC,CAAC,IAAIvD,CAAC,CAAC8D,SAAS;MAC7E;MACA;MACA;MACM,OAAO9D,CAAC,CAAC2D,MAAM,EAAE;QACf;QACA3D,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACgB,GAAG,GAAGjF,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;QAEnF9D,CAAC,CAAC0C,IAAI,CAACa,GAAG,GAAGvD,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;QACxC5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAGL,GAAG;QACrBA,GAAG,EAAE;QACLvD,CAAC,CAAC2D,MAAM,EAAE;QACV,IAAI3D,CAAC,CAAC+C,SAAS,GAAG/C,CAAC,CAAC2D,MAAM,GAAGrF,SAAS,EAAE;UACtC;QACF;MACF;IACF;IACA;AACJ;AACA;EAEE,CAAC,QAAQ0B,CAAC,CAAC+C,SAAS,GAAGvE,aAAa,IAAIwB,CAAC,CAACT,IAAI,CAAC8B,QAAQ,KAAK,CAAC;;EAE7D;AACF;AACA;AACA;AACA;AACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,cAAcA,CAAC/D,CAAC,EAAEgE,KAAK,EAAE;EAChC;AACF;AACA;EACE,IAAIC,cAAc,GAAG,MAAM;EAE3B,IAAIA,cAAc,GAAGjE,CAAC,CAACkE,gBAAgB,GAAG,CAAC,EAAE;IAC3CD,cAAc,GAAGjE,CAAC,CAACkE,gBAAgB,GAAG,CAAC;EACzC;;EAEA;EACA,SAAS;IACP;IACA,IAAIlE,CAAC,CAAC+C,SAAS,IAAI,CAAC,EAAE;MAEpB;MACA;MACN;MACA;MACA;MACA;;MAEME,WAAW,CAACjD,CAAC,CAAC;MACd,IAAIA,CAAC,CAAC+C,SAAS,KAAK,CAAC,IAAIiB,KAAK,KAAKtH,UAAU,EAAE;QAC7C,OAAOuC,YAAY;MACrB;MAEA,IAAIe,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;QACrB;MACF;MACA;IACF;IACA;IACJ;;IAEI/C,CAAC,CAACc,QAAQ,IAAId,CAAC,CAAC+C,SAAS;IACzB/C,CAAC,CAAC+C,SAAS,GAAG,CAAC;;IAEf;IACA,IAAIoB,SAAS,GAAGnE,CAAC,CAACa,WAAW,GAAGoD,cAAc;IAE9C,IAAIjE,CAAC,CAACc,QAAQ,KAAK,CAAC,IAAId,CAAC,CAACc,QAAQ,IAAIqD,SAAS,EAAE;MAC/C;MACAnE,CAAC,CAAC+C,SAAS,GAAG/C,CAAC,CAACc,QAAQ,GAAGqD,SAAS;MACpCnE,CAAC,CAACc,QAAQ,GAAGqD,SAAS;MACtB;MACAzD,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;MAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;MACA;IAGF;IACA;AACJ;AACA;IACI,IAAIe,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACa,WAAW,IAAKb,CAAC,CAACqC,MAAM,GAAG7D,aAAc,EAAE;MAC5D;MACAkC,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;MAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;MACA;IACF;EACF;EAEAe,CAAC,CAAC2D,MAAM,GAAG,CAAC;EAEZ,IAAIK,KAAK,KAAKnH,QAAQ,EAAE;IACtB;IACA6D,gBAAgB,CAACV,CAAC,EAAE,IAAI,CAAC;IACzB,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOhB,iBAAiB;IAC1B;IACA;IACA,OAAOC,cAAc;EACvB;EAEA,IAAIY,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACa,WAAW,EAAE;IAC9B;IACAH,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;IAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOlB,YAAY;IACrB;IACA;EACF;EAEA,OAAOA,YAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmF,YAAYA,CAACpE,CAAC,EAAEgE,KAAK,EAAE;EAC9B,IAAIK,SAAS,CAAC,CAAQ;EACtB,IAAIC,MAAM,CAAC,CAAW;;EAEtB,SAAS;IACP;AACJ;AACA;AACA;AACA;IACI,IAAItE,CAAC,CAAC+C,SAAS,GAAGvE,aAAa,EAAE;MAC/ByE,WAAW,CAACjD,CAAC,CAAC;MACd,IAAIA,CAAC,CAAC+C,SAAS,GAAGvE,aAAa,IAAIwF,KAAK,KAAKtH,UAAU,EAAE;QACvD,OAAOuC,YAAY;MACrB;MACA,IAAIe,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;QACrB,MAAM,CAAC;MACT;IACF;;IAEA;AACJ;AACA;IACIsB,SAAS,GAAG,CAAC;IACb,IAAIrE,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,EAAE;MAC5B;MACA0B,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;MAC1FO,SAAS,GAAGrE,CAAC,CAAC0C,IAAI,CAAC1C,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;MAC3D5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAG5D,CAAC,CAACc,QAAQ;MAC5B;IACF;;IAEA;AACJ;AACA;IACI,IAAIuD,SAAS,KAAK,CAAC,YAAarE,CAAC,CAACc,QAAQ,GAAGuD,SAAS,IAAMrE,CAAC,CAACqC,MAAM,GAAG7D,aAAe,EAAE;MACtF;AACN;AACA;AACA;MACMwB,CAAC,CAACuE,YAAY,GAAG5C,aAAa,CAAC3B,CAAC,EAAEqE,SAAS,CAAC;MAC5C;IACF;IACA,IAAIrE,CAAC,CAACuE,YAAY,IAAIjG,SAAS,EAAE;MAC/B;;MAEA;AACN;MACMgG,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAEA,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACgD,WAAW,EAAEhD,CAAC,CAACuE,YAAY,GAAGjG,SAAS,CAAC;MAEnF0B,CAAC,CAAC+C,SAAS,IAAI/C,CAAC,CAACuE,YAAY;;MAE7B;AACN;AACA;MACM,IAAIvE,CAAC,CAACuE,YAAY,IAAIvE,CAAC,CAACyE,cAAc,0BAAyBzE,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,EAAE;QACvF0B,CAAC,CAACuE,YAAY,EAAE,CAAC,CAAC;QAClB,GAAG;UACDvE,CAAC,CAACc,QAAQ,EAAE;UACZ;UACAd,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;UAC1FO,SAAS,GAAGrE,CAAC,CAAC0C,IAAI,CAAC1C,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;UAC3D5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAG5D,CAAC,CAACc,QAAQ;UAC5B;UACA;AACV;AACA;QACQ,CAAC,QAAQ,EAAEd,CAAC,CAACuE,YAAY,KAAK,CAAC;QAC/BvE,CAAC,CAACc,QAAQ,EAAE;MACd,CAAC,MACD;QACEd,CAAC,CAACc,QAAQ,IAAId,CAAC,CAACuE,YAAY;QAC5BvE,CAAC,CAACuE,YAAY,GAAG,CAAC;QAClBvE,CAAC,CAAC4D,KAAK,GAAG5D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,CAAC;QAC9B;QACAd,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAG,CAAC,CAAC,IAAId,CAAC,CAAC8D,SAAS;;QAEtF;QACA;QACA;QACQ;AACR;AACA;MACM;IACF,CAAC,MAAM;MACL;MACA;MACA;MACAQ,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,CAAC,CAAC;MAEpDd,CAAC,CAAC+C,SAAS,EAAE;MACb/C,CAAC,CAACc,QAAQ,EAAE;IACd;IACA,IAAIwD,MAAM,EAAE;MACV;MACA5D,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;MAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;MACA;IACF;EACF;EACAe,CAAC,CAAC2D,MAAM,GAAK3D,CAAC,CAACc,QAAQ,GAAIxC,SAAS,GAAG,CAAE,GAAI0B,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAE;EACxE,IAAI0F,KAAK,KAAKnH,QAAQ,EAAE;IACtB;IACA6D,gBAAgB,CAACV,CAAC,EAAE,IAAI,CAAC;IACzB,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOhB,iBAAiB;IAC1B;IACA;IACA,OAAOC,cAAc;EACvB;EACA,IAAIY,CAAC,CAAC0E,QAAQ,EAAE;IACd;IACAhE,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;IAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOlB,YAAY;IACrB;IACA;EACF;EACA,OAAOC,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyF,YAAYA,CAAC3E,CAAC,EAAEgE,KAAK,EAAE;EAC9B,IAAIK,SAAS,CAAC,CAAU;EACxB,IAAIC,MAAM,CAAC,CAAc;;EAEzB,IAAIM,UAAU;;EAEd;EACA,SAAS;IACP;AACJ;AACA;AACA;AACA;IACI,IAAI5E,CAAC,CAAC+C,SAAS,GAAGvE,aAAa,EAAE;MAC/ByE,WAAW,CAACjD,CAAC,CAAC;MACd,IAAIA,CAAC,CAAC+C,SAAS,GAAGvE,aAAa,IAAIwF,KAAK,KAAKtH,UAAU,EAAE;QACvD,OAAOuC,YAAY;MACrB;MACA,IAAIe,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;QAAE;MAAO,CAAC,CAAC;IACpC;;IAEA;AACJ;AACA;IACIsB,SAAS,GAAG,CAAC;IACb,IAAIrE,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,EAAE;MAC5B;MACA0B,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;MAC1FO,SAAS,GAAGrE,CAAC,CAAC0C,IAAI,CAAC1C,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;MAC3D5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAG5D,CAAC,CAACc,QAAQ;MAC5B;IACF;;IAEA;AACJ;IACId,CAAC,CAACkC,WAAW,GAAGlC,CAAC,CAACuE,YAAY;IAC9BvE,CAAC,CAAC6E,UAAU,GAAG7E,CAAC,CAACgD,WAAW;IAC5BhD,CAAC,CAACuE,YAAY,GAAGjG,SAAS,GAAG,CAAC;IAE9B,IAAI+F,SAAS,KAAK,CAAC,YAAWrE,CAAC,CAACkC,WAAW,GAAGlC,CAAC,CAACyE,cAAc,IAC1DzE,CAAC,CAACc,QAAQ,GAAGuD,SAAS,IAAKrE,CAAC,CAACqC,MAAM,GAAG7D,aAAc,kBAAiB;MACvE;AACN;AACA;AACA;MACMwB,CAAC,CAACuE,YAAY,GAAG5C,aAAa,CAAC3B,CAAC,EAAEqE,SAAS,CAAC;MAC5C;;MAEA,IAAIrE,CAAC,CAACuE,YAAY,IAAI,CAAC,KACnBvE,CAAC,CAAC8E,QAAQ,KAAKzH,UAAU,IAAK2C,CAAC,CAACuE,YAAY,KAAKjG,SAAS,IAAI0B,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACgD,WAAW,GAAG,IAAI,YAAY,CAAC,EAAE;QAEhH;AACR;AACA;QACQhD,CAAC,CAACuE,YAAY,GAAGjG,SAAS,GAAG,CAAC;MAChC;IACF;IACA;AACJ;AACA;IACI,IAAI0B,CAAC,CAACkC,WAAW,IAAI5D,SAAS,IAAI0B,CAAC,CAACuE,YAAY,IAAIvE,CAAC,CAACkC,WAAW,EAAE;MACjE0C,UAAU,GAAG5E,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAAC+C,SAAS,GAAGzE,SAAS;MACjD;;MAEA;;MAEA;AACN;MACMgG,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAEA,CAAC,CAACc,QAAQ,GAAG,CAAC,GAAGd,CAAC,CAAC6E,UAAU,EAAE7E,CAAC,CAACkC,WAAW,GAAG5D,SAAS,CAAC;MACrF;AACN;AACA;AACA;AACA;MACM0B,CAAC,CAAC+C,SAAS,IAAI/C,CAAC,CAACkC,WAAW,GAAG,CAAC;MAChClC,CAAC,CAACkC,WAAW,IAAI,CAAC;MAClB,GAAG;QACD,IAAI,EAAElC,CAAC,CAACc,QAAQ,IAAI8D,UAAU,EAAE;UAC9B;UACA5E,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;UAC1FO,SAAS,GAAGrE,CAAC,CAAC0C,IAAI,CAAC1C,CAAC,CAACc,QAAQ,GAAGd,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;UAC3D5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAG5D,CAAC,CAACc,QAAQ;UAC5B;QACF;MACF,CAAC,QAAQ,EAAEd,CAAC,CAACkC,WAAW,KAAK,CAAC;MAC9BlC,CAAC,CAAC+E,eAAe,GAAG,CAAC;MACrB/E,CAAC,CAACuE,YAAY,GAAGjG,SAAS,GAAG,CAAC;MAC9B0B,CAAC,CAACc,QAAQ,EAAE;MAEZ,IAAIwD,MAAM,EAAE;QACV;QACA5D,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;QAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;UAC1B,OAAOlB,YAAY;QACrB;QACA;MACF;IAEF,CAAC,MAAM,IAAIe,CAAC,CAAC+E,eAAe,EAAE;MAC5B;AACN;AACA;AACA;MACM;MACA;MACAT,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAG,CAAC,CAAC,CAAC;MAExD,IAAIwD,MAAM,EAAE;QACV;QACA5D,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;QAC1B;MACF;MACAA,CAAC,CAACc,QAAQ,EAAE;MACZd,CAAC,CAAC+C,SAAS,EAAE;MACb,IAAI/C,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;IACF,CAAC,MAAM;MACL;AACN;AACA;MACMe,CAAC,CAAC+E,eAAe,GAAG,CAAC;MACrB/E,CAAC,CAACc,QAAQ,EAAE;MACZd,CAAC,CAAC+C,SAAS,EAAE;IACf;EACF;EACA;EACA,IAAI/C,CAAC,CAAC+E,eAAe,EAAE;IACrB;IACA;IACAT,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,GAAG,CAAC,CAAC,CAAC;IAExDd,CAAC,CAAC+E,eAAe,GAAG,CAAC;EACvB;EACA/E,CAAC,CAAC2D,MAAM,GAAG3D,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC,GAAG0B,CAAC,CAACc,QAAQ,GAAGxC,SAAS,GAAG,CAAC;EAClE,IAAI0F,KAAK,KAAKnH,QAAQ,EAAE;IACtB;IACA6D,gBAAgB,CAACV,CAAC,EAAE,IAAI,CAAC;IACzB,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOhB,iBAAiB;IAC1B;IACA;IACA,OAAOC,cAAc;EACvB;EACA,IAAIY,CAAC,CAAC0E,QAAQ,EAAE;IACd;IACAhE,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;IAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOlB,YAAY;IACrB;IACA;EACF;EAEA,OAAOC,aAAa;AACtB;;AAGA;AACA;AACA;AACA;AACA;AACA,SAAS8F,WAAWA,CAAChF,CAAC,EAAEgE,KAAK,EAAE;EAC7B,IAAIM,MAAM,CAAC,CAAY;EACvB,IAAI5B,IAAI,CAAC,CAAc;EACvB,IAAIX,IAAI,EAAEY,MAAM,CAAC,CAAM;;EAEvB,IAAIL,IAAI,GAAGtC,CAAC,CAACuC,MAAM;EAEnB,SAAS;IACP;AACJ;AACA;AACA;IACI,IAAIvC,CAAC,CAAC+C,SAAS,IAAIxE,SAAS,EAAE;MAC5B0E,WAAW,CAACjD,CAAC,CAAC;MACd,IAAIA,CAAC,CAAC+C,SAAS,IAAIxE,SAAS,IAAIyF,KAAK,KAAKtH,UAAU,EAAE;QACpD,OAAOuC,YAAY;MACrB;MACA,IAAIe,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;QAAE;MAAO,CAAC,CAAC;IACpC;;IAEA;IACA/C,CAAC,CAACuE,YAAY,GAAG,CAAC;IAClB,IAAIvE,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,IAAI0B,CAAC,CAACc,QAAQ,GAAG,CAAC,EAAE;MAC9CiB,IAAI,GAAG/B,CAAC,CAACc,QAAQ,GAAG,CAAC;MACrB4B,IAAI,GAAGJ,IAAI,CAACP,IAAI,CAAC;MACjB,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,EAAE;QAC3EY,MAAM,GAAG3C,CAAC,CAACc,QAAQ,GAAGvC,SAAS;QAC/B,GAAG;UACD;QAAA,CACD,QAAQmE,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAC9CW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAC9CW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAC9CW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAAIW,IAAI,KAAKJ,IAAI,CAAC,EAAEP,IAAI,CAAC,IAC9CA,IAAI,GAAGY,MAAM;QACtB3C,CAAC,CAACuE,YAAY,GAAGhG,SAAS,IAAIoE,MAAM,GAAGZ,IAAI,CAAC;QAC5C,IAAI/B,CAAC,CAACuE,YAAY,GAAGvE,CAAC,CAAC+C,SAAS,EAAE;UAChC/C,CAAC,CAACuE,YAAY,GAAGvE,CAAC,CAAC+C,SAAS;QAC9B;MACF;MACA;IACF;;IAEA;IACA,IAAI/C,CAAC,CAACuE,YAAY,IAAIjG,SAAS,EAAE;MAC/B;;MAEA;MACAgG,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuE,YAAY,GAAGjG,SAAS,CAAC;MAE1D0B,CAAC,CAAC+C,SAAS,IAAI/C,CAAC,CAACuE,YAAY;MAC7BvE,CAAC,CAACc,QAAQ,IAAId,CAAC,CAACuE,YAAY;MAC5BvE,CAAC,CAACuE,YAAY,GAAG,CAAC;IACpB,CAAC,MAAM;MACL;MACA;MACA;MACAD,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,CAAC,CAAC;MAEpDd,CAAC,CAAC+C,SAAS,EAAE;MACb/C,CAAC,CAACc,QAAQ,EAAE;IACd;IACA,IAAIwD,MAAM,EAAE;MACV;MACA5D,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;MAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;MACA;IACF;EACF;EACAe,CAAC,CAAC2D,MAAM,GAAG,CAAC;EACZ,IAAIK,KAAK,KAAKnH,QAAQ,EAAE;IACtB;IACA6D,gBAAgB,CAACV,CAAC,EAAE,IAAI,CAAC;IACzB,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOhB,iBAAiB;IAC1B;IACA;IACA,OAAOC,cAAc;EACvB;EACA,IAAIY,CAAC,CAAC0E,QAAQ,EAAE;IACd;IACAhE,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;IAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOlB,YAAY;IACrB;IACA;EACF;EACA,OAAOC,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAAS+F,YAAYA,CAACjF,CAAC,EAAEgE,KAAK,EAAE;EAC9B,IAAIM,MAAM,CAAC,CAAa;;EAExB,SAAS;IACP;IACA,IAAItE,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;MACrBE,WAAW,CAACjD,CAAC,CAAC;MACd,IAAIA,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;QACrB,IAAIiB,KAAK,KAAKtH,UAAU,EAAE;UACxB,OAAOuC,YAAY;QACrB;QACA,MAAM,CAAM;MACd;IACF;;IAEA;IACAe,CAAC,CAACuE,YAAY,GAAG,CAAC;IAClB;IACA;IACAD,MAAM,GAAGhI,KAAK,CAACkI,SAAS,CAACxE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACuC,MAAM,CAACvC,CAAC,CAACc,QAAQ,CAAC,CAAC;IACpDd,CAAC,CAAC+C,SAAS,EAAE;IACb/C,CAAC,CAACc,QAAQ,EAAE;IACZ,IAAIwD,MAAM,EAAE;MACV;MACA5D,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;MAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QAC1B,OAAOlB,YAAY;MACrB;MACA;IACF;EACF;EACAe,CAAC,CAAC2D,MAAM,GAAG,CAAC;EACZ,IAAIK,KAAK,KAAKnH,QAAQ,EAAE;IACtB;IACA6D,gBAAgB,CAACV,CAAC,EAAE,IAAI,CAAC;IACzB,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOhB,iBAAiB;IAC1B;IACA;IACA,OAAOC,cAAc;EACvB;EACA,IAAIY,CAAC,CAAC0E,QAAQ,EAAE;IACd;IACAhE,gBAAgB,CAACV,CAAC,EAAE,KAAK,CAAC;IAC1B,IAAIA,CAAC,CAACT,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MAC1B,OAAOlB,YAAY;IACrB;IACA;EACF;EACA,OAAOC,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASgG,MAAMA,CAACC,WAAW,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAE;EACnE,IAAI,CAACJ,WAAW,GAAGA,WAAW;EAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;AAClB;AAEA,IAAIC,mBAAmB;AAEvBA,mBAAmB,GAAG,CACpB;AACA,IAAIN,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEnB,cAAc,CAAC,EAAW;AACjD,IAAImB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEd,YAAY,CAAC,EAAa;AACjD,IAAIc,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAEd,YAAY,CAAC,EAAY;AACjD,IAAIc,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAEd,YAAY,CAAC,EAAW;;AAEjD,IAAIc,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAEP,YAAY,CAAC,EAAW;AACjD,IAAIO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEP,YAAY,CAAC,EAAU;AACjD,IAAIO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAEP,YAAY,CAAC,EAAQ;AACjD,IAAIO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAEP,YAAY,CAAC,EAAQ;AACjD,IAAIO,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEP,YAAY,CAAC,EAAK;AACjD,IAAIO,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEP,YAAY,CAAC,CAAK,wBAClD;;AAGD;AACA;AACA;AACA,SAASc,OAAOA,CAACzF,CAAC,EAAE;EAClBA,CAAC,CAACwD,WAAW,GAAG,CAAC,GAAGxD,CAAC,CAACqC,MAAM;;EAE5B;EACA1C,IAAI,CAACK,CAAC,CAAC0D,IAAI,CAAC,CAAC,CAAC;;EAEd;AACF;EACE1D,CAAC,CAACyE,cAAc,GAAGe,mBAAmB,CAACxF,CAAC,CAAC0F,KAAK,CAAC,CAACN,QAAQ;EACxDpF,CAAC,CAAC8C,UAAU,GAAG0C,mBAAmB,CAACxF,CAAC,CAAC0F,KAAK,CAAC,CAACP,WAAW;EACvDnF,CAAC,CAACmC,UAAU,GAAGqD,mBAAmB,CAACxF,CAAC,CAAC0F,KAAK,CAAC,CAACL,WAAW;EACvDrF,CAAC,CAAC8B,gBAAgB,GAAG0D,mBAAmB,CAACxF,CAAC,CAAC0F,KAAK,CAAC,CAACJ,SAAS;EAE3DtF,CAAC,CAACc,QAAQ,GAAG,CAAC;EACdd,CAAC,CAACa,WAAW,GAAG,CAAC;EACjBb,CAAC,CAAC+C,SAAS,GAAG,CAAC;EACf/C,CAAC,CAAC2D,MAAM,GAAG,CAAC;EACZ3D,CAAC,CAACuE,YAAY,GAAGvE,CAAC,CAACkC,WAAW,GAAG5D,SAAS,GAAG,CAAC;EAC9C0B,CAAC,CAAC+E,eAAe,GAAG,CAAC;EACrB/E,CAAC,CAAC4D,KAAK,GAAG,CAAC;AACb;AAGA,SAAS+B,YAAYA,CAAA,EAAG;EACtB,IAAI,CAACpG,IAAI,GAAG,IAAI,CAAC,CAAY;EAC7B,IAAI,CAACqG,MAAM,GAAG,CAAC,CAAC,CAAY;EAC5B,IAAI,CAACtF,WAAW,GAAG,IAAI,CAAC,CAAM;EAC9B,IAAI,CAAC4D,gBAAgB,GAAG,CAAC,CAAC,CAAE;EAC5B,IAAI,CAAC3D,WAAW,GAAG,CAAC,CAAC,CAAO;EAC5B,IAAI,CAACL,OAAO,GAAG,CAAC,CAAC,CAAW;EAC5B,IAAI,CAACsB,IAAI,GAAG,CAAC,CAAC,CAAc;EAC5B,IAAI,CAACqE,MAAM,GAAG,IAAI,CAAC,CAAS;EAC5B,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAW;EAC5B,IAAI,CAACC,MAAM,GAAGpI,UAAU,CAAC,CAAC;EAC1B,IAAI,CAACqI,UAAU,GAAG,CAAC,CAAC,CAAC,CAAG;;EAExB,IAAI,CAAC3D,MAAM,GAAG,CAAC,CAAC,CAAE;EAClB,IAAI,CAAC4D,MAAM,GAAG,CAAC,CAAC,CAAE;EAClB,IAAI,CAACxD,MAAM,GAAG,CAAC,CAAC,CAAE;;EAElB,IAAI,CAACF,MAAM,GAAG,IAAI;EAClB;AACF;AACA;AACA;AACA;AACA;;EAEE,IAAI,CAACiB,WAAW,GAAG,CAAC;EACpB;AACF;AACA;;EAEE,IAAI,CAACd,IAAI,GAAG,IAAI;EAChB;AACF;AACA;AACA;;EAEE,IAAI,CAACgB,IAAI,GAAG,IAAI,CAAC,CAAG;;EAEpB,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,CAAO;EACtB,IAAI,CAACH,SAAS,GAAG,CAAC,CAAC,CAAG;EACtB,IAAI,CAACyC,SAAS,GAAG,CAAC,CAAC,CAAG;EACtB,IAAI,CAACpC,SAAS,GAAG,CAAC,CAAC,CAAG;;EAEtB,IAAI,CAACD,UAAU,GAAG,CAAC;EACnB;AACF;AACA;AACA;AACA;;EAEE,IAAI,CAAChD,WAAW,GAAG,CAAC;EACpB;AACF;AACA;;EAEE,IAAI,CAAC0D,YAAY,GAAG,CAAC,CAAC,CAAM;EAC5B,IAAI,CAACM,UAAU,GAAG,CAAC,CAAC,CAAQ;EAC5B,IAAI,CAACE,eAAe,GAAG,CAAC,CAAC,CAAG;EAC5B,IAAI,CAACjE,QAAQ,GAAG,CAAC,CAAC,CAAU;EAC5B,IAAI,CAACkC,WAAW,GAAG,CAAC,CAAC,CAAO;EAC5B,IAAI,CAACD,SAAS,GAAG,CAAC,CAAC,CAAS;;EAE5B,IAAI,CAACb,WAAW,GAAG,CAAC;EACpB;AACF;AACA;;EAEE,IAAI,CAACJ,gBAAgB,GAAG,CAAC;EACzB;AACF;AACA;AACA;;EAEE,IAAI,CAAC2C,cAAc,GAAG,CAAC;EACvB;AACF;AACA;AACA;EACE;EACA;EACA;AACF;AACA;AACA;;EAEE,IAAI,CAACiB,KAAK,GAAG,CAAC,CAAC,CAAK;EACpB,IAAI,CAACZ,QAAQ,GAAG,CAAC,CAAC,CAAE;;EAEpB,IAAI,CAAChC,UAAU,GAAG,CAAC;EACnB;;EAEA,IAAI,CAACX,UAAU,GAAG,CAAC,CAAC,CAAC;;EAET;;EAEZ;;EAEA;EACA;EACA;;EAEA;EACA;EACA,IAAI,CAACgE,SAAS,GAAI,IAAI/J,KAAK,CAACgK,KAAK,CAAChI,SAAS,GAAG,CAAC,CAAC;EAChD,IAAI,CAACiI,SAAS,GAAI,IAAIjK,KAAK,CAACgK,KAAK,CAAC,CAAC,CAAC,GAAGlI,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;EACxD,IAAI,CAACoI,OAAO,GAAM,IAAIlK,KAAK,CAACgK,KAAK,CAAC,CAAC,CAAC,GAAGjI,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;EACzDwB,IAAI,CAAC,IAAI,CAACwG,SAAS,CAAC;EACpBxG,IAAI,CAAC,IAAI,CAAC0G,SAAS,CAAC;EACpB1G,IAAI,CAAC,IAAI,CAAC2G,OAAO,CAAC;EAElB,IAAI,CAACC,MAAM,GAAK,IAAI,CAAC,CAAS;EAC9B,IAAI,CAACC,MAAM,GAAK,IAAI,CAAC,CAAS;EAC9B,IAAI,CAACC,OAAO,GAAI,IAAI,CAAC,CAAS;;EAE9B;EACA,IAAI,CAACC,QAAQ,GAAG,IAAItK,KAAK,CAACgK,KAAK,CAAC/H,QAAQ,GAAG,CAAC,CAAC;EAC7C;;EAEA;EACA,IAAI,CAACsI,IAAI,GAAG,IAAIvK,KAAK,CAACgK,KAAK,CAAC,CAAC,GAAGnI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAE;EAC/C0B,IAAI,CAAC,IAAI,CAACgH,IAAI,CAAC;EAEf,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAAe;EACjC,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAAe;EACjC;AACF;AACA;;EAEE,IAAI,CAACC,KAAK,GAAG,IAAI1K,KAAK,CAACgK,KAAK,CAAC,CAAC,GAAGnI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/C0B,IAAI,CAAC,IAAI,CAACmH,KAAK,CAAC;EAChB;AACF;;EAEE,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,CAAU;;EAEzB,IAAI,CAACC,WAAW,GAAG,CAAC;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAI,CAACtC,QAAQ,GAAG,CAAC,CAAC,CAAM;;EAExB,IAAI,CAACuC,KAAK,GAAG,CAAC;EACd;AACF;AACA;AACA;;EAEE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAO;EACxB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAAI;EACxB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAO;EACxB,IAAI,CAACzD,MAAM,GAAG,CAAC,CAAC,CAAQ;;EAGxB,IAAI,CAAC0D,MAAM,GAAG,CAAC;EACf;AACF;AACA;EACE,IAAI,CAACC,QAAQ,GAAG,CAAC;EACjB;AACF;AACA;;EAEE;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AAGA,SAASC,gBAAgBA,CAAChI,IAAI,EAAE;EAC9B,IAAIS,CAAC;EAEL,IAAI,CAACT,IAAI,IAAI,CAACA,IAAI,CAACU,KAAK,EAAE;IACxB,OAAOX,GAAG,CAACC,IAAI,EAAEtC,cAAc,CAAC;EAClC;EAEAsC,IAAI,CAACmC,QAAQ,GAAGnC,IAAI,CAACkB,SAAS,GAAG,CAAC;EAClClB,IAAI,CAACiI,SAAS,GAAG9J,SAAS;EAE1BsC,CAAC,GAAGT,IAAI,CAACU,KAAK;EACdD,CAAC,CAACE,OAAO,GAAG,CAAC;EACbF,CAAC,CAACO,WAAW,GAAG,CAAC;EAEjB,IAAIP,CAAC,CAACwB,IAAI,GAAG,CAAC,EAAE;IACdxB,CAAC,CAACwB,IAAI,GAAG,CAACxB,CAAC,CAACwB,IAAI;IAChB;EACF;EACAxB,CAAC,CAAC4F,MAAM,GAAI5F,CAAC,CAACwB,IAAI,GAAG9C,UAAU,GAAGK,UAAW;EAC7CQ,IAAI,CAACkC,KAAK,GAAIzB,CAAC,CAACwB,IAAI,KAAK,CAAC,GACxB,CAAC,CAAE;EAAA,EAEH,CAAC,CAAC,CAAC;EACLxB,CAAC,CAACgG,UAAU,GAAGtJ,UAAU;EACzBJ,KAAK,CAACmL,QAAQ,CAACzH,CAAC,CAAC;EACjB,OAAOjD,IAAI;AACb;AAGA,SAAS2K,YAAYA,CAACnI,IAAI,EAAE;EAC1B,IAAIoI,GAAG,GAAGJ,gBAAgB,CAAChI,IAAI,CAAC;EAChC,IAAIoI,GAAG,KAAK5K,IAAI,EAAE;IAChB0I,OAAO,CAAClG,IAAI,CAACU,KAAK,CAAC;EACrB;EACA,OAAO0H,GAAG;AACZ;AAGA,SAASC,gBAAgBA,CAACrI,IAAI,EAAEmE,IAAI,EAAE;EACpC,IAAI,CAACnE,IAAI,IAAI,CAACA,IAAI,CAACU,KAAK,EAAE;IAAE,OAAOhD,cAAc;EAAE;EACnD,IAAIsC,IAAI,CAACU,KAAK,CAACuB,IAAI,KAAK,CAAC,EAAE;IAAE,OAAOvE,cAAc;EAAE;EACpDsC,IAAI,CAACU,KAAK,CAAC4F,MAAM,GAAGnC,IAAI;EACxB,OAAO3G,IAAI;AACb;AAGA,SAAS8K,YAAYA,CAACtI,IAAI,EAAEmG,KAAK,EAAEK,MAAM,EAAE+B,UAAU,EAAEC,QAAQ,EAAEjD,QAAQ,EAAE;EACzE,IAAI,CAACvF,IAAI,EAAE;IAAE;IACX,OAAOtC,cAAc;EACvB;EACA,IAAIuE,IAAI,GAAG,CAAC;EAEZ,IAAIkE,KAAK,KAAKtI,qBAAqB,EAAE;IACnCsI,KAAK,GAAG,CAAC;EACX;EAEA,IAAIoC,UAAU,GAAG,CAAC,EAAE;IAAE;IACpBtG,IAAI,GAAG,CAAC;IACRsG,UAAU,GAAG,CAACA,UAAU;EAC1B,CAAC,MAEI,IAAIA,UAAU,GAAG,EAAE,EAAE;IACxBtG,IAAI,GAAG,CAAC,CAAC,CAAW;IACpBsG,UAAU,IAAI,EAAE;EAClB;EAGA,IAAIC,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAGnK,aAAa,IAAImI,MAAM,KAAKpI,UAAU,IACnEmK,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,IAAIpC,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,IAC3DZ,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAGtH,OAAO,EAAE;IACpC,OAAO8B,GAAG,CAACC,IAAI,EAAEtC,cAAc,CAAC;EAClC;EAGA,IAAI6K,UAAU,KAAK,CAAC,EAAE;IACpBA,UAAU,GAAG,CAAC;EAChB;EACA;;EAEA,IAAI9H,CAAC,GAAG,IAAI2F,YAAY,CAAC,CAAC;EAE1BpG,IAAI,CAACU,KAAK,GAAGD,CAAC;EACdA,CAAC,CAACT,IAAI,GAAGA,IAAI;EAEbS,CAAC,CAACwB,IAAI,GAAGA,IAAI;EACbxB,CAAC,CAAC6F,MAAM,GAAG,IAAI;EACf7F,CAAC,CAACiG,MAAM,GAAG6B,UAAU;EACrB9H,CAAC,CAACqC,MAAM,GAAG,CAAC,IAAIrC,CAAC,CAACiG,MAAM;EACxBjG,CAAC,CAACyC,MAAM,GAAGzC,CAAC,CAACqC,MAAM,GAAG,CAAC;EAEvBrC,CAAC,CAACkG,SAAS,GAAG6B,QAAQ,GAAG,CAAC;EAC1B/H,CAAC,CAACyD,SAAS,GAAG,CAAC,IAAIzD,CAAC,CAACkG,SAAS;EAC9BlG,CAAC,CAAC8D,SAAS,GAAG9D,CAAC,CAACyD,SAAS,GAAG,CAAC;EAC7BzD,CAAC,CAAC6D,UAAU,GAAG,CAAC,EAAE,CAAC7D,CAAC,CAACkG,SAAS,GAAG5H,SAAS,GAAG,CAAC,IAAIA,SAAS,CAAC;EAE5D0B,CAAC,CAACuC,MAAM,GAAG,IAAInG,KAAK,CAAC4L,IAAI,CAAChI,CAAC,CAACqC,MAAM,GAAG,CAAC,CAAC;EACvCrC,CAAC,CAAC0D,IAAI,GAAG,IAAItH,KAAK,CAACgK,KAAK,CAACpG,CAAC,CAACyD,SAAS,CAAC;EACrCzD,CAAC,CAAC0C,IAAI,GAAG,IAAItG,KAAK,CAACgK,KAAK,CAACpG,CAAC,CAACqC,MAAM,CAAC;;EAElC;EACA;;EAEArC,CAAC,CAACgH,WAAW,GAAG,CAAC,IAAKe,QAAQ,GAAG,CAAE,CAAC,CAAC;;EAErC/H,CAAC,CAACkE,gBAAgB,GAAGlE,CAAC,CAACgH,WAAW,GAAG,CAAC;;EAEtC;EACA;EACAhH,CAAC,CAACM,WAAW,GAAG,IAAIlE,KAAK,CAAC4L,IAAI,CAAChI,CAAC,CAACkE,gBAAgB,CAAC;;EAElD;EACA;EACAlE,CAAC,CAACiH,KAAK,GAAG,CAAC,GAAGjH,CAAC,CAACgH,WAAW;;EAE3B;EACAhH,CAAC,CAAC+G,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI/G,CAAC,CAACgH,WAAW;EAEjChH,CAAC,CAAC0F,KAAK,GAAGA,KAAK;EACf1F,CAAC,CAAC8E,QAAQ,GAAGA,QAAQ;EACrB9E,CAAC,CAAC+F,MAAM,GAAGA,MAAM;EAEjB,OAAO2B,YAAY,CAACnI,IAAI,CAAC;AAC3B;AAEA,SAAS0I,WAAWA,CAAC1I,IAAI,EAAEmG,KAAK,EAAE;EAChC,OAAOmC,YAAY,CAACtI,IAAI,EAAEmG,KAAK,EAAE/H,UAAU,EAAEE,SAAS,EAAEC,aAAa,EAAEL,kBAAkB,CAAC;AAC5F;AAGA,SAASyK,OAAOA,CAAC3I,IAAI,EAAEyE,KAAK,EAAE;EAC5B,IAAImE,SAAS,EAAEnI,CAAC;EAChB,IAAIoI,GAAG,EAAEC,GAAG,CAAC,CAAC;;EAEd,IAAI,CAAC9I,IAAI,IAAI,CAACA,IAAI,CAACU,KAAK,IACtB+D,KAAK,GAAGlH,OAAO,IAAIkH,KAAK,GAAG,CAAC,EAAE;IAC9B,OAAOzE,IAAI,GAAGD,GAAG,CAACC,IAAI,EAAEtC,cAAc,CAAC,GAAGA,cAAc;EAC1D;EAEA+C,CAAC,GAAGT,IAAI,CAACU,KAAK;EAEd,IAAI,CAACV,IAAI,CAACc,MAAM,IACX,CAACd,IAAI,CAAC+B,KAAK,IAAI/B,IAAI,CAAC8B,QAAQ,KAAK,CAAE,IACnCrB,CAAC,CAAC4F,MAAM,KAAK5G,YAAY,IAAIgF,KAAK,KAAKnH,QAAS,EAAE;IACrD,OAAOyC,GAAG,CAACC,IAAI,EAAGA,IAAI,CAACY,SAAS,KAAK,CAAC,GAAIhD,WAAW,GAAGF,cAAc,CAAC;EACzE;EAEA+C,CAAC,CAACT,IAAI,GAAGA,IAAI,CAAC,CAAC;EACf4I,SAAS,GAAGnI,CAAC,CAACgG,UAAU;EACxBhG,CAAC,CAACgG,UAAU,GAAGhC,KAAK;;EAEpB;EACA,IAAIhE,CAAC,CAAC4F,MAAM,KAAKlH,UAAU,EAAE;IAE3B,IAAIsB,CAAC,CAACwB,IAAI,KAAK,CAAC,EAAE;MAAE;MAClBjC,IAAI,CAACkC,KAAK,GAAG,CAAC,CAAC,CAAE;MACjBV,QAAQ,CAACf,CAAC,EAAE,EAAE,CAAC;MACfe,QAAQ,CAACf,CAAC,EAAE,GAAG,CAAC;MAChBe,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;MACd,IAAI,CAACA,CAAC,CAAC6F,MAAM,EAAE;QAAE;QACf9E,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;QACde,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;QACde,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;QACde,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;QACde,QAAQ,CAACf,CAAC,EAAE,CAAC,CAAC;QACde,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC0F,KAAK,KAAK,CAAC,GAAG,CAAC,GAChB1F,CAAC,CAAC8E,QAAQ,IAAIxH,cAAc,IAAI0C,CAAC,CAAC0F,KAAK,GAAG,CAAC,GAC3C,CAAC,GAAG,CAAE,CAAC;QACpB3E,QAAQ,CAACf,CAAC,EAAEX,OAAO,CAAC;QACpBW,CAAC,CAAC4F,MAAM,GAAG7G,UAAU;MACvB,CAAC,MACI;QACHgC,QAAQ,CAACf,CAAC,EAAE,CAACA,CAAC,CAAC6F,MAAM,CAACyC,IAAI,GAAG,CAAC,GAAG,CAAC,KACrBtI,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,IACtB,CAACvI,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IACxB,CAACxI,CAAC,CAAC6F,MAAM,CAAC4C,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,IACvB,CAACzI,CAAC,CAAC6F,MAAM,CAAC6C,OAAO,GAAG,CAAC,GAAG,EAAE,CACvC,CAAC;QACD3H,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC6F,MAAM,CAAC8C,IAAI,GAAG,IAAI,CAAC;QACjC5H,QAAQ,CAACf,CAAC,EAAGA,CAAC,CAAC6F,MAAM,CAAC8C,IAAI,IAAI,CAAC,GAAI,IAAI,CAAC;QACxC5H,QAAQ,CAACf,CAAC,EAAGA,CAAC,CAAC6F,MAAM,CAAC8C,IAAI,IAAI,EAAE,GAAI,IAAI,CAAC;QACzC5H,QAAQ,CAACf,CAAC,EAAGA,CAAC,CAAC6F,MAAM,CAAC8C,IAAI,IAAI,EAAE,GAAI,IAAI,CAAC;QACzC5H,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC0F,KAAK,KAAK,CAAC,GAAG,CAAC,GAChB1F,CAAC,CAAC8E,QAAQ,IAAIxH,cAAc,IAAI0C,CAAC,CAAC0F,KAAK,GAAG,CAAC,GAC3C,CAAC,GAAG,CAAE,CAAC;QACpB3E,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC6F,MAAM,CAAC+C,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI5I,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,IAAIxI,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAAC1I,MAAM,EAAE;UAC3CiB,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAAC1I,MAAM,GAAG,IAAI,CAAC;UACzCiB,QAAQ,CAACf,CAAC,EAAGA,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAAC1I,MAAM,IAAI,CAAC,GAAI,IAAI,CAAC;QAClD;QACA,IAAIE,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,EAAE;UACjBhJ,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,EAAE,CAAC,CAAC;QAC7D;QACAF,CAAC,CAAC8F,OAAO,GAAG,CAAC;QACb9F,CAAC,CAAC4F,MAAM,GAAGjH,WAAW;MACxB;IACF,CAAC;MACI;MACL;QACE,IAAIkK,MAAM,GAAIlL,UAAU,IAAKqC,CAAC,CAACiG,MAAM,GAAG,CAAC,IAAK,CAAC,CAAC,IAAK,CAAC;QACtD,IAAI6C,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAI9I,CAAC,CAAC8E,QAAQ,IAAIxH,cAAc,IAAI0C,CAAC,CAAC0F,KAAK,GAAG,CAAC,EAAE;UAC/CoD,WAAW,GAAG,CAAC;QACjB,CAAC,MAAM,IAAI9I,CAAC,CAAC0F,KAAK,GAAG,CAAC,EAAE;UACtBoD,WAAW,GAAG,CAAC;QACjB,CAAC,MAAM,IAAI9I,CAAC,CAAC0F,KAAK,KAAK,CAAC,EAAE;UACxBoD,WAAW,GAAG,CAAC;QACjB,CAAC,MAAM;UACLA,WAAW,GAAG,CAAC;QACjB;QACAD,MAAM,IAAKC,WAAW,IAAI,CAAE;QAC5B,IAAI9I,CAAC,CAACc,QAAQ,KAAK,CAAC,EAAE;UAAE+H,MAAM,IAAIpK,WAAW;QAAE;QAC/CoK,MAAM,IAAI,EAAE,GAAIA,MAAM,GAAG,EAAG;QAE5B7I,CAAC,CAAC4F,MAAM,GAAG7G,UAAU;QACrBkC,WAAW,CAACjB,CAAC,EAAE6I,MAAM,CAAC;;QAEtB;QACA,IAAI7I,CAAC,CAACc,QAAQ,KAAK,CAAC,EAAE;UACpBG,WAAW,CAACjB,CAAC,EAAET,IAAI,CAACkC,KAAK,KAAK,EAAE,CAAC;UACjCR,WAAW,CAACjB,CAAC,EAAET,IAAI,CAACkC,KAAK,GAAG,MAAM,CAAC;QACrC;QACAlC,IAAI,CAACkC,KAAK,GAAG,CAAC,CAAC,CAAC;MAClB;EACF;;EAEF;EACE,IAAIzB,CAAC,CAAC4F,MAAM,KAAKjH,WAAW,EAAE;IAC5B,IAAIqB,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,iBAAgB;MAChCJ,GAAG,GAAGpI,CAAC,CAACE,OAAO,CAAC,CAAE;;MAElB,OAAOF,CAAC,CAAC8F,OAAO,IAAI9F,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAAC1I,MAAM,GAAG,MAAM,CAAC,EAAE;QACnD,IAAIE,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;UACpC,IAAIlE,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;YACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;UACrE;UACArI,aAAa,CAACR,IAAI,CAAC;UACnB6I,GAAG,GAAGpI,CAAC,CAACE,OAAO;UACf,IAAIF,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;YACpC;UACF;QACF;QACAnD,QAAQ,CAACf,CAAC,EAAEA,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAACxI,CAAC,CAAC8F,OAAO,CAAC,GAAG,IAAI,CAAC;QAC7C9F,CAAC,CAAC8F,OAAO,EAAE;MACb;MACA,IAAI9F,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;QACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;MACrE;MACA,IAAIpI,CAAC,CAAC8F,OAAO,KAAK9F,CAAC,CAAC6F,MAAM,CAAC2C,KAAK,CAAC1I,MAAM,EAAE;QACvCE,CAAC,CAAC8F,OAAO,GAAG,CAAC;QACb9F,CAAC,CAAC4F,MAAM,GAAGhH,UAAU;MACvB;IACF,CAAC,MACI;MACHoB,CAAC,CAAC4F,MAAM,GAAGhH,UAAU;IACvB;EACF;EACA,IAAIoB,CAAC,CAAC4F,MAAM,KAAKhH,UAAU,EAAE;IAC3B,IAAIoB,CAAC,CAAC6F,MAAM,CAAC4C,IAAI,iBAAgB;MAC/BL,GAAG,GAAGpI,CAAC,CAACE,OAAO,CAAC,CAAE;MAClB;;MAEA,GAAG;QACD,IAAIF,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;UACpC,IAAIlE,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;YACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;UACrE;UACArI,aAAa,CAACR,IAAI,CAAC;UACnB6I,GAAG,GAAGpI,CAAC,CAACE,OAAO;UACf,IAAIF,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;YACpCmE,GAAG,GAAG,CAAC;YACP;UACF;QACF;QACA;QACA,IAAIrI,CAAC,CAAC8F,OAAO,GAAG9F,CAAC,CAAC6F,MAAM,CAAC4C,IAAI,CAAC3I,MAAM,EAAE;UACpCuI,GAAG,GAAGrI,CAAC,CAAC6F,MAAM,CAAC4C,IAAI,CAACM,UAAU,CAAC/I,CAAC,CAAC8F,OAAO,EAAE,CAAC,GAAG,IAAI;QACpD,CAAC,MAAM;UACLuC,GAAG,GAAG,CAAC;QACT;QACAtH,QAAQ,CAACf,CAAC,EAAEqI,GAAG,CAAC;MAClB,CAAC,QAAQA,GAAG,KAAK,CAAC;MAElB,IAAIrI,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;QACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;MACrE;MACA,IAAIC,GAAG,KAAK,CAAC,EAAE;QACbrI,CAAC,CAAC8F,OAAO,GAAG,CAAC;QACb9F,CAAC,CAAC4F,MAAM,GAAG/G,aAAa;MAC1B;IACF,CAAC,MACI;MACHmB,CAAC,CAAC4F,MAAM,GAAG/G,aAAa;IAC1B;EACF;EACA,IAAImB,CAAC,CAAC4F,MAAM,KAAK/G,aAAa,EAAE;IAC9B,IAAImB,CAAC,CAAC6F,MAAM,CAAC6C,OAAO,iBAAgB;MAClCN,GAAG,GAAGpI,CAAC,CAACE,OAAO,CAAC,CAAE;MAClB;;MAEA,GAAG;QACD,IAAIF,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;UACpC,IAAIlE,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;YACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;UACrE;UACArI,aAAa,CAACR,IAAI,CAAC;UACnB6I,GAAG,GAAGpI,CAAC,CAACE,OAAO;UACf,IAAIF,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACkE,gBAAgB,EAAE;YACpCmE,GAAG,GAAG,CAAC;YACP;UACF;QACF;QACA;QACA,IAAIrI,CAAC,CAAC8F,OAAO,GAAG9F,CAAC,CAAC6F,MAAM,CAAC6C,OAAO,CAAC5I,MAAM,EAAE;UACvCuI,GAAG,GAAGrI,CAAC,CAAC6F,MAAM,CAAC6C,OAAO,CAACK,UAAU,CAAC/I,CAAC,CAAC8F,OAAO,EAAE,CAAC,GAAG,IAAI;QACvD,CAAC,MAAM;UACLuC,GAAG,GAAG,CAAC;QACT;QACAtH,QAAQ,CAACf,CAAC,EAAEqI,GAAG,CAAC;MAClB,CAAC,QAAQA,GAAG,KAAK,CAAC;MAElB,IAAIrI,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,IAAIvI,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAE;QACpC7I,IAAI,CAACkC,KAAK,GAAGjF,KAAK,CAAC+C,IAAI,CAACkC,KAAK,EAAEzB,CAAC,CAACM,WAAW,EAAEN,CAAC,CAACE,OAAO,GAAGkI,GAAG,EAAEA,GAAG,CAAC;MACrE;MACA,IAAIC,GAAG,KAAK,CAAC,EAAE;QACbrI,CAAC,CAAC4F,MAAM,GAAG9G,UAAU;MACvB;IACF,CAAC,MACI;MACHkB,CAAC,CAAC4F,MAAM,GAAG9G,UAAU;IACvB;EACF;EACA,IAAIkB,CAAC,CAAC4F,MAAM,KAAK9G,UAAU,EAAE;IAC3B,IAAIkB,CAAC,CAAC6F,MAAM,CAAC0C,IAAI,EAAE;MACjB,IAAIvI,CAAC,CAACE,OAAO,GAAG,CAAC,GAAGF,CAAC,CAACkE,gBAAgB,EAAE;QACtCnE,aAAa,CAACR,IAAI,CAAC;MACrB;MACA,IAAIS,CAAC,CAACE,OAAO,GAAG,CAAC,IAAIF,CAAC,CAACkE,gBAAgB,EAAE;QACvCnD,QAAQ,CAACf,CAAC,EAAET,IAAI,CAACkC,KAAK,GAAG,IAAI,CAAC;QAC9BV,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACkC,KAAK,IAAI,CAAC,GAAI,IAAI,CAAC;QACrClC,IAAI,CAACkC,KAAK,GAAG,CAAC,CAAC,CAAC;QAChBzB,CAAC,CAAC4F,MAAM,GAAG7G,UAAU;MACvB;IACF,CAAC,MACI;MACHiB,CAAC,CAAC4F,MAAM,GAAG7G,UAAU;IACvB;EACF;EACF;;EAEE;EACA,IAAIiB,CAAC,CAACE,OAAO,KAAK,CAAC,EAAE;IACnBH,aAAa,CAACR,IAAI,CAAC;IACnB,IAAIA,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;MACxB;AACN;AACA;AACA;AACA;AACA;MACMH,CAAC,CAACgG,UAAU,GAAG,CAAC,CAAC;MACjB,OAAOjJ,IAAI;IACb;;IAEA;AACJ;AACA;AACA;EACE,CAAC,MAAM,IAAIwC,IAAI,CAAC8B,QAAQ,KAAK,CAAC,IAAI5B,IAAI,CAACuE,KAAK,CAAC,IAAIvE,IAAI,CAAC0I,SAAS,CAAC,IAC9DnE,KAAK,KAAKnH,QAAQ,EAAE;IACpB,OAAOyC,GAAG,CAACC,IAAI,EAAEpC,WAAW,CAAC;EAC/B;;EAEA;EACA,IAAI6C,CAAC,CAAC4F,MAAM,KAAK5G,YAAY,IAAIO,IAAI,CAAC8B,QAAQ,KAAK,CAAC,EAAE;IACpD,OAAO/B,GAAG,CAACC,IAAI,EAAEpC,WAAW,CAAC;EAC/B;;EAEA;AACF;EACE,IAAIoC,IAAI,CAAC8B,QAAQ,KAAK,CAAC,IAAIrB,CAAC,CAAC+C,SAAS,KAAK,CAAC,IACzCiB,KAAK,KAAKtH,UAAU,IAAIsD,CAAC,CAAC4F,MAAM,KAAK5G,YAAa,EAAE;IACrD,IAAIgK,MAAM,GAAIhJ,CAAC,CAAC8E,QAAQ,KAAKxH,cAAc,GAAI2H,YAAY,CAACjF,CAAC,EAAEgE,KAAK,CAAC,GAClEhE,CAAC,CAAC8E,QAAQ,KAAKvH,KAAK,GAAGyH,WAAW,CAAChF,CAAC,EAAEgE,KAAK,CAAC,GAC3CwB,mBAAmB,CAACxF,CAAC,CAAC0F,KAAK,CAAC,CAACH,IAAI,CAACvF,CAAC,EAAEgE,KAAK,CAAE;IAEhD,IAAIgF,MAAM,KAAK7J,iBAAiB,IAAI6J,MAAM,KAAK5J,cAAc,EAAE;MAC7DY,CAAC,CAAC4F,MAAM,GAAG5G,YAAY;IACzB;IACA,IAAIgK,MAAM,KAAK/J,YAAY,IAAI+J,MAAM,KAAK7J,iBAAiB,EAAE;MAC3D,IAAII,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QACxBH,CAAC,CAACgG,UAAU,GAAG,CAAC,CAAC;QACjB;MACF;MACA,OAAOjJ,IAAI;MACX;AACN;AACA;AACA;AACA;AACA;AACA;IACI;IACA,IAAIiM,MAAM,KAAK9J,aAAa,EAAE;MAC5B,IAAI8E,KAAK,KAAKrH,eAAe,EAAE;QAC7BL,KAAK,CAAC2M,SAAS,CAACjJ,CAAC,CAAC;MACpB,CAAC,MACI,IAAIgE,KAAK,KAAKlH,OAAO,EAAE;QAAE;;QAE5BR,KAAK,CAAC4M,gBAAgB,CAAClJ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;QACtC;AACR;AACA;QACQ,IAAIgE,KAAK,KAAKpH,YAAY,EAAE;UAC1B,yBAAqC;UACrC+C,IAAI,CAACK,CAAC,CAAC0D,IAAI,CAAC,CAAC,CAAC;;UAEd,IAAI1D,CAAC,CAAC+C,SAAS,KAAK,CAAC,EAAE;YACrB/C,CAAC,CAACc,QAAQ,GAAG,CAAC;YACdd,CAAC,CAACa,WAAW,GAAG,CAAC;YACjBb,CAAC,CAAC2D,MAAM,GAAG,CAAC;UACd;QACF;MACF;MACA5D,aAAa,CAACR,IAAI,CAAC;MACnB,IAAIA,IAAI,CAACY,SAAS,KAAK,CAAC,EAAE;QACxBH,CAAC,CAACgG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;QACnB,OAAOjJ,IAAI;MACb;IACF;EACF;EACA;EACA;;EAEA,IAAIiH,KAAK,KAAKnH,QAAQ,EAAE;IAAE,OAAOE,IAAI;EAAE;EACvC,IAAIiD,CAAC,CAACwB,IAAI,IAAI,CAAC,EAAE;IAAE,OAAOxE,YAAY;EAAE;;EAExC;EACA,IAAIgD,CAAC,CAACwB,IAAI,KAAK,CAAC,EAAE;IAChBT,QAAQ,CAACf,CAAC,EAAET,IAAI,CAACkC,KAAK,GAAG,IAAI,CAAC;IAC9BV,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACkC,KAAK,IAAI,CAAC,GAAI,IAAI,CAAC;IACrCV,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACkC,KAAK,IAAI,EAAE,GAAI,IAAI,CAAC;IACtCV,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACkC,KAAK,IAAI,EAAE,GAAI,IAAI,CAAC;IACtCV,QAAQ,CAACf,CAAC,EAAET,IAAI,CAACmC,QAAQ,GAAG,IAAI,CAAC;IACjCX,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACmC,QAAQ,IAAI,CAAC,GAAI,IAAI,CAAC;IACxCX,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACmC,QAAQ,IAAI,EAAE,GAAI,IAAI,CAAC;IACzCX,QAAQ,CAACf,CAAC,EAAGT,IAAI,CAACmC,QAAQ,IAAI,EAAE,GAAI,IAAI,CAAC;EAC3C,CAAC,MAED;IACET,WAAW,CAACjB,CAAC,EAAET,IAAI,CAACkC,KAAK,KAAK,EAAE,CAAC;IACjCR,WAAW,CAACjB,CAAC,EAAET,IAAI,CAACkC,KAAK,GAAG,MAAM,CAAC;EACrC;EAEA1B,aAAa,CAACR,IAAI,CAAC;EACnB;AACF;AACA;EACE,IAAIS,CAAC,CAACwB,IAAI,GAAG,CAAC,EAAE;IAAExB,CAAC,CAACwB,IAAI,GAAG,CAACxB,CAAC,CAACwB,IAAI;EAAE;EACpC;EACA,OAAOxB,CAAC,CAACE,OAAO,KAAK,CAAC,GAAGnD,IAAI,GAAGC,YAAY;AAC9C;AAEA,SAASmM,UAAUA,CAAC5J,IAAI,EAAE;EACxB,IAAIqG,MAAM;EAEV,IAAI,CAACrG,IAAI,kBAAiB,CAACA,IAAI,CAACU,KAAK,gBAAe;IAClD,OAAOhD,cAAc;EACvB;EAEA2I,MAAM,GAAGrG,IAAI,CAACU,KAAK,CAAC2F,MAAM;EAC1B,IAAIA,MAAM,KAAKlH,UAAU,IACvBkH,MAAM,KAAKjH,WAAW,IACtBiH,MAAM,KAAKhH,UAAU,IACrBgH,MAAM,KAAK/G,aAAa,IACxB+G,MAAM,KAAK9G,UAAU,IACrB8G,MAAM,KAAK7G,UAAU,IACrB6G,MAAM,KAAK5G,YAAY,EACvB;IACA,OAAOM,GAAG,CAACC,IAAI,EAAEtC,cAAc,CAAC;EAClC;EAEAsC,IAAI,CAACU,KAAK,GAAG,IAAI;EAEjB,OAAO2F,MAAM,KAAK7G,UAAU,GAAGO,GAAG,CAACC,IAAI,EAAErC,YAAY,CAAC,GAAGH,IAAI;AAC/D;;AAGA;AACA;AACA;AACA;AACA,SAASqM,oBAAoBA,CAAC7J,IAAI,EAAE8J,UAAU,EAAE;EAC9C,IAAIC,UAAU,GAAGD,UAAU,CAACvJ,MAAM;EAElC,IAAIE,CAAC;EACL,IAAIuD,GAAG,EAAEH,CAAC;EACV,IAAI5B,IAAI;EACR,IAAI+H,KAAK;EACT,IAAIC,IAAI;EACR,IAAIlI,KAAK;EACT,IAAImI,OAAO;EAEX,IAAI,CAAClK,IAAI,kBAAiB,CAACA,IAAI,CAACU,KAAK,gBAAe;IAClD,OAAOhD,cAAc;EACvB;EAEA+C,CAAC,GAAGT,IAAI,CAACU,KAAK;EACduB,IAAI,GAAGxB,CAAC,CAACwB,IAAI;EAEb,IAAIA,IAAI,KAAK,CAAC,IAAKA,IAAI,KAAK,CAAC,IAAIxB,CAAC,CAAC4F,MAAM,KAAKlH,UAAW,IAAIsB,CAAC,CAAC+C,SAAS,EAAE;IACxE,OAAO9F,cAAc;EACvB;;EAEA;EACA,IAAIuE,IAAI,KAAK,CAAC,EAAE;IACd;IACAjC,IAAI,CAACkC,KAAK,GAAGlF,OAAO,CAACgD,IAAI,CAACkC,KAAK,EAAE4H,UAAU,EAAEC,UAAU,EAAE,CAAC,CAAC;EAC7D;EAEAtJ,CAAC,CAACwB,IAAI,GAAG,CAAC,CAAC,CAAG;;EAEd;EACA,IAAI8H,UAAU,IAAItJ,CAAC,CAACqC,MAAM,EAAE;IAC1B,IAAIb,IAAI,KAAK,CAAC,EAAE;MAAa;MAC3B;MACA7B,IAAI,CAACK,CAAC,CAAC0D,IAAI,CAAC,CAAC,CAAC;MACd1D,CAAC,CAACc,QAAQ,GAAG,CAAC;MACdd,CAAC,CAACa,WAAW,GAAG,CAAC;MACjBb,CAAC,CAAC2D,MAAM,GAAG,CAAC;IACd;IACA;IACA;IACA8F,OAAO,GAAG,IAAIrN,KAAK,CAAC4L,IAAI,CAAChI,CAAC,CAACqC,MAAM,CAAC;IAClCjG,KAAK,CAACgE,QAAQ,CAACqJ,OAAO,EAAEJ,UAAU,EAAEC,UAAU,GAAGtJ,CAAC,CAACqC,MAAM,EAAErC,CAAC,CAACqC,MAAM,EAAE,CAAC,CAAC;IACvEgH,UAAU,GAAGI,OAAO;IACpBH,UAAU,GAAGtJ,CAAC,CAACqC,MAAM;EACvB;EACA;EACAkH,KAAK,GAAGhK,IAAI,CAAC8B,QAAQ;EACrBmI,IAAI,GAAGjK,IAAI,CAACgC,OAAO;EACnBD,KAAK,GAAG/B,IAAI,CAAC+B,KAAK;EAClB/B,IAAI,CAAC8B,QAAQ,GAAGiI,UAAU;EAC1B/J,IAAI,CAACgC,OAAO,GAAG,CAAC;EAChBhC,IAAI,CAAC+B,KAAK,GAAG+H,UAAU;EACvBpG,WAAW,CAACjD,CAAC,CAAC;EACd,OAAOA,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,EAAE;IAC/BiF,GAAG,GAAGvD,CAAC,CAACc,QAAQ;IAChBsC,CAAC,GAAGpD,CAAC,CAAC+C,SAAS,IAAIzE,SAAS,GAAG,CAAC,CAAC;IACjC,GAAG;MACD;MACA0B,CAAC,CAAC4D,KAAK,GAAG,CAAE5D,CAAC,CAAC4D,KAAK,IAAI5D,CAAC,CAAC6D,UAAU,GAAI7D,CAAC,CAACuC,MAAM,CAACgB,GAAG,GAAGjF,SAAS,GAAG,CAAC,CAAC,IAAI0B,CAAC,CAAC8D,SAAS;MAEnF9D,CAAC,CAAC0C,IAAI,CAACa,GAAG,GAAGvD,CAAC,CAACyC,MAAM,CAAC,GAAGzC,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC;MAExC5D,CAAC,CAAC0D,IAAI,CAAC1D,CAAC,CAAC4D,KAAK,CAAC,GAAGL,GAAG;MACrBA,GAAG,EAAE;IACP,CAAC,QAAQ,EAAEH,CAAC;IACZpD,CAAC,CAACc,QAAQ,GAAGyC,GAAG;IAChBvD,CAAC,CAAC+C,SAAS,GAAGzE,SAAS,GAAG,CAAC;IAC3B2E,WAAW,CAACjD,CAAC,CAAC;EAChB;EACAA,CAAC,CAACc,QAAQ,IAAId,CAAC,CAAC+C,SAAS;EACzB/C,CAAC,CAACa,WAAW,GAAGb,CAAC,CAACc,QAAQ;EAC1Bd,CAAC,CAAC2D,MAAM,GAAG3D,CAAC,CAAC+C,SAAS;EACtB/C,CAAC,CAAC+C,SAAS,GAAG,CAAC;EACf/C,CAAC,CAACuE,YAAY,GAAGvE,CAAC,CAACkC,WAAW,GAAG5D,SAAS,GAAG,CAAC;EAC9C0B,CAAC,CAAC+E,eAAe,GAAG,CAAC;EACrBxF,IAAI,CAACgC,OAAO,GAAGiI,IAAI;EACnBjK,IAAI,CAAC+B,KAAK,GAAGA,KAAK;EAClB/B,IAAI,CAAC8B,QAAQ,GAAGkI,KAAK;EACrBvJ,CAAC,CAACwB,IAAI,GAAGA,IAAI;EACb,OAAOzE,IAAI;AACb;AAGA2M,OAAO,CAACzB,WAAW,GAAGA,WAAW;AACjCyB,OAAO,CAAC7B,YAAY,GAAGA,YAAY;AACnC6B,OAAO,CAAChC,YAAY,GAAGA,YAAY;AACnCgC,OAAO,CAACnC,gBAAgB,GAAGA,gBAAgB;AAC3CmC,OAAO,CAAC9B,gBAAgB,GAAGA,gBAAgB;AAC3C8B,OAAO,CAACxB,OAAO,GAAGA,OAAO;AACzBwB,OAAO,CAACP,UAAU,GAAGA,UAAU;AAC/BO,OAAO,CAACN,oBAAoB,GAAGA,oBAAoB;AACnDM,OAAO,CAACC,WAAW,GAAG,oCAAoC;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}