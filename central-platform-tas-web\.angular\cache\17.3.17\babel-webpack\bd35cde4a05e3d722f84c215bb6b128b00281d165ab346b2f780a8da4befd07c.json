{"ast": null, "code": "import { VesselBayRoutingModule } from './vessel-bay-routing.module';\nimport { VesselBayComponent } from './vessel-bay.component';\nimport { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [VesselBayComponent];\nexport class VesselBayModule {\n  static {\n    this.ɵfac = function VesselBayModule_Factory(t) {\n      return new (t || VesselBayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselBayModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VesselBayRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselBayModule, {\n    declarations: [VesselBayComponent],\n    imports: [SharedModule, VesselBayRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["VesselBayRoutingModule", "VesselBayComponent", "SharedModule", "LayoutModule", "COMPONENTS", "VesselBayModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-bay\\vessel-bay.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { VesselBayRoutingModule } from './vessel-bay-routing.module';\r\nimport { VesselBayComponent } from './vessel-bay.component';\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\n\r\nconst COMPONENTS = [VesselBayComponent];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VesselBayRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VesselBayModule { }\r\n"], "mappings": "AACA,SAASA,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD,MAAMC,UAAU,GAAG,CAACH,kBAAkB,CAAC;AAMvC,OAAM,MAAOI,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBH,YAAY,EAAEF,sBAAsB,EAAEG,YAAY;IAAA;EAAA;;;2EAGjDE,eAAe;IAAAC,YAAA,GANRL,kBAAkB;IAAAM,OAAA,GAG1BL,YAAY,EAAEF,sBAAsB,EAAEG,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}