{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'joan den' eeee, LT\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'joan den' eeee, p\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "getUTCHours"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/eu/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'joan den' eeee, LT\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'joan den' eeee, p\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,0BAA0B,GAAG;EAC/BN,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACxD,IAAIA,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAOJ,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}