{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CwfBaseCrud, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\nimport { FormControl, Validators } from '@angular/forms';\nimport { BASE_T_FEPCONFIG } from '@store/BCD/BASE_T_FEPCONFIG';\nimport { filter } from 'rxjs/operators';\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\nimport { HttpHeaders, HttpRequest, HttpResponse } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"ng-zorro-antd/message\";\nimport * as i5 from \"@service/cwfuploadService\";\nimport * as i6 from \"@service/cwfRestful.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"angular-svg-icon\";\nimport * as i10 from \"ng-zorro-antd/grid\";\nimport * as i11 from \"ng-zorro-antd/form\";\nimport * as i12 from \"ng-zorro-antd/button\";\nimport * as i13 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i14 from \"ng-zorro-antd/core/wave\";\nimport * as i15 from \"ng-zorro-antd/input\";\nimport * as i16 from \"ng-zorro-antd/select\";\nimport * as i17 from \"ng-zorro-antd/card\";\nimport * as i18 from \"ng-zorro-antd/popconfirm\";\nimport * as i19 from \"ng-zorro-antd/table\";\nimport * as i20 from \"ng-zorro-antd/tooltip\";\nimport * as i21 from \"ng-zorro-antd/icon\";\nimport * as i22 from \"ng-zorro-antd/upload\";\nimport * as i23 from \"@ngx-translate/core\";\nconst _c0 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c1 = () => [8, 10];\nconst _c2 = () => ({\n  x: \"1000px\"\n});\nfunction FepconfigEditComponent_nz_col_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 36)(1, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FepconfigEditComponent_nz_col_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveData());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function FepconfigEditComponent_nz_col_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzLoading\", ctx_r2.loading)(\"nzType\", \"primary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"FP.SAVE\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 6, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction FepconfigEditComponent_nz_col_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 36)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function FepconfigEditComponent_nz_col_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction FepconfigEditComponent_nz_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 39);\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r5.label)(\"nzValue\", option_r5.value);\n  }\n}\nfunction FepconfigEditComponent_nz_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 39);\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r6.label)(\"nzValue\", option_r6.value);\n  }\n}\nfunction FepconfigEditComponent_nz_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 39);\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r7.label)(\"nzValue\", option_r7.value);\n  }\n}\nfunction FepconfigEditComponent_tr_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 40)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 40)(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 40)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 41)(14, \"a\", 42);\n    i0.ɵɵlistener(\"nzOnConfirm\", function FepconfigEditComponent_tr_116_Template_a_nzOnConfirm_14_listener() {\n      const data_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileDel(data_r9.id));\n    });\n    i0.ɵɵtext(15, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function FepconfigEditComponent_tr_116_Template_a_click_16_listener() {\n      const data_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.preview(data_r9));\n    });\n    i0.ɵɵtext(17, \"\\u9884\\u89C8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function FepconfigEditComponent_tr_116_Template_a_click_18_listener() {\n      const data_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileDownload(data_r9.attachmentId));\n    });\n    i0.ɵɵtext(19, \"\\u4E0B\\u8F7D\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r10 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.originalFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.createdUserName || ctx_r2.translate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 8, data_r9.createdTime, \"yyyy-MM-dd HH:mm:ss\"));\n  }\n}\nexport class FepconfigEditComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, http, message, uploadService, cwfRestfulService) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.http = http;\n    this.message = message;\n    this.uploadService = uploadService;\n    this.cwfRestfulService = cwfRestfulService;\n    this.mainStore = new BASE_T_FEPCONFIG();\n    this.editStores = [this.mainStore];\n    this.id = \"\";\n    this.serviceName = \"\";\n    this.portData = [];\n    this.wharfData = [];\n    this.companyData = [];\n    this.fileList = [];\n    this.fileUploading = false;\n    this.fileloading = false;\n    this.fileStore = new BASE_T_PARFILE();\n    this.baseServiceName = '';\n    this.USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\n    this.fileUploadUrl = this.gol.serverUrl + '/' + this.gol.serviceName['tas'].en + '/storage/new/upload'; // 文件上传请求服务地址\n    // 定义禁用编辑表单的对象，用于控制表单的禁用状态\n    // ALL属性表示是否全局禁用编辑表单的功能\n    this.disabledEditForm = {\n      ALL: false\n    };\n    // 导入文件前\n    this.fileBeforeUpload = file => {\n      console.log('查看id', this.editForm.controls['id'].value);\n      if (this.editForm.controls['id'].value == null || this.editForm.controls['id'].value == '') {\n        this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\n        return false;\n      }\n      this.fileList = [file];\n      console.log(this.fileList[0]);\n      if (this.fileList.length != 1) {\n        this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\n        return false;\n      }\n      if (this.fileList[0].size > this.uploadService.chunkSize) {\n        // 文件大于5M，使用分片上传\n        let params = {\n          pkId: this.editForm.controls['id'].value,\n          expireTime: 60000,\n          module: 'tasModule',\n          businessTypeCode: 'tas_fepconfig',\n          businessTypeName: 'tas_fepconfig'\n        };\n        this.uploadService.uploadData(this.fileList[0], params);\n      } else {\n        // 文件小于5M，普通上传\n        this.fileUpload();\n      }\n      return false;\n    };\n  }\n  /**\n  * 初始化查询条件\n  */\n  initEdit() {\n    return {\n      id: new FormControl('', Validators.nullValidator),\n      // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\n      portId: new FormControl('', Validators.required),\n      portCd: new FormControl('', Validators.nullValidator),\n      portNm: new FormControl('', Validators.nullValidator),\n      portNmEn: new FormControl('', Validators.nullValidator),\n      wharfId: new FormControl('', Validators.required),\n      wharfCd: new FormControl('', Validators.nullValidator),\n      wharfNm: new FormControl('', Validators.nullValidator),\n      wharfNmEn: new FormControl('', Validators.nullValidator),\n      fepIp: new FormControl('', [Validators.required, Validators.maxLength(36)]),\n      fepPort: new FormControl('', [Validators.nullValidator, Validators.maxLength(12)]),\n      userName: new FormControl('', [Validators.required, Validators.maxLength(105)]),\n      userPassword: new FormControl('', [Validators.required, Validators.maxLength(105)]),\n      filePath: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\n      interfaceAddr: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\n      orgId: new FormControl('', Validators.required),\n      orgNm: new FormControl('', Validators.required),\n      entLevelNo: new FormControl('', Validators.nullValidator),\n      orgLevelNo: new FormControl('', Validators.nullValidator),\n      remark: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\n      createdUser: new FormControl('', Validators.nullValidator),\n      // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\n      createdTime: new FormControl('', Validators.nullValidator),\n      // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedUser: new FormControl('', Validators.nullValidator),\n      // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedTime: new FormControl('', Validators.nullValidator),\n      // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\n      isDelete: new FormControl('', Validators.nullValidator),\n      // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\n      version: new FormControl('', Validators.nullValidator) // 版本号，初始值为空，验证规则为nullValidator（允许为空）\n    };\n  }\n  /**\n   * 页面加载完成后出发\n   */\n  onShow() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.baseServiceName = _this.gol.serviceName['tas'].en;\n      _this.serviceName = _this.gol.serviceName['main'].en;\n      if (_this.openParam['state'] === PageModeEnum.Custom) {\n        _this.disabledEditForm['ALL'] = true;\n        _this.cwfRestfulService.get('/fep_config/' + _this.openParam['id'], _this.gol.serviceName['tas'].en).then(rps => {\n          if (rps.ok) {\n            _this.editForm.patchValue(rps.data);\n          } else {\n            _this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\n            _this.openMainPage();\n          }\n        });\n      }\n      if (_this.openParam['state'] !== PageModeEnum.Add) {\n        _this.onQueryData();\n      }\n      _this.getOrgData();\n      _this.onQueryPort(_this.editForm.controls['portCd']?.value);\n      _this.onQueryWharf();\n    })();\n  }\n  onQueryWharf() {\n    this.cwfRestfulService.post('/wharf/getAllWharfInfo', {\n      portId: this.editForm.controls['portId']?.value\n    }, this.gol.serviceName['bc'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 wharfData 数组\n        this.wharfData = rps.data.map(item => ({\n          label: item.wharfCd + '/' + item.wharfNm + '/' + item.portNm,\n          value: item.id,\n          wharfCd: item.wharfCd,\n          wharfNm: item.wharfNm,\n          wharfNmEn: item.wharfNmEn\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  onQueryPort(selectedValue) {\n    let requestData = {\n      portNm: selectedValue,\n      page: 1,\n      size: 100\n    };\n    this.cwfRestfulService.post('/port/getAllPortInfo', requestData, this.gol.serviceName['bc'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 portData 数组\n        this.portData = rps.data.map(item => ({\n          label: item.portCd + '/' + item.portNm + '/' + item.countryNm,\n          value: item.id,\n          portCd: item.portCd,\n          portNm: item.portNm,\n          portNmEn: item.portNmEn\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  onSearchChange(selectedValue) {\n    this.onQueryPort(selectedValue);\n  }\n  /**\n   * desc:保存数据\n   * by:\n   */\n  saveData() {\n    const url = '/fep_config';\n    for (const i in this.editForm.controls) {\n      this.editForm.controls[i].markAsDirty();\n      this.editForm.controls[i].updateValueAndValidity();\n    }\n    if (this.editForm.invalid) {\n      return;\n    }\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\n    this.loading = true;\n    if (this.openParam['state'] === PageModeEnum.Add) {\n      this.editForm.removeControl('id');\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    } else {\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    }\n  }\n  onClose() {\n    if (this.checkSave()) {\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\n        switch (state) {\n          case DialogResultEnum.yes:\n            // 是\n            this.saveData();\n            return true;\n          case DialogResultEnum.no:\n            // 否\n            this.openMainPage({});\n            return true;\n          case DialogResultEnum.cancel:\n            // 取消\n            return false;\n        }\n      });\n    } else {\n      this.openMainPage({});\n      return true;\n    }\n  }\n  getEditFromDisabled(feildName) {\n    return this.disabledEditForm[feildName];\n  }\n  onCompanyChange(selectedValues) {\n    if (selectedValues == null) {\n      // 清理数据\n      this.editForm.controls['orgLevelNo'].setValue(\"\");\n      this.editForm.controls['orgNm'].setValue(\"\");\n      this.editForm.controls['entLevelNo'].setValue(\"\");\n    } else {\n      let model = this.companyData.find(item => item.value === selectedValues);\n      this.editForm.controls['orgLevelNo'].setValue(model.orgLevelNo);\n      this.editForm.controls['entLevelNo'].setValue(model.entLevelNo);\n      this.editForm.controls['orgNm'].setValue(model.orgNm);\n    }\n  }\n  getOrgData() {\n    const rdata = {\n      type: null\n    };\n    this.cwfRestfulService.post('/org/getRelateChildren', rdata, this.gol.serviceName['main'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 companyData 数组\n        this.companyData = rps.data.map(item => ({\n          label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\n          value: item.orgId,\n          orgLevelNo: item.orgCode,\n          orgNm: item.orgName,\n          entLevelNo: item.companyCode\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  onPortChange(selectedValues) {\n    if (selectedValues == null) {\n      // 清理数据\n      this.editForm.controls['portId'].setValue(\"\");\n      this.editForm.controls['portCd'].setValue(\"\");\n      this.editForm.controls['portNm'].setValue(\"\");\n      this.editForm.controls['portNmEn'].setValue(\"\");\n    } else {\n      let model = this.portData.find(item => item.value === selectedValues);\n      this.editForm.controls['portCd'].setValue(model.portCd?.trim());\n      this.editForm.controls['portNm'].setValue(model.portNm?.trim());\n      this.editForm.controls['portNmEn'].setValue(model.portNmEn?.trim());\n    }\n    this.editForm.controls['wharfId'].setValue(\"\");\n    this.onQueryWharf();\n  }\n  onWharfChange(selectedValues) {\n    if (selectedValues == null) {\n      // 清理数据\n      this.editForm.controls['wharfCd'].setValue(\"\");\n      this.editForm.controls['wharfNm'].setValue(\"\");\n      this.editForm.controls['wharfNmEn'].setValue(\"\");\n    } else {\n      let model = this.wharfData.find(item => item.value === selectedValues);\n      this.editForm.controls['wharfNm'].setValue(model.wharfNm?.trim());\n      this.editForm.controls['wharfNmEn'].setValue(model.wharfNmEn?.trim());\n    }\n  }\n  // 普通上传\n  fileUpload() {\n    let self = this;\n    const formData = new FormData();\n    formData.append('file', this.fileList[0]);\n    formData.append('pkId', this.editForm.controls['id'].value);\n    formData.append('module', 'tasModule');\n    formData.append('businessTypeCode', 'tas_fepconfig');\n    formData.append('businessTypeName', 'tas_fepconfig');\n    formData.append('businessTypeNameEn', 'tas_fepconfig');\n    formData.append('recordTypeCode', 'tas_fepconfig');\n    formData.append('recordTypeName', 'tas_fepconfig');\n    formData.append('recordTypeNameEn', 'tas_fepconfig');\n    formData.append('serviceTypeCode', 'tas_fepconfig');\n    formData.append('serviceTypeName', 'tas_fepconfig');\n    formData.append('serviceTypeNameEn', 'tas_fepconfig');\n    const headers = new HttpHeaders({\n      'x-ccf-token': this.USER_SESSION\n    });\n    const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\n      headers: headers,\n      reportProgress: true,\n      // 启用进度报告\n      withCredentials: false // 携带跨域凭证\n    });\n    this.http.request(req).pipe(filter(e => e instanceof HttpResponse)).subscribe({\n      next(res) {\n        self.fileUploading = false;\n        self.fileList = [];\n        if (res['ok']) {\n          if (res['body']['ok']) {\n            self.showState(ModalTypeEnum.success, '文件上传成功');\n            self.onQueryData();\n          } else {\n            self.showState(ModalTypeEnum.error, res['body']?.['msg'] ?? '文件上传失败');\n          }\n        } else {\n          self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\n        }\n      },\n      error(err) {\n        self.fileUploading = false;\n        self.showState(ModalTypeEnum.error, err.msg);\n      }\n    });\n  }\n  // 附件预览\n  onFilePreview(ATTACH_ID) {\n    console.log('查看进来了么');\n    let condition = {\n      ATTACH_ID: ATTACH_ID,\n      SESSION_USERS: this.USER_SESSION\n    };\n    let downloadurl = this.cwfBusContext.getGlobalData().serverUrl + '/' + this.cwfBusContext.getGlobalData().serviceName['bc'].en + '/spefiledownload' + '?operation=download&actionId=' + ATTACH_ID;\n    console.log('查看下载url', downloadurl);\n    //创建导出iframe\n    let element = document.createElement('iframe');\n    element.setAttribute('style', 'display:none');\n    element.setAttribute('name', 'download');\n    let downloadiframe = document.body.appendChild(element);\n    // 创建下在form\n    let formElement = document.createElement('form');\n    formElement.setAttribute('style', 'display:none');\n    formElement.setAttribute('name', 'downloadForm');\n    let downloadForm = downloadiframe.appendChild(formElement);\n    downloadForm.setAttribute('target', 'download');\n    downloadForm.setAttribute('method', 'post');\n    //设置请求地址\n    downloadForm.setAttribute('action', downloadurl);\n    // 构造传递条件\n    for (let property in condition) {\n      let inputElement = document.createElement('input');\n      inputElement.setAttribute('type', 'hidden');\n      inputElement.setAttribute('name', property);\n      inputElement.setAttribute('value', condition[property]);\n      downloadForm.appendChild(inputElement);\n    }\n    //提交\n    downloadForm.submit();\n  }\n  onQueryData() {\n    this.fileloading = true;\n    let requestData = {\n      pkId: this.editForm.controls['id'].value,\n      module: 'tasModule',\n      businessTypeCode: 'tas_fepconfig'\n    };\n    this.fileStore.clearData();\n    this.cwfRestfulService.post('/storage/new/list', {\n      'data': requestData\n    }, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.fileStore.loadDatas(rps.data ?? []);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).finally(() => {\n      this.fileloading = false;\n    });\n  }\n  // 文件删除\n  onFileDel(id) {\n    this.cwfRestfulService.delete('/storage/new/' + id, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '删除成功');\n        this.onQueryData();\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  // 文件下载\n  onFileDownload(id) {\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          window.open(downloadUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  // 文件预览\n  onPreview(fileId) {\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [fileId], 600000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\n          window.open(previewUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  preview(data) {\n    var file_name = data['originalFileName'];\n    const fileExtension = file_name.split('.').pop();\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\n          window.open(previewUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n    // this.uploadService.downloadFile(this.serviceName, [data['attachmentId']], 1000, false).then((res) => {\n    //   if (fileExtension.toLocaleUpperCase() == \"PDF\" || fileExtension.toLocaleUpperCase() == \"JPG\" || fileExtension.toLocaleUpperCase() == \"PNG\") {\n    //     window.open(res['data'][0])\n    //   }\n    // })\n  }\n  static {\n    this.ɵfac = function FepconfigEditComponent_Factory(t) {\n      return new (t || FepconfigEditComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.NzMessageService), i0.ɵɵdirectiveInject(i5.CwfUploadService), i0.ɵɵdirectiveInject(i6.CwfRestfulService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FepconfigEditComponent,\n      selectors: [[\"fepconfig-edit\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 117,\n      vars: 92,\n      consts: [[\"fileitem\", \"\"], [1, \"system-index-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"10\", 1, \"list-title\"], [3, \"src\"], [1, \"title\"], [\"nzSpan\", \"14\", \"class\", \"list-button\", 4, \"ngIf\"], [\"nz-form\", \"\", 2, \"margin-top\", \"20px\", 3, \"formGroup\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"10\"], [\"nzRequired\", \"\", 2, \"width\", \"120px\"], [\"formControlName\", \"portId\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"nzOnSearch\", \"nzPlaceHolder\", \"nzShowSearch\"], [3, \"nzLabel\", \"nzValue\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"120px\"], [\"formControlName\", \"wharfId\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"nzPlaceHolder\", \"nzShowSearch\"], [\"nz-col\", \"\", \"nzSpan\", \"6\"], [\"nz-input\", \"\", \"formControlName\", \"fepIp\", 3, \"placeholder\", \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"fepPort\", 3, \"placeholder\", \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"userName\", 3, \"placeholder\", \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"userPassword\", 3, \"placeholder\", \"readonly\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"formControlName\", \"orgId\", 3, \"ngModelChange\", \"nzPlaceHolder\", \"nzShowSearch\"], [\"nz-input\", \"\", \"formControlName\", \"filePath\", 3, \"placeholder\", \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"interfaceAddr\", 3, \"placeholder\", \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"remark\", 2, \"height\", \"100px\", \"resize\", \"none\", 3, \"placeholder\", \"readonly\"], [1, \"list-button\"], [2, \"width\", \"8%\", \"float\", \"left\", \"margin-top\", \"10px\"], [2, \"height\", \"10px\", \"width\", \"10px\", \"color\", \"rgb(20, 96, 237)\", \"border\", \"2px solid\"], [3, \"nzFileListChange\", \"nzFileList\", \"nzBeforeUpload\", \"nzShowUploadList\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"primary\", 3, \"nzLoading\"], [\"nz-icon\", \"\", \"nzType\", \"import\", \"nzTheme\", \"outline\"], [3, \"nzFrontPagination\", \"nzData\", \"nzScroll\", \"nzLoading\"], [\"nzWidth\", \"75px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"300px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"260px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzRight\", \"\", \"nzWidth\", \"140px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\"], [4, \"ngFor\", \"ngForOf\"], [\"nzSpan\", \"14\", 1, \"list-button\"], [\"nz-button\", \"\", 3, \"click\", \"nzLoading\", \"nzType\"], [\"nz-button\", \"\", 3, \"click\"], [3, \"nzLabel\", \"nzValue\"], [\"nz-tooltip\", \"\", 3, \"nzAlign\"], [\"nzRight\", \"\"], [\"nz-popconfirm\", \"\", \"nzPopconfirmTitle\", \"\\u662F\\u5426\\u5220\\u9664\\u6B64\\u6570\\u636E\\uFF1F\", 2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"nzOnConfirm\"], [2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"click\"]],\n      template: function FepconfigEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 1)(1, \"nz-row\")(2, \"nz-col\", 2);\n          i0.ɵɵelement(3, \"svg-icon\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, FepconfigEditComponent_nz_col_7_Template, 7, 8, \"nz-col\", 5)(8, FepconfigEditComponent_nz_col_8_Template, 4, 3, \"nz-col\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"form\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"nz-form-item\")(13, \"nz-form-label\", 9);\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nz-form-control\")(17, \"nz-select\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function FepconfigEditComponent_Template_nz_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPortChange($event));\n          })(\"nzOnSearch\", function FepconfigEditComponent_Template_nz_select_nzOnSearch_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchChange($event));\n          });\n          i0.ɵɵtemplate(18, FepconfigEditComponent_nz_option_18_Template, 1, 2, \"nz-option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"nz-form-item\")(21, \"nz-form-label\", 12);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nz-form-control\")(25, \"nz-select\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function FepconfigEditComponent_Template_nz_select_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onWharfChange($event));\n          });\n          i0.ɵɵtemplate(26, FepconfigEditComponent_nz_option_26_Template, 1, 2, \"nz-option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"nz-form-item\")(29, \"nz-form-label\", 9);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"nz-form-control\");\n          i0.ɵɵelement(33, \"input\", 15);\n          i0.ɵɵpipe(34, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 14)(36, \"nz-form-item\")(37, \"nz-form-label\", 12);\n          i0.ɵɵtext(38);\n          i0.ɵɵpipe(39, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nz-form-control\");\n          i0.ɵɵelement(41, \"input\", 16);\n          i0.ɵɵpipe(42, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 14)(44, \"nz-form-item\")(45, \"nz-form-label\", 9);\n          i0.ɵɵtext(46);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"nz-form-control\");\n          i0.ɵɵelement(49, \"input\", 17);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 14)(52, \"nz-form-item\")(53, \"nz-form-label\", 9);\n          i0.ɵɵtext(54);\n          i0.ɵɵpipe(55, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"nz-form-control\");\n          i0.ɵɵelement(57, \"input\", 18);\n          i0.ɵɵpipe(58, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 19)(60, \"nz-form-item\")(61, \"nz-form-label\", 9);\n          i0.ɵɵtext(62);\n          i0.ɵɵpipe(63, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"nz-form-control\")(65, \"nz-select\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function FepconfigEditComponent_Template_nz_select_ngModelChange_65_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCompanyChange($event));\n          });\n          i0.ɵɵtemplate(66, FepconfigEditComponent_nz_option_66_Template, 1, 2, \"nz-option\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"div\", 19)(68, \"nz-form-item\")(69, \"nz-form-label\", 12);\n          i0.ɵɵtext(70);\n          i0.ɵɵpipe(71, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"nz-form-control\");\n          i0.ɵɵelement(73, \"input\", 21);\n          i0.ɵɵpipe(74, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 19)(76, \"nz-form-item\")(77, \"nz-form-label\", 12);\n          i0.ɵɵtext(78);\n          i0.ɵɵpipe(79, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"nz-form-control\");\n          i0.ɵɵelement(81, \"input\", 22);\n          i0.ɵɵpipe(82, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 19)(84, \"nz-form-item\")(85, \"nz-form-label\", 12);\n          i0.ɵɵtext(86);\n          i0.ɵɵpipe(87, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"nz-form-control\");\n          i0.ɵɵelement(89, \"textarea\", 23);\n          i0.ɵɵpipe(90, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(91, \"h4\")(92, \"div\", 24)(93, \"div\", 25);\n          i0.ɵɵelement(94, \"span\", 26);\n          i0.ɵɵtext(95, \"\\u00A0\\u6587\\u4EF6\\u8D44\\u6599\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"nz-upload\", 27);\n          i0.ɵɵtwoWayListener(\"nzFileListChange\", function FepconfigEditComponent_Template_nz_upload_nzFileListChange_96_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.fileList, $event) || (ctx.fileList = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(97, \"button\", 28);\n          i0.ɵɵelement(98, \"span\", 29);\n          i0.ɵɵelementStart(99, \"span\");\n          i0.ɵɵtext(100, \"\\u65B0\\u5EFA\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(101, \"nz-table\", 30, 0)(103, \"thead\")(104, \"tr\")(105, \"th\", 31);\n          i0.ɵɵtext(106, \" \\u5E8F\\u53F7 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"th\", 32);\n          i0.ɵɵtext(108, \" \\u6587\\u4EF6\\u540D\\u79F0 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"th\", 33);\n          i0.ɵɵtext(110, \" \\u4E0A\\u4F20\\u4EBA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"th\", 33);\n          i0.ɵɵtext(112, \" \\u4E0A\\u4F20\\u65F6\\u95F4 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"th\", 34);\n          i0.ɵɵtext(114, \" \\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(115, \"tbody\");\n          i0.ɵɵtemplate(116, FepconfigEditComponent_tr_116_Template, 20, 11, \"tr\", 35);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const fileitem_r11 = i0.ɵɵreference(102);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(89, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", \"assets/img/icons/filter.svg\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 53, \"TAS.FEP_CONFIG_EDIT\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(90, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 55, \"TAS.PORT_NM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.portData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 57, \"TAS.WHARF_NM\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.wharfData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 59, \"TAS.FEP_IP\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(34, 61, \"TAS.FEP_IP\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(39, 63, \"TAS.FEP_PORT\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(42, 65, \"TAS.FEP_PORT\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(47, 67, \"TAS.USER_NAME\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(50, 69, \"TAS.USER_NAME\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 71, \"TAS.USER_PASSWORD\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(58, 73, \"TAS.USER_PASSWORD\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(63, 75, \"TAS.ORGLEVEL\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.companyData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(71, 77, \"TAS.FILE_PATH\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(74, 79, \"TAS.FILE_PATH\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(79, 81, \"TAS.INTERFACE\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(82, 83, \"TAS.INTERFACE\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(87, 85, \"TAS.FEP_REMARK\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(90, 87, \"TAS.FEP_REMARK\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"nzFileList\", ctx.fileList);\n          i0.ɵɵproperty(\"nzBeforeUpload\", ctx.fileBeforeUpload)(\"nzShowUploadList\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzLoading\", ctx.fileUploading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzFrontPagination\", false)(\"nzData\", ctx.fileStore.getDatas())(\"nzScroll\", i0.ɵɵpureFunction0(91, _c2))(\"nzLoading\", ctx.fileloading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", fileitem_r11.data);\n        }\n      },\n      dependencies: [i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i8.NgForOf, i8.NgIf, i7.FormGroupDirective, i7.FormControlName, i9.SvgIconComponent, i10.NzColDirective, i10.NzRowDirective, i11.NzFormDirective, i11.NzFormItemComponent, i11.NzFormLabelComponent, i11.NzFormControlComponent, i12.NzButtonComponent, i13.ɵNzTransitionPatchDirective, i14.NzWaveDirective, i15.NzInputDirective, i16.NzOptionComponent, i16.NzSelectComponent, i17.NzCardComponent, i18.NzPopconfirmDirective, i19.NzTableComponent, i19.NzTableCellDirective, i19.NzThMeasureDirective, i19.NzTheadComponent, i19.NzTbodyComponent, i19.NzTrDirective, i19.NzCellFixedDirective, i19.NzCellAlignDirective, i20.NzTooltipDirective, i21.NzIconDirective, i22.NzUploadComponent, i8.DatePipe, i23.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CwfBaseCrud", "DialogResultEnum", "ModalTypeEnum", "PageModeEnum", "FormControl", "Validators", "BASE_T_FEPCONFIG", "filter", "BASE_T_PARFILE", "HttpHeaders", "HttpRequest", "HttpResponse", "i0", "ɵɵelementStart", "ɵɵlistener", "FepconfigEditComponent_nz_col_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "saveData", "ɵɵtext", "ɵɵelementEnd", "FepconfigEditComponent_nz_col_7_Template_button_click_4_listener", "onClose", "ɵɵadvance", "ɵɵproperty", "loading", "ɵɵtextInterpolate", "ɵɵpipeBind1", "FepconfigEditComponent_nz_col_8_Template_button_click_1_listener", "_r4", "ɵɵelement", "option_r5", "label", "value", "option_r6", "option_r7", "FepconfigEditComponent_tr_116_Template_a_nzOnConfirm_14_listener", "data_r9", "_r8", "$implicit", "onFileDel", "id", "FepconfigEditComponent_tr_116_Template_a_click_16_listener", "preview", "FepconfigEditComponent_tr_116_Template_a_click_18_listener", "onFileDownload", "attachmentId", "i_r10", "originalFileName", "createdUserName", "translate", "ɵɵpipeBind2", "createdTime", "FepconfigEditComponent", "constructor", "cwfBusContextService", "gol", "http", "message", "uploadService", "cwfRestfulService", "mainStore", "editStores", "serviceName", "portData", "wharfData", "companyData", "fileList", "fileUploading", "fileloading", "fileStore", "baseServiceName", "USER_SESSION", "cwfBusContext", "getContext", "getSessionId", "fileUploadUrl", "serverUrl", "en", "disabledEditForm", "ALL", "fileBeforeUpload", "file", "console", "log", "editForm", "controls", "showState", "error", "length", "size", "chunkSize", "params", "pkId", "expireTime", "module", "businessTypeCode", "businessTypeName", "uploadData", "fileUpload", "initEdit", "nullValidator", "portId", "required", "portCd", "portNm", "portNmEn", "wharfId", "wharfCd", "wharfNm", "wharfNmEn", "fepIp", "max<PERSON><PERSON><PERSON>", "fepPort", "userName", "userPassword", "filePath", "interfaceAddr", "orgId", "orgNm", "entLevelNo", "orgLevelNo", "remark", "created<PERSON>ser", "modifiedUser", "modifiedTime", "isDelete", "version", "onShow", "_this", "_asyncToGenerator", "openParam", "Custom", "get", "then", "rps", "ok", "patchValue", "data", "openMainPage", "Add", "onQueryData", "getOrgData", "onQueryPort", "onQueryWharf", "post", "map", "item", "msg", "selected<PERSON><PERSON><PERSON>", "requestData", "page", "countryNm", "onSearchChange", "url", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "getNotify", "showLoading", "removeControl", "getRawValue", "removeShow", "success", "getMainController", "put", "checkSave", "showConfirm", "getMsgi18nString", "state", "yes", "no", "cancel", "getEditFromDisabled", "feild<PERSON><PERSON>", "onCompanyChange", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "model", "find", "rdata", "type", "orgCode", "orgName", "companyCode", "companyName", "onPortChange", "trim", "onWharfChange", "self", "formData", "FormData", "append", "headers", "req", "reportProgress", "withCredentials", "request", "pipe", "e", "subscribe", "next", "res", "err", "onFilePreview", "ATTACH_ID", "condition", "SESSION_USERS", "downloadurl", "getGlobalData", "element", "document", "createElement", "setAttribute", "downloadiframe", "body", "append<PERSON><PERSON><PERSON>", "formElement", "downloadForm", "property", "inputElement", "submit", "clearData", "loadDatas", "finally", "delete", "downloadFile", "downloadUrl", "window", "open", "onPreview", "fileId", "previewUrl", "btoa", "file_name", "fileExtension", "split", "pop", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "HttpClient", "i4", "NzMessageService", "i5", "CwfUploadService", "i6", "CwfRestfulService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "FepconfigEditComponent_Template", "rf", "ctx", "ɵɵtemplate", "FepconfigEditComponent_nz_col_7_Template", "FepconfigEditComponent_nz_col_8_Template", "FepconfigEditComponent_Template_nz_select_ngModelChange_17_listener", "$event", "_r1", "FepconfigEditComponent_Template_nz_select_nzOnSearch_17_listener", "FepconfigEditComponent_nz_option_18_Template", "FepconfigEditComponent_Template_nz_select_ngModelChange_25_listener", "FepconfigEditComponent_nz_option_26_Template", "FepconfigEditComponent_Template_nz_select_ngModelChange_65_listener", "FepconfigEditComponent_nz_option_66_Template", "ɵɵtwoWayListener", "FepconfigEditComponent_Template_nz_upload_nzFileListChange_96_listener", "ɵɵtwoWayBindingSet", "FepconfigEditComponent_tr_116_Template", "ɵɵpureFunction0", "_c0", "_c1", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "getDatas", "_c2", "fileitem_r11"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\fepconfig\\fepconfig-edit\\fepconfig-edit.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\fepconfig\\fepconfig-edit\\fepconfig-edit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CwfBaseCrud, CwfBusContextService, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { responseInterface, UploadParams } from 'app/interface/request.interface';\r\nimport { BASE_T_FEPCONFIG } from '@store/BCD/BASE_T_FEPCONFIG';\r\nimport { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';\r\nimport { CwfUploadService } from '@service/cwfuploadService';\r\nimport { filter } from 'rxjs/operators';\r\nimport {NzMessageService} from \"ng-zorro-antd/message\";\r\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\r\nimport {\r\n  HttpClient,\r\n  HttpHeaders,\r\n  HttpRequest,\r\n  HttpResponse,\r\n} from '@angular/common/http';\r\n\r\n@Component({\r\n  selector: 'fepconfig-edit',\r\n  templateUrl: './fepconfig-edit.component.html'\r\n})\r\n\r\nexport class FepconfigEditComponent extends CwfBaseCrud {\r\n  editForm: FormGroup;\r\n  mainStore = new BASE_T_FEPCONFIG();\r\n  editStores = [this.mainStore];\r\n  id = \"\";\r\n  serviceName = \"\";\r\n  portData = [];\r\n  wharfData = [];\r\n  companyData = [];\r\n  fileList: NzUploadFile[] = [];\r\n  fileUploading: boolean = false;\r\n  fileloading = false;\r\n  fileStore = new BASE_T_PARFILE();\r\n  baseServiceName = '';\r\n  USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\r\n  fileUploadUrl =\r\n    this.gol.serverUrl +\r\n    '/' +\r\n    this.gol.serviceName['tas'].en +\r\n    '/storage/new/upload'; // 文件上传请求服务地址\r\n  // 定义禁用编辑表单的对象，用于控制表单的禁用状态\r\n  // ALL属性表示是否全局禁用编辑表单的功能\r\n  disabledEditForm: Object = {\r\n    ALL: false,\r\n  };\r\n  constructor(cwfBusContextService: CwfBusContextService,\r\n    private gol: GlobalDataService,\r\n    private http: HttpClient,\r\n    private message: NzMessageService,\r\n    private uploadService: CwfUploadService,\r\n    private cwfRestfulService: CwfRestfulService) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  /**\r\n * 初始化查询条件\r\n */\r\n  initEdit() {\r\n    return {\r\n      id: new FormControl('', Validators.nullValidator), // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\r\n      portId: new FormControl('', Validators.required),\r\n      portCd: new FormControl('', Validators.nullValidator),\r\n      portNm: new FormControl('', Validators.nullValidator),\r\n      portNmEn: new FormControl('', Validators.nullValidator),\r\n      wharfId: new FormControl('', Validators.required),\r\n      wharfCd: new FormControl('', Validators.nullValidator),\r\n      wharfNm: new FormControl('', Validators.nullValidator),\r\n      wharfNmEn: new FormControl('', Validators.nullValidator),\r\n      fepIp: new FormControl('', [Validators.required, Validators.maxLength(36)]),\r\n      fepPort: new FormControl('', [Validators.nullValidator, Validators.maxLength(12)]),\r\n      userName: new FormControl('', [Validators.required, Validators.maxLength(105)]),\r\n      userPassword: new FormControl('', [Validators.required, Validators.maxLength(105)]),\r\n      filePath: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\r\n      interfaceAddr: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\r\n      orgId: new FormControl('', Validators.required),\r\n      orgNm: new FormControl('', Validators.required),\r\n      entLevelNo: new FormControl('', Validators.nullValidator),\r\n      orgLevelNo: new FormControl('', Validators.nullValidator),\r\n      remark: new FormControl('', [Validators.nullValidator, Validators.maxLength(75)]),\r\n      createdUser: new FormControl('', Validators.nullValidator), // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      createdTime: new FormControl('', Validators.nullValidator), // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedUser: new FormControl('', Validators.nullValidator), // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedTime: new FormControl('', Validators.nullValidator), // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      isDelete: new FormControl('', Validators.nullValidator), // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\r\n      version: new FormControl('', Validators.nullValidator), // 版本号，初始值为空，验证规则为nullValidator（允许为空）\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 页面加载完成后出发\r\n   */\r\n  async onShow() {\r\n    this.baseServiceName = this.gol.serviceName['tas'].en;\r\n    this.serviceName = this.gol.serviceName['main'].en;\r\n    if (this.openParam['state'] === PageModeEnum.Custom) {\r\n      this.disabledEditForm['ALL'] = true;\r\n      this.cwfRestfulService.get('/fep_config/' + this.openParam['id'],this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.editForm.patchValue(rps.data);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\r\n          this.openMainPage();\r\n        }\r\n      });\r\n    }\r\n    if(this.openParam['state'] !== PageModeEnum.Add){\r\n      this.onQueryData();\r\n    }\r\n    this.getOrgData();\r\n    this.onQueryPort(this.editForm.controls['portCd']?.value);\r\n    this.onQueryWharf();\r\n  }\r\n\r\n  onQueryWharf() {\r\n    this.cwfRestfulService\r\n      .post(\r\n        '/wharf/getAllWharfInfo',\r\n        {portId: this.editForm.controls['portId']?.value},\r\n        this.gol.serviceName['bc'].en\r\n      )\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          // 更新 wharfData 数组\r\n          this.wharfData = rps.data.map((item) => ({\r\n            label: item.wharfCd + '/' + item.wharfNm + '/' + item.portNm,\r\n            value: item.id,\r\n            wharfCd: item.wharfCd,\r\n            wharfNm: item.wharfNm,\r\n            wharfNmEn: item.wharfNmEn\r\n          }));\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n  }\r\n\r\n  onQueryPort(selectedValue: any) {\r\n    let requestData = {\r\n      portNm: selectedValue,\r\n      page: 1,\r\n      size: 100,\r\n    };\r\n    this.cwfRestfulService\r\n      .post(\r\n        '/port/getAllPortInfo',\r\n        requestData,\r\n        this.gol.serviceName['bc'].en\r\n      )\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          // 更新 portData 数组\r\n          this.portData = rps.data.map((item) => ({\r\n            label: item.portCd + '/' + item.portNm + '/' + item.countryNm,\r\n            value: item.id,\r\n            portCd: item.portCd,\r\n            portNm: item.portNm,\r\n            portNmEn: item.portNmEn\r\n          }));\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n  }\r\n\r\n  onSearchChange(selectedValue: any): void {\r\n    this.onQueryPort(selectedValue);\r\n  }\r\n\r\n  /**\r\n   * desc:保存数据\r\n   * by:\r\n   */\r\n  saveData() {\r\n    const url = '/fep_config';\r\n    for (const i in this.editForm.controls) {\r\n      this.editForm.controls[i].markAsDirty();\r\n      this.editForm.controls[i].updateValueAndValidity();\r\n    }\r\n    if (this.editForm.invalid) {\r\n      return;\r\n    }\r\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\r\n    this.loading = true;\r\n    if (this.openParam['state'] === PageModeEnum.Add) {\r\n      this.editForm.removeControl('id');\r\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    } else {\r\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    if (this.checkSave()) {\r\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\r\n        switch (state) {\r\n          case DialogResultEnum.yes:   // 是\r\n            this.saveData();\r\n            return true;\r\n          case DialogResultEnum.no:    // 否\r\n            this.openMainPage({});\r\n            return true;\r\n          case DialogResultEnum.cancel: // 取消\r\n            return false;\r\n        }\r\n      });\r\n    } else {\r\n      this.openMainPage({});\r\n      return true;\r\n    }\r\n  }\r\n  getEditFromDisabled(feildName) {\r\n    return this.disabledEditForm[feildName];\r\n  }\r\n\r\n  onCompanyChange(selectedValues: any[]): void {\r\n    if (selectedValues == null) {\r\n      // 清理数据\r\n      this.editForm.controls['orgLevelNo'].setValue(\"\");\r\n      this.editForm.controls['orgNm'].setValue(\"\");\r\n      this.editForm.controls['entLevelNo'].setValue(\"\");\r\n    } else {\r\n      let model = this.companyData.find(item => item.value === selectedValues);\r\n      this.editForm.controls['orgLevelNo'].setValue(model.orgLevelNo);\r\n      this.editForm.controls['entLevelNo'].setValue(model.entLevelNo);\r\n      this.editForm.controls['orgNm'].setValue(model.orgNm);\r\n    }\r\n  }\r\n\r\n  getOrgData() {\r\n    const rdata = { type: null };\r\n    this.cwfRestfulService\r\n        .post(\r\n          '/org/getRelateChildren',\r\n          rdata,\r\n          this.gol.serviceName['main'].en\r\n        )\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            // 更新 companyData 数组\r\n            this.companyData = rps.data.map((item) => ({\r\n              label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\r\n              value: item.orgId,\r\n              orgLevelNo: item.orgCode,\r\n              orgNm: item.orgName,\r\n              entLevelNo: item.companyCode\r\n            }));\r\n          }else{\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n    })\r\n  }\r\n\r\n  onPortChange(selectedValues: any[]): void {\r\n    if (selectedValues == null) {\r\n      // 清理数据\r\n      this.editForm.controls['portId'].setValue(\"\");\r\n      this.editForm.controls['portCd'].setValue(\"\");\r\n      this.editForm.controls['portNm'].setValue(\"\");\r\n      this.editForm.controls['portNmEn'].setValue(\"\");\r\n    } else {\r\n      let model = this.portData.find(item => item.value === selectedValues);\r\n      this.editForm.controls['portCd'].setValue(model.portCd?.trim());\r\n      this.editForm.controls['portNm'].setValue(model.portNm?.trim());\r\n      this.editForm.controls['portNmEn'].setValue(model.portNmEn?.trim());\r\n    }\r\n    this.editForm.controls['wharfId'].setValue(\"\");\r\n    this.onQueryWharf();\r\n  }\r\n\r\n  onWharfChange(selectedValues: any[]): void {\r\n    if (selectedValues == null) {\r\n      // 清理数据\r\n      this.editForm.controls['wharfCd'].setValue(\"\");\r\n      this.editForm.controls['wharfNm'].setValue(\"\");\r\n      this.editForm.controls['wharfNmEn'].setValue(\"\");\r\n    } else {\r\n      let model = this.wharfData.find(item => item.value === selectedValues);\r\n      this.editForm.controls['wharfNm'].setValue(model.wharfNm?.trim());\r\n      this.editForm.controls['wharfNmEn'].setValue(model.wharfNmEn?.trim());\r\n    }\r\n  }\r\n\r\n  // 导入文件前\r\n  fileBeforeUpload = (file: NzUploadFile): boolean => {\r\n      console.log('查看id', this.editForm.controls['id'].value);\r\n      if (\r\n        this.editForm.controls['id'].value == null ||\r\n        this.editForm.controls['id'].value == ''\r\n      ) {\r\n        this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\r\n        return false;\r\n      }\r\n      this.fileList = [file];\r\n      console.log(this.fileList[0]);\r\n      if (this.fileList.length != 1) {\r\n        this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\r\n        return false;\r\n      }\r\n      if (this.fileList[0].size > this.uploadService.chunkSize) {\r\n        // 文件大于5M，使用分片上传\r\n        let params: UploadParams = {\r\n          pkId: this.editForm.controls['id'].value,\r\n          expireTime: 60000,\r\n          module: 'tasModule',\r\n          businessTypeCode: 'tas_fepconfig',\r\n          businessTypeName: 'tas_fepconfig',\r\n        };\r\n        this.uploadService.uploadData(this.fileList[0], params);\r\n      } else {\r\n        // 文件小于5M，普通上传\r\n        this.fileUpload();\r\n      }\r\n      return false;\r\n    };\r\n  \r\n    // 普通上传\r\n    fileUpload() {\r\n      let self = this;\r\n      const formData = new FormData();\r\n      formData.append('file', this.fileList[0] as any);\r\n      formData.append('pkId', this.editForm.controls['id'].value);\r\n      formData.append('module', 'tasModule');\r\n      formData.append('businessTypeCode', 'tas_fepconfig');\r\n      formData.append('businessTypeName', 'tas_fepconfig');\r\n      formData.append('businessTypeNameEn', 'tas_fepconfig');\r\n      formData.append('recordTypeCode', 'tas_fepconfig');\r\n      formData.append('recordTypeName', 'tas_fepconfig');\r\n      formData.append('recordTypeNameEn', 'tas_fepconfig');\r\n      formData.append('serviceTypeCode', 'tas_fepconfig');\r\n      formData.append('serviceTypeName', 'tas_fepconfig');\r\n      formData.append('serviceTypeNameEn', 'tas_fepconfig');\r\n      const headers = new HttpHeaders({\r\n        'x-ccf-token': this.USER_SESSION,\r\n      });\r\n      const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\r\n        headers: headers,\r\n        reportProgress: true, // 启用进度报告\r\n        withCredentials: false, // 携带跨域凭证\r\n      });\r\n      this.http\r\n        .request(req)\r\n        .pipe(filter((e) => e instanceof HttpResponse))\r\n        .subscribe({\r\n          next(res): any {\r\n            self.fileUploading = false;\r\n            self.fileList = [];\r\n            if (res['ok']) {\r\n              if (res['body']['ok']) {\r\n                self.showState(ModalTypeEnum.success, '文件上传成功');\r\n                self.onQueryData();\r\n              } else {\r\n                self.showState(\r\n                  ModalTypeEnum.error,\r\n                  res['body']?.['msg'] ?? '文件上传失败'\r\n                );\r\n              }\r\n            } else {\r\n              self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\r\n            }\r\n          },\r\n          error(err): any {\r\n            self.fileUploading = false;\r\n            self.showState(ModalTypeEnum.error, err.msg);\r\n          },\r\n        });\r\n    }\r\n  \r\n    // 附件预览\r\n    onFilePreview(ATTACH_ID) {\r\n      console.log('查看进来了么');\r\n      let condition = { ATTACH_ID: ATTACH_ID, SESSION_USERS: this.USER_SESSION };\r\n      let downloadurl =\r\n        this.cwfBusContext.getGlobalData().serverUrl +\r\n        '/' +\r\n        this.cwfBusContext.getGlobalData().serviceName['bc'].en +\r\n        '/spefiledownload' +\r\n        '?operation=download&actionId=' +\r\n        ATTACH_ID;\r\n      console.log('查看下载url', downloadurl);\r\n      //创建导出iframe\r\n      let element = document.createElement('iframe');\r\n      element.setAttribute('style', 'display:none');\r\n      element.setAttribute('name', 'download');\r\n      let downloadiframe = document.body.appendChild(element);\r\n      // 创建下在form\r\n      let formElement = document.createElement('form');\r\n      formElement.setAttribute('style', 'display:none');\r\n      formElement.setAttribute('name', 'downloadForm');\r\n      let downloadForm = downloadiframe.appendChild(formElement);\r\n      downloadForm.setAttribute('target', 'download');\r\n      downloadForm.setAttribute('method', 'post');\r\n      //设置请求地址\r\n      downloadForm.setAttribute('action', downloadurl);\r\n      // 构造传递条件\r\n      for (let property in condition) {\r\n        let inputElement = document.createElement('input');\r\n        inputElement.setAttribute('type', 'hidden');\r\n        inputElement.setAttribute('name', property);\r\n        inputElement.setAttribute('value', condition[property]);\r\n        downloadForm.appendChild(inputElement);\r\n      }\r\n      //提交\r\n      downloadForm.submit();\r\n    }\r\n  \r\n    onQueryData() {\r\n      this.fileloading = true;\r\n      let requestData = {\r\n        pkId: this.editForm.controls['id'].value,\r\n        module: 'tasModule',\r\n        businessTypeCode: 'tas_fepconfig',\r\n      };\r\n      this.fileStore.clearData();\r\n      this.cwfRestfulService\r\n        .post('/storage/new/list', {'data': requestData}, this.baseServiceName)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.fileStore.loadDatas(rps.data ?? []);\r\n          } else {\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.fileloading = false;\r\n        });\r\n    }\r\n  \r\n      // 文件删除\r\n    onFileDel(id) {\r\n      this.cwfRestfulService\r\n        .delete('/storage/new/' + id, this.baseServiceName)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.showState(ModalTypeEnum.success, '删除成功');\r\n            this.onQueryData();\r\n          } else {\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n          }\r\n        });\r\n    }\r\n  \r\n    // 文件下载\r\n    onFileDownload(id) {\r\n      this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          const downloadUrl = rps.data[0];\r\n          if (downloadUrl) {\r\n            window.open(downloadUrl, '_blank');\r\n          } else {\r\n            this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n          }\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n\r\n    // 文件预览\r\n    onPreview(fileId) {\r\n        this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [fileId], 600000, true).then((rps: responseInterface) => {\r\n            if (rps.ok) {\r\n                const downloadUrl = rps.data[0];\r\n                if (downloadUrl) {\r\n                    let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\r\n                    window.open(previewUrl, '_blank');\r\n                } else {\r\n                    this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n                }\r\n            } else {\r\n                this.showState(ModalTypeEnum.error, rps.msg);\r\n            }\r\n        });\r\n    }\r\n\r\n  preview(data) {\r\n    var file_name = data['originalFileName'];\r\n    const fileExtension = file_name.split('.').pop();\r\n        this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n            const downloadUrl = rps.data[0];\r\n            if (downloadUrl) {\r\n                let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\r\n                window.open(previewUrl, '_blank');\r\n            } else {\r\n                this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n            }\r\n        } else {\r\n            this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n    });\r\n\r\n\r\n    // this.uploadService.downloadFile(this.serviceName, [data['attachmentId']], 1000, false).then((res) => {\r\n    //   if (fileExtension.toLocaleUpperCase() == \"PDF\" || fileExtension.toLocaleUpperCase() == \"JPG\" || fileExtension.toLocaleUpperCase() == \"PNG\") {\r\n    //     window.open(res['data'][0])\r\n    //   }\r\n    // })\r\n  }\r\n\r\n\r\n}\r\n", "<nz-card class=\"system-index-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"10\" class=\"list-title\">\r\n      <svg-icon [src]=\"'assets/img/icons/filter.svg'\"></svg-icon>\r\n      <div class=\"title\">{{'TAS.FEP_CONFIG_EDIT' | translate}}</div>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"!getEditFromDisabled('ALL')\">\r\n      <button nz-button [nzLoading]=\"loading\" (click)=\"saveData()\" [nzType]=\"'primary'\">{{'FP.SAVE' | translate}}</button>\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"getEditFromDisabled('ALL')\">\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n  </nz-row>\r\n  <!-- 查询条件表单 -->\r\n  <form style=\"margin-top: 20px;\" nz-form [formGroup]=\"editForm\">\r\n    <div nz-row [nzGutter]=\"[8,10]\">\r\n\r\n      <div nz-col nzSpan=\"10\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">{{'TAS.PORT_NM' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"portId\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onPortChange($event)\" (nzOnSearch)=\"onSearchChange($event)\" nzAllowClear>\r\n              <nz-option *ngFor=\"let option of portData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"10\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.WHARF_NM' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"wharfId\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onWharfChange($event)\" nzAllowClear>\r\n              <nz-option *ngFor=\"let option of wharfData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">{{'TAS.FEP_IP' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.FEP_IP' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"fepIp\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.FEP_PORT' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.FEP_PORT' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"fepPort\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">{{'TAS.USER_NAME' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.USER_NAME' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"userName\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">{{'TAS.USER_PASSWORD' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.USER_PASSWORD' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"userPassword\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">{{'TAS.ORGLEVEL' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"orgId\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n              (ngModelChange)=\"onCompanyChange($event)\">\r\n              <nz-option *ngFor=\"let option of companyData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.FILE_PATH' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.FILE_PATH' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"filePath\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.INTERFACE' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input placeholder=\"{{'TAS.INTERFACE' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"interfaceAddr\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!--remark :正文 -->\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.FEP_REMARK' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <textarea nz-input placeholder=\"{{'TAS.FEP_REMARK' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n                      formControlName=\"remark\" style=\"height: 100px;resize: none;\"></textarea>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n    </div>\r\n  </form>\r\n  <h4>  \r\n    <div class=\"list-button\">\r\n      <div style=\"width: 8%;float:left; margin-top: 10px;\"><span style=\"height: 10px;width: 10px;color: rgb(20, 96, 237);border: 2px solid;\"></span>&nbsp;文件资料</div>\r\n      <nz-upload [(nzFileList)]=\"fileList\" [nzBeforeUpload]=\"fileBeforeUpload\" [nzShowUploadList]=\"false\">\r\n        <button type=\"button\" nz-button nzType=\"primary\" [nzLoading]=\"fileUploading\">\r\n          <span nz-icon nzType=\"import\" nzTheme=\"outline\"></span>\r\n          <span>新建</span>\r\n        </button>\r\n      </nz-upload>\r\n    </div>\r\n  </h4>\r\n  <nz-table #fileitem [nzFrontPagination]=\"false\" [nzData]=\"fileStore.getDatas()\"\r\n    [nzScroll]=\"{ x: '1000px' }\" [nzLoading]=\"fileloading\">\r\n    <thead>\r\n      <tr>\r\n        <!--序号-->\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"75px\">\r\n          序号\r\n        </th>\r\n        <!--文件名称-->\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"300px\">\r\n          文件名称\r\n        </th>\r\n\r\n        <!--文件类型-->\r\n        <!-- <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          文件大小\r\n        </th> -->\r\n        <!--文件格式-->\r\n        <!-- <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          文件格式\r\n        </th> -->\r\n\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          上传人\r\n        </th>\r\n\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          上传时间\r\n        </th>\r\n\r\n        <th nzRight nzWidth=\"140px\" style=\"color: #5f8bd3; background-color: #e6edf5\">\r\n          操作\r\n        </th>\r\n        <!-- 操作 -->\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      <tr *ngFor=\"let data of fileitem.data; let i = index\">\r\n        <td nz-tooltip [nzAlign]=\"'center'\">{{ i + 1 }}</td>\r\n        <!-- 文件名称 -->\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.originalFileName }}</span>\r\n        </td>\r\n        <!-- 文件大小 -->\r\n        <!-- <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.fileSize || 0 }}字节</span>\r\n        </td> -->\r\n        <!-- 文件格式 -->\r\n        <!-- <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.mediaType }}</span>\r\n        </td> -->\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.createdUserName || translate }}</span>\r\n        </td>\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.createdTime | date:'yyyy-MM-dd HH:mm:ss' }}</span>\r\n        </td>\r\n        <td nzRight>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" nz-popconfirm\r\n            nzPopconfirmTitle=\"是否删除此数据？\" (nzOnConfirm)=\"onFileDel(data.id)\">删除</a>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"preview(data)\">预览</a>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"onFileDownload(data.attachmentId)\">下载</a>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </nz-table>\r\n</nz-card>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAwBC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB;AACjH,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,gBAAgB,QAAQ,6BAA6B;AAG9D,SAASC,MAAM,QAAQ,gBAAgB;AAEvC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAEEC,WAAW,EACXC,WAAW,EACXC,YAAY,QACP,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICVvBC,EADF,CAAAC,cAAA,iBAA4E,iBACQ;IAA1CD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAAsBT,EAAA,CAAAU,MAAA,GAAyB;;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACpHX,EAAA,CAAAC,cAAA,iBAAsC;IAApBD,EAAA,CAAAE,UAAA,mBAAAU,iEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;;IAFWX,EAAA,CAAAc,SAAA,EAAqB;IAAsBd,EAA3C,CAAAe,UAAA,cAAAT,MAAA,CAAAU,OAAA,CAAqB,qBAA0C;IAAChB,EAAA,CAAAc,SAAA,EAAyB;IAAzBd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,kBAAyB;IACrElB,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;;IAGpElB,EADF,CAAAC,cAAA,iBAA2E,iBACnC;IAApBD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;IAD+BX,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;IAa5DlB,EAAA,CAAAqB,SAAA,oBACY;;;;IADwDrB,EAAzB,CAAAe,UAAA,YAAAO,SAAA,CAAAC,KAAA,CAAwB,YAAAD,SAAA,CAAAE,KAAA,CAAyB;;;;;IAa5FxB,EAAA,CAAAqB,SAAA,oBACY;;;;IADyDrB,EAAzB,CAAAe,UAAA,YAAAU,SAAA,CAAAF,KAAA,CAAwB,YAAAE,SAAA,CAAAD,KAAA,CAAyB;;;;;IAqD7FxB,EAAA,CAAAqB,SAAA,oBACY;;;;IAD2DrB,EAAzB,CAAAe,UAAA,YAAAW,SAAA,CAAAH,KAAA,CAAwB,YAAAG,SAAA,CAAAF,KAAA,CAAyB;;;;;;IAwFrGxB,EADF,CAAAC,cAAA,SAAsD,aAChB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAGlDX,EADF,CAAAC,cAAA,aAAoC,WAC5B;IAAAD,EAAA,CAAAU,MAAA,GAA2B;IACnCV,EADmC,CAAAW,YAAA,EAAO,EACrC;IAUHX,EADF,CAAAC,cAAA,aAAoC,WAC5B;IAAAD,EAAA,CAAAU,MAAA,GAAuC;IAC/CV,EAD+C,CAAAW,YAAA,EAAO,EACjD;IAEHX,EADF,CAAAC,cAAA,aAAoC,YAC5B;IAAAD,EAAA,CAAAU,MAAA,IAAmD;;IAC3DV,EAD2D,CAAAW,YAAA,EAAO,EAC7D;IAEHX,EADF,CAAAC,cAAA,cAAY,aAEwD;IAAnCD,EAAA,CAAAE,UAAA,yBAAAyB,iEAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAyB,SAAA,CAAAH,OAAA,CAAAI,EAAA,CAAkB;IAAA,EAAC;IAAChC,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACxEX,EAAA,CAAAC,cAAA,aAAuE;IAAxBD,EAAA,CAAAE,UAAA,mBAAA+B,2DAAA;MAAA,MAAAL,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4B,OAAA,CAAAN,OAAA,CAAa;IAAA,EAAC;IAAC5B,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC7EX,EAAA,CAAAC,cAAA,aAA2F;IAA5CD,EAAA,CAAAE,UAAA,mBAAAiC,2DAAA;MAAA,MAAAP,OAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,cAAA,CAAAR,OAAA,CAAAS,YAAA,CAAiC;IAAA,EAAC;IAACrC,EAAA,CAAAU,MAAA,oBAAE;IAEjGV,EAFiG,CAAAW,YAAA,EAAI,EAC9F,EACF;;;;;;IAzBYX,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAACf,EAAA,CAAAc,SAAA,EAAW;IAAXd,EAAA,CAAAiB,iBAAA,CAAAqB,KAAA,KAAW;IAEhCtC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAA2B;IAA3Bd,EAAA,CAAAiB,iBAAA,CAAAW,OAAA,CAAAW,gBAAA,CAA2B;IAUpBvC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAiB,iBAAA,CAAAW,OAAA,CAAAY,eAAA,IAAAlC,MAAA,CAAAmC,SAAA,CAAuC;IAEhCzC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAAmD;IAAnDd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAA0C,WAAA,QAAAd,OAAA,CAAAe,WAAA,yBAAmD;;;AD3KnE,OAAM,MAAOC,sBAAuB,SAAQxD,WAAW;EAyBrDyD,YAAYC,oBAA0C,EAC5CC,GAAsB,EACtBC,IAAgB,EAChBC,OAAyB,EACzBC,aAA+B,EAC/BC,iBAAoC;IAC5C,KAAK,CAACL,oBAAoB,CAAC;IALnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IA5B3B,KAAAC,SAAS,GAAG,IAAI1D,gBAAgB,EAAE;IAClC,KAAA2D,UAAU,GAAG,CAAC,IAAI,CAACD,SAAS,CAAC;IAC7B,KAAApB,EAAE,GAAG,EAAE;IACP,KAAAsB,WAAW,GAAG,EAAE;IAChB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,QAAQ,GAAmB,EAAE;IAC7B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,SAAS,GAAG,IAAIjE,cAAc,EAAE;IAChC,KAAAkE,eAAe,GAAG,EAAE;IACpB,KAAAC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,UAAU,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC;IAC/D,KAAAC,aAAa,GACX,IAAI,CAACpB,GAAG,CAACqB,SAAS,GAClB,GAAG,GACH,IAAI,CAACrB,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,GAC9B,qBAAqB,CAAC,CAAC;IACzB;IACA;IACA,KAAAC,gBAAgB,GAAW;MACzBC,GAAG,EAAE;KACN;IAkQD;IACA,KAAAC,gBAAgB,GAAIC,IAAkB,IAAa;MAC/CC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK,CAAC;MACvD,IACE,IAAI,CAACoD,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK,IAAI,IAAI,IAC1C,IAAI,CAACoD,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK,IAAI,EAAE,EACxC;QACA,IAAI,CAACsD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,YAAY,CAAC;QACjD,OAAO,KAAK;MACd;MACA,IAAI,CAACrB,QAAQ,GAAG,CAACe,IAAI,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI,IAAI,CAACA,QAAQ,CAACsB,MAAM,IAAI,CAAC,EAAE;QAC7B,IAAI,CAACF,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,aAAa,CAAC;QAClD,OAAO,KAAK;MACd;MACA,IAAI,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAACuB,IAAI,GAAG,IAAI,CAAC/B,aAAa,CAACgC,SAAS,EAAE;QACxD;QACA,IAAIC,MAAM,GAAiB;UACzBC,IAAI,EAAE,IAAI,CAACR,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK;UACxC6D,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,WAAW;UACnBC,gBAAgB,EAAE,eAAe;UACjCC,gBAAgB,EAAE;SACnB;QACD,IAAI,CAACtC,aAAa,CAACuC,UAAU,CAAC,IAAI,CAAC/B,QAAQ,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAAC;MACzD,CAAC,MAAM;QACL;QACA,IAAI,CAACO,UAAU,EAAE;MACnB;MACA,OAAO,KAAK;IACd,CAAC;EAzRH;EAEA;;;EAGAC,QAAQA,CAAA;IACN,OAAO;MACL3D,EAAE,EAAE,IAAIxC,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MACnDC,MAAM,EAAE,IAAIrG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqG,QAAQ,CAAC;MAChDC,MAAM,EAAE,IAAIvG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACrDI,MAAM,EAAE,IAAIxG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACrDK,QAAQ,EAAE,IAAIzG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACvDM,OAAO,EAAE,IAAI1G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqG,QAAQ,CAAC;MACjDK,OAAO,EAAE,IAAI3G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACtDQ,OAAO,EAAE,IAAI5G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACtDS,SAAS,EAAE,IAAI7G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACxDU,KAAK,EAAE,IAAI9G,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACqG,QAAQ,EAAErG,UAAU,CAAC8G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EC,OAAO,EAAE,IAAIhH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACmG,aAAa,EAAEnG,UAAU,CAAC8G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClFE,QAAQ,EAAE,IAAIjH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACqG,QAAQ,EAAErG,UAAU,CAAC8G,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/EG,YAAY,EAAE,IAAIlH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACqG,QAAQ,EAAErG,UAAU,CAAC8G,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACnFI,QAAQ,EAAE,IAAInH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACmG,aAAa,EAAEnG,UAAU,CAAC8G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACnFK,aAAa,EAAE,IAAIpH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACmG,aAAa,EAAEnG,UAAU,CAAC8G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACxFM,KAAK,EAAE,IAAIrH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqG,QAAQ,CAAC;MAC/CgB,KAAK,EAAE,IAAItH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACqG,QAAQ,CAAC;MAC/CiB,UAAU,EAAE,IAAIvH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACzDoB,UAAU,EAAE,IAAIxH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MACzDqB,MAAM,EAAE,IAAIzH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACmG,aAAa,EAAEnG,UAAU,CAAC8G,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACjFW,WAAW,EAAE,IAAI1H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MAC5DjD,WAAW,EAAE,IAAInD,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MAC5DuB,YAAY,EAAE,IAAI3H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MAC7DwB,YAAY,EAAE,IAAI5H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MAC7DyB,QAAQ,EAAE,IAAI7H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC;MAAE;MACzD0B,OAAO,EAAE,IAAI9H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACmG,aAAa,CAAC,CAAE;KACzD;EACH;EAEA;;;EAGM2B,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACVD,KAAI,CAAC1D,eAAe,GAAG0D,KAAI,CAACzE,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE;MACrDmD,KAAI,CAAClE,WAAW,GAAGkE,KAAI,CAACzE,GAAG,CAACO,WAAW,CAAC,MAAM,CAAC,CAACe,EAAE;MAClD,IAAImD,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKnI,YAAY,CAACoI,MAAM,EAAE;QACnDH,KAAI,CAAClD,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;QACnCkD,KAAI,CAACrE,iBAAiB,CAACyE,GAAG,CAAC,cAAc,GAAGJ,KAAI,CAACE,SAAS,CAAC,IAAI,CAAC,EAACF,KAAI,CAACzE,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,CAAC,CAACwD,IAAI,CAAEC,GAAsB,IAAI;UAC/H,IAAIA,GAAG,CAACC,EAAE,EAAE;YACVP,KAAI,CAAC5C,QAAQ,CAACoD,UAAU,CAACF,GAAG,CAACG,IAAI,CAAC;UACpC,CAAC,MAAM;YACLT,KAAI,CAAC1C,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,cAAc,CAAC;YACnDyC,KAAI,CAACU,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ;MACA,IAAGV,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKnI,YAAY,CAAC4I,GAAG,EAAC;QAC9CX,KAAI,CAACY,WAAW,EAAE;MACpB;MACAZ,KAAI,CAACa,UAAU,EAAE;MACjBb,KAAI,CAACc,WAAW,CAACd,KAAI,CAAC5C,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAErD,KAAK,CAAC;MACzDgG,KAAI,CAACe,YAAY,EAAE;IAAC;EACtB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACpF,iBAAiB,CACnBqF,IAAI,CACH,wBAAwB,EACxB;MAAC3C,MAAM,EAAE,IAAI,CAACjB,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAErD;IAAK,CAAC,EACjD,IAAI,CAACuB,GAAG,CAACO,WAAW,CAAC,IAAI,CAAC,CAACe,EAAE,CAC9B,CACAwD,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAACvE,SAAS,GAAGsE,GAAG,CAACG,IAAI,CAACQ,GAAG,CAAEC,IAAI,KAAM;UACvCnH,KAAK,EAAEmH,IAAI,CAACvC,OAAO,GAAG,GAAG,GAAGuC,IAAI,CAACtC,OAAO,GAAG,GAAG,GAAGsC,IAAI,CAAC1C,MAAM;UAC5DxE,KAAK,EAAEkH,IAAI,CAAC1G,EAAE;UACdmE,OAAO,EAAEuC,IAAI,CAACvC,OAAO;UACrBC,OAAO,EAAEsC,IAAI,CAACtC,OAAO;UACrBC,SAAS,EAAEqC,IAAI,CAACrC;SACjB,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACvB,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEAL,WAAWA,CAACM,aAAkB;IAC5B,IAAIC,WAAW,GAAG;MAChB7C,MAAM,EAAE4C,aAAa;MACrBE,IAAI,EAAE,CAAC;MACP7D,IAAI,EAAE;KACP;IACD,IAAI,CAAC9B,iBAAiB,CACnBqF,IAAI,CACH,sBAAsB,EACtBK,WAAW,EACX,IAAI,CAAC9F,GAAG,CAACO,WAAW,CAAC,IAAI,CAAC,CAACe,EAAE,CAC9B,CACAwD,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAACxE,QAAQ,GAAGuE,GAAG,CAACG,IAAI,CAACQ,GAAG,CAAEC,IAAI,KAAM;UACtCnH,KAAK,EAAEmH,IAAI,CAAC3C,MAAM,GAAG,GAAG,GAAG2C,IAAI,CAAC1C,MAAM,GAAG,GAAG,GAAG0C,IAAI,CAACK,SAAS;UAC7DvH,KAAK,EAAEkH,IAAI,CAAC1G,EAAE;UACd+D,MAAM,EAAE2C,IAAI,CAAC3C,MAAM;UACnBC,MAAM,EAAE0C,IAAI,CAAC1C,MAAM;UACnBC,QAAQ,EAAEyC,IAAI,CAACzC;SAChB,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACnB,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEAK,cAAcA,CAACJ,aAAkB;IAC/B,IAAI,CAACN,WAAW,CAACM,aAAa,CAAC;EACjC;EAEA;;;;EAIAnI,QAAQA,CAAA;IACN,MAAMwI,GAAG,GAAG,aAAa;IACzB,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACtE,QAAQ,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,QAAQ,CAACC,QAAQ,CAACqE,CAAC,CAAC,CAACC,WAAW,EAAE;MACvC,IAAI,CAACvE,QAAQ,CAACC,QAAQ,CAACqE,CAAC,CAAC,CAACE,sBAAsB,EAAE;IACpD;IACA,IAAI,IAAI,CAACxE,QAAQ,CAACyE,OAAO,EAAE;MACzB;IACF;IACA,MAAMrH,EAAE,GAAG,IAAI,CAACgC,aAAa,CAACsF,SAAS,EAAE,CAACC,WAAW,CAAC,WAAW,CAAC;IAClE,IAAI,CAACvI,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAAC0G,SAAS,CAAC,OAAO,CAAC,KAAKnI,YAAY,CAAC4I,GAAG,EAAE;MAChD,IAAI,CAACvD,QAAQ,CAAC4E,aAAa,CAAC,IAAI,CAAC;MACjC,IAAI,CAACrG,iBAAiB,CAACqF,IAAI,CAACS,GAAG,EAAE,IAAI,CAACrE,QAAQ,CAAC6E,WAAW,EAAE,EAAE,IAAI,CAAC1G,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,CAAC,CAACwD,IAAI,CAAEC,GAAsB,IAAI;QAC5H,IAAI,CAAC9D,aAAa,CAACsF,SAAS,EAAE,CAACI,UAAU,CAAC1H,EAAE,CAAC;QAC7C,IAAI,CAAChB,OAAO,GAAG,KAAK;QACpB,IAAI8G,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACjD,SAAS,CAACxF,aAAa,CAACqK,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAACzB,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAAC0B,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAC9E,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACxF,iBAAiB,CAAC0G,GAAG,CAACZ,GAAG,EAAE,IAAI,CAACrE,QAAQ,CAAC6E,WAAW,EAAE,EAAE,IAAI,CAAC1G,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,CAAC,CAACwD,IAAI,CAAEC,GAAsB,IAAI;QAC3H,IAAI,CAAC9D,aAAa,CAACsF,SAAS,EAAE,CAACI,UAAU,CAAC1H,EAAE,CAAC;QAC7C,IAAI,CAAChB,OAAO,GAAG,KAAK;QACpB,IAAI8G,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACjD,SAAS,CAACxF,aAAa,CAACqK,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAACzB,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAAC0B,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAC9E,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ;EACF;EAEA9H,OAAOA,CAAA;IACL,IAAI,IAAI,CAACiJ,SAAS,EAAE,EAAE;MACpB,OAAO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAACnC,IAAI,CAACoC,KAAK,IAAG;QACxG,QAAQA,KAAK;UACX,KAAK5K,gBAAgB,CAAC6K,GAAG;YAAI;YAC3B,IAAI,CAACzJ,QAAQ,EAAE;YACf,OAAO,IAAI;UACb,KAAKpB,gBAAgB,CAAC8K,EAAE;YAAK;YAC3B,IAAI,CAACjC,YAAY,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI;UACb,KAAK7I,gBAAgB,CAAC+K,MAAM;YAAE;YAC5B,OAAO,KAAK;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAClC,YAAY,CAAC,EAAE,CAAC;MACrB,OAAO,IAAI;IACb;EACF;EACAmC,mBAAmBA,CAACC,SAAS;IAC3B,OAAO,IAAI,CAAChG,gBAAgB,CAACgG,SAAS,CAAC;EACzC;EAEAC,eAAeA,CAACC,cAAqB;IACnC,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC1B;MACA,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MACjD,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC5C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;IACnD,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,IAAI,CAACjH,WAAW,CAACkH,IAAI,CAACjC,IAAI,IAAIA,IAAI,CAAClH,KAAK,KAAKgJ,cAAc,CAAC;MACxE,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAAC1D,UAAU,CAAC;MAC/D,IAAI,CAACpC,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAAC3D,UAAU,CAAC;MAC/D,IAAI,CAACnC,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAAC5D,KAAK,CAAC;IACvD;EACF;EAEAuB,UAAUA,CAAA;IACR,MAAMuC,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAI,CAAE;IAC5B,IAAI,CAAC1H,iBAAiB,CACjBqF,IAAI,CACH,wBAAwB,EACxBoC,KAAK,EACL,IAAI,CAAC7H,GAAG,CAACO,WAAW,CAAC,MAAM,CAAC,CAACe,EAAE,CAChC,CACAwD,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAACtE,WAAW,GAAGqE,GAAG,CAACG,IAAI,CAACQ,GAAG,CAAEC,IAAI,KAAM;UACzCnH,KAAK,EAAEmH,IAAI,CAACoC,OAAO,GAAG,GAAG,GAAGpC,IAAI,CAACqC,OAAO,GAAG,GAAG,GAAGrC,IAAI,CAACsC,WAAW,GAAG,GAAG,GAAGtC,IAAI,CAACuC,WAAW;UAC1FzJ,KAAK,EAAEkH,IAAI,CAAC7B,KAAK;UACjBG,UAAU,EAAE0B,IAAI,CAACoC,OAAO;UACxBhE,KAAK,EAAE4B,IAAI,CAACqC,OAAO;UACnBhE,UAAU,EAAE2B,IAAI,CAACsC;SAClB,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAAClG,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACN,CAAC,CAAC;EACJ;EAEAuC,YAAYA,CAACV,cAAqB;IAChC,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC1B;MACA,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC7C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC7C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC7C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;IACjD,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,IAAI,CAACnH,QAAQ,CAACoH,IAAI,CAACjC,IAAI,IAAIA,IAAI,CAAClH,KAAK,KAAKgJ,cAAc,CAAC;MACrE,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAAC3E,MAAM,EAAEoF,IAAI,EAAE,CAAC;MAC/D,IAAI,CAACvG,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAAC1E,MAAM,EAAEmF,IAAI,EAAE,CAAC;MAC/D,IAAI,CAACvG,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAACzE,QAAQ,EAAEkF,IAAI,EAAE,CAAC;IACrE;IACA,IAAI,CAACvG,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAClC,YAAY,EAAE;EACrB;EAEA6C,aAAaA,CAACZ,cAAqB;IACjC,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC1B;MACA,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC9C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;MAC9C,IAAI,CAAC7F,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC4F,QAAQ,CAAC,EAAE,CAAC;IAClD,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,IAAI,CAAClH,SAAS,CAACmH,IAAI,CAACjC,IAAI,IAAIA,IAAI,CAAClH,KAAK,KAAKgJ,cAAc,CAAC;MACtE,IAAI,CAAC5F,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAACtE,OAAO,EAAE+E,IAAI,EAAE,CAAC;MACjE,IAAI,CAACvG,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC4F,QAAQ,CAACC,KAAK,CAACrE,SAAS,EAAE8E,IAAI,EAAE,CAAC;IACvE;EACF;EAmCE;EACAzF,UAAUA,CAAA;IACR,IAAI2F,IAAI,GAAG,IAAI;IACf,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC9H,QAAQ,CAAC,CAAC,CAAQ,CAAC;IAChD4H,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC5G,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK,CAAC;IAC3D8J,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;IACtCF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAE,eAAe,CAAC;IACtDF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC;IAClDF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC;IAClDF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;IACnDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;IACnDF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC;IACrD,MAAMC,OAAO,GAAG,IAAI5L,WAAW,CAAC;MAC9B,aAAa,EAAE,IAAI,CAACkE;KACrB,CAAC;IACF,MAAM2H,GAAG,GAAG,IAAI5L,WAAW,CAAC,MAAM,EAAE,IAAI,CAACqE,aAAa,EAAEmH,QAAQ,EAAE;MAChEG,OAAO,EAAEA,OAAO;MAChBE,cAAc,EAAE,IAAI;MAAE;MACtBC,eAAe,EAAE,KAAK,CAAE;KACzB,CAAC;IACF,IAAI,CAAC5I,IAAI,CACN6I,OAAO,CAACH,GAAG,CAAC,CACZI,IAAI,CAACnM,MAAM,CAAEoM,CAAC,IAAKA,CAAC,YAAYhM,YAAY,CAAC,CAAC,CAC9CiM,SAAS,CAAC;MACTC,IAAIA,CAACC,GAAG;QACNb,IAAI,CAAC1H,aAAa,GAAG,KAAK;QAC1B0H,IAAI,CAAC3H,QAAQ,GAAG,EAAE;QAClB,IAAIwI,GAAG,CAAC,IAAI,CAAC,EAAE;UACb,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;YACrBb,IAAI,CAACvG,SAAS,CAACxF,aAAa,CAACqK,OAAO,EAAE,QAAQ,CAAC;YAC/C0B,IAAI,CAACjD,WAAW,EAAE;UACpB,CAAC,MAAM;YACLiD,IAAI,CAACvG,SAAS,CACZxF,aAAa,CAACyF,KAAK,EACnBmH,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,QAAQ,CACjC;UACH;QACF,CAAC,MAAM;UACLb,IAAI,CAACvG,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAEmH,GAAG,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC;QAC7D;MACF,CAAC;MACDnH,KAAKA,CAACoH,GAAG;QACPd,IAAI,CAAC1H,aAAa,GAAG,KAAK;QAC1B0H,IAAI,CAACvG,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAEoH,GAAG,CAACxD,GAAG,CAAC;MAC9C;KACD,CAAC;EACN;EAEA;EACAyD,aAAaA,CAACC,SAAS;IACrB3H,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACrB,IAAI2H,SAAS,GAAG;MAAED,SAAS,EAAEA,SAAS;MAAEE,aAAa,EAAE,IAAI,CAACxI;IAAY,CAAE;IAC1E,IAAIyI,WAAW,GACb,IAAI,CAACxI,aAAa,CAACyI,aAAa,EAAE,CAACrI,SAAS,GAC5C,GAAG,GACH,IAAI,CAACJ,aAAa,CAACyI,aAAa,EAAE,CAACnJ,WAAW,CAAC,IAAI,CAAC,CAACe,EAAE,GACvD,kBAAkB,GAClB,+BAA+B,GAC/BgI,SAAS;IACX3H,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6H,WAAW,CAAC;IACnC;IACA,IAAIE,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC9CF,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;IAC7CH,OAAO,CAACG,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;IACxC,IAAIC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;IACvD;IACA,IAAIO,WAAW,GAAGN,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAChDK,WAAW,CAACJ,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;IACjDI,WAAW,CAACJ,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAChD,IAAIK,YAAY,GAAGJ,cAAc,CAACE,WAAW,CAACC,WAAW,CAAC;IAC1DC,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC/CK,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3C;IACAK,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAEL,WAAW,CAAC;IAChD;IACA,KAAK,IAAIW,QAAQ,IAAIb,SAAS,EAAE;MAC9B,IAAIc,YAAY,GAAGT,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAClDQ,YAAY,CAACP,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MAC3CO,YAAY,CAACP,YAAY,CAAC,MAAM,EAAEM,QAAQ,CAAC;MAC3CC,YAAY,CAACP,YAAY,CAAC,OAAO,EAAEP,SAAS,CAACa,QAAQ,CAAC,CAAC;MACvDD,YAAY,CAACF,WAAW,CAACI,YAAY,CAAC;IACxC;IACA;IACAF,YAAY,CAACG,MAAM,EAAE;EACvB;EAEAjF,WAAWA,CAAA;IACT,IAAI,CAACxE,WAAW,GAAG,IAAI;IACvB,IAAIiF,WAAW,GAAG;MAChBzD,IAAI,EAAE,IAAI,CAACR,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACrD,KAAK;MACxC8D,MAAM,EAAE,WAAW;MACnBC,gBAAgB,EAAE;KACnB;IACD,IAAI,CAAC1B,SAAS,CAACyJ,SAAS,EAAE;IAC1B,IAAI,CAACnK,iBAAiB,CACnBqF,IAAI,CAAC,mBAAmB,EAAE;MAAC,MAAM,EAAEK;IAAW,CAAC,EAAE,IAAI,CAAC/E,eAAe,CAAC,CACtE+D,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAClE,SAAS,CAAC0J,SAAS,CAACzF,GAAG,CAACG,IAAI,IAAI,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACnD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACD6E,OAAO,CAAC,MAAK;MACZ,IAAI,CAAC5J,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;EACN;EAEE;EACF7B,SAASA,CAACC,EAAE;IACV,IAAI,CAACmB,iBAAiB,CACnBsK,MAAM,CAAC,eAAe,GAAGzL,EAAE,EAAE,IAAI,CAAC8B,eAAe,CAAC,CAClD+D,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAACjD,SAAS,CAACxF,aAAa,CAACqK,OAAO,EAAE,MAAM,CAAC;QAC7C,IAAI,CAACvB,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACtD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;EACAvG,cAAcA,CAACJ,EAAE;IACf,IAAI,CAACkB,aAAa,CAACwK,YAAY,CAAC,IAAI,CAAC3K,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,EAAE,CAACrC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC6F,IAAI,CAAEC,GAAsB,IAAI;MACjH,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,MAAM4F,WAAW,GAAG7F,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI0F,WAAW,EAAE;UACfC,MAAM,CAACC,IAAI,CAACF,WAAW,EAAE,QAAQ,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAAC7I,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;EAEA;EACAmF,SAASA,CAACC,MAAM;IACZ,IAAI,CAAC7K,aAAa,CAACwK,YAAY,CAAC,IAAI,CAAC3K,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,EAAE,CAAC0J,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAClG,IAAI,CAAEC,GAAsB,IAAI;MACpH,IAAIA,GAAG,CAACC,EAAE,EAAE;QACR,MAAM4F,WAAW,GAAG7F,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI0F,WAAW,EAAE;UACb,IAAIK,UAAU,GAAG,IAAI,CAACjL,GAAG,CAACiL,UAAU,GAAGC,IAAI,CAACN,WAAW,CAAC;UACxDC,MAAM,CAACC,IAAI,CAACG,UAAU,EAAE,QAAQ,CAAC;QACrC,CAAC,MAAM;UACH,IAAI,CAAClJ,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,QAAQ,CAAC;QACjD;MACJ,CAAC,MAAM;QACH,IAAI,CAACD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAChD;IACJ,CAAC,CAAC;EACN;EAEFzG,OAAOA,CAAC+F,IAAI;IACV,IAAIiG,SAAS,GAAGjG,IAAI,CAAC,kBAAkB,CAAC;IACxC,MAAMkG,aAAa,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC5C,IAAI,CAACnL,aAAa,CAACwK,YAAY,CAAC,IAAI,CAAC3K,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACe,EAAE,EAAE,CAAC4D,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAACJ,IAAI,CAAEC,GAAsB,IAAI;MACtI,IAAIA,GAAG,CAACC,EAAE,EAAE;QACR,MAAM4F,WAAW,GAAG7F,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI0F,WAAW,EAAE;UACb,IAAIK,UAAU,GAAG,IAAI,CAACjL,GAAG,CAACiL,UAAU,GAAGC,IAAI,CAACN,WAAW,CAAC;UACxDC,MAAM,CAACC,IAAI,CAACG,UAAU,EAAE,QAAQ,CAAC;QACrC,CAAC,MAAM;UACH,IAAI,CAAClJ,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE,QAAQ,CAAC;QACjD;MACJ,CAAC,MAAM;QACH,IAAI,CAACD,SAAS,CAACxF,aAAa,CAACyF,KAAK,EAAE+C,GAAG,CAACa,GAAG,CAAC;MAChD;IACJ,CAAC,CAAC;IAGF;IACA;IACA;IACA;IACA;EACF;;;uBAjfW/F,sBAAsB,EAAA5C,EAAA,CAAAsO,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAxO,EAAA,CAAAsO,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA1O,EAAA,CAAAsO,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA5O,EAAA,CAAAsO,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA9O,EAAA,CAAAsO,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhP,EAAA,CAAAsO,iBAAA,CAAAW,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAtBtM,sBAAsB;MAAAuM,SAAA;MAAAC,QAAA,GAAApP,EAAA,CAAAqP,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtB/B3P,EAFJ,CAAAC,cAAA,iBAAwE,aAC9D,gBACiC;UACrCD,EAAA,CAAAqB,SAAA,kBAA2D;UAC3DrB,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAU,MAAA,GAAqC;;UAC1DV,EAD0D,CAAAW,YAAA,EAAM,EACvD;UAKTX,EAJA,CAAA6P,UAAA,IAAAC,wCAAA,oBAA4E,IAAAC,wCAAA,oBAID;UAG7E/P,EAAA,CAAAW,YAAA,EAAS;UAODX,EALR,CAAAC,cAAA,cAA+D,cAC7B,cAEN,oBACR,wBACmC;UAAAD,EAAA,CAAAU,MAAA,IAA6B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAE1FX,EADF,CAAAC,cAAA,uBAAiB,qBAE6E;UAAnDD,EAAvC,CAAAE,UAAA,2BAAA8P,oEAAAC,MAAA;YAAAjQ,EAAA,CAAAI,aAAA,CAAA8P,GAAA;YAAA,OAAAlQ,EAAA,CAAAQ,WAAA,CAAiBoP,GAAA,CAAA1E,YAAA,CAAA+E,MAAA,CAAoB;UAAA,EAAC,wBAAAE,iEAAAF,MAAA;YAAAjQ,EAAA,CAAAI,aAAA,CAAA8P,GAAA;YAAA,OAAAlQ,EAAA,CAAAQ,WAAA,CAAeoP,GAAA,CAAA5G,cAAA,CAAAiH,MAAA,CAAsB;UAAA,EAAC;UAC5EjQ,EAAA,CAAA6P,UAAA,KAAAO,4CAAA,wBAA6F;UAKrGpQ,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,cAAwB,oBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAA8B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAEhFX,EADF,CAAAC,cAAA,uBAAiB,qBAEwC;UAArDD,EAAA,CAAAE,UAAA,2BAAAmQ,oEAAAJ,MAAA;YAAAjQ,EAAA,CAAAI,aAAA,CAAA8P,GAAA;YAAA,OAAAlQ,EAAA,CAAAQ,WAAA,CAAiBoP,GAAA,CAAAxE,aAAA,CAAA6E,MAAA,CAAqB;UAAA,EAAC;UACvCjQ,EAAA,CAAA6P,UAAA,KAAAS,4CAAA,wBAA8F;UAKtGtQ,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACmC;UAAAD,EAAA,CAAAU,MAAA,IAA4B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAC3FX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAC0B;;UAGhCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAA8B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAClFX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAC4B;;UAGlCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACmC;UAAAD,EAAA,CAAAU,MAAA,IAA+B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAC9FX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAC6B;;UAGnCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACmC;UAAAD,EAAA,CAAAU,MAAA,IAAmC;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAClGX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBACiC;;UAGvCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAwB,oBACR,wBACmC;UAAAD,EAAA,CAAAU,MAAA,IAA8B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAE3FX,EADF,CAAAC,cAAA,uBAAiB,qBAE6B;UAA1CD,EAAA,CAAAE,UAAA,2BAAAqQ,oEAAAN,MAAA;YAAAjQ,EAAA,CAAAI,aAAA,CAAA8P,GAAA;YAAA,OAAAlQ,EAAA,CAAAQ,WAAA,CAAiBoP,GAAA,CAAArF,eAAA,CAAA0F,MAAA,CAAuB;UAAA,EAAC;UACzCjQ,EAAA,CAAA6P,UAAA,KAAAW,4CAAA,wBAAgG;UAKxGxQ,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAwB,oBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAA+B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACnFX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAC6B;;UAGnCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAIFX,EAFJ,CAAAC,cAAA,eAAwB,oBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAA+B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACnFX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBACkC;;UAGxCrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAwB,oBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,IAAgC;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACpFX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,oBACkF;;UAK5FrB,EAJQ,CAAAW,YAAA,EAAkB,EACL,EACX,EACF,EACD;UAGHX,EAFJ,CAAAC,cAAA,UAAI,eACuB,eAC8B;UAAAD,EAAA,CAAAqB,SAAA,gBAAyF;UAAArB,EAAA,CAAAU,MAAA,sCAAU;UAAAV,EAAA,CAAAW,YAAA,EAAM;UAC9JX,EAAA,CAAAC,cAAA,qBAAoG;UAAzFD,EAAA,CAAAyQ,gBAAA,8BAAAC,uEAAAT,MAAA;YAAAjQ,EAAA,CAAAI,aAAA,CAAA8P,GAAA;YAAAlQ,EAAA,CAAA2Q,kBAAA,CAAAf,GAAA,CAAAlM,QAAA,EAAAuM,MAAA,MAAAL,GAAA,CAAAlM,QAAA,GAAAuM,MAAA;YAAA,OAAAjQ,EAAA,CAAAQ,WAAA,CAAAyP,MAAA;UAAA,EAAyB;UAClCjQ,EAAA,CAAAC,cAAA,kBAA6E;UAC3ED,EAAA,CAAAqB,SAAA,gBAAuD;UACvDrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,qBAAE;UAIhBV,EAJgB,CAAAW,YAAA,EAAO,EACR,EACC,EACR,EACH;UAMCX,EALN,CAAAC,cAAA,wBACyD,cAChD,WACD,eAEwF;UACxFD,EAAA,CAAAU,MAAA,uBACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,mCACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAWLX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,6BACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,mCACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAU,MAAA,uBACF;UAGJV,EAHI,CAAAW,YAAA,EAAK,EAEF,EACC;UACRX,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAA6P,UAAA,MAAAe,sCAAA,mBAAsD;UA6B5D5Q,EAFI,CAAAW,YAAA,EAAQ,EACC,EACH;;;;UA9MyBX,EAAA,CAAAe,UAAA,gBAAAf,EAAA,CAAA6Q,eAAA,KAAAC,GAAA,EAAoC;UAGvD9Q,EAAA,CAAAc,SAAA,GAAqC;UAArCd,EAAA,CAAAe,UAAA,sCAAqC;UAC5Bf,EAAA,CAAAc,SAAA,GAAqC;UAArCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,+BAAqC;UAEjBlB,EAAA,CAAAc,SAAA,GAAiC;UAAjCd,EAAA,CAAAe,UAAA,UAAA6O,GAAA,CAAAvF,mBAAA,QAAiC;UAIjCrK,EAAA,CAAAc,SAAA,EAAgC;UAAhCd,EAAA,CAAAe,UAAA,SAAA6O,GAAA,CAAAvF,mBAAA,QAAgC;UAKnCrK,EAAA,CAAAc,SAAA,EAAsB;UAAtBd,EAAA,CAAAe,UAAA,cAAA6O,GAAA,CAAAhL,QAAA,CAAsB;UAChD5E,EAAA,CAAAc,SAAA,EAAmB;UAAnBd,EAAA,CAAAe,UAAA,aAAAf,EAAA,CAAA6Q,eAAA,KAAAE,GAAA,EAAmB;UAIsB/Q,EAAA,CAAAc,SAAA,GAA6B;UAA7Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,wBAA6B;UAEtClB,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,sBAAsB;UAEjDf,EAAA,CAAAc,SAAA,EAAW;UAAXd,EAAA,CAAAe,UAAA,YAAA6O,GAAA,CAAArM,QAAA,CAAW;UASTvD,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,yBAA8B;UAE3BlB,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,sBAAsB;UAElDf,EAAA,CAAAc,SAAA,EAAY;UAAZd,EAAA,CAAAe,UAAA,YAAA6O,GAAA,CAAApM,SAAA,CAAY;UASCxD,EAAA,CAAAc,SAAA,GAA4B;UAA5Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA4B;UAEzDlB,EAAA,CAAAc,SAAA,GAA0C;UAA1Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,uBAA0C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAQhErK,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,yBAA8B;UAEhDlB,EAAA,CAAAc,SAAA,GAA4C;UAA5Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,yBAA4C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAQvDrK,EAAA,CAAAc,SAAA,GAA+B;UAA/Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,0BAA+B;UAE5DlB,EAAA,CAAAc,SAAA,GAA6C;UAA7Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,0BAA6C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAQxDrK,EAAA,CAAAc,SAAA,GAAmC;UAAnCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,8BAAmC;UAEhElB,EAAA,CAAAc,SAAA,GAAiD;UAAjDd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,8BAAiD;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAQ5DrK,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,yBAA8B;UAExClB,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,sBAAsB;UAEhDf,EAAA,CAAAc,SAAA,EAAc;UAAdd,EAAA,CAAAe,UAAA,YAAA6O,GAAA,CAAAnM,WAAA,CAAc;UASZzD,EAAA,CAAAc,SAAA,GAA+B;UAA/Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,0BAA+B;UAEjDlB,EAAA,CAAAc,SAAA,GAA6C;UAA7Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,0BAA6C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAQnErK,EAAA,CAAAc,SAAA,GAA+B;UAA/Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,0BAA+B;UAEjDlB,EAAA,CAAAc,SAAA,GAA6C;UAA7Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,0BAA6C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UASnErK,EAAA,CAAAc,SAAA,GAAgC;UAAhCd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,2BAAgC;UAE/ClB,EAAA,CAAAc,SAAA,GAA8C;UAA9Cd,EAAA,CAAAgR,qBAAA,gBAAAhR,EAAA,CAAAkB,WAAA,2BAA8C;UAAClB,EAAA,CAAAe,UAAA,aAAA6O,GAAA,CAAAvF,mBAAA,QAAuC;UAUpGrK,EAAA,CAAAc,SAAA,GAAyB;UAAzBd,EAAA,CAAAiR,gBAAA,eAAArB,GAAA,CAAAlM,QAAA,CAAyB;UAAqC1D,EAApC,CAAAe,UAAA,mBAAA6O,GAAA,CAAApL,gBAAA,CAAmC,2BAA2B;UAChDxE,EAAA,CAAAc,SAAA,EAA2B;UAA3Bd,EAAA,CAAAe,UAAA,cAAA6O,GAAA,CAAAjM,aAAA,CAA2B;UAO9D3D,EAAA,CAAAc,SAAA,GAA2B;UAChBd,EADX,CAAAe,UAAA,4BAA2B,WAAA6O,GAAA,CAAA/L,SAAA,CAAAqN,QAAA,GAAgC,aAAAlR,EAAA,CAAA6Q,eAAA,KAAAM,GAAA,EACjD,cAAAvB,GAAA,CAAAhM,WAAA,CAA0B;UAI9C5D,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAIpBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAapBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAIpBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAWLf,EAAA,CAAAc,SAAA,GAAkB;UAAlBd,EAAA,CAAAe,UAAA,YAAAqQ,YAAA,CAAAnJ,IAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}