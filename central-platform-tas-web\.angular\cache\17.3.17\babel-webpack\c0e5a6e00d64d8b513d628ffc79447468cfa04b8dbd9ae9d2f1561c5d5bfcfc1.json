{"ast": null, "code": "import { VesselKayRoutingModule } from './vessel-kay-routing.module';\nimport { VesselKayComponent } from './vessel-kay.component';\nimport { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [VesselKayComponent];\nexport class VesselKayModule {\n  static {\n    this.ɵfac = function VesselKayModule_Factory(t) {\n      return new (t || VesselKayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselKayModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VesselKayRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselKayModule, {\n    declarations: [VesselKayComponent],\n    imports: [SharedModule, VesselKayRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["VesselKayRoutingModule", "VesselKayComponent", "SharedModule", "LayoutModule", "COMPONENTS", "VesselKayModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-kay\\vessel-kay.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { VesselKayRoutingModule } from './vessel-kay-routing.module';\r\nimport { VesselKayComponent } from './vessel-kay.component';\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\n\r\nconst COMPONENTS = [VesselKayComponent];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VesselKayRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VesselKayModule { }\r\n"], "mappings": "AACA,SAASA,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD,MAAMC,UAAU,GAAG,CAACH,kBAAkB,CAAC;AAMvC,OAAM,MAAOI,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBH,YAAY,EAAEF,sBAAsB,EAAEG,YAAY;IAAA;EAAA;;;2EAGjDE,eAAe;IAAAC,YAAA,GANRL,kBAAkB;IAAAM,OAAA,GAG1BL,YAAY,EAAEF,sBAAsB,EAAEG,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}