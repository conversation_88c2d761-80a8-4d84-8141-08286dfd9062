{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder as 1 sekonde',\n    other: 'minder as {{count}} sekonden'\n  },\n  xSeconds: {\n    one: '1 sekonde',\n    other: '{{count}} sekonden'\n  },\n  halfAMinute: 'oardel minút',\n  lessThanXMinutes: {\n    one: 'minder as 1 minút',\n    other: 'minder as {{count}} minuten'\n  },\n  xMinutes: {\n    one: '1 minút',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'sawat 1 oere',\n    other: 'sawat {{count}} oere'\n  },\n  xHours: {\n    one: '1 oere',\n    other: '{{count}} oere'\n  },\n  xDays: {\n    one: '1 dei',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'sawat 1 wike',\n    other: 'sawat {{count}} wiken'\n  },\n  xWeeks: {\n    one: '1 wike',\n    other: '{{count}} wiken'\n  },\n  aboutXMonths: {\n    one: 'sawat 1 moanne',\n    other: 'sawat {{count}} moannen'\n  },\n  xMonths: {\n    one: '1 moanne',\n    other: '{{count}} moannen'\n  },\n  aboutXYears: {\n    one: 'sawat 1 jier',\n    other: 'sawat {{count}} jier'\n  },\n  xYears: {\n    one: '1 jier',\n    other: '{{count}} jier'\n  },\n  overXYears: {\n    one: 'mear as 1 jier',\n    other: 'mear as {{count}}s jier'\n  },\n  almostXYears: {\n    one: 'hast 1 jier',\n    other: 'hast {{count}} jier'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'oer ' + result;\n    } else {\n      return result + ' lyn';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/fy/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder as 1 sekonde',\n    other: 'minder as {{count}} sekonden'\n  },\n  xSeconds: {\n    one: '1 sekonde',\n    other: '{{count}} sekonden'\n  },\n  halfAMinute: 'oardel minút',\n  lessThanXMinutes: {\n    one: 'minder as 1 minút',\n    other: 'minder as {{count}} minuten'\n  },\n  xMinutes: {\n    one: '1 minút',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'sawat 1 oere',\n    other: 'sawat {{count}} oere'\n  },\n  xHours: {\n    one: '1 oere',\n    other: '{{count}} oere'\n  },\n  xDays: {\n    one: '1 dei',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'sawat 1 wike',\n    other: 'sawat {{count}} wiken'\n  },\n  xWeeks: {\n    one: '1 wike',\n    other: '{{count}} wiken'\n  },\n  aboutXMonths: {\n    one: 'sawat 1 moanne',\n    other: 'sawat {{count}} moannen'\n  },\n  xMonths: {\n    one: '1 moanne',\n    other: '{{count}} moannen'\n  },\n  aboutXYears: {\n    one: 'sawat 1 jier',\n    other: 'sawat {{count}} jier'\n  },\n  xYears: {\n    one: '1 jier',\n    other: '{{count}} jier'\n  },\n  overXYears: {\n    one: 'mear as 1 jier',\n    other: 'mear as {{count}}s jier'\n  },\n  almostXYears: {\n    one: 'hast 1 jier',\n    other: 'hast {{count}} jier'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'oer ' + result;\n    } else {\n      return result + ' lyn';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGL,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}