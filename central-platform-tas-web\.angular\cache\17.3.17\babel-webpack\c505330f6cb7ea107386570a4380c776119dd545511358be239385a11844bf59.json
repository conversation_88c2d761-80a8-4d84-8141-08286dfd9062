{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { auditTime, finalize, map, filter, takeUntil, startWith, distinctUntilChanged } from 'rxjs/operators';\nimport { environment } from 'ng-zorro-antd/core/environments';\nimport { getEventPosition, isTouchEvent } from 'ng-zorro-antd/core/util';\nimport { DOCUMENT } from '@angular/common';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport * as i2 from '@angular/cdk/layout';\nimport * as i1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NOOP = () => {};\nclass NzResizeService {\n  constructor(ngZone, rendererFactory2) {\n    this.ngZone = ngZone;\n    this.rendererFactory2 = rendererFactory2;\n    this.resizeSource$ = new Subject();\n    this.listeners = 0;\n    this.disposeHandle = NOOP;\n    this.handler = () => {\n      this.ngZone.run(() => {\n        this.resizeSource$.next();\n      });\n    };\n    this.renderer = this.rendererFactory2.createRenderer(null, null);\n  }\n  ngOnDestroy() {\n    // Caretaker note: the `handler` is an instance property (it's not defined on the class prototype).\n    // The `handler` captures `this` and prevents the `NzResizeService` from being GC'd.\n    this.handler = NOOP;\n  }\n  subscribe() {\n    this.registerListener();\n    return this.resizeSource$.pipe(auditTime(16), finalize(() => this.unregisterListener()));\n  }\n  unsubscribe() {\n    this.unregisterListener();\n  }\n  registerListener() {\n    if (this.listeners === 0) {\n      this.ngZone.runOutsideAngular(() => {\n        this.disposeHandle = this.renderer.listen('window', 'resize', this.handler);\n      });\n    }\n    this.listeners += 1;\n  }\n  unregisterListener() {\n    this.listeners -= 1;\n    if (this.listeners === 0) {\n      this.disposeHandle();\n      this.disposeHandle = NOOP;\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeService_Factory(t) {\n      return new (t || NzResizeService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.RendererFactory2));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeService,\n      factory: NzResizeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.RendererFactory2\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * When running in test, singletons should not be destroyed. So we keep references of singletons\n * in this global variable.\n */\nconst testSingleRegistry = new Map();\n/**\n * Some singletons should have life cycle that is same to Angular's. This service make sure that\n * those singletons get destroyed in HMR.\n */\nclass NzSingletonService {\n  constructor() {\n    /**\n     * This registry is used to register singleton in dev mode.\n     * So that singletons get destroyed when hot module reload happens.\n     *\n     * This works in prod mode too but with no specific effect.\n     */\n    this._singletonRegistry = new Map();\n  }\n  get singletonRegistry() {\n    return environment.isTestMode ? testSingleRegistry : this._singletonRegistry;\n  }\n  registerSingletonWithKey(key, target) {\n    const alreadyHave = this.singletonRegistry.has(key);\n    const item = alreadyHave ? this.singletonRegistry.get(key) : this.withNewTarget(target);\n    if (!alreadyHave) {\n      this.singletonRegistry.set(key, item);\n    }\n  }\n  unregisterSingletonWithKey(key) {\n    if (this.singletonRegistry.has(key)) {\n      this.singletonRegistry.delete(key);\n    }\n  }\n  getSingletonWithKey(key) {\n    return this.singletonRegistry.has(key) ? this.singletonRegistry.get(key).target : null;\n  }\n  withNewTarget(target) {\n    return {\n      target\n    };\n  }\n  static {\n    this.ɵfac = function NzSingletonService_Factory(t) {\n      return new (t || NzSingletonService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzSingletonService,\n      factory: NzSingletonService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSingletonService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPagePosition(event) {\n  const e = getEventPosition(event);\n  return {\n    x: e.pageX,\n    y: e.pageY\n  };\n}\n/**\n * This module provide a global dragging service to other components.\n */\nclass NzDragService {\n  constructor(rendererFactory2) {\n    this.draggingThreshold = 5;\n    this.currentDraggingSequence = null;\n    this.currentStartingPoint = null;\n    this.handleRegistry = new Set();\n    this.renderer = rendererFactory2.createRenderer(null, null);\n  }\n  requestDraggingSequence(event) {\n    if (!this.handleRegistry.size) {\n      this.registerDraggingHandler(isTouchEvent(event));\n    }\n    // Complete last dragging sequence if a new target is dragged.\n    if (this.currentDraggingSequence) {\n      this.currentDraggingSequence.complete();\n    }\n    this.currentStartingPoint = getPagePosition(event);\n    this.currentDraggingSequence = new Subject();\n    return this.currentDraggingSequence.pipe(map(e => ({\n      x: e.pageX - this.currentStartingPoint.x,\n      y: e.pageY - this.currentStartingPoint.y\n    })), filter(e => Math.abs(e.x) > this.draggingThreshold || Math.abs(e.y) > this.draggingThreshold), finalize(() => this.teardownDraggingSequence()));\n  }\n  registerDraggingHandler(isTouch) {\n    if (isTouch) {\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'touchmove', e => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.next(e.touches[0] || e.changedTouches[0]);\n          }\n        })\n      });\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'touchend', () => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.complete();\n          }\n        })\n      });\n    } else {\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'mousemove', e => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.next(e);\n          }\n        })\n      });\n      this.handleRegistry.add({\n        teardown: this.renderer.listen('document', 'mouseup', () => {\n          if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.complete();\n          }\n        })\n      });\n    }\n  }\n  teardownDraggingSequence() {\n    this.currentDraggingSequence = null;\n  }\n  static {\n    this.ɵfac = function NzDragService_Factory(t) {\n      return new (t || NzDragService)(i0.ɵɵinject(i0.RendererFactory2));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzDragService,\n      factory: NzDragService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDragService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction easeInOutCubic(t, b, c, d) {\n  const cc = c - b;\n  let tt = t / (d / 2);\n  if (tt < 1) {\n    return cc / 2 * tt * tt * tt + b;\n  } else {\n    return cc / 2 * ((tt -= 2) * tt * tt + 2) + b;\n  }\n}\nclass NzScrollService {\n  constructor(ngZone, doc) {\n    this.ngZone = ngZone;\n    this.doc = doc;\n  }\n  /** Set the position of the scroll bar of `el`. */\n  setScrollTop(el, topValue = 0) {\n    if (el === window) {\n      this.doc.body.scrollTop = topValue;\n      this.doc.documentElement.scrollTop = topValue;\n    } else {\n      el.scrollTop = topValue;\n    }\n  }\n  /** Get position of `el` against window. */\n  getOffset(el) {\n    const ret = {\n      top: 0,\n      left: 0\n    };\n    if (!el || !el.getClientRects().length) {\n      return ret;\n    }\n    const rect = el.getBoundingClientRect();\n    if (rect.width || rect.height) {\n      const doc = el.ownerDocument.documentElement;\n      ret.top = rect.top - doc.clientTop;\n      ret.left = rect.left - doc.clientLeft;\n    } else {\n      ret.top = rect.top;\n      ret.left = rect.left;\n    }\n    return ret;\n  }\n  /** Get the position of the scoll bar of `el`. */\n  // TODO: remove '| Window' as the fallback already happens here\n  getScroll(target, top = true) {\n    if (typeof window === 'undefined') {\n      return 0;\n    }\n    const method = top ? 'scrollTop' : 'scrollLeft';\n    let result = 0;\n    if (this.isWindow(target)) {\n      result = target[top ? 'pageYOffset' : 'pageXOffset'];\n    } else if (target instanceof Document) {\n      result = target.documentElement[method];\n    } else if (target) {\n      result = target[method];\n    }\n    if (target && !this.isWindow(target) && typeof result !== 'number') {\n      result = (target.ownerDocument || target).documentElement[method];\n    }\n    return result;\n  }\n  isWindow(obj) {\n    return obj !== null && obj !== undefined && obj === obj.window;\n  }\n  /**\n   * Scroll `el` to some position with animation.\n   *\n   * @param containerEl container, `window` by default\n   * @param y Scroll to `top`, 0 by default\n   */\n  scrollTo(containerEl, y = 0, options = {}) {\n    const target = containerEl ? containerEl : window;\n    const scrollTop = this.getScroll(target);\n    const startTime = Date.now();\n    const {\n      easing,\n      callback,\n      duration = 450\n    } = options;\n    const frameFunc = () => {\n      const timestamp = Date.now();\n      const time = timestamp - startTime;\n      const nextScrollTop = (easing || easeInOutCubic)(time > duration ? duration : time, scrollTop, y, duration);\n      if (this.isWindow(target)) {\n        target.scrollTo(window.pageXOffset, nextScrollTop);\n      } else if (target instanceof HTMLDocument || target.constructor.name === 'HTMLDocument') {\n        target.documentElement.scrollTop = nextScrollTop;\n      } else {\n        target.scrollTop = nextScrollTop;\n      }\n      if (time < duration) {\n        reqAnimFrame(frameFunc);\n      } else if (typeof callback === 'function') {\n        // Caretaker note: the `frameFunc` is called within the `<root>` zone, but we have to re-enter\n        // the Angular zone when calling custom callback to be backwards-compatible.\n        this.ngZone.run(callback);\n      }\n    };\n    // Caretaker note: the `requestAnimationFrame` triggers change detection, but updating a `scrollTop` property or\n    // calling `window.scrollTo` doesn't require Angular to run `ApplicationRef.tick()`.\n    this.ngZone.runOutsideAngular(() => reqAnimFrame(frameFunc));\n  }\n  static {\n    this.ɵfac = function NzScrollService_Factory(t) {\n      return new (t || NzScrollService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzScrollService,\n      factory: NzScrollService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzScrollService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nvar NzBreakpointEnum;\n(function (NzBreakpointEnum) {\n  NzBreakpointEnum[\"xxl\"] = \"xxl\";\n  NzBreakpointEnum[\"xl\"] = \"xl\";\n  NzBreakpointEnum[\"lg\"] = \"lg\";\n  NzBreakpointEnum[\"md\"] = \"md\";\n  NzBreakpointEnum[\"sm\"] = \"sm\";\n  NzBreakpointEnum[\"xs\"] = \"xs\";\n})(NzBreakpointEnum || (NzBreakpointEnum = {}));\nconst gridResponsiveMap = {\n  xs: '(max-width: 575px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  xxl: '(min-width: 1600px)'\n};\nconst siderResponsiveMap = {\n  xs: '(max-width: 479.98px)',\n  sm: '(max-width: 575.98px)',\n  md: '(max-width: 767.98px)',\n  lg: '(max-width: 991.98px)',\n  xl: '(max-width: 1199.98px)',\n  xxl: '(max-width: 1599.98px)'\n};\nclass NzBreakpointService {\n  constructor(resizeService, mediaMatcher) {\n    this.resizeService = resizeService;\n    this.mediaMatcher = mediaMatcher;\n    this.destroy$ = new Subject();\n    this.resizeService.subscribe().pipe(takeUntil(this.destroy$)).subscribe(() => {});\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  subscribe(breakpointMap, fullMap) {\n    if (fullMap) {\n      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n      const get = () => this.matchMedia(breakpointMap, true);\n      return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged((x, y) => x[0] === y[0]), map(x => x[1]));\n    } else {\n      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n      const get = () => this.matchMedia(breakpointMap);\n      return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged());\n    }\n  }\n  matchMedia(breakpointMap, fullMap) {\n    let bp = NzBreakpointEnum.md;\n    const breakpointBooleanMap = {};\n    Object.keys(breakpointMap).map(breakpoint => {\n      const castBP = breakpoint;\n      const matched = this.mediaMatcher.matchMedia(gridResponsiveMap[castBP]).matches;\n      breakpointBooleanMap[breakpoint] = matched;\n      if (matched) {\n        bp = castBP;\n      }\n    });\n    if (fullMap) {\n      return [bp, breakpointBooleanMap];\n    } else {\n      return bp;\n    }\n  }\n  static {\n    this.ɵfac = function NzBreakpointService_Factory(t) {\n      return new (t || NzBreakpointService)(i0.ɵɵinject(NzResizeService), i0.ɵɵinject(i2.MediaMatcher));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzBreakpointService,\n      factory: NzBreakpointService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreakpointService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: NzResizeService\n  }, {\n    type: i2.MediaMatcher\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDestroyService extends Subject {\n  ngOnDestroy() {\n    this.next();\n    this.complete();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵNzDestroyService_BaseFactory;\n      return function NzDestroyService_Factory(t) {\n        return (ɵNzDestroyService_BaseFactory || (ɵNzDestroyService_BaseFactory = i0.ɵɵgetInheritedFactory(NzDestroyService)))(t || NzDestroyService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzDestroyService,\n      factory: NzDestroyService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDestroyService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass ImagePreloadService {\n  constructor(document, platform) {\n    this.document = document;\n    this.platform = platform;\n    this.counter = new Map();\n    this.linkRefs = new Map();\n  }\n  addPreload(option) {\n    if (this.platform.isBrowser) {\n      return () => void 0;\n    }\n    const uniqueKey = `${option.src}${option.srcset}`;\n    let currentCount = this.counter.get(uniqueKey) || 0;\n    currentCount++;\n    this.counter.set(uniqueKey, currentCount);\n    if (!this.linkRefs.has(uniqueKey)) {\n      const linkNode = this.appendPreloadLink(option);\n      this.linkRefs.set(uniqueKey, linkNode);\n    }\n    return () => {\n      if (this.counter.has(uniqueKey)) {\n        let count = this.counter.get(uniqueKey);\n        count--;\n        if (count === 0) {\n          const linkNode = this.linkRefs.get(uniqueKey);\n          this.removePreloadLink(linkNode);\n          this.counter.delete(uniqueKey);\n          this.linkRefs.delete(uniqueKey);\n        } else {\n          this.counter.set(uniqueKey, count);\n        }\n      }\n    };\n  }\n  appendPreloadLink(option) {\n    const linkNode = this.document.createElement('link');\n    linkNode.setAttribute('rel', 'preload');\n    linkNode.setAttribute('as', 'image');\n    linkNode.setAttribute('href', option.src);\n    if (option.srcset) {\n      linkNode.setAttribute('imagesrcset', option.srcset);\n    }\n    this.document.head.appendChild(linkNode);\n    return linkNode;\n  }\n  removePreloadLink(linkNode) {\n    if (this.document.head.contains(linkNode)) {\n      this.document.head.removeChild(linkNode);\n    }\n  }\n  static {\n    this.ɵfac = function ImagePreloadService_Factory(t) {\n      return new (t || ImagePreloadService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ImagePreloadService,\n      factory: ImagePreloadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImagePreloadService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.Platform\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ImagePreloadService, NzBreakpointEnum, NzBreakpointService, NzDestroyService, NzDragService, NzResizeService, NzScrollService, NzSingletonService, gridResponsiveMap, siderResponsiveMap };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "Subject", "auditTime", "finalize", "map", "filter", "takeUntil", "startWith", "distinctUntilChanged", "environment", "getEventPosition", "isTouchEvent", "DOCUMENT", "reqAnimFrame", "i2", "i1", "NOOP", "NzResizeService", "constructor", "ngZone", "rendererFactory2", "resizeSource$", "listeners", "dispose<PERSON><PERSON><PERSON>", "handler", "run", "next", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "subscribe", "registerListener", "pipe", "unregisterListener", "unsubscribe", "runOutsideAngular", "listen", "ɵfac", "NzResizeService_Factory", "t", "ɵɵinject", "NgZone", "RendererFactory2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "testSingleRegistry", "Map", "NzSingletonService", "_singletonRegistry", "singletonRegistry", "isTestMode", "registerSingletonWithKey", "key", "target", "alreadyHave", "has", "item", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "unregisterSingletonWithKey", "delete", "getSingletonWithKey", "NzSingletonService_Factory", "getPagePosition", "event", "e", "x", "pageX", "y", "pageY", "NzDragService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentDraggingSequence", "currentStartingPoint", "handleRegistry", "Set", "requestDraggingSequence", "size", "registerDraggingHandler", "complete", "Math", "abs", "teardownDraggingSequence", "is<PERSON><PERSON>ch", "add", "teardown", "touches", "changedTouches", "NzDragService_Factory", "easeInOutCubic", "b", "c", "d", "cc", "tt", "NzScrollService", "doc", "setScrollTop", "el", "topValue", "window", "body", "scrollTop", "documentElement", "getOffset", "ret", "top", "left", "getClientRects", "length", "rect", "getBoundingClientRect", "width", "height", "ownerDocument", "clientTop", "clientLeft", "getScroll", "method", "result", "isWindow", "Document", "obj", "undefined", "scrollTo", "containerEl", "options", "startTime", "Date", "now", "easing", "callback", "duration", "frameFunc", "timestamp", "time", "nextScrollTop", "pageXOffset", "HTMLDocument", "name", "NzScrollService_Factory", "decorators", "NzBreakpointEnum", "gridResponsiveMap", "xs", "sm", "md", "lg", "xl", "xxl", "siderResponsiveMap", "NzBreakpointService", "resizeService", "mediaMatcher", "destroy$", "breakpointMap", "fullMap", "matchMedia", "bp", "breakpointBooleanMap", "Object", "keys", "breakpoint", "castBP", "matched", "matches", "NzBreakpointService_Factory", "MediaMatcher", "NzDestroyService", "ɵNzDestroyService_BaseFactory", "NzDestroyService_Factory", "ɵɵgetInheritedFactory", "ImagePreloadService", "document", "platform", "counter", "linkRefs", "addPreload", "option", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "src", "srcset", "currentCount", "linkNode", "appendPreloadLink", "count", "removePreloadLink", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "contains", "<PERSON><PERSON><PERSON><PERSON>", "ImagePreloadService_Factory", "Platform"], "sources": ["G:/web/central-platform-tas-web/node_modules/@delon/theme/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-services.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { auditTime, finalize, map, filter, takeUntil, startWith, distinctUntilChanged } from 'rxjs/operators';\nimport { environment } from 'ng-zorro-antd/core/environments';\nimport { getEventPosition, isTouchEvent } from 'ng-zorro-antd/core/util';\nimport { DOCUMENT } from '@angular/common';\nimport { reqAnimFrame } from 'ng-zorro-antd/core/polyfill';\nimport * as i2 from '@angular/cdk/layout';\nimport * as i1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NOOP = () => { };\nclass NzResizeService {\n    constructor(ngZone, rendererFactory2) {\n        this.ngZone = ngZone;\n        this.rendererFactory2 = rendererFactory2;\n        this.resizeSource$ = new Subject();\n        this.listeners = 0;\n        this.disposeHandle = NOOP;\n        this.handler = () => {\n            this.ngZone.run(() => {\n                this.resizeSource$.next();\n            });\n        };\n        this.renderer = this.rendererFactory2.createRenderer(null, null);\n    }\n    ngOnDestroy() {\n        // Caretaker note: the `handler` is an instance property (it's not defined on the class prototype).\n        // The `handler` captures `this` and prevents the `NzResizeService` from being GC'd.\n        this.handler = NOOP;\n    }\n    subscribe() {\n        this.registerListener();\n        return this.resizeSource$.pipe(auditTime(16), finalize(() => this.unregisterListener()));\n    }\n    unsubscribe() {\n        this.unregisterListener();\n    }\n    registerListener() {\n        if (this.listeners === 0) {\n            this.ngZone.runOutsideAngular(() => {\n                this.disposeHandle = this.renderer.listen('window', 'resize', this.handler);\n            });\n        }\n        this.listeners += 1;\n    }\n    unregisterListener() {\n        this.listeners -= 1;\n        if (this.listeners === 0) {\n            this.disposeHandle();\n            this.disposeHandle = NOOP;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeService, deps: [{ token: i0.NgZone }, { token: i0.RendererFactory2 }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.RendererFactory2 }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * When running in test, singletons should not be destroyed. So we keep references of singletons\n * in this global variable.\n */\nconst testSingleRegistry = new Map();\n/**\n * Some singletons should have life cycle that is same to Angular's. This service make sure that\n * those singletons get destroyed in HMR.\n */\nclass NzSingletonService {\n    constructor() {\n        /**\n         * This registry is used to register singleton in dev mode.\n         * So that singletons get destroyed when hot module reload happens.\n         *\n         * This works in prod mode too but with no specific effect.\n         */\n        this._singletonRegistry = new Map();\n    }\n    get singletonRegistry() {\n        return environment.isTestMode ? testSingleRegistry : this._singletonRegistry;\n    }\n    registerSingletonWithKey(key, target) {\n        const alreadyHave = this.singletonRegistry.has(key);\n        const item = alreadyHave ? this.singletonRegistry.get(key) : this.withNewTarget(target);\n        if (!alreadyHave) {\n            this.singletonRegistry.set(key, item);\n        }\n    }\n    unregisterSingletonWithKey(key) {\n        if (this.singletonRegistry.has(key)) {\n            this.singletonRegistry.delete(key);\n        }\n    }\n    getSingletonWithKey(key) {\n        return this.singletonRegistry.has(key) ? this.singletonRegistry.get(key).target : null;\n    }\n    withNewTarget(target) {\n        return {\n            target\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSingletonService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSingletonService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSingletonService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPagePosition(event) {\n    const e = getEventPosition(event);\n    return {\n        x: e.pageX,\n        y: e.pageY\n    };\n}\n/**\n * This module provide a global dragging service to other components.\n */\nclass NzDragService {\n    constructor(rendererFactory2) {\n        this.draggingThreshold = 5;\n        this.currentDraggingSequence = null;\n        this.currentStartingPoint = null;\n        this.handleRegistry = new Set();\n        this.renderer = rendererFactory2.createRenderer(null, null);\n    }\n    requestDraggingSequence(event) {\n        if (!this.handleRegistry.size) {\n            this.registerDraggingHandler(isTouchEvent(event));\n        }\n        // Complete last dragging sequence if a new target is dragged.\n        if (this.currentDraggingSequence) {\n            this.currentDraggingSequence.complete();\n        }\n        this.currentStartingPoint = getPagePosition(event);\n        this.currentDraggingSequence = new Subject();\n        return this.currentDraggingSequence.pipe(map((e) => ({\n            x: e.pageX - this.currentStartingPoint.x,\n            y: e.pageY - this.currentStartingPoint.y\n        })), filter((e) => Math.abs(e.x) > this.draggingThreshold || Math.abs(e.y) > this.draggingThreshold), finalize(() => this.teardownDraggingSequence()));\n    }\n    registerDraggingHandler(isTouch) {\n        if (isTouch) {\n            this.handleRegistry.add({\n                teardown: this.renderer.listen('document', 'touchmove', (e) => {\n                    if (this.currentDraggingSequence) {\n                        this.currentDraggingSequence.next(e.touches[0] || e.changedTouches[0]);\n                    }\n                })\n            });\n            this.handleRegistry.add({\n                teardown: this.renderer.listen('document', 'touchend', () => {\n                    if (this.currentDraggingSequence) {\n                        this.currentDraggingSequence.complete();\n                    }\n                })\n            });\n        }\n        else {\n            this.handleRegistry.add({\n                teardown: this.renderer.listen('document', 'mousemove', e => {\n                    if (this.currentDraggingSequence) {\n                        this.currentDraggingSequence.next(e);\n                    }\n                })\n            });\n            this.handleRegistry.add({\n                teardown: this.renderer.listen('document', 'mouseup', () => {\n                    if (this.currentDraggingSequence) {\n                        this.currentDraggingSequence.complete();\n                    }\n                })\n            });\n        }\n    }\n    teardownDraggingSequence() {\n        this.currentDraggingSequence = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDragService, deps: [{ token: i0.RendererFactory2 }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDragService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDragService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: i0.RendererFactory2 }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction easeInOutCubic(t, b, c, d) {\n    const cc = c - b;\n    let tt = t / (d / 2);\n    if (tt < 1) {\n        return (cc / 2) * tt * tt * tt + b;\n    }\n    else {\n        return (cc / 2) * ((tt -= 2) * tt * tt + 2) + b;\n    }\n}\nclass NzScrollService {\n    constructor(ngZone, doc) {\n        this.ngZone = ngZone;\n        this.doc = doc;\n    }\n    /** Set the position of the scroll bar of `el`. */\n    setScrollTop(el, topValue = 0) {\n        if (el === window) {\n            this.doc.body.scrollTop = topValue;\n            this.doc.documentElement.scrollTop = topValue;\n        }\n        else {\n            el.scrollTop = topValue;\n        }\n    }\n    /** Get position of `el` against window. */\n    getOffset(el) {\n        const ret = {\n            top: 0,\n            left: 0\n        };\n        if (!el || !el.getClientRects().length) {\n            return ret;\n        }\n        const rect = el.getBoundingClientRect();\n        if (rect.width || rect.height) {\n            const doc = el.ownerDocument.documentElement;\n            ret.top = rect.top - doc.clientTop;\n            ret.left = rect.left - doc.clientLeft;\n        }\n        else {\n            ret.top = rect.top;\n            ret.left = rect.left;\n        }\n        return ret;\n    }\n    /** Get the position of the scoll bar of `el`. */\n    // TODO: remove '| Window' as the fallback already happens here\n    getScroll(target, top = true) {\n        if (typeof window === 'undefined') {\n            return 0;\n        }\n        const method = top ? 'scrollTop' : 'scrollLeft';\n        let result = 0;\n        if (this.isWindow(target)) {\n            result = target[top ? 'pageYOffset' : 'pageXOffset'];\n        }\n        else if (target instanceof Document) {\n            result = target.documentElement[method];\n        }\n        else if (target) {\n            result = target[method];\n        }\n        if (target && !this.isWindow(target) && typeof result !== 'number') {\n            result = (target.ownerDocument || target).documentElement[method];\n        }\n        return result;\n    }\n    isWindow(obj) {\n        return obj !== null && obj !== undefined && obj === obj.window;\n    }\n    /**\n     * Scroll `el` to some position with animation.\n     *\n     * @param containerEl container, `window` by default\n     * @param y Scroll to `top`, 0 by default\n     */\n    scrollTo(containerEl, y = 0, options = {}) {\n        const target = containerEl ? containerEl : window;\n        const scrollTop = this.getScroll(target);\n        const startTime = Date.now();\n        const { easing, callback, duration = 450 } = options;\n        const frameFunc = () => {\n            const timestamp = Date.now();\n            const time = timestamp - startTime;\n            const nextScrollTop = (easing || easeInOutCubic)(time > duration ? duration : time, scrollTop, y, duration);\n            if (this.isWindow(target)) {\n                target.scrollTo(window.pageXOffset, nextScrollTop);\n            }\n            else if (target instanceof HTMLDocument || target.constructor.name === 'HTMLDocument') {\n                target.documentElement.scrollTop = nextScrollTop;\n            }\n            else {\n                target.scrollTop = nextScrollTop;\n            }\n            if (time < duration) {\n                reqAnimFrame(frameFunc);\n            }\n            else if (typeof callback === 'function') {\n                // Caretaker note: the `frameFunc` is called within the `<root>` zone, but we have to re-enter\n                // the Angular zone when calling custom callback to be backwards-compatible.\n                this.ngZone.run(callback);\n            }\n        };\n        // Caretaker note: the `requestAnimationFrame` triggers change detection, but updating a `scrollTop` property or\n        // calling `window.scrollTo` doesn't require Angular to run `ApplicationRef.tick()`.\n        this.ngZone.runOutsideAngular(() => reqAnimFrame(frameFunc));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzScrollService, deps: [{ token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzScrollService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzScrollService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\nvar NzBreakpointEnum;\n(function (NzBreakpointEnum) {\n    NzBreakpointEnum[\"xxl\"] = \"xxl\";\n    NzBreakpointEnum[\"xl\"] = \"xl\";\n    NzBreakpointEnum[\"lg\"] = \"lg\";\n    NzBreakpointEnum[\"md\"] = \"md\";\n    NzBreakpointEnum[\"sm\"] = \"sm\";\n    NzBreakpointEnum[\"xs\"] = \"xs\";\n})(NzBreakpointEnum || (NzBreakpointEnum = {}));\nconst gridResponsiveMap = {\n    xs: '(max-width: 575px)',\n    sm: '(min-width: 576px)',\n    md: '(min-width: 768px)',\n    lg: '(min-width: 992px)',\n    xl: '(min-width: 1200px)',\n    xxl: '(min-width: 1600px)'\n};\nconst siderResponsiveMap = {\n    xs: '(max-width: 479.98px)',\n    sm: '(max-width: 575.98px)',\n    md: '(max-width: 767.98px)',\n    lg: '(max-width: 991.98px)',\n    xl: '(max-width: 1199.98px)',\n    xxl: '(max-width: 1599.98px)'\n};\nclass NzBreakpointService {\n    constructor(resizeService, mediaMatcher) {\n        this.resizeService = resizeService;\n        this.mediaMatcher = mediaMatcher;\n        this.destroy$ = new Subject();\n        this.resizeService\n            .subscribe()\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(() => { });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n    }\n    subscribe(breakpointMap, fullMap) {\n        if (fullMap) {\n            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n            const get = () => this.matchMedia(breakpointMap, true);\n            return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged((x, y) => x[0] === y[0]), map(x => x[1]));\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n            const get = () => this.matchMedia(breakpointMap);\n            return this.resizeService.subscribe().pipe(map(get), startWith(get()), distinctUntilChanged());\n        }\n    }\n    matchMedia(breakpointMap, fullMap) {\n        let bp = NzBreakpointEnum.md;\n        const breakpointBooleanMap = {};\n        Object.keys(breakpointMap).map(breakpoint => {\n            const castBP = breakpoint;\n            const matched = this.mediaMatcher.matchMedia(gridResponsiveMap[castBP]).matches;\n            breakpointBooleanMap[breakpoint] = matched;\n            if (matched) {\n                bp = castBP;\n            }\n        });\n        if (fullMap) {\n            return [bp, breakpointBooleanMap];\n        }\n        else {\n            return bp;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreakpointService, deps: [{ token: NzResizeService }, { token: i2.MediaMatcher }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreakpointService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreakpointService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: NzResizeService }, { type: i2.MediaMatcher }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDestroyService extends Subject {\n    ngOnDestroy() {\n        this.next();\n        this.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDestroyService, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDestroyService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDestroyService, decorators: [{\n            type: Injectable\n        }] });\n\nclass ImagePreloadService {\n    constructor(document, platform) {\n        this.document = document;\n        this.platform = platform;\n        this.counter = new Map();\n        this.linkRefs = new Map();\n    }\n    addPreload(option) {\n        if (this.platform.isBrowser) {\n            return () => void 0;\n        }\n        const uniqueKey = `${option.src}${option.srcset}`;\n        let currentCount = this.counter.get(uniqueKey) || 0;\n        currentCount++;\n        this.counter.set(uniqueKey, currentCount);\n        if (!this.linkRefs.has(uniqueKey)) {\n            const linkNode = this.appendPreloadLink(option);\n            this.linkRefs.set(uniqueKey, linkNode);\n        }\n        return () => {\n            if (this.counter.has(uniqueKey)) {\n                let count = this.counter.get(uniqueKey);\n                count--;\n                if (count === 0) {\n                    const linkNode = this.linkRefs.get(uniqueKey);\n                    this.removePreloadLink(linkNode);\n                    this.counter.delete(uniqueKey);\n                    this.linkRefs.delete(uniqueKey);\n                }\n                else {\n                    this.counter.set(uniqueKey, count);\n                }\n            }\n        };\n    }\n    appendPreloadLink(option) {\n        const linkNode = this.document.createElement('link');\n        linkNode.setAttribute('rel', 'preload');\n        linkNode.setAttribute('as', 'image');\n        linkNode.setAttribute('href', option.src);\n        if (option.srcset) {\n            linkNode.setAttribute('imagesrcset', option.srcset);\n        }\n        this.document.head.appendChild(linkNode);\n        return linkNode;\n    }\n    removePreloadLink(linkNode) {\n        if (this.document.head.contains(linkNode)) {\n            this.document.head.removeChild(linkNode);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: ImagePreloadService, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: ImagePreloadService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: ImagePreloadService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ImagePreloadService, NzBreakpointEnum, NzBreakpointService, NzDestroyService, NzDragService, NzResizeService, NzScrollService, NzSingletonService, gridResponsiveMap, siderResponsiveMap };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAClD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC7G,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,OAAO,KAAKC,EAAE,MAAM,uBAAuB;;AAE3C;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,MAAM,EAAEC,gBAAgB,EAAE;IAClC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,aAAa,GAAG,IAAIpB,OAAO,CAAC,CAAC;IAClC,IAAI,CAACqB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,aAAa,GAAGP,IAAI;IACzB,IAAI,CAACQ,OAAO,GAAG,MAAM;MACjB,IAAI,CAACL,MAAM,CAACM,GAAG,CAAC,MAAM;QAClB,IAAI,CAACJ,aAAa,CAACK,IAAI,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACP,gBAAgB,CAACQ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EACpE;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACL,OAAO,GAAGR,IAAI;EACvB;EACAc,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAO,IAAI,CAACV,aAAa,CAACW,IAAI,CAAC9B,SAAS,CAAC,EAAE,CAAC,EAAEC,QAAQ,CAAC,MAAM,IAAI,CAAC8B,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAC5F;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,kBAAkB,CAAC,CAAC;EAC7B;EACAF,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACT,SAAS,KAAK,CAAC,EAAE;MACtB,IAAI,CAACH,MAAM,CAACgB,iBAAiB,CAAC,MAAM;QAChC,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACI,QAAQ,CAACS,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAACZ,OAAO,CAAC;MAC/E,CAAC,CAAC;IACN;IACA,IAAI,CAACF,SAAS,IAAI,CAAC;EACvB;EACAW,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACX,SAAS,IAAI,CAAC;IACnB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACtB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACA,aAAa,GAAGP,IAAI;IAC7B;EACJ;EACA;IAAS,IAAI,CAACqB,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtB,eAAe,EAAzBnB,EAAE,CAAA0C,QAAA,CAAyC1C,EAAE,CAAC2C,MAAM,GAApD3C,EAAE,CAAA0C,QAAA,CAA+D1C,EAAE,CAAC4C,gBAAgB;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAACC,KAAK,kBAD6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EACY5B,eAAe;MAAA6B,OAAA,EAAf7B,eAAe,CAAAoB,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGlD,EAAE,CAAAmD,iBAAA,CAGXhC,eAAe,EAAc,CAAC;IAC7GiC,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEpD,EAAE,CAAC2C;EAAO,CAAC,EAAE;IAAES,IAAI,EAAEpD,EAAE,CAAC4C;EAAiB,CAAC,CAAC;AAAA;;AAEtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBpC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqC,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;EACvC;EACA,IAAIG,iBAAiBA,CAAA,EAAG;IACpB,OAAO/C,WAAW,CAACgD,UAAU,GAAGL,kBAAkB,GAAG,IAAI,CAACG,kBAAkB;EAChF;EACAG,wBAAwBA,CAACC,GAAG,EAAEC,MAAM,EAAE;IAClC,MAAMC,WAAW,GAAG,IAAI,CAACL,iBAAiB,CAACM,GAAG,CAACH,GAAG,CAAC;IACnD,MAAMI,IAAI,GAAGF,WAAW,GAAG,IAAI,CAACL,iBAAiB,CAACQ,GAAG,CAACL,GAAG,CAAC,GAAG,IAAI,CAACM,aAAa,CAACL,MAAM,CAAC;IACvF,IAAI,CAACC,WAAW,EAAE;MACd,IAAI,CAACL,iBAAiB,CAACU,GAAG,CAACP,GAAG,EAAEI,IAAI,CAAC;IACzC;EACJ;EACAI,0BAA0BA,CAACR,GAAG,EAAE;IAC5B,IAAI,IAAI,CAACH,iBAAiB,CAACM,GAAG,CAACH,GAAG,CAAC,EAAE;MACjC,IAAI,CAACH,iBAAiB,CAACY,MAAM,CAACT,GAAG,CAAC;IACtC;EACJ;EACAU,mBAAmBA,CAACV,GAAG,EAAE;IACrB,OAAO,IAAI,CAACH,iBAAiB,CAACM,GAAG,CAACH,GAAG,CAAC,GAAG,IAAI,CAACH,iBAAiB,CAACQ,GAAG,CAACL,GAAG,CAAC,CAACC,MAAM,GAAG,IAAI;EAC1F;EACAK,aAAaA,CAACL,MAAM,EAAE;IAClB,OAAO;MACHA;IACJ,CAAC;EACL;EACA;IAAS,IAAI,CAACvB,IAAI,YAAAiC,2BAAA/B,CAAA;MAAA,YAAAA,CAAA,IAAwFe,kBAAkB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACX,KAAK,kBAzD6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EAyDYS,kBAAkB;MAAAR,OAAA,EAAlBQ,kBAAkB,CAAAjB,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AAC7J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3DoGlD,EAAE,CAAAmD,iBAAA,CA2DXK,kBAAkB,EAAc,CAAC;IAChHJ,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,SAASwB,eAAeA,CAACC,KAAK,EAAE;EAC5B,MAAMC,CAAC,GAAG/D,gBAAgB,CAAC8D,KAAK,CAAC;EACjC,OAAO;IACHE,CAAC,EAAED,CAAC,CAACE,KAAK;IACVC,CAAC,EAAEH,CAAC,CAACI;EACT,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB5D,WAAWA,CAACE,gBAAgB,EAAE;IAC1B,IAAI,CAAC2D,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACxD,QAAQ,GAAGP,gBAAgB,CAACQ,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/D;EACAwD,uBAAuBA,CAACZ,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACU,cAAc,CAACG,IAAI,EAAE;MAC3B,IAAI,CAACC,uBAAuB,CAAC3E,YAAY,CAAC6D,KAAK,CAAC,CAAC;IACrD;IACA;IACA,IAAI,IAAI,CAACQ,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACO,QAAQ,CAAC,CAAC;IAC3C;IACA,IAAI,CAACN,oBAAoB,GAAGV,eAAe,CAACC,KAAK,CAAC;IAClD,IAAI,CAACQ,uBAAuB,GAAG,IAAI/E,OAAO,CAAC,CAAC;IAC5C,OAAO,IAAI,CAAC+E,uBAAuB,CAAChD,IAAI,CAAC5B,GAAG,CAAEqE,CAAC,KAAM;MACjDC,CAAC,EAAED,CAAC,CAACE,KAAK,GAAG,IAAI,CAACM,oBAAoB,CAACP,CAAC;MACxCE,CAAC,EAAEH,CAAC,CAACI,KAAK,GAAG,IAAI,CAACI,oBAAoB,CAACL;IAC3C,CAAC,CAAC,CAAC,EAAEvE,MAAM,CAAEoE,CAAC,IAAKe,IAAI,CAACC,GAAG,CAAChB,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI,CAACK,iBAAiB,IAAIS,IAAI,CAACC,GAAG,CAAChB,CAAC,CAACG,CAAC,CAAC,GAAG,IAAI,CAACG,iBAAiB,CAAC,EAAE5E,QAAQ,CAAC,MAAM,IAAI,CAACuF,wBAAwB,CAAC,CAAC,CAAC,CAAC;EAC1J;EACAJ,uBAAuBA,CAACK,OAAO,EAAE;IAC7B,IAAIA,OAAO,EAAE;MACT,IAAI,CAACT,cAAc,CAACU,GAAG,CAAC;QACpBC,QAAQ,EAAE,IAAI,CAAClE,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGqC,CAAC,IAAK;UAC3D,IAAI,IAAI,CAACO,uBAAuB,EAAE;YAC9B,IAAI,CAACA,uBAAuB,CAACtD,IAAI,CAAC+C,CAAC,CAACqB,OAAO,CAAC,CAAC,CAAC,IAAIrB,CAAC,CAACsB,cAAc,CAAC,CAAC,CAAC,CAAC;UAC1E;QACJ,CAAC;MACL,CAAC,CAAC;MACF,IAAI,CAACb,cAAc,CAACU,GAAG,CAAC;QACpBC,QAAQ,EAAE,IAAI,CAAClE,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM;UACzD,IAAI,IAAI,CAAC4C,uBAAuB,EAAE;YAC9B,IAAI,CAACA,uBAAuB,CAACO,QAAQ,CAAC,CAAC;UAC3C;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACL,cAAc,CAACU,GAAG,CAAC;QACpBC,QAAQ,EAAE,IAAI,CAAClE,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE,WAAW,EAAEqC,CAAC,IAAI;UACzD,IAAI,IAAI,CAACO,uBAAuB,EAAE;YAC9B,IAAI,CAACA,uBAAuB,CAACtD,IAAI,CAAC+C,CAAC,CAAC;UACxC;QACJ,CAAC;MACL,CAAC,CAAC;MACF,IAAI,CAACS,cAAc,CAACU,GAAG,CAAC;QACpBC,QAAQ,EAAE,IAAI,CAAClE,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM;UACxD,IAAI,IAAI,CAAC4C,uBAAuB,EAAE;YAC9B,IAAI,CAACA,uBAAuB,CAACO,QAAQ,CAAC,CAAC;UAC3C;QACJ,CAAC;MACL,CAAC,CAAC;IACN;EACJ;EACAG,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACV,uBAAuB,GAAG,IAAI;EACvC;EACA;IAAS,IAAI,CAAC3C,IAAI,YAAA2D,sBAAAzD,CAAA;MAAA,YAAAA,CAAA,IAAwFuC,aAAa,EA5IvBhF,EAAE,CAAA0C,QAAA,CA4IuC1C,EAAE,CAAC4C,gBAAgB;IAAA,CAA6C;EAAE;EAC3M;IAAS,IAAI,CAACC,KAAK,kBA7I6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EA6IYiC,aAAa;MAAAhC,OAAA,EAAbgC,aAAa,CAAAzC,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/IoGlD,EAAE,CAAAmD,iBAAA,CA+IX6B,aAAa,EAAc,CAAC;IAC3G5B,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEpD,EAAE,CAAC4C;EAAiB,CAAC,CAAC;AAAA;;AAEjE;AACA;AACA;AACA;AACA,SAASuD,cAAcA,CAAC1D,CAAC,EAAE2D,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChC,MAAMC,EAAE,GAAGF,CAAC,GAAGD,CAAC;EAChB,IAAII,EAAE,GAAG/D,CAAC,IAAI6D,CAAC,GAAG,CAAC,CAAC;EACpB,IAAIE,EAAE,GAAG,CAAC,EAAE;IACR,OAAQD,EAAE,GAAG,CAAC,GAAIC,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAGJ,CAAC;EACtC,CAAC,MACI;IACD,OAAQG,EAAE,GAAG,CAAC,IAAK,CAACC,EAAE,IAAI,CAAC,IAAIA,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAGJ,CAAC;EACnD;AACJ;AACA,MAAMK,eAAe,CAAC;EAClBrF,WAAWA,CAACC,MAAM,EAAEqF,GAAG,EAAE;IACrB,IAAI,CAACrF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqF,GAAG,GAAGA,GAAG;EAClB;EACA;EACAC,YAAYA,CAACC,EAAE,EAAEC,QAAQ,GAAG,CAAC,EAAE;IAC3B,IAAID,EAAE,KAAKE,MAAM,EAAE;MACf,IAAI,CAACJ,GAAG,CAACK,IAAI,CAACC,SAAS,GAAGH,QAAQ;MAClC,IAAI,CAACH,GAAG,CAACO,eAAe,CAACD,SAAS,GAAGH,QAAQ;IACjD,CAAC,MACI;MACDD,EAAE,CAACI,SAAS,GAAGH,QAAQ;IAC3B;EACJ;EACA;EACAK,SAASA,CAACN,EAAE,EAAE;IACV,MAAMO,GAAG,GAAG;MACRC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACT,EAAE,IAAI,CAACA,EAAE,CAACU,cAAc,CAAC,CAAC,CAACC,MAAM,EAAE;MACpC,OAAOJ,GAAG;IACd;IACA,MAAMK,IAAI,GAAGZ,EAAE,CAACa,qBAAqB,CAAC,CAAC;IACvC,IAAID,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;MAC3B,MAAMjB,GAAG,GAAGE,EAAE,CAACgB,aAAa,CAACX,eAAe;MAC5CE,GAAG,CAACC,GAAG,GAAGI,IAAI,CAACJ,GAAG,GAAGV,GAAG,CAACmB,SAAS;MAClCV,GAAG,CAACE,IAAI,GAAGG,IAAI,CAACH,IAAI,GAAGX,GAAG,CAACoB,UAAU;IACzC,CAAC,MACI;MACDX,GAAG,CAACC,GAAG,GAAGI,IAAI,CAACJ,GAAG;MAClBD,GAAG,CAACE,IAAI,GAAGG,IAAI,CAACH,IAAI;IACxB;IACA,OAAOF,GAAG;EACd;EACA;EACA;EACAY,SAASA,CAACjE,MAAM,EAAEsD,GAAG,GAAG,IAAI,EAAE;IAC1B,IAAI,OAAON,MAAM,KAAK,WAAW,EAAE;MAC/B,OAAO,CAAC;IACZ;IACA,MAAMkB,MAAM,GAAGZ,GAAG,GAAG,WAAW,GAAG,YAAY;IAC/C,IAAIa,MAAM,GAAG,CAAC;IACd,IAAI,IAAI,CAACC,QAAQ,CAACpE,MAAM,CAAC,EAAE;MACvBmE,MAAM,GAAGnE,MAAM,CAACsD,GAAG,GAAG,aAAa,GAAG,aAAa,CAAC;IACxD,CAAC,MACI,IAAItD,MAAM,YAAYqE,QAAQ,EAAE;MACjCF,MAAM,GAAGnE,MAAM,CAACmD,eAAe,CAACe,MAAM,CAAC;IAC3C,CAAC,MACI,IAAIlE,MAAM,EAAE;MACbmE,MAAM,GAAGnE,MAAM,CAACkE,MAAM,CAAC;IAC3B;IACA,IAAIlE,MAAM,IAAI,CAAC,IAAI,CAACoE,QAAQ,CAACpE,MAAM,CAAC,IAAI,OAAOmE,MAAM,KAAK,QAAQ,EAAE;MAChEA,MAAM,GAAG,CAACnE,MAAM,CAAC8D,aAAa,IAAI9D,MAAM,EAAEmD,eAAe,CAACe,MAAM,CAAC;IACrE;IACA,OAAOC,MAAM;EACjB;EACAC,QAAQA,CAACE,GAAG,EAAE;IACV,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAKA,GAAG,CAACtB,MAAM;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwB,QAAQA,CAACC,WAAW,EAAEzD,CAAC,GAAG,CAAC,EAAE0D,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,MAAM1E,MAAM,GAAGyE,WAAW,GAAGA,WAAW,GAAGzB,MAAM;IACjD,MAAME,SAAS,GAAG,IAAI,CAACe,SAAS,CAACjE,MAAM,CAAC;IACxC,MAAM2E,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAM;MAAEC,MAAM;MAAEC,QAAQ;MAAEC,QAAQ,GAAG;IAAI,CAAC,GAAGN,OAAO;IACpD,MAAMO,SAAS,GAAGA,CAAA,KAAM;MACpB,MAAMC,SAAS,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMM,IAAI,GAAGD,SAAS,GAAGP,SAAS;MAClC,MAAMS,aAAa,GAAG,CAACN,MAAM,IAAIzC,cAAc,EAAE8C,IAAI,GAAGH,QAAQ,GAAGA,QAAQ,GAAGG,IAAI,EAAEjC,SAAS,EAAElC,CAAC,EAAEgE,QAAQ,CAAC;MAC3G,IAAI,IAAI,CAACZ,QAAQ,CAACpE,MAAM,CAAC,EAAE;QACvBA,MAAM,CAACwE,QAAQ,CAACxB,MAAM,CAACqC,WAAW,EAAED,aAAa,CAAC;MACtD,CAAC,MACI,IAAIpF,MAAM,YAAYsF,YAAY,IAAItF,MAAM,CAAC1C,WAAW,CAACiI,IAAI,KAAK,cAAc,EAAE;QACnFvF,MAAM,CAACmD,eAAe,CAACD,SAAS,GAAGkC,aAAa;MACpD,CAAC,MACI;QACDpF,MAAM,CAACkD,SAAS,GAAGkC,aAAa;MACpC;MACA,IAAID,IAAI,GAAGH,QAAQ,EAAE;QACjB/H,YAAY,CAACgI,SAAS,CAAC;MAC3B,CAAC,MACI,IAAI,OAAOF,QAAQ,KAAK,UAAU,EAAE;QACrC;QACA;QACA,IAAI,CAACxH,MAAM,CAACM,GAAG,CAACkH,QAAQ,CAAC;MAC7B;IACJ,CAAC;IACD;IACA;IACA,IAAI,CAACxH,MAAM,CAACgB,iBAAiB,CAAC,MAAMtB,YAAY,CAACgI,SAAS,CAAC,CAAC;EAChE;EACA;IAAS,IAAI,CAACxG,IAAI,YAAA+G,wBAAA7G,CAAA;MAAA,YAAAA,CAAA,IAAwFgE,eAAe,EAtQzBzG,EAAE,CAAA0C,QAAA,CAsQyC1C,EAAE,CAAC2C,MAAM,GAtQpD3C,EAAE,CAAA0C,QAAA,CAsQ+D5B,QAAQ;IAAA,CAA6C;EAAE;EACxN;IAAS,IAAI,CAAC+B,KAAK,kBAvQ6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EAuQY0D,eAAe;MAAAzD,OAAA,EAAfyD,eAAe,CAAAlE,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzQoGlD,EAAE,CAAAmD,iBAAA,CAyQXsD,eAAe,EAAc,CAAC;IAC7GrD,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEpD,EAAE,CAAC2C;EAAO,CAAC,EAAE;IAAES,IAAI,EAAEiF,SAAS;IAAEkB,UAAU,EAAE,CAAC;MACpEnG,IAAI,EAAElD,MAAM;MACZmD,IAAI,EAAE,CAACvC,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,IAAI0I,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK;EAC/BA,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7BA,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7BA,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7BA,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7BA,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI;AACjC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,iBAAiB,GAAG;EACtBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,qBAAqB;EACzBC,GAAG,EAAE;AACT,CAAC;AACD,MAAMC,kBAAkB,GAAG;EACvBN,EAAE,EAAE,uBAAuB;EAC3BC,EAAE,EAAE,uBAAuB;EAC3BC,EAAE,EAAE,uBAAuB;EAC3BC,EAAE,EAAE,uBAAuB;EAC3BC,EAAE,EAAE,wBAAwB;EAC5BC,GAAG,EAAE;AACT,CAAC;AACD,MAAME,mBAAmB,CAAC;EACtB7I,WAAWA,CAAC8I,aAAa,EAAEC,YAAY,EAAE;IACrC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,QAAQ,GAAG,IAAIjK,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC+J,aAAa,CACblI,SAAS,CAAC,CAAC,CACXE,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAAC4J,QAAQ,CAAC,CAAC,CAC9BpI,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC7B;EACAD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqI,QAAQ,CAACxI,IAAI,CAAC,CAAC;EACxB;EACAI,SAASA,CAACqI,aAAa,EAAEC,OAAO,EAAE;IAC9B,IAAIA,OAAO,EAAE;MACT;MACA,MAAMpG,GAAG,GAAGA,CAAA,KAAM,IAAI,CAACqG,UAAU,CAACF,aAAa,EAAE,IAAI,CAAC;MACtD,OAAO,IAAI,CAACH,aAAa,CAAClI,SAAS,CAAC,CAAC,CAACE,IAAI,CAAC5B,GAAG,CAAC4D,GAAG,CAAC,EAAEzD,SAAS,CAACyD,GAAG,CAAC,CAAC,CAAC,EAAExD,oBAAoB,CAAC,CAACkE,CAAC,EAAEE,CAAC,KAAKF,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAExE,GAAG,CAACsE,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzI,CAAC,MACI;MACD;MACA,MAAMV,GAAG,GAAGA,CAAA,KAAM,IAAI,CAACqG,UAAU,CAACF,aAAa,CAAC;MAChD,OAAO,IAAI,CAACH,aAAa,CAAClI,SAAS,CAAC,CAAC,CAACE,IAAI,CAAC5B,GAAG,CAAC4D,GAAG,CAAC,EAAEzD,SAAS,CAACyD,GAAG,CAAC,CAAC,CAAC,EAAExD,oBAAoB,CAAC,CAAC,CAAC;IAClG;EACJ;EACA6J,UAAUA,CAACF,aAAa,EAAEC,OAAO,EAAE;IAC/B,IAAIE,EAAE,GAAGhB,gBAAgB,CAACI,EAAE;IAC5B,MAAMa,oBAAoB,GAAG,CAAC,CAAC;IAC/BC,MAAM,CAACC,IAAI,CAACN,aAAa,CAAC,CAAC/J,GAAG,CAACsK,UAAU,IAAI;MACzC,MAAMC,MAAM,GAAGD,UAAU;MACzB,MAAME,OAAO,GAAG,IAAI,CAACX,YAAY,CAACI,UAAU,CAACd,iBAAiB,CAACoB,MAAM,CAAC,CAAC,CAACE,OAAO;MAC/EN,oBAAoB,CAACG,UAAU,CAAC,GAAGE,OAAO;MAC1C,IAAIA,OAAO,EAAE;QACTN,EAAE,GAAGK,MAAM;MACf;IACJ,CAAC,CAAC;IACF,IAAIP,OAAO,EAAE;MACT,OAAO,CAACE,EAAE,EAAEC,oBAAoB,CAAC;IACrC,CAAC,MACI;MACD,OAAOD,EAAE;IACb;EACJ;EACA;IAAS,IAAI,CAACjI,IAAI,YAAAyI,4BAAAvI,CAAA;MAAA,YAAAA,CAAA,IAAwFwH,mBAAmB,EAvV7BjK,EAAE,CAAA0C,QAAA,CAuV6CvB,eAAe,GAvV9DnB,EAAE,CAAA0C,QAAA,CAuVyE1B,EAAE,CAACiK,YAAY;IAAA,CAA6C;EAAE;EACzO;IAAS,IAAI,CAACpI,KAAK,kBAxV6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EAwVYkH,mBAAmB;MAAAjH,OAAA,EAAnBiH,mBAAmB,CAAA1H,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1VoGlD,EAAE,CAAAmD,iBAAA,CA0VX8G,mBAAmB,EAAc,CAAC;IACjH7G,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEjC;EAAgB,CAAC,EAAE;IAAEiC,IAAI,EAAEpC,EAAE,CAACiK;EAAa,CAAC,CAAC;AAAA;;AAExF;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,SAAS/K,OAAO,CAAC;EACnC4B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,IAAI,CAAC,CAAC;IACX,IAAI,CAAC6D,QAAQ,CAAC,CAAC;EACnB;EACA;IAAS,IAAI,CAAClD,IAAI;MAAA,IAAA4I,6BAAA;MAAA,gBAAAC,yBAAA3I,CAAA;QAAA,QAAA0I,6BAAA,KAAAA,6BAAA,GA1W8EnL,EAAE,CAAAqL,qBAAA,CA0WQH,gBAAgB,IAAAzI,CAAA,IAAhByI,gBAAgB;MAAA;IAAA,IAAsD;EAAE;EAClL;IAAS,IAAI,CAACrI,KAAK,kBA3W6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EA2WYmI,gBAAgB;MAAAlI,OAAA,EAAhBkI,gBAAgB,CAAA3I;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KA7WoGlD,EAAE,CAAAmD,iBAAA,CA6WX+H,gBAAgB,EAAc,CAAC;IAC9G9H,IAAI,EAAEnD;EACV,CAAC,CAAC;AAAA;AAEV,MAAMqL,mBAAmB,CAAC;EACtBlK,WAAWA,CAACmK,QAAQ,EAAEC,QAAQ,EAAE;IAC5B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAG,IAAIlI,GAAG,CAAC,CAAC;IACxB,IAAI,CAACmI,QAAQ,GAAG,IAAInI,GAAG,CAAC,CAAC;EAC7B;EACAoI,UAAUA,CAACC,MAAM,EAAE;IACf,IAAI,IAAI,CAACJ,QAAQ,CAACK,SAAS,EAAE;MACzB,OAAO,MAAM,KAAK,CAAC;IACvB;IACA,MAAMC,SAAS,GAAG,GAAGF,MAAM,CAACG,GAAG,GAAGH,MAAM,CAACI,MAAM,EAAE;IACjD,IAAIC,YAAY,GAAG,IAAI,CAACR,OAAO,CAACvH,GAAG,CAAC4H,SAAS,CAAC,IAAI,CAAC;IACnDG,YAAY,EAAE;IACd,IAAI,CAACR,OAAO,CAACrH,GAAG,CAAC0H,SAAS,EAAEG,YAAY,CAAC;IACzC,IAAI,CAAC,IAAI,CAACP,QAAQ,CAAC1H,GAAG,CAAC8H,SAAS,CAAC,EAAE;MAC/B,MAAMI,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACP,MAAM,CAAC;MAC/C,IAAI,CAACF,QAAQ,CAACtH,GAAG,CAAC0H,SAAS,EAAEI,QAAQ,CAAC;IAC1C;IACA,OAAO,MAAM;MACT,IAAI,IAAI,CAACT,OAAO,CAACzH,GAAG,CAAC8H,SAAS,CAAC,EAAE;QAC7B,IAAIM,KAAK,GAAG,IAAI,CAACX,OAAO,CAACvH,GAAG,CAAC4H,SAAS,CAAC;QACvCM,KAAK,EAAE;QACP,IAAIA,KAAK,KAAK,CAAC,EAAE;UACb,MAAMF,QAAQ,GAAG,IAAI,CAACR,QAAQ,CAACxH,GAAG,CAAC4H,SAAS,CAAC;UAC7C,IAAI,CAACO,iBAAiB,CAACH,QAAQ,CAAC;UAChC,IAAI,CAACT,OAAO,CAACnH,MAAM,CAACwH,SAAS,CAAC;UAC9B,IAAI,CAACJ,QAAQ,CAACpH,MAAM,CAACwH,SAAS,CAAC;QACnC,CAAC,MACI;UACD,IAAI,CAACL,OAAO,CAACrH,GAAG,CAAC0H,SAAS,EAAEM,KAAK,CAAC;QACtC;MACJ;IACJ,CAAC;EACL;EACAD,iBAAiBA,CAACP,MAAM,EAAE;IACtB,MAAMM,QAAQ,GAAG,IAAI,CAACX,QAAQ,CAACe,aAAa,CAAC,MAAM,CAAC;IACpDJ,QAAQ,CAACK,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC;IACvCL,QAAQ,CAACK,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;IACpCL,QAAQ,CAACK,YAAY,CAAC,MAAM,EAAEX,MAAM,CAACG,GAAG,CAAC;IACzC,IAAIH,MAAM,CAACI,MAAM,EAAE;MACfE,QAAQ,CAACK,YAAY,CAAC,aAAa,EAAEX,MAAM,CAACI,MAAM,CAAC;IACvD;IACA,IAAI,CAACT,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;IACxC,OAAOA,QAAQ;EACnB;EACAG,iBAAiBA,CAACH,QAAQ,EAAE;IACxB,IAAI,IAAI,CAACX,QAAQ,CAACiB,IAAI,CAACE,QAAQ,CAACR,QAAQ,CAAC,EAAE;MACvC,IAAI,CAACX,QAAQ,CAACiB,IAAI,CAACG,WAAW,CAACT,QAAQ,CAAC;IAC5C;EACJ;EACA;IAAS,IAAI,CAAC3J,IAAI,YAAAqK,4BAAAnK,CAAA;MAAA,YAAAA,CAAA,IAAwF6I,mBAAmB,EApa7BtL,EAAE,CAAA0C,QAAA,CAoa6C5B,QAAQ,GApavDd,EAAE,CAAA0C,QAAA,CAoakEzB,EAAE,CAAC4L,QAAQ;IAAA,CAA6C;EAAE;EAC9N;IAAS,IAAI,CAAChK,KAAK,kBAra6E7C,EAAE,CAAA8C,kBAAA;MAAAC,KAAA,EAqaYuI,mBAAmB;MAAAtI,OAAA,EAAnBsI,mBAAmB,CAAA/I,IAAA;MAAAU,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvaoGlD,EAAE,CAAAmD,iBAAA,CAuaXmI,mBAAmB,EAAc,CAAC;IACjHlI,IAAI,EAAEnD,UAAU;IAChBoD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEiF,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAC/CnG,IAAI,EAAElD,MAAM;MACZmD,IAAI,EAAE,CAACvC,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEsC,IAAI,EAAEnC,EAAE,CAAC4L;EAAS,CAAC,CAAC;AAAA;;AAE5C;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASvB,mBAAmB,EAAE9B,gBAAgB,EAAES,mBAAmB,EAAEiB,gBAAgB,EAAElG,aAAa,EAAE7D,eAAe,EAAEsF,eAAe,EAAEjD,kBAAkB,EAAEiG,iBAAiB,EAAEO,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}