{"ast": null, "code": "export default function buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // TODO: Remove String()\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}", "map": {"version": 3, "names": ["buildFormatLongFn", "args", "options", "arguments", "length", "undefined", "width", "String", "defaultWidth", "format", "formats"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/_lib/buildFormatLongFn/index.js"], "sourcesContent": ["export default function buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // TODO: Remove String()\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,OAAO,YAAY;IACjB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF;IACA,IAAIG,KAAK,GAAGJ,OAAO,CAACI,KAAK,GAAGC,MAAM,CAACL,OAAO,CAACI,KAAK,CAAC,GAAGL,IAAI,CAACO,YAAY;IACrE,IAAIC,MAAM,GAAGR,IAAI,CAACS,OAAO,CAACJ,KAAK,CAAC,IAAIL,IAAI,CAACS,OAAO,CAACT,IAAI,CAACO,YAAY,CAAC;IACnE,OAAOC,MAAM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}