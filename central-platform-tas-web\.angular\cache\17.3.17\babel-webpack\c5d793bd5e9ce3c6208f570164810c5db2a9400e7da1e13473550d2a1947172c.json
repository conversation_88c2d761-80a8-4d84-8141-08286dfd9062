{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '1びょうみまん',\n    other: '{{count}}びょうみまん',\n    oneWithSuffix: 'やく1びょう',\n    otherWithSuffix: 'やく{{count}}びょう'\n  },\n  xSeconds: {\n    one: '1びょう',\n    other: '{{count}}びょう'\n  },\n  halfAMinute: '30びょう',\n  lessThanXMinutes: {\n    one: '1ぷんみまん',\n    other: '{{count}}ふんみまん',\n    oneWithSuffix: 'やく1ぷん',\n    otherWithSuffix: 'やく{{count}}ふん'\n  },\n  xMinutes: {\n    one: '1ぷん',\n    other: '{{count}}ふん'\n  },\n  aboutXHours: {\n    one: 'やく1じかん',\n    other: 'やく{{count}}じかん'\n  },\n  xHours: {\n    one: '1じかん',\n    other: '{{count}}じかん'\n  },\n  xDays: {\n    one: '1にち',\n    other: '{{count}}にち'\n  },\n  aboutXWeeks: {\n    one: 'やく1しゅうかん',\n    other: 'やく{{count}}しゅうかん'\n  },\n  xWeeks: {\n    one: '1しゅうかん',\n    other: '{{count}}しゅうかん'\n  },\n  aboutXMonths: {\n    one: 'やく1かげつ',\n    other: 'やく{{count}}かげつ'\n  },\n  xMonths: {\n    one: '1かげつ',\n    other: '{{count}}かげつ'\n  },\n  aboutXYears: {\n    one: 'やく1ねん',\n    other: 'やく{{count}}ねん'\n  },\n  xYears: {\n    one: '1ねん',\n    other: '{{count}}ねん'\n  },\n  overXYears: {\n    one: '1ねんいじょう',\n    other: '{{count}}ねんいじょう'\n  },\n  almostXYears: {\n    one: '1ねんちかく',\n    other: '{{count}}ねんちかく'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'あと';\n    } else {\n      return result + 'まえ';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "oneWithSuffix", "otherWithSuffix", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "replace", "String", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ja-Hira/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '1びょうみまん',\n    other: '{{count}}びょうみまん',\n    oneWithSuffix: 'やく1びょう',\n    otherWithSuffix: 'やく{{count}}びょう'\n  },\n  xSeconds: {\n    one: '1びょう',\n    other: '{{count}}びょう'\n  },\n  halfAMinute: '30びょう',\n  lessThanXMinutes: {\n    one: '1ぷんみまん',\n    other: '{{count}}ふんみまん',\n    oneWithSuffix: 'やく1ぷん',\n    otherWithSuffix: 'やく{{count}}ふん'\n  },\n  xMinutes: {\n    one: '1ぷん',\n    other: '{{count}}ふん'\n  },\n  aboutXHours: {\n    one: 'やく1じかん',\n    other: 'やく{{count}}じかん'\n  },\n  xHours: {\n    one: '1じかん',\n    other: '{{count}}じかん'\n  },\n  xDays: {\n    one: '1にち',\n    other: '{{count}}にち'\n  },\n  aboutXWeeks: {\n    one: 'やく1しゅうかん',\n    other: 'やく{{count}}しゅうかん'\n  },\n  xWeeks: {\n    one: '1しゅうかん',\n    other: '{{count}}しゅうかん'\n  },\n  aboutXMonths: {\n    one: 'やく1かげつ',\n    other: 'やく{{count}}かげつ'\n  },\n  xMonths: {\n    one: '1かげつ',\n    other: '{{count}}かげつ'\n  },\n  aboutXYears: {\n    one: 'やく1ねん',\n    other: 'やく{{count}}ねん'\n  },\n  xYears: {\n    one: '1ねん',\n    other: '{{count}}ねん'\n  },\n  overXYears: {\n    one: '1ねんいじょう',\n    other: '{{count}}ねんいじょう'\n  },\n  almostXYears: {\n    one: '1ねんちかく',\n    other: '{{count}}ねんちかく'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + 'あと';\n    } else {\n      return result + 'まえ';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,iBAAiB;IACxBC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE;EACnB,CAAC;EACDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDI,WAAW,EAAE,OAAO;EACpBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,gBAAgB;IACvBC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE;EACnB,CAAC;EACDI,QAAQ,EAAE;IACRP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDO,WAAW,EAAE;IACXR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDS,KAAK,EAAE;IACLV,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDU,WAAW,EAAE;IACXX,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDW,MAAM,EAAE;IACNZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDY,YAAY,EAAE;IACZb,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDa,OAAO,EAAE;IACPd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,WAAW,EAAE;IACXf,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDe,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDgB,UAAU,EAAE;IACVjB,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDiB,YAAY,EAAE;IACZlB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIkB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIC,OAAO,CAACG,SAAS,IAAID,UAAU,CAACtB,aAAa,EAAE;MACjDqB,MAAM,GAAGC,UAAU,CAACtB,aAAa;IACnC,CAAC,MAAM;MACLqB,MAAM,GAAGC,UAAU,CAACxB,GAAG;IACzB;EACF,CAAC,MAAM;IACL,IAAIsB,OAAO,CAACG,SAAS,IAAID,UAAU,CAACrB,eAAe,EAAE;MACnDoB,MAAM,GAAGC,UAAU,CAACrB,eAAe,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IACzE,CAAC,MAAM;MACLE,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACyB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IAC/D;EACF;EACA,IAAIC,OAAO,CAACG,SAAS,EAAE;IACrB,IAAIH,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,IAAI;IACtB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}