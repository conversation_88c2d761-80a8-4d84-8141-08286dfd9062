{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomsCodeComponent } from './customscode.component';\nimport { CustomsCodeEditComponent } from '@business/tas/customscode/customscode-edit/customscode-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: CustomsCodeComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: CustomsCodeEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class CustomsCodeRoutingModule {\n  static {\n    this.ɵfac = function CustomsCodeRoutingModule_Factory(t) {\n      return new (t || CustomsCodeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomsCodeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomsCodeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomsCodeComponent", "CustomsCodeEditComponent", "routes", "path", "component", "data", "cache", "CustomsCodeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\customscode\\customscode-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CustomsCodeComponent } from './customscode.component';\r\nimport { CustomsCodeEditComponent } from '@business/tas/customscode/customscode-edit/customscode-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: CustomsCodeComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: CustomsCodeEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CustomsCodeRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,uEAAuE;;;AAChH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,oBAAoB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACxE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,wBAAwB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAClF;AAMD,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAHzBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,wBAAwB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFzBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}