{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_COMPARECODE_DTL extends CwfStore {\n  constructor() {\n    super({\n      id: \"对照码明细表主键\",\n      comparecode_id: \"对照码主键\",\n      original_value: \"原码\",\n      compare_value: \"对照后代码\",\n      remark: \"备注\",\n      created_user: \"创建人\",\n      created_time: \"创建时间\",\n      modified_user: \"修改人\",\n      modified_time: \"修改时间\",\n      is_delete: \"逻辑删除\"\n    });\n    this.actionId = ''; // action\n    this.queryOperation = ''; // 查询operation\n    this.saveOperation = ''; // 保存operation\n    this.tableName = 'TAS_T_COMPARECODE_DTL'; // 表名\n    this.idProperty = 'id'; // 主键名\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_COMPARECODE_DTL", "constructor", "id", "comparecode_id", "original_value", "compare_value", "remark", "created_user", "created_time", "modified_user", "modified_time", "is_delete", "actionId", "queryOperation", "saveOperation", "tableName", "idProperty"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_COMPARECODEDTL.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_COMPARECODE_DTL extends CwfStore {\r\n\r\n  actionId = ''; // action\r\n  queryOperation = ''; // 查询operation\r\n  saveOperation = ''; // 保存operation\r\n  tableName = 'TAS_T_COMPARECODE_DTL'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n\r\n  constructor() {\r\n    super({\r\n      id:\"对照码明细表主键\",\r\n      comparecode_id:\"对照码主键\",\r\n      original_value:\"原码\",\r\n      compare_value:\"对照后代码\",\r\n      remark:\"备注\",\r\n      created_user:\"创建人\",\r\n      created_time:\"创建时间\",\r\n      modified_user:\"修改人\",\r\n      modified_time:\"修改时间\",\r\n      is_delete:\"逻辑删除\",\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,sBAAuB,SAAQD,QAAQ;EAQlDE,YAAA;IACE,KAAK,CAAC;MACJC,EAAE,EAAC,UAAU;MACbC,cAAc,EAAC,OAAO;MACtBC,cAAc,EAAC,IAAI;MACnBC,aAAa,EAAC,OAAO;MACrBC,MAAM,EAAC,IAAI;MACXC,YAAY,EAAC,KAAK;MAClBC,YAAY,EAAC,MAAM;MACnBC,aAAa,EAAC,KAAK;MACnBC,aAAa,EAAC,MAAM;MACpBC,SAAS,EAAC;KACX,CAAC;IAlBJ,KAAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACf,KAAAC,cAAc,GAAG,EAAE,CAAC,CAAC;IACrB,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,SAAS,GAAG,uBAAuB,CAAC,CAAC;IACrC,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;EAenB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}