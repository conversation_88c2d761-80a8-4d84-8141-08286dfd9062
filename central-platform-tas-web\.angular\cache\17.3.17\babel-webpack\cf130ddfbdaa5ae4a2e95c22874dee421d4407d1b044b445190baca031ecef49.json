{"ast": null, "code": "import { VesselRoutingModule } from './vessel-routing.module';\nimport { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [];\nexport class VesselModule {\n  static {\n    this.ɵfac = function VesselModule_Factory(t) {\n      return new (t || VesselModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VesselRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselModule, {\n    imports: [SharedModule, VesselRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["VesselRoutingModule", "SharedModule", "LayoutModule", "COMPONENTS", "VesselModule", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { VesselRoutingModule } from './vessel-routing.module';\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\n\r\nconst COMPONENTS = [];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VesselRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VesselModule { }\r\n"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD,MAAMC,UAAU,GAAG,EAAE;AAMrB,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAHbH,YAAY,EAAED,mBAAmB,EAAEE,YAAY;IAAA;EAAA;;;2EAG9CE,YAAY;IAAAC,OAAA,GAHbJ,YAAY,EAAED,mBAAmB,EAAEE,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}