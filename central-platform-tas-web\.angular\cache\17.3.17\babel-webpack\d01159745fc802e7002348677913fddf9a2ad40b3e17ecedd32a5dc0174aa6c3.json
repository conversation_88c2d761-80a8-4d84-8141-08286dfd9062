{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { DamageAreaComponent } from './damagearea.component';\nimport { DamageAreaRoutingModule } from './damagearea-routing.module';\nimport { DamageAreaEditComponent } from '@business/tas/damagearea/damagearea-edit/damagearea-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [DamageAreaComponent, DamageAreaEditComponent];\nexport class DamageAreaModule {\n  static {\n    this.ɵfac = function DamageAreaModule_Factory(t) {\n      return new (t || DamageAreaModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DamageAreaModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, DamageAreaRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DamageAreaModule, {\n    declarations: [DamageAreaComponent, DamageAreaEditComponent],\n    imports: [SharedModule, DamageAreaRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "DamageAreaComponent", "DamageAreaRoutingModule", "DamageAreaEditComponent", "COMPONENTS", "DamageAreaModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\damagearea\\damagearea.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { DamageAreaComponent } from './damagearea.component';\r\nimport { DamageAreaRoutingModule } from './damagearea-routing.module';\r\nimport {DamageAreaEditComponent} from '@business/tas/damagearea/damagearea-edit/damagearea-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  DamageAreaComponent,\r\n  DamageAreaEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, DamageAreaRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class DamageAreaModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAAQC,uBAAuB,QAAO,oEAAoE;;AAE1G,MAAMC,UAAU,GAAG,CACjBH,mBAAmB,EACnBE,uBAAuB,CACxB;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBN,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;IAAA;EAAA;;;2EAGlDK,gBAAgB;IAAAC,YAAA,GAR3BL,mBAAmB,EACnBE,uBAAuB;IAAAI,OAAA,GAIbR,YAAY,EAAEG,uBAAuB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}