{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { DamageLevelComponent } from './damagelevel.component';\nimport { DamageLevelRoutingModule } from './damagelevel-routing.module';\nimport { DamageLevelEditComponent } from '@business/tas/damagelevel/damagelevel-edit/damagelevel-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [DamageLevelComponent, DamageLevelEditComponent];\nexport class DamageLevelModule {\n  static {\n    this.ɵfac = function DamageLevelModule_Factory(t) {\n      return new (t || DamageLevelModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DamageLevelModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, DamageLevelRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DamageLevelModule, {\n    declarations: [DamageLevelComponent, DamageLevelEditComponent],\n    imports: [SharedModule, DamageLevelRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "DamageLevelComponent", "DamageLevelRoutingModule", "DamageLevelEditComponent", "COMPONENTS", "DamageLevelModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\damagelevel\\damagelevel.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { DamageLevelComponent } from './damagelevel.component';\r\nimport { DamageLevelRoutingModule } from './damagelevel-routing.module';\r\nimport {DamageLevelEditComponent} from '@business/tas/damagelevel/damagelevel-edit/damagelevel-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  DamageLevelComponent,\r\n  DamageLevelEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, DamageLevelRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class DamageLevelModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}