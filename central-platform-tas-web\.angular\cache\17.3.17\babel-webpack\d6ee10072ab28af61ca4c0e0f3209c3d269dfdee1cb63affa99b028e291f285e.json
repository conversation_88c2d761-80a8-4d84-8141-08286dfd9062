{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  loadChildren: () => import('./vessel-list/vessel-list.module').then(m => m.VesselListModule),\n  data: {\n    cache: true\n  }\n}, {\n  path: 'bay',\n  loadChildren: () => import('./vessel-bay/vessel-bay.module').then(m => m.VesselBayModule),\n  data: {\n    cache: true\n  }\n}, {\n  path: 'kay',\n  loadChildren: () => import('./vessel-kay/vessel-kay.module').then(m => m.VesselKayModule),\n  data: {\n    cache: true\n  }\n}];\nexport class VesselRoutingModule {\n  static {\n    this.ɵfac = function VesselRoutingModule_Factory(t) {\n      return new (t || VesselRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "loadChildren", "then", "m", "VesselListModule", "data", "cache", "VesselBayModule", "VesselKayModule", "VesselRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'list',\r\n    loadChildren: () => import('./vessel-list/vessel-list.module').then(m => m.VesselListModule),\r\n    data: { cache: true }\r\n  },\r\n  {\r\n    path: 'bay',\r\n    loadChildren: () => import('./vessel-bay/vessel-bay.module').then(m => m.VesselBayModule),\r\n    data: { cache: true }\r\n  },\r\n  {\r\n    path: 'kay',\r\n    loadChildren: () => import('./vessel-kay/vessel-kay.module').then(m => m.VesselKayModule),\r\n    data: { cache: true }\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class VesselRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;EAC5FC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;CACpB,EACD;EACEN,IAAI,EAAE,KAAK;EACXC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,eAAe,CAAC;EACzFF,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;CACpB,EACD;EACEN,IAAI,EAAE,KAAK;EACXC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,eAAe,CAAC;EACzFH,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;CACpB,CACF;AAMD,OAAM,MAAOG,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBX,YAAY,CAACY,QAAQ,CAACX,MAAM,CAAC,EAC7BD,YAAY;IAAA;EAAA;;;2EAEXW,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFpBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}