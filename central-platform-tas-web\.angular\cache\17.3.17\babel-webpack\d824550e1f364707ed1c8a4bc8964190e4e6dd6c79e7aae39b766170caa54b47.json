{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { StationComponent } from './station.component';\nimport { StationEditComponent } from '@business/tas/station/station-edit/station-edit.component';\nimport { StationRoutingModule } from './station-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [StationComponent, StationEditComponent];\nexport class StationModule {\n  static {\n    this.ɵfac = function StationModule_Factory(t) {\n      return new (t || StationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StationModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, StationRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StationModule, {\n    declarations: [StationComponent, StationEditComponent],\n    imports: [SharedModule, StationRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "StationComponent", "StationEditComponent", "StationRoutingModule", "COMPONENTS", "StationModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\station\\station.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { StationComponent } from './station.component';\r\nimport { StationEditComponent } from '@business/tas/station/station-edit/station-edit.component';\r\nimport { StationRoutingModule } from './station-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  StationComponent,\r\n  StationEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, StationRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class StationModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,0BAA0B;;AAE/D,MAAMC,UAAU,GAAG,CACjBH,gBAAgB,EAChBC,oBAAoB,CACrB;AAMD,OAAM,MAAOG,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAHdN,YAAY,EAAEI,oBAAoB,EAAEH,YAAY;IAAA;EAAA;;;2EAG/CK,aAAa;IAAAC,YAAA,GARxBL,gBAAgB,EAChBC,oBAAoB;IAAAK,OAAA,GAIVR,YAAY,EAAEI,oBAAoB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}