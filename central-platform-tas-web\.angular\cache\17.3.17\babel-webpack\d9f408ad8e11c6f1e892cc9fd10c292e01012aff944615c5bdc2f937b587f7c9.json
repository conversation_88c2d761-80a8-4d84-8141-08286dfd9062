{"ast": null, "code": "import { CwfStore } from 'cwf-ng-library';\nexport class BASE_T_PARFILE extends CwfStore {\n  constructor() {\n    super({\n      id: '',\n      // 附件主键\n      businessTypeCode: '',\n      //\n      businessTypeName: '',\n      //\n      filePath: '',\n      //\n      fileSize: '',\n      //\n      mediaType: '',\n      //\n      module: '',\n      //\n      originalFileName: '',\n      //文件名称\n      url: '' //\n    });\n    this.actionId = 'basecode.PartnerService'; // action\n    this.queryOperation = 'queryInfo'; // 查询operation\n    this.saveOperation = 'save'; // 保存operation\n    this.talbeName = 'BASE_T_PARFILE'; // 表名\n    this.idProperty = 'id'; // 主键名\n    this.serviceName = 'bc';\n  }\n}", "map": {"version": 3, "names": ["CwfStore", "BASE_T_PARFILE", "constructor", "id", "businessTypeCode", "businessTypeName", "filePath", "fileSize", "mediaType", "module", "originalFileName", "url", "actionId", "queryOperation", "saveOperation", "talbeName", "idProperty", "serviceName"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\store\\BCD\\BASE_T_PARFILE.ts"], "sourcesContent": ["import { CwfStore } from 'cwf-ng-library';\r\n\r\nexport class BASE_T_PARFILE extends CwfStore {\r\n  actionId = 'basecode.PartnerService'; // action\r\n  queryOperation = 'queryInfo'; // 查询operation\r\n  saveOperation = 'save'; // 保存operation\r\n  talbeName = 'BASE_T_PARFILE'; // 表名\r\n  idProperty = 'id'; // 主键名\r\n  serviceName = 'bc';\r\n\r\n  constructor() {\r\n    super({\r\n      id: '', // 附件主键\r\n      businessTypeCode: '', //\r\n      businessTypeName: '', //\r\n      filePath: '', //\r\n      fileSize: '', //\r\n      mediaType: '', //\r\n      module: '', //\r\n      originalFileName: '', //文件名称\r\n      url: '', //\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,OAAM,MAAOC,cAAe,SAAQD,QAAQ;EAQ1CE,YAAA;IACE,KAAK,CAAC;MACJC,EAAE,EAAE,EAAE;MAAE;MACRC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,QAAQ,EAAE,EAAE;MAAE;MACdC,QAAQ,EAAE,EAAE;MAAE;MACdC,SAAS,EAAE,EAAE;MAAE;MACfC,MAAM,EAAE,EAAE;MAAE;MACZC,gBAAgB,EAAE,EAAE;MAAE;MACtBC,GAAG,EAAE,EAAE,CAAE;KACV,CAAC;IAlBJ,KAAAC,QAAQ,GAAG,yBAAyB,CAAC,CAAC;IACtC,KAAAC,cAAc,GAAG,WAAW,CAAC,CAAC;IAC9B,KAAAC,aAAa,GAAG,MAAM,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;IAC9B,KAAAC,UAAU,GAAG,IAAI,CAAC,CAAC;IACnB,KAAAC,WAAW,GAAG,IAAI;EAclB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}