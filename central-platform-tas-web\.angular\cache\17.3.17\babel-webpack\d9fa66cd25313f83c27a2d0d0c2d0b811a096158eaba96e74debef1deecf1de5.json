{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { CapacityComponent } from './capacity.component';\nimport { CapacityRoutingModule } from './capacity-routing.module';\nimport { CapacityEditComponent } from '@business/tas/capacity/capacity-edit/capacity-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [CapacityComponent, CapacityEditComponent];\nexport class CapacityModule {\n  static {\n    this.ɵfac = function CapacityModule_Factory(t) {\n      return new (t || CapacityModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CapacityModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CapacityRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CapacityModule, {\n    declarations: [CapacityComponent, CapacityEditComponent],\n    imports: [SharedModule, CapacityRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "CapacityComponent", "CapacityRoutingModule", "CapacityEditComponent", "COMPONENTS", "CapacityModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\capacity\\capacity.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { CapacityComponent } from './capacity.component';\r\nimport { CapacityRoutingModule } from './capacity-routing.module';\r\nimport {CapacityEditComponent} from '@business/tas/capacity/capacity-edit/capacity-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  CapacityComponent,\r\n  CapacityEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, CapacityRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class CapacityModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAAQC,qBAAqB,QAAO,8DAA8D;;AAElG,MAAMC,UAAU,GAAG,CACjBH,iBAAiB,EACjBE,qBAAqB,CACtB;AAMD,OAAM,MAAOE,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAHfN,YAAY,EAAEG,qBAAqB,EAAEF,YAAY;IAAA;EAAA;;;2EAGhDK,cAAc;IAAAC,YAAA,GARzBL,iBAAiB,EACjBE,qBAAqB;IAAAI,OAAA,GAIXR,YAAY,EAAEG,qBAAqB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}