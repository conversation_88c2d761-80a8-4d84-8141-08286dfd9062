{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VesselListComponent } from './vessel-list.component';\nimport { VesselEditComponent } from './vessel-edit/vessel-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: VesselListComponent\n}, {\n  path: 'list-edit',\n  component: VesselEditComponent\n}];\nexport class VesselListRoutingModule {\n  static {\n    this.ɵfac = function VesselListRoutingModule_Factory(t) {\n      return new (t || VesselListRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselListRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselListRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VesselListComponent", "VesselEditComponent", "routes", "path", "component", "VesselListRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-list\\vessel-list-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VesselListComponent } from './vessel-list.component';\r\nimport { VesselEditComponent } from './vessel-edit/vessel-edit.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: VesselListComponent },\r\n  { path: 'list-edit', component: VesselEditComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class VesselListRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,mBAAmB,QAAQ,qCAAqC;;;AAEzE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAmB,CAAE,EAC5C;EAAEG,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH;AAAmB,CAAE,CACtD;AAMD,OAAM,MAAOI,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXM,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAFxBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}