{"ast": null, "code": "import componentData from '@layout/components/component-data';\nimport { FormControl, Validators } from '@angular/forms';\nimport { CwfBaseCrud, CwfBusContextService, LogTypeEnum, PageModeEnum } from 'cwf-ng-library';\n/**\n *  查询主子表操作\n */\nconst QUERY_MAINCHILD = \"querychilds\";\n/**\n * 父级控制器\n */\nconst PARENT_CONTROLLER = \"parentcontroller\";\n//\n/**\n * 继承cwf业务操作基类（自动生成fromcontrol时，原基类修改）\n * <AUTHOR> Mingzhi\n */\nexport class CwfBaseCrudcto extends CwfBaseCrud {\n  constructor() {\n    super(...arguments);\n    this.editArray = null;\n    // savedefault2(comp_cd: String, page_cd: String, formGroup: FormGroup) {\n    //         var mainStore = new T_CBC_COLUMN_DEFAULT();\n    //         var array = formGroup.getRawValue();\n    //         var row = {};\n    //         for (var key in array) {\n    //             if (array[key] != '' && array[key] != null && array[key] != undefined) {\n    //                 if (key == 'buCd' || key == 'buNm') {\n    //                     continue;\n    //                 }\n    //                 row['pageCd'] = page_cd;\n    //                 row['compCd'] = comp_cd;\n    //                 row['systemCd'] = '';//this.cwfBusContext.getContext().getSystemCd();\n    //                 row['defaultvalue'] = key;\n    //                 row['defaultvalue'] = array[key];\n    //                 mainStore.add(row);\n    //             }\n    //         }\n    //         mainStore.getModelDatas().forEach(item => {\n    //             item.setDirty();\n    //         });\n    //         const saveRequestObj = {\n    //             \"pageCd\": page_cd,\n    //             \"pageNm\": comp_cd,\n    //             columnDefault: mainStore.getDirtyDatas()\n    //         }\n    //         this.cwfRestfulService.post('/PageColumnConfigService/saveDefaultValue', saveRequestObj).then((rps: responseInterface) => {\n    //             if (rps.ok === true) {\n    //                 this.cwfBusContext.getNotify().showAlert(ModalTypeEnum.success, '保存成功，请刷新页面加载数据！');\n    //             } else {\n    //                 this.cwfBusContext.getNotify().showAlert('提示', rps.msg);\n    //             }\n    //         })\n    //     }\n  }\n  // constructor(\n  //   protected cwfBusContext:CwfBusContextService,\n  //   private cwfRestfulService: CwfRestfulService\n  // ){\n  //     super(cwfBusContext);\n  // }\n  /**\n     * 初始化\n     */\n  initLoad() {\n    let me = this;\n    //初始化查询条件\n    this.conditionForm = this.setFormControl({});\n    //初始化编辑\n    this.beforeLoad();\n    let edit = null;\n    if (this.editArray != null) {\n      edit = this.setEdit();\n    }\n    if (edit != null) {\n      // this.conditionForm = this.setFormControl(edit);\n      this.editForm = this.setFormControl(edit);\n      if (this.editForms.length == 0) {\n        this.editForms = [this.editForm];\n      }\n    }\n    this.conditionForm = this.setFormControl({});\n    //构造业务主界面路由路径\n    this.getMainPath();\n    //获取传递参数\n    this.paramsubscription = this.cwfBusContext.getActivatedRoute().queryParamMap.subscribe(params => {\n      //参数处理\n      me.openParam = this.decodeParam(params);\n      me.currentPageMode = me.openParam[this.PAGE_STATE];\n      //设置页面默认操作状态\n      if (me.currentPageMode === undefined) {\n        me.currentPageMode = PageModeEnum.Main;\n      }\n    });\n    this.onLoad();\n  }\n  /**\n      * 组件显示完成\n      */\n  initShow() {\n    var tab = this.cwfBusContext.getContext().getCurrentTab();\n    if (tab !== undefined) {\n      tab['controller'] = this;\n    }\n    // this.cwfBusContext.setTopCrudInstance(this)\n    //页面状态处理\n    if (this.currentPageMode == PageModeEnum.Main) {\n      //保存当前controller\n      CwfBusContextService.currnetCrud = this.mainPath;\n      CwfBusContextService.add(this.mainPath, this);\n    } else if (this.currentPageMode == PageModeEnum.Add) {\n      //新建记录\n      if (this.editForms.length > 0) {\n        if (this.editStores.length > 0) {\n          let record = this.editStores[0].add({});\n        }\n      } else {\n        this.writeLog(LogTypeEnum.warn, \"editForms属性不能为空\");\n      }\n    } else if (this.currentPageMode == PageModeEnum.Modify) {\n      //修改，执行查询\n      if (this.editForms.length > 0) {\n        let controller = this.getMainController();\n        if (controller === undefined) {\n          this.openMainPage({});\n        } else {\n          //绑定选择记录\n          let editData = this.getParentControllerSyncStore().getSelecteds()[0].data;\n          if (this.editStores.length > 0) {\n            let record = this.editStores[0].add({});\n            record.setData(editData);\n            record.commit();\n          }\n          //初始化显示\n          this.editForms[0].patchValue(editData);\n        }\n      } else {\n        this.writeLog(LogTypeEnum.warn, \"editForms属性不能为空\");\n      }\n    }\n    this.onShow();\n  }\n  /**\n   * 初始化编辑\n   */\n  setEdit() {\n    let obj = {};\n    let systemVersion = \"\"; //this.cwfBusContext.getContext().getSystemVersion();\n    for (var K = 0; K < this.editArray.length; K++) {\n      var data = this.editArray[K];\n      for (var i = 0; i < data.length; i++) {\n        if (this.isshow(data[i], systemVersion, false)) {\n          var cd = data[i].attr.formControlName;\n          if (cd == undefined || cd == '') {\n            cd = componentData[data[i].attr.key].formControlName;\n          }\n          var Required = data[i].attr.Required; //必输项字段 true/false\n          if (Required == false) {} else if (Required == undefined || Required == '') {\n            Required = componentData[data[i].attr.key]?.Required || false;\n          }\n          var xtype = data[i].attr.xtype;\n          if (xtype == undefined || xtype == '') {\n            xtype = componentData[data[i].attr.key]?.xtype || 'text';\n          }\n          // var xtype = this.isXtype(data[i]);\n          var cds = undefined;\n          if (xtype == 'lookup') {\n            cds = data[i].attr.valuefield; //类型为lookup或者时间类型的控件需要多个字段\n            if (cds == undefined || cds == '') {\n              cds = componentData[data[i].attr.key].valuefield;\n            }\n          }\n          var initvalue = data[i].attr.initvalue; //初始值定义\n          if (initvalue == undefined) {\n            initvalue = null;\n          }\n          //必输项\n          if (Required == true) {\n            //如果设置了valuefield那就将valuefield设置成字段\n            if (cds != undefined && cds.split(',').length > 0) {\n              //valuefield为多字段时，循环设置初始化值\n              for (var j = 0; j < cds.split(',').length; j++) {\n                if (null == initvalue) {\n                  if (cd == cds.split(',')[j]) {\n                    obj[cds.split(',')[j]] = new FormControl(null, Validators.required);\n                  } else {\n                    obj[cds.split(',')[j]] = new FormControl(null, Validators.nullValidator);\n                  }\n                  // CHECKLIST_ID: new FormControl('', Validators.nullValidator),\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(null, Validators.required));//必输\n                } else {\n                  //valuefield的逗号分隔的字段必须和initvalue逗号分隔的值数量相同，而且要一一对应\n                  if (cd == cds.split(',')[j]) {\n                    obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.required);\n                  } else {\n                    obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.nullValidator);\n                  }\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(initvalue.split(',')[j], Validators.required));//必输\n                }\n              }\n            } else {\n              //未设置valuefield或者valuefield为空时，直接将cd设置为字段\n              obj[cd] = new FormControl(initvalue, Validators.required);\n              // this.FormGroup.addControl(cd, new FormControl(initvalue, Validators.required));//必输\n            }\n            //非必输项\n          } else {\n            if (cds != undefined && cds.split(',').length > 0) {\n              //valuefield为多字段时，循环设置初始化值\n              for (var j = 0; j < cds.split(',').length; j++) {\n                if (null == initvalue) {\n                  obj[cds.split(',')[j]] = new FormControl(null, Validators.nullValidator);\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(null, Validators.nullValidator));//非必输\n                } else {\n                  //valuefield的逗号分隔的字段必须和initvalue逗号分隔的值数量相同，而且要一一对应\n                  obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.nullValidator);\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(initvalue.split(',')[j], Validators.nullValidator));//非必输\n                }\n              }\n            } else {\n              obj[cd] = new FormControl(initvalue, Validators.nullValidator);\n              // this.FormGroup.addControl(cd, new FormControl(initvalue, Validators.nullValidator));//非必输\n            }\n          }\n        }\n      }\n    }\n    return obj;\n  }\n  isshow(info, system_cd, mainstatus) {\n    var pagesystem_cd = info.attr.system_cd;\n    var key = info.attr.key;\n    if (pagesystem_cd == undefined || pagesystem_cd == '') {\n      pagesystem_cd = componentData[key].system_cd;\n    }\n    if (pagesystem_cd == undefined || pagesystem_cd == '') {\n      return false;\n    }\n    if (pagesystem_cd == '*all' || pagesystem_cd == system_cd) {\n      if (mainstatus) {\n        var display = info.attr.display;\n        if (display == '' || display == undefined) {\n          display = componentData[info.attr.key].display;\n        }\n        if (display != false) {\n          return true;\n        }\n        //设置隐藏的情况下判断expandForm的\n        if (this.expandForm) {\n          return true;\n        }\n        return false;\n      }\n      return true;\n    } else if (pagesystem_cd.split(',').length > 1) {\n      for (var i = 0; i < pagesystem_cd.split(',').length; i++) {\n        if (pagesystem_cd.split(',')[i] == system_cd) {\n          if (mainstatus) {\n            if (info.attr.display != false) {\n              return true;\n            }\n            //设置隐藏的情况下判断expandForm的\n            if (this.expandForm) {\n              return true;\n            }\n            return false;\n          }\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  /**\n   * 清空\n   */\n  clear() {\n    this.conditionForm.reset();\n    return true;\n  }\n  /**\n     * 重载 - 单选事件\n     * @param info\n     */\n  onCheck_S(info, store) {\n    this.numberOfChecked = 0;\n    info.SELECTED = !info.SELECTED;\n    let selectNum = store.getSelecteds().length,\n      storeNum = store.getDatas().length;\n    this.numberOfChecked = selectNum;\n    if (selectNum == 0) {\n      this.isAllDisplayDataChecked = false;\n      this.isIndeterminate = false;\n    } else if (selectNum > 0 && selectNum < storeNum) {\n      this.isAllDisplayDataChecked = false;\n      this.isIndeterminate = true;\n    } else if (selectNum == selectNum && selectNum > 0) {\n      this.isAllDisplayDataChecked = true;\n      this.isIndeterminate = false;\n    }\n  }\n  /**\n  * 全选事件\n  * 传入store\n  * 处理情况：相同页面中含有两个或两个以上的grid\n  * @param info\n  */\n  checkAll_S($event, store) {\n    this.isAllDisplayDataChecked = $event;\n    this.isIndeterminate = false;\n    for (let info of store.getDatas()) {\n      if (info['VISIBLE'] === 'FALSE') {\n        continue;\n      } //未显示的列不在处理之内\n      info.SELECTED = $event;\n    }\n    this.numberOfChecked = store.getSelecteds().length;\n  }\n  // 分页\n  searchData_S(storeF, reset = false) {\n    if (reset) {\n      storeF.pageing.PAGE = 1;\n    }\n    this.onQuery(storeF.pageing.PAGE);\n  }\n  goTop() {\n    // document.body.scrollTop =1;\n    // document.documentElement.scrollTop=1;\n    // setTimeout(function(){\n    //   document.body.scrollTop =1;\n    //   document.documentElement.scrollTop=1;\n    document.body.scrollTop = 0;\n    document.documentElement.scrollTop = 0;\n    // })\n  }\n  /**\n     * 针对form组件 保存（查询）前校验必输数据 sunmz\n     * @param formarray //自定义数组\n     * @param formRawValue //输入的数据集\n     * @param form //form组件字段属性集合\n     */\n  isAllowBlank(formarray, formRawValue, form) {\n    var array = [];\n    for (const i in form.controls) {\n      array.push(i);\n      form.controls[i].markAsDirty();\n      form.controls[i].updateValueAndValidity();\n    }\n    for (var j = 0; j < formarray.length; j++) {\n      for (var i = 0; i < array.length; i++) {\n        // var systemversion = this.cwfBusContext.getContext().getSystemVersion();\n        var formControlName = formarray[j].attr.formControlName;\n        var system_cd = formarray[j].attr.system_cd;\n        var i18n_cd = formarray[j].attr.i18n_cd;\n        // var xtype = formarray[j].attr.xtype;\n        var key = formarray[j].attr.key;\n        if (formControlName == '' || formControlName == undefined) {\n          formControlName = componentData[key].formControlName;\n        }\n        if (system_cd == '' || system_cd == undefined) {\n          system_cd = componentData[key].system_cd;\n        }\n        if (i18n_cd == '' || i18n_cd == undefined) {\n          i18n_cd = componentData[key].i18n_cd;\n        }\n        // if (system_cd.indexOf(systemversion) === -1 && system_cd !== '*all') {//数组中system_cd 必须和系统版本一致时才做校验\n        //   continue;\n        // }\n        if (formControlName == array[i] && formarray[j].attr.Required == true) {\n          if (formRawValue[formControlName] === '' || formRawValue[formControlName] === null) {\n            // var msg = this.cwfTranslateService.geti18nString(i18n_cd);\n            // this.message.info(msg + '不能为空！');\n            this.showAlert('提示', '不能为空！');\n            return false;\n          }\n        }\n      }\n    }\n    return true;\n  }\n}", "map": {"version": 3, "names": ["componentData", "FormControl", "Validators", "CwfBaseCrud", "CwfBusContextService", "LogTypeEnum", "PageModeEnum", "QUERY_MAINCHILD", "PARENT_CONTROLLER", "CwfBaseCrudcto", "constructor", "edit<PERSON><PERSON>y", "initLoad", "me", "conditionForm", "setFormControl", "beforeLoad", "edit", "setEdit", "editForm", "editForms", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsubscription", "cwfBusContext", "getActivatedRoute", "queryParamMap", "subscribe", "params", "openParam", "decodeParam", "currentPageMode", "PAGE_STATE", "undefined", "Main", "onLoad", "initShow", "tab", "getContext", "getCurrentTab", "currnetCrud", "mainP<PERSON>", "add", "Add", "editStores", "record", "writeLog", "warn", "Modify", "controller", "getMainController", "openMainPage", "editData", "getParentControllerSyncStore", "getSelecteds", "data", "setData", "commit", "patchValue", "onShow", "obj", "systemVersion", "K", "i", "isshow", "cd", "attr", "formControlName", "key", "Required", "xtype", "cds", "valuefield", "initvalue", "split", "j", "required", "nullValidator", "info", "system_cd", "mainstatus", "pagesystem_cd", "display", "expandForm", "clear", "reset", "onCheck_S", "store", "numberOfChecked", "SELECTED", "selectNum", "storeNum", "getDatas", "isAllDisplayDataChecked", "isIndeterminate", "checkAll_S", "$event", "searchData_S", "storeF", "pageing", "PAGE", "on<PERSON><PERSON>y", "goTop", "document", "body", "scrollTop", "documentElement", "isAllowBlank", "formarray", "formRawValue", "form", "array", "controls", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "i18n_cd", "show<PERSON><PERSON><PERSON>"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\core\\cwfbasecrudcto.ts"], "sourcesContent": ["import componentData from '@layout/components/component-data';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { Observable } from 'rxjs';\r\nimport {CwfBaseCrud, CwfBusContextService, CwfStore, LogTypeEnum, ModalTypeEnum, PageModeEnum} from 'cwf-ng-library';\r\nimport { T_CBC_COLUMN_DEFAULT } from '@store/CBC/T_CBC_COLUMN_DEFAULT';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\n\r\n\r\n/**\r\n *  查询主子表操作\r\n */\r\nconst QUERY_MAINCHILD: string = \"querychilds\";\r\n\r\n/**\r\n * 父级控制器\r\n */\r\nconst PARENT_CONTROLLER: string = \"parentcontroller\";\r\n\r\n//\r\n/**\r\n * 继承cwf业务操作基类（自动生成fromcontrol时，原基类修改）\r\n * <AUTHOR>\r\n */\r\nexport class CwfBaseCrudcto extends CwfBaseCrud {\r\n \r\n  editArray = null;\r\n  system_cd;\r\n\r\n  // constructor(\r\n  //   protected cwfBusContext:CwfBusContextService,\r\n  //   private cwfRestfulService: CwfRestfulService\r\n  // ){\r\n  //     super(cwfBusContext);\r\n  // }\r\n  /**\r\n     * 初始化\r\n     */\r\n  protected initLoad() {\r\n    let me = this;\r\n    //初始化查询条件\r\n    this.conditionForm = this.setFormControl({});\r\n    //初始化编辑\r\n    this.beforeLoad();\r\n    let edit = null;\r\n    if (this.editArray != null) {\r\n      edit = this.setEdit();\r\n    }\r\n    if (edit != null) {\r\n      // this.conditionForm = this.setFormControl(edit);\r\n      this.editForm = this.setFormControl(edit);\r\n      if (this.editForms.length == 0) {\r\n        this.editForms = [this.editForm];\r\n      }\r\n    }\r\n    this.conditionForm = this.setFormControl({});\r\n    //构造业务主界面路由路径\r\n    this.getMainPath();\r\n    //获取传递参数\r\n    this.paramsubscription = this.cwfBusContext.getActivatedRoute().queryParamMap\r\n      .subscribe(params => {\r\n        //参数处理\r\n        me.openParam = this.decodeParam(params);\r\n        me.currentPageMode = me.openParam[this.PAGE_STATE];\r\n        //设置页面默认操作状态\r\n        if (me.currentPageMode === undefined) {\r\n          me.currentPageMode = PageModeEnum.Main;\r\n        }\r\n      })\r\n    this.onLoad();\r\n  }\r\n  /**\r\n      * 组件显示完成\r\n      */\r\n  initShow() {\r\n    var tab = this.cwfBusContext.getContext().getCurrentTab();\r\n    if(tab!==undefined){\r\n      tab['controller'] = this;\r\n    }\r\n    // this.cwfBusContext.setTopCrudInstance(this)\r\n    //页面状态处理\r\n    if (this.currentPageMode == PageModeEnum.Main) {\r\n      //保存当前controller\r\n\r\n      CwfBusContextService.currnetCrud = this.mainPath;\r\n      CwfBusContextService.add(this.mainPath, this);\r\n\r\n\r\n    } else if (this.currentPageMode == PageModeEnum.Add) {\r\n\r\n      //新建记录\r\n      if (this.editForms.length > 0) {\r\n        if (this.editStores.length > 0) {\r\n          let record = this.editStores[0].add({});\r\n\r\n        }\r\n      } else {\r\n        this.writeLog(LogTypeEnum.warn, \"editForms属性不能为空\");\r\n      }\r\n\r\n    } else if (this.currentPageMode == PageModeEnum.Modify) {\r\n      //修改，执行查询\r\n      if (this.editForms.length > 0) {\r\n        let controller = this.getMainController();\r\n\r\n        if (controller === undefined) {\r\n          this.openMainPage({});\r\n        } else {\r\n          //绑定选择记录\r\n          let editData = this.getParentControllerSyncStore().getSelecteds()[0].data;\r\n          if (this.editStores.length > 0) {\r\n            let record = this.editStores[0].add({});\r\n            record.setData(editData);\r\n            record.commit();\r\n          }\r\n\r\n          //初始化显示\r\n          this.editForms[0].patchValue(editData);\r\n        }\r\n      } else {\r\n        this.writeLog(LogTypeEnum.warn, \"editForms属性不能为空\");\r\n      }\r\n    }\r\n\r\n    this.onShow();\r\n  }\r\n  /**\r\n   * 初始化编辑\r\n   */\r\n  setEdit() {\r\n    let obj = {};\r\n    let systemVersion = \"\";//this.cwfBusContext.getContext().getSystemVersion();\r\n    for (var K = 0; K < this.editArray.length; K++) {\r\n      var data = this.editArray[K];\r\n      for (var i = 0; i < data.length; i++) {\r\n        if (this.isshow(data[i],systemVersion , false)) {\r\n          var cd = data[i].attr.formControlName;\r\n          if (cd == undefined || cd == '') {\r\n            cd = componentData[data[i].attr.key].formControlName;\r\n          }\r\n          var Required = data[i].attr.Required;//必输项字段 true/false\r\n          if (Required == false) {\r\n\r\n          } else if (Required == undefined || Required == '') {\r\n            Required = componentData[data[i].attr.key]?.Required || false;\r\n          }\r\n          var xtype = data[i].attr.xtype;\r\n          if (xtype == undefined || xtype == '') {\r\n            xtype = componentData[data[i].attr.key]?.xtype || 'text';\r\n          }\r\n          // var xtype = this.isXtype(data[i]);\r\n          var cds = undefined;\r\n          if (xtype == 'lookup') {\r\n            cds = data[i].attr.valuefield;//类型为lookup或者时间类型的控件需要多个字段\r\n            if (cds == undefined || cds == '') {\r\n              cds = componentData[data[i].attr.key].valuefield;\r\n            }\r\n          }\r\n\r\n          var initvalue = data[i].attr.initvalue;//初始值定义\r\n          if (initvalue == undefined) {\r\n            initvalue = null;\r\n          }\r\n          //必输项\r\n          if (Required == true) {\r\n            //如果设置了valuefield那就将valuefield设置成字段\r\n            if (cds != undefined && cds.split(',').length > 0) {\r\n              //valuefield为多字段时，循环设置初始化值\r\n              for (var j = 0; j < cds.split(',').length; j++) {\r\n                if (null == initvalue) {\r\n                  if(cd==cds.split(',')[j]){\r\n                    obj[cds.split(',')[j]] = new FormControl(null, Validators.required);\r\n                  }else{\r\n                    obj[cds.split(',')[j]] = new FormControl(null, Validators.nullValidator);\r\n                  }\r\n                  // CHECKLIST_ID: new FormControl('', Validators.nullValidator),\r\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(null, Validators.required));//必输\r\n                } else {//valuefield的逗号分隔的字段必须和initvalue逗号分隔的值数量相同，而且要一一对应\r\n                  if(cd==cds.split(',')[j]){\r\n                    obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.required);\r\n                  }else{\r\n                    obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.nullValidator);\r\n                  }\r\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(initvalue.split(',')[j], Validators.required));//必输\r\n                }\r\n              }\r\n            } else {\r\n              //未设置valuefield或者valuefield为空时，直接将cd设置为字段\r\n              obj[cd] = new FormControl(initvalue, Validators.required);\r\n              // this.FormGroup.addControl(cd, new FormControl(initvalue, Validators.required));//必输\r\n            }\r\n            //非必输项\r\n          } else {\r\n            if (cds != undefined && cds.split(',').length > 0) {\r\n              //valuefield为多字段时，循环设置初始化值\r\n              for (var j = 0; j < cds.split(',').length; j++) {\r\n                if (null == initvalue) {\r\n                  obj[cds.split(',')[j]] = new FormControl(null, Validators.nullValidator);\r\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(null, Validators.nullValidator));//非必输\r\n                } else {//valuefield的逗号分隔的字段必须和initvalue逗号分隔的值数量相同，而且要一一对应\r\n                  obj[cds.split(',')[j]] = new FormControl(initvalue.split(',')[j], Validators.nullValidator);\r\n                  // this.FormGroup.addControl(cds.split(',')[j], new FormControl(initvalue.split(',')[j], Validators.nullValidator));//非必输\r\n                }\r\n              }\r\n            } else {\r\n              obj[cd] = new FormControl(initvalue, Validators.nullValidator);\r\n              // this.FormGroup.addControl(cd, new FormControl(initvalue, Validators.nullValidator));//非必输\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n    }\r\n    return obj;\r\n  }\r\n  isshow(info, system_cd, mainstatus) {\r\n\r\n    var pagesystem_cd = info.attr.system_cd;\r\n    var key = info.attr.key;\r\n    if (pagesystem_cd == undefined || pagesystem_cd == '') {\r\n      pagesystem_cd = componentData[key].system_cd;\r\n    }\r\n    if (pagesystem_cd == undefined || pagesystem_cd == '') {\r\n      return false;\r\n    }\r\n    if (pagesystem_cd == '*all' || pagesystem_cd == system_cd) {\r\n      if (mainstatus) {\r\n        var display = info.attr.display;\r\n        if (display == '' || display == undefined) {\r\n          display = componentData[info.attr.key].display;\r\n        }\r\n        if (display != false) {\r\n          return true;\r\n        }\r\n        //设置隐藏的情况下判断expandForm的\r\n        if (this.expandForm) {\r\n          return true;\r\n        }\r\n        return false;\r\n      }\r\n      return true;\r\n\r\n    } else if (pagesystem_cd.split(',').length > 1) {\r\n      for (var i = 0; i < pagesystem_cd.split(',').length; i++) {\r\n        if (pagesystem_cd.split(',')[i] == system_cd) {\r\n          if (mainstatus) {\r\n            if (info.attr.display != false) {\r\n              return true;\r\n            }\r\n            //设置隐藏的情况下判断expandForm的\r\n            if (this.expandForm) {\r\n              return true;\r\n            }\r\n            return false;\r\n          }\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n  /**\r\n   * 清空\r\n   */\r\n  clear(): boolean | Promise<boolean> {\r\n    this.conditionForm.reset();\r\n    return true;\r\n  }\r\n  /**\r\n     * 重载 - 单选事件\r\n     * @param info\r\n     */\r\n  onCheck_S(info, store) {\r\n    this.numberOfChecked = 0;\r\n    info.SELECTED = !info.SELECTED;\r\n    let selectNum: number = store.getSelecteds().length, storeNum: number = store.getDatas().length;\r\n    this.numberOfChecked = selectNum;\r\n    if (selectNum == 0) {\r\n      this.isAllDisplayDataChecked = false;\r\n      this.isIndeterminate = false;\r\n    } else if (selectNum > 0 && selectNum < storeNum) {\r\n      this.isAllDisplayDataChecked = false;\r\n      this.isIndeterminate = true;\r\n    } else if (selectNum == selectNum && selectNum > 0) {\r\n      this.isAllDisplayDataChecked = true;\r\n      this.isIndeterminate = false;\r\n    }\r\n  }\r\n  /**\r\n * 全选事件\r\n * 传入store\r\n * 处理情况：相同页面中含有两个或两个以上的grid\r\n * @param info\r\n */\r\n  checkAll_S($event: boolean, store) {\r\n    this.isAllDisplayDataChecked = $event;\r\n    this.isIndeterminate = false;\r\n    for (let info of store.getDatas()) {\r\n      if(info['VISIBLE'] === 'FALSE'){continue;}//未显示的列不在处理之内\r\n      info.SELECTED = $event;\r\n    }\r\n    this.numberOfChecked = store.getSelecteds().length;\r\n  }\r\n\r\n  // 分页\r\n  searchData_S(storeF: CwfStore, reset: boolean = false): void {\r\n    if (reset) {\r\n      storeF.pageing.PAGE = 1;\r\n    }\r\n    this.onQuery(storeF.pageing.PAGE);\r\n\r\n  }\r\n\r\n  goTop(){\r\n    // document.body.scrollTop =1;\r\n    // document.documentElement.scrollTop=1;\r\n    // setTimeout(function(){\r\n    //   document.body.scrollTop =1;\r\n    //   document.documentElement.scrollTop=1;\r\n       document.body.scrollTop =0;\r\n       document.documentElement.scrollTop=0;\r\n    // })\r\n  }\r\n\r\n  /**\r\n     * 针对form组件 保存（查询）前校验必输数据 sunmz\r\n     * @param formarray //自定义数组\r\n     * @param formRawValue //输入的数据集\r\n     * @param form //form组件字段属性集合\r\n     */\r\n    isAllowBlank(formarray: any, formRawValue: any, form) {\r\n      var array = [];\r\n      for (const i in form.controls) {\r\n        array.push(i);\r\n        form.controls[i].markAsDirty();\r\n        form.controls[i].updateValueAndValidity();\r\n      }\r\n      for (var j = 0; j < formarray.length; j++) {\r\n        for (var i = 0; i < array.length; i++) {\r\n          // var systemversion = this.cwfBusContext.getContext().getSystemVersion();\r\n          var formControlName = formarray[j].attr.formControlName;\r\n          var system_cd = formarray[j].attr.system_cd;\r\n          var i18n_cd = formarray[j].attr.i18n_cd;\r\n          // var xtype = formarray[j].attr.xtype;\r\n          var key = formarray[j].attr.key;\r\n          if (formControlName == '' || formControlName == undefined) {\r\n            formControlName = componentData[key].formControlName;\r\n          }\r\n          if (system_cd == '' || system_cd == undefined) {\r\n            system_cd = componentData[key].system_cd;\r\n          }\r\n          if (i18n_cd == '' || i18n_cd == undefined) {\r\n            i18n_cd = componentData[key].i18n_cd;\r\n          }\r\n          // if (system_cd.indexOf(systemversion) === -1 && system_cd !== '*all') {//数组中system_cd 必须和系统版本一致时才做校验\r\n          //   continue;\r\n          // }\r\n          if (formControlName == array[i] && formarray[j].attr.Required == true) {\r\n            if (formRawValue[formControlName] === '' || formRawValue[formControlName] === null) {\r\n              // var msg = this.cwfTranslateService.geti18nString(i18n_cd);\r\n              // this.message.info(msg + '不能为空！');\r\n              this.showAlert('提示','不能为空！')\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return true;\r\n    }\r\n\r\n    // savedefault2(comp_cd: String, page_cd: String, formGroup: FormGroup) {\r\n    //         var mainStore = new T_CBC_COLUMN_DEFAULT();\r\n    //         var array = formGroup.getRawValue();\r\n    //         var row = {};\r\n    //         for (var key in array) {\r\n    //             if (array[key] != '' && array[key] != null && array[key] != undefined) {\r\n    //                 if (key == 'buCd' || key == 'buNm') {\r\n    //                     continue;\r\n    //                 }\r\n    //                 row['pageCd'] = page_cd;\r\n    //                 row['compCd'] = comp_cd;\r\n    //                 row['systemCd'] = '';//this.cwfBusContext.getContext().getSystemCd();\r\n    //                 row['defaultvalue'] = key;\r\n    //                 row['defaultvalue'] = array[key];\r\n    //                 mainStore.add(row);\r\n    //             }\r\n    //         }\r\n    //         mainStore.getModelDatas().forEach(item => {\r\n    //             item.setDirty();\r\n    //         });\r\n    \r\n    //         const saveRequestObj = {\r\n    //             \"pageCd\": page_cd,\r\n    //             \"pageNm\": comp_cd,\r\n    //             columnDefault: mainStore.getDirtyDatas()\r\n    //         }\r\n    //         this.cwfRestfulService.post('/PageColumnConfigService/saveDefaultValue', saveRequestObj).then((rps: responseInterface) => {\r\n    //             if (rps.ok === true) {\r\n    //                 this.cwfBusContext.getNotify().showAlert(ModalTypeEnum.success, '保存成功，请刷新页面加载数据！');\r\n    //             } else {\r\n    //                 this.cwfBusContext.getNotify().showAlert('提示', rps.msg);\r\n    //             }\r\n    //         })\r\n    //     }\r\n}\r\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,mCAAmC;AAC7D,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAEnE,SAAQC,WAAW,EAAEC,oBAAoB,EAAYC,WAAW,EAAiBC,YAAY,QAAO,gBAAgB;AAMpH;;;AAGA,MAAMC,eAAe,GAAW,aAAa;AAE7C;;;AAGA,MAAMC,iBAAiB,GAAW,kBAAkB;AAEpD;AACA;;;;AAIA,OAAM,MAAOC,cAAe,SAAQN,WAAW;EAA/CO,YAAA;;IAEE,KAAAC,SAAS,GAAG,IAAI;IAwVd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ;EAvXE;EACA;EACA;EACA;EACA;EACA;EACA;;;EAGUC,QAAQA,CAAA;IAChB,IAAIC,EAAE,GAAG,IAAI;IACb;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,EAAE,CAAC;IAC5C;IACA,IAAI,CAACC,UAAU,EAAE;IACjB,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI,IAAI,CAACN,SAAS,IAAI,IAAI,EAAE;MAC1BM,IAAI,GAAG,IAAI,CAACC,OAAO,EAAE;IACvB;IACA,IAAID,IAAI,IAAI,IAAI,EAAE;MAChB;MACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,cAAc,CAACE,IAAI,CAAC;MACzC,IAAI,IAAI,CAACG,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACD,SAAS,GAAG,CAAC,IAAI,CAACD,QAAQ,CAAC;MAClC;IACF;IACA,IAAI,CAACL,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,EAAE,CAAC;IAC5C;IACA,IAAI,CAACO,WAAW,EAAE;IAClB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,aAAa,CAACC,iBAAiB,EAAE,CAACC,aAAa,CAC1EC,SAAS,CAACC,MAAM,IAAG;MAClB;MACAf,EAAE,CAACgB,SAAS,GAAG,IAAI,CAACC,WAAW,CAACF,MAAM,CAAC;MACvCf,EAAE,CAACkB,eAAe,GAAGlB,EAAE,CAACgB,SAAS,CAAC,IAAI,CAACG,UAAU,CAAC;MAClD;MACA,IAAInB,EAAE,CAACkB,eAAe,KAAKE,SAAS,EAAE;QACpCpB,EAAE,CAACkB,eAAe,GAAGzB,YAAY,CAAC4B,IAAI;MACxC;IACF,CAAC,CAAC;IACJ,IAAI,CAACC,MAAM,EAAE;EACf;EACA;;;EAGAC,QAAQA,CAAA;IACN,IAAIC,GAAG,GAAG,IAAI,CAACb,aAAa,CAACc,UAAU,EAAE,CAACC,aAAa,EAAE;IACzD,IAAGF,GAAG,KAAGJ,SAAS,EAAC;MACjBI,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI;IAC1B;IACA;IACA;IACA,IAAI,IAAI,CAACN,eAAe,IAAIzB,YAAY,CAAC4B,IAAI,EAAE;MAC7C;MAEA9B,oBAAoB,CAACoC,WAAW,GAAG,IAAI,CAACC,QAAQ;MAChDrC,oBAAoB,CAACsC,GAAG,CAAC,IAAI,CAACD,QAAQ,EAAE,IAAI,CAAC;IAG/C,CAAC,MAAM,IAAI,IAAI,CAACV,eAAe,IAAIzB,YAAY,CAACqC,GAAG,EAAE;MAEnD;MACA,IAAI,IAAI,CAACvB,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,IAAI,CAACuB,UAAU,CAACvB,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAIwB,MAAM,GAAG,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC,CAACF,GAAG,CAAC,EAAE,CAAC;QAEzC;MACF,CAAC,MAAM;QACL,IAAI,CAACI,QAAQ,CAACzC,WAAW,CAAC0C,IAAI,EAAE,iBAAiB,CAAC;MACpD;IAEF,CAAC,MAAM,IAAI,IAAI,CAAChB,eAAe,IAAIzB,YAAY,CAAC0C,MAAM,EAAE;MACtD;MACA,IAAI,IAAI,CAAC5B,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI4B,UAAU,GAAG,IAAI,CAACC,iBAAiB,EAAE;QAEzC,IAAID,UAAU,KAAKhB,SAAS,EAAE;UAC5B,IAAI,CAACkB,YAAY,CAAC,EAAE,CAAC;QACvB,CAAC,MAAM;UACL;UACA,IAAIC,QAAQ,GAAG,IAAI,CAACC,4BAA4B,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI;UACzE,IAAI,IAAI,CAACX,UAAU,CAACvB,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAIwB,MAAM,GAAG,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC,CAACF,GAAG,CAAC,EAAE,CAAC;YACvCG,MAAM,CAACW,OAAO,CAACJ,QAAQ,CAAC;YACxBP,MAAM,CAACY,MAAM,EAAE;UACjB;UAEA;UACA,IAAI,CAACrC,SAAS,CAAC,CAAC,CAAC,CAACsC,UAAU,CAACN,QAAQ,CAAC;QACxC;MACF,CAAC,MAAM;QACL,IAAI,CAACN,QAAQ,CAACzC,WAAW,CAAC0C,IAAI,EAAE,iBAAiB,CAAC;MACpD;IACF;IAEA,IAAI,CAACY,MAAM,EAAE;EACf;EACA;;;EAGAzC,OAAOA,CAAA;IACL,IAAI0C,GAAG,GAAG,EAAE;IACZ,IAAIC,aAAa,GAAG,EAAE,CAAC;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnD,SAAS,CAACU,MAAM,EAAEyC,CAAC,EAAE,EAAE;MAC9C,IAAIP,IAAI,GAAG,IAAI,CAAC5C,SAAS,CAACmD,CAAC,CAAC;MAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAAClC,MAAM,EAAE0C,CAAC,EAAE,EAAE;QACpC,IAAI,IAAI,CAACC,MAAM,CAACT,IAAI,CAACQ,CAAC,CAAC,EAACF,aAAa,EAAG,KAAK,CAAC,EAAE;UAC9C,IAAII,EAAE,GAAGV,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACC,eAAe;UACrC,IAAIF,EAAE,IAAIhC,SAAS,IAAIgC,EAAE,IAAI,EAAE,EAAE;YAC/BA,EAAE,GAAGjE,aAAa,CAACuD,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACE,GAAG,CAAC,CAACD,eAAe;UACtD;UACA,IAAIE,QAAQ,GAAGd,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACG,QAAQ,CAAC;UACrC,IAAIA,QAAQ,IAAI,KAAK,EAAE,CAEvB,CAAC,MAAM,IAAIA,QAAQ,IAAIpC,SAAS,IAAIoC,QAAQ,IAAI,EAAE,EAAE;YAClDA,QAAQ,GAAGrE,aAAa,CAACuD,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACE,GAAG,CAAC,EAAEC,QAAQ,IAAI,KAAK;UAC/D;UACA,IAAIC,KAAK,GAAGf,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACI,KAAK;UAC9B,IAAIA,KAAK,IAAIrC,SAAS,IAAIqC,KAAK,IAAI,EAAE,EAAE;YACrCA,KAAK,GAAGtE,aAAa,CAACuD,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACE,GAAG,CAAC,EAAEE,KAAK,IAAI,MAAM;UAC1D;UACA;UACA,IAAIC,GAAG,GAAGtC,SAAS;UACnB,IAAIqC,KAAK,IAAI,QAAQ,EAAE;YACrBC,GAAG,GAAGhB,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACM,UAAU,CAAC;YAC9B,IAAID,GAAG,IAAItC,SAAS,IAAIsC,GAAG,IAAI,EAAE,EAAE;cACjCA,GAAG,GAAGvE,aAAa,CAACuD,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACE,GAAG,CAAC,CAACI,UAAU;YAClD;UACF;UAEA,IAAIC,SAAS,GAAGlB,IAAI,CAACQ,CAAC,CAAC,CAACG,IAAI,CAACO,SAAS,CAAC;UACvC,IAAIA,SAAS,IAAIxC,SAAS,EAAE;YAC1BwC,SAAS,GAAG,IAAI;UAClB;UACA;UACA,IAAIJ,QAAQ,IAAI,IAAI,EAAE;YACpB;YACA,IAAIE,GAAG,IAAItC,SAAS,IAAIsC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,GAAG,CAAC,EAAE;cACjD;cACA,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,EAAEsD,CAAC,EAAE,EAAE;gBAC9C,IAAI,IAAI,IAAIF,SAAS,EAAE;kBACrB,IAAGR,EAAE,IAAEM,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,EAAC;oBACvBf,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAAC,IAAI,EAAEC,UAAU,CAAC0E,QAAQ,CAAC;kBACrE,CAAC,MAAI;oBACHhB,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAAC,IAAI,EAAEC,UAAU,CAAC2E,aAAa,CAAC;kBAC1E;kBACA;kBACA;gBACF,CAAC,MAAM;kBAAC;kBACN,IAAGZ,EAAE,IAAEM,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,EAAC;oBACvBf,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAACwE,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,EAAEzE,UAAU,CAAC0E,QAAQ,CAAC;kBACxF,CAAC,MAAI;oBACHhB,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAACwE,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,EAAEzE,UAAU,CAAC2E,aAAa,CAAC;kBAC7F;kBACA;gBACF;cACF;YACF,CAAC,MAAM;cACL;cACAjB,GAAG,CAACK,EAAE,CAAC,GAAG,IAAIhE,WAAW,CAACwE,SAAS,EAAEvE,UAAU,CAAC0E,QAAQ,CAAC;cACzD;YACF;YACA;UACF,CAAC,MAAM;YACL,IAAIL,GAAG,IAAItC,SAAS,IAAIsC,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,GAAG,CAAC,EAAE;cACjD;cACA,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,EAAEsD,CAAC,EAAE,EAAE;gBAC9C,IAAI,IAAI,IAAIF,SAAS,EAAE;kBACrBb,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAAC,IAAI,EAAEC,UAAU,CAAC2E,aAAa,CAAC;kBACxE;gBACF,CAAC,MAAM;kBAAC;kBACNjB,GAAG,CAACW,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI1E,WAAW,CAACwE,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,CAAC,CAAC,EAAEzE,UAAU,CAAC2E,aAAa,CAAC;kBAC3F;gBACF;cACF;YACF,CAAC,MAAM;cACLjB,GAAG,CAACK,EAAE,CAAC,GAAG,IAAIhE,WAAW,CAACwE,SAAS,EAAEvE,UAAU,CAAC2E,aAAa,CAAC;cAC9D;YACF;UACF;QACF;MACF;IAEF;IACA,OAAOjB,GAAG;EACZ;EACAI,MAAMA,CAACc,IAAI,EAAEC,SAAS,EAAEC,UAAU;IAEhC,IAAIC,aAAa,GAAGH,IAAI,CAACZ,IAAI,CAACa,SAAS;IACvC,IAAIX,GAAG,GAAGU,IAAI,CAACZ,IAAI,CAACE,GAAG;IACvB,IAAIa,aAAa,IAAIhD,SAAS,IAAIgD,aAAa,IAAI,EAAE,EAAE;MACrDA,aAAa,GAAGjF,aAAa,CAACoE,GAAG,CAAC,CAACW,SAAS;IAC9C;IACA,IAAIE,aAAa,IAAIhD,SAAS,IAAIgD,aAAa,IAAI,EAAE,EAAE;MACrD,OAAO,KAAK;IACd;IACA,IAAIA,aAAa,IAAI,MAAM,IAAIA,aAAa,IAAIF,SAAS,EAAE;MACzD,IAAIC,UAAU,EAAE;QACd,IAAIE,OAAO,GAAGJ,IAAI,CAACZ,IAAI,CAACgB,OAAO;QAC/B,IAAIA,OAAO,IAAI,EAAE,IAAIA,OAAO,IAAIjD,SAAS,EAAE;UACzCiD,OAAO,GAAGlF,aAAa,CAAC8E,IAAI,CAACZ,IAAI,CAACE,GAAG,CAAC,CAACc,OAAO;QAChD;QACA,IAAIA,OAAO,IAAI,KAAK,EAAE;UACpB,OAAO,IAAI;QACb;QACA;QACA,IAAI,IAAI,CAACC,UAAU,EAAE;UACnB,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IAEb,CAAC,MAAM,IAAIF,aAAa,CAACP,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,GAAG,CAAC,EAAE;MAC9C,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,aAAa,CAACP,KAAK,CAAC,GAAG,CAAC,CAACrD,MAAM,EAAE0C,CAAC,EAAE,EAAE;QACxD,IAAIkB,aAAa,CAACP,KAAK,CAAC,GAAG,CAAC,CAACX,CAAC,CAAC,IAAIgB,SAAS,EAAE;UAC5C,IAAIC,UAAU,EAAE;YACd,IAAIF,IAAI,CAACZ,IAAI,CAACgB,OAAO,IAAI,KAAK,EAAE;cAC9B,OAAO,IAAI;YACb;YACA;YACA,IAAI,IAAI,CAACC,UAAU,EAAE;cACnB,OAAO,IAAI;YACb;YACA,OAAO,KAAK;UACd;UACA,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd;EACA;;;EAGAC,KAAKA,CAAA;IACH,IAAI,CAACtE,aAAa,CAACuE,KAAK,EAAE;IAC1B,OAAO,IAAI;EACb;EACA;;;;EAIAC,SAASA,CAACR,IAAI,EAAES,KAAK;IACnB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxBV,IAAI,CAACW,QAAQ,GAAG,CAACX,IAAI,CAACW,QAAQ;IAC9B,IAAIC,SAAS,GAAWH,KAAK,CAACjC,YAAY,EAAE,CAACjC,MAAM;MAAEsE,QAAQ,GAAWJ,KAAK,CAACK,QAAQ,EAAE,CAACvE,MAAM;IAC/F,IAAI,CAACmE,eAAe,GAAGE,SAAS;IAChC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,IAAI,CAACG,uBAAuB,GAAG,KAAK;MACpC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC9B,CAAC,MAAM,IAAIJ,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGC,QAAQ,EAAE;MAChD,IAAI,CAACE,uBAAuB,GAAG,KAAK;MACpC,IAAI,CAACC,eAAe,GAAG,IAAI;IAC7B,CAAC,MAAM,IAAIJ,SAAS,IAAIA,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;MAClD,IAAI,CAACG,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC9B;EACF;EACA;;;;;;EAMAC,UAAUA,CAACC,MAAe,EAAET,KAAK;IAC/B,IAAI,CAACM,uBAAuB,GAAGG,MAAM;IACrC,IAAI,CAACF,eAAe,GAAG,KAAK;IAC5B,KAAK,IAAIhB,IAAI,IAAIS,KAAK,CAACK,QAAQ,EAAE,EAAE;MACjC,IAAGd,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO,EAAC;QAAC;MAAS,CAAC;MAC1CA,IAAI,CAACW,QAAQ,GAAGO,MAAM;IACxB;IACA,IAAI,CAACR,eAAe,GAAGD,KAAK,CAACjC,YAAY,EAAE,CAACjC,MAAM;EACpD;EAEA;EACA4E,YAAYA,CAACC,MAAgB,EAAEb,KAAA,GAAiB,KAAK;IACnD,IAAIA,KAAK,EAAE;MACTa,MAAM,CAACC,OAAO,CAACC,IAAI,GAAG,CAAC;IACzB;IACA,IAAI,CAACC,OAAO,CAACH,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC;EAEnC;EAEAE,KAAKA,CAAA;IACH;IACA;IACA;IACA;IACA;IACGC,QAAQ,CAACC,IAAI,CAACC,SAAS,GAAE,CAAC;IAC1BF,QAAQ,CAACG,eAAe,CAACD,SAAS,GAAC,CAAC;IACvC;EACF;EAEA;;;;;;EAMEE,YAAYA,CAACC,SAAc,EAAEC,YAAiB,EAAEC,IAAI;IAClD,IAAIC,KAAK,GAAG,EAAE;IACd,KAAK,MAAMhD,CAAC,IAAI+C,IAAI,CAACE,QAAQ,EAAE;MAC7BD,KAAK,CAACE,IAAI,CAAClD,CAAC,CAAC;MACb+C,IAAI,CAACE,QAAQ,CAACjD,CAAC,CAAC,CAACmD,WAAW,EAAE;MAC9BJ,IAAI,CAACE,QAAQ,CAACjD,CAAC,CAAC,CAACoD,sBAAsB,EAAE;IAC3C;IACA,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,SAAS,CAACvF,MAAM,EAAEsD,CAAC,EAAE,EAAE;MACzC,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,KAAK,CAAC1F,MAAM,EAAE0C,CAAC,EAAE,EAAE;QACrC;QACA,IAAII,eAAe,GAAGyC,SAAS,CAACjC,CAAC,CAAC,CAACT,IAAI,CAACC,eAAe;QACvD,IAAIY,SAAS,GAAG6B,SAAS,CAACjC,CAAC,CAAC,CAACT,IAAI,CAACa,SAAS;QAC3C,IAAIqC,OAAO,GAAGR,SAAS,CAACjC,CAAC,CAAC,CAACT,IAAI,CAACkD,OAAO;QACvC;QACA,IAAIhD,GAAG,GAAGwC,SAAS,CAACjC,CAAC,CAAC,CAACT,IAAI,CAACE,GAAG;QAC/B,IAAID,eAAe,IAAI,EAAE,IAAIA,eAAe,IAAIlC,SAAS,EAAE;UACzDkC,eAAe,GAAGnE,aAAa,CAACoE,GAAG,CAAC,CAACD,eAAe;QACtD;QACA,IAAIY,SAAS,IAAI,EAAE,IAAIA,SAAS,IAAI9C,SAAS,EAAE;UAC7C8C,SAAS,GAAG/E,aAAa,CAACoE,GAAG,CAAC,CAACW,SAAS;QAC1C;QACA,IAAIqC,OAAO,IAAI,EAAE,IAAIA,OAAO,IAAInF,SAAS,EAAE;UACzCmF,OAAO,GAAGpH,aAAa,CAACoE,GAAG,CAAC,CAACgD,OAAO;QACtC;QACA;QACA;QACA;QACA,IAAIjD,eAAe,IAAI4C,KAAK,CAAChD,CAAC,CAAC,IAAI6C,SAAS,CAACjC,CAAC,CAAC,CAACT,IAAI,CAACG,QAAQ,IAAI,IAAI,EAAE;UACrE,IAAIwC,YAAY,CAAC1C,eAAe,CAAC,KAAK,EAAE,IAAI0C,YAAY,CAAC1C,eAAe,CAAC,KAAK,IAAI,EAAE;YAClF;YACA;YACA,IAAI,CAACkD,SAAS,CAAC,IAAI,EAAC,OAAO,CAAC;YAC5B,OAAO,KAAK;UACd;QACF;MACF;IACF;IACA,OAAO,IAAI;EACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}