{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { VslvoyComponent } from './vslvoy.component';\nimport { VslvoyEditComponent } from '@business/tas/vslvoy/vslvoy-edit/vslvoy-edit.component';\nimport { VslvoyRoutingModule } from './vslvoy-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [VslvoyComponent, VslvoyEditComponent];\nexport class VslvoyModule {\n  static {\n    this.ɵfac = function VslvoyModule_Factory(t) {\n      return new (t || VslvoyModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VslvoyModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VslvoyRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VslvoyModule, {\n    declarations: [VslvoyComponent, VslvoyEditComponent],\n    imports: [SharedModule, VslvoyRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "VslvoyComponent", "VslvoyEditComponent", "VslvoyRoutingModule", "COMPONENTS", "VslvoyModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vslvoy\\vslvoy.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { VslvoyComponent } from './vslvoy.component';\r\nimport { VslvoyEditComponent } from '@business/tas/vslvoy/vslvoy-edit/vslvoy-edit.component';\r\nimport { VslvoyRoutingModule } from './vslvoy-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  VslvoyComponent,\r\n  VslvoyEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VslvoyRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VslvoyModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,QAAQ,wDAAwD;AAC5F,SAASC,mBAAmB,QAAQ,yBAAyB;;AAE7D,MAAMC,UAAU,GAAG,CACjBH,eAAe,EACfC,mBAAmB,CACpB;AAMD,OAAM,MAAOG,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAHbN,YAAY,EAAEI,mBAAmB,EAAEH,YAAY;IAAA;EAAA;;;2EAG9CK,YAAY;IAAAC,YAAA,GARvBL,eAAe,EACfC,mBAAmB;IAAAK,OAAA,GAITR,YAAY,EAAEI,mBAAmB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}