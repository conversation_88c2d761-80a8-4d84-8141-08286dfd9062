{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { BusinessLineComponent } from './businessline.component';\nimport { BusinessLineRoutingModule } from './businessline-routing.module';\nimport { BusinessLineEditComponent } from '@business/tas/businessline/businessline-edit/businessline-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [BusinessLineComponent, BusinessLineEditComponent];\nexport class BusinessLineModule {\n  static {\n    this.ɵfac = function BusinessLineModule_Factory(t) {\n      return new (t || BusinessLineModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BusinessLineModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, BusinessLineRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BusinessLineModule, {\n    declarations: [BusinessLineComponent, BusinessLineEditComponent],\n    imports: [SharedModule, BusinessLineRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "BusinessLineComponent", "BusinessLineRoutingModule", "BusinessLineEditComponent", "COMPONENTS", "BusinessLineModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\businessline\\businessline.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { BusinessLineComponent } from './businessline.component';\r\nimport { BusinessLineRoutingModule } from './businessline-routing.module';\r\nimport {BusinessLineEditComponent} from '@business/tas/businessline/businessline-edit/businessline-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  BusinessLineComponent,\r\n  BusinessLineEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, BusinessLineRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class BusinessLineModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAAQC,yBAAyB,QAAO,0EAA0E;;AAElH,MAAMC,UAAU,GAAG,CACjBH,qBAAqB,EACrBE,yBAAyB,CAC1B;AAMD,OAAM,MAAOE,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBN,YAAY,EAAEG,yBAAyB,EAAEF,YAAY;IAAA;EAAA;;;2EAGpDK,kBAAkB;IAAAC,YAAA,GAR7BL,qBAAqB,EACrBE,yBAAyB;IAAAI,OAAA,GAIfR,YAAY,EAAEG,yBAAyB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}