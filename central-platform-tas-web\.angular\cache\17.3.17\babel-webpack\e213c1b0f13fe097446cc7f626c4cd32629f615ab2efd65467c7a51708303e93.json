{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { CabinComponent } from './cabin.component';\nimport { CabinEditComponent } from '@business/tas/cabin/cabin-edit/cabin-edit.component';\nimport { CabinRoutingModule } from './cabin-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [CabinComponent, CabinEditComponent];\nexport class CabinModule {\n  static {\n    this.ɵfac = function CabinModule_Factory(t) {\n      return new (t || CabinModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CabinModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, CabinRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CabinModule, {\n    declarations: [CabinComponent, CabinEditComponent],\n    imports: [SharedModule, CabinRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "CabinComponent", "CabinEditComponent", "CabinRoutingModule", "COMPONENTS", "CabinModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\cabin\\cabin.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { CabinComponent } from './cabin.component';\r\nimport { CabinEditComponent } from '@business/tas/cabin/cabin-edit/cabin-edit.component';\r\nimport { CabinRoutingModule } from './cabin-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  CabinComponent,\r\n  CabinEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, CabinRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class CabinModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,kBAAkB,QAAQ,wBAAwB;;AAE3D,MAAMC,UAAU,GAAG,CACjBH,cAAc,EACdC,kBAAkB,CACnB;AAMD,OAAM,MAAOG,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZN,YAAY,EAAEI,kBAAkB,EAAEH,YAAY;IAAA;EAAA;;;2EAG7CK,WAAW;IAAAC,YAAA,GARtBL,cAAc,EACdC,kBAAkB;IAAAK,OAAA,GAIRR,YAAY,EAAEI,kBAAkB,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}