{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { VesselMessageComponent } from './vesselmessage.component';\nimport { VesselMessageEditComponent } from '@business/tas/vesselmessage/vesselmessage-edit/vesselmessage-edit.component';\nimport { VesselMessageRoutingModule } from './vesselmessage-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [VesselMessageComponent, VesselMessageEditComponent];\nexport class VesselMessageModule {\n  static {\n    this.ɵfac = function VesselMessageModule_Factory(t) {\n      return new (t || VesselMessageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VesselMessageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, VesselMessageRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VesselMessageModule, {\n    declarations: [VesselMessageComponent, VesselMessageEditComponent],\n    imports: [SharedModule, VesselMessageRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "VesselMessageComponent", "VesselMessageEditComponent", "VesselMessageRoutingModule", "COMPONENTS", "VesselMessageModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vesselmessage\\vesselmessage.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { VesselMessageComponent } from './vesselmessage.component';\r\nimport { VesselMessageEditComponent } from '@business/tas/vesselmessage/vesselmessage-edit/vesselmessage-edit.component';\r\nimport { VesselMessageRoutingModule } from './vesselmessage-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  VesselMessageComponent,\r\n  VesselMessageEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, VesselMessageRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class VesselMessageModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,0BAA0B,QAAQ,6EAA6E;AACxH,SAASC,0BAA0B,QAAQ,gCAAgC;;AAE3E,MAAMC,UAAU,GAAG,CACjBH,sBAAsB,EACtBC,0BAA0B,CAC3B;AAMD,OAAM,MAAOG,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBN,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;IAAA;EAAA;;;2EAGrDK,mBAAmB;IAAAC,YAAA,GAR9BL,sBAAsB,EACtBC,0BAA0B;IAAAK,OAAA,GAIhBR,YAAY,EAAEI,0BAA0B,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}