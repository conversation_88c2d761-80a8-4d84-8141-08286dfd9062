{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, dd MMMM yyyy',\n  long: 'dd MMMM yyyy',\n  medium: 'dd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "any", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/bg/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, dd MMMM yyyy',\n  long: 'dd MMMM yyyy',\n  medium: 'dd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C;AACxE,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,eAAe,GAAG;EACpBC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,UAAU,GAAG;EACfC,IAAI,EAAEV,iBAAiB,CAAC;IACtBW,OAAO,EAAEV,WAAW;IACpBW,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEb,iBAAiB,CAAC;IACtBW,OAAO,EAAEL,WAAW;IACpBM,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEd,iBAAiB,CAAC;IAC1BW,OAAO,EAAEJ,eAAe;IACxBK,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}