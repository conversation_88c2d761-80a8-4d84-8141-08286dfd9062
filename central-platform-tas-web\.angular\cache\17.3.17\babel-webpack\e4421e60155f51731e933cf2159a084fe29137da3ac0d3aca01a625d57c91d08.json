{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { SenderReceiverComponent } from './senderreceiver.component';\nimport { SenderReceiverEditComponent } from '@business/tas/senderreceiver/senderreceiver-edit/senderreceiver-edit.component';\nimport { SenderReceiverRoutingModule } from './senderreceiver-routing.module';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [SenderReceiverComponent, SenderReceiverEditComponent];\nexport class SenderReceiverModule {\n  static {\n    this.ɵfac = function SenderReceiverModule_Factory(t) {\n      return new (t || SenderReceiverModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SenderReceiverModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, SenderReceiverRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SenderReceiverModule, {\n    declarations: [SenderReceiverComponent, SenderReceiverEditComponent],\n    imports: [SharedModule, SenderReceiverRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "SenderReceiverComponent", "SenderReceiverEditComponent", "SenderReceiverRoutingModule", "COMPONENTS", "SenderReceiverModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\senderreceiver\\senderreceiver.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { SenderReceiverComponent } from './senderreceiver.component';\r\nimport { SenderReceiverEditComponent } from '@business/tas/senderreceiver/senderreceiver-edit/senderreceiver-edit.component';\r\nimport { SenderReceiverRoutingModule } from './senderreceiver-routing.module';\r\n\r\nconst COMPONENTS = [\r\n  SenderReceiverComponent,\r\n  SenderReceiverEditComponent,\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, SenderReceiverRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class SenderReceiverModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,2BAA2B,QAAQ,gFAAgF;AAC5H,SAASC,2BAA2B,QAAQ,iCAAiC;;AAE7E,MAAMC,UAAU,GAAG,CACjBH,uBAAuB,EACvBC,2BAA2B,CAC5B;AAMD,OAAM,MAAOG,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBN,YAAY,EAAEI,2BAA2B,EAAEH,YAAY;IAAA;EAAA;;;2EAGtDK,oBAAoB;IAAAC,YAAA,GAR/BL,uBAAuB,EACvBC,2BAA2B;IAAAK,OAAA,GAIjBR,YAAY,EAAEI,2BAA2B,EAAEH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}