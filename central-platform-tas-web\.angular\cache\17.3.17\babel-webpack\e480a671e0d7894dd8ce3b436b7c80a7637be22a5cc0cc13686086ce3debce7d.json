{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { DraftsurveyComponent } from './draftsurvey.component';\nimport { DraftsurveyRoutingModule } from './draftsurvey-routing.module';\nimport { DraftsurveyEditComponent } from '@business/tas/draftsurvey/draftsurvey-edit/draftsurvey-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [DraftsurveyComponent, DraftsurveyEditComponent];\nexport class DraftsurveyModule {\n  static {\n    this.ɵfac = function DraftsurveyModule_Factory(t) {\n      return new (t || DraftsurveyModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DraftsurveyModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, DraftsurveyRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DraftsurveyModule, {\n    declarations: [DraftsurveyComponent, DraftsurveyEditComponent],\n    imports: [SharedModule, DraftsurveyRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "DraftsurveyComponent", "DraftsurveyRoutingModule", "DraftsurveyEditComponent", "COMPONENTS", "DraftsurveyModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\draftsurvey\\draftsurvey.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { DraftsurveyComponent } from './draftsurvey.component';\r\nimport { DraftsurveyRoutingModule } from './draftsurvey-routing.module';\r\nimport {DraftsurveyEditComponent} from '@business/tas/draftsurvey/draftsurvey-edit/draftsurvey-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  DraftsurveyComponent,\r\n  DraftsurveyEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, DraftsurveyRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class DraftsurveyModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAAQC,wBAAwB,QAAO,uEAAuE;;AAE9G,MAAMC,UAAU,GAAG,CACjBH,oBAAoB,EACpBE,wBAAwB,CACzB;AAMD,OAAM,MAAOE,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBN,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;IAAA;EAAA;;;2EAGnDK,iBAAiB;IAAAC,YAAA,GAR5BL,oBAAoB,EACpBE,wBAAwB;IAAAI,OAAA,GAIdR,YAAY,EAAEG,wBAAwB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}