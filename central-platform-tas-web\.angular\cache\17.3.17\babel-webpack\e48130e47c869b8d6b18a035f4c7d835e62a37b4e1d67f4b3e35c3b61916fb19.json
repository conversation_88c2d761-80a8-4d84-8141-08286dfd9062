{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'р.'\",\n  long: \"do MMMM y 'р.'\",\n  medium: \"d MMM y 'р.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'о' {{time}}\",\n  long: \"{{date}} 'о' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/uk/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'р.'\",\n  long: \"do MMMM y 'р.'\",\n  medium: \"d MMM y 'р.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'о' {{time}}\",\n  long: \"{{date}} 'о' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C;AACxE,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAE,cAAc;EACtBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}