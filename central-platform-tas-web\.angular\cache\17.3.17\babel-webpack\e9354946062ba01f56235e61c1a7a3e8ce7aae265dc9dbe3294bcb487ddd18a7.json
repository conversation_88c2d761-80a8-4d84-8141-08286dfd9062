{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['vC', 'nC'],\n  abbreviated: ['vC', 'nC'],\n  wide: ['voor <PERSON>', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1ste kwartaal', '2de kwartaal', '3de kwartaal', '4de kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mrt', 'Apr', 'Mei', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'],\n  wide: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'September', '<PERSON><PERSON><PERSON>', 'November', '<PERSON><PERSON><PERSON>']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'W', 'D', 'V', 'S'],\n  short: ['So', 'Ma', 'Di', 'Wo', 'Do', 'Vr', 'Sa'],\n  abbreviated: ['Son', 'Maa', 'Din', 'Woe', 'Don', 'Vry', 'Sat'],\n  wide: ['Sondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrydag', 'Saterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 < 20) {\n    switch (rem100) {\n      case 1:\n      case 8:\n        return number + 'ste';\n      default:\n        return number + 'de';\n    }\n  }\n  return number + 'ste';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/af/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['vC', 'nC'],\n  abbreviated: ['vC', 'nC'],\n  wide: ['voor <PERSON>', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1ste kwartaal', '2de kwartaal', '3de kwartaal', '4de kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mrt', 'Apr', 'Mei', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'],\n  wide: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'September', '<PERSON><PERSON><PERSON>', 'November', '<PERSON><PERSON><PERSON>']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'W', 'D', 'V', 'S'],\n  short: ['So', 'Ma', 'Di', 'Wo', 'Do', 'Vr', 'Sa'],\n  abbreviated: ['Son', 'Maa', 'Din', 'Woe', 'Don', 'Vry', 'Sat'],\n  wide: ['Sondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrydag', 'Saterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'middaguur',\n    morning: 'oggend',\n    afternoon: 'middag',\n    evening: 'laat middag',\n    night: 'aand'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  abbreviated: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  },\n  wide: {\n    am: 'vm',\n    pm: 'nm',\n    midnight: 'middernag',\n    noon: 'uur die middag',\n    morning: 'uur die oggend',\n    afternoon: 'uur die middag',\n    evening: 'uur die aand',\n    night: 'uur die aand'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 < 20) {\n    switch (rem100) {\n      case 1:\n      case 8:\n        return number + 'ste';\n      default:\n        return number + 'de';\n    }\n  }\n  return number + 'ste';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,eAAe,EAAE,aAAa;AACvC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACxE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AACvI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU;AACtF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,cAAc;IACvBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,cAAc;IACvBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,cAAc;IACvBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAE;EACtD,IAAIC,MAAM,GAAGC,MAAM,CAACF,WAAW,CAAC;EAChC,IAAIG,MAAM,GAAGF,MAAM,GAAG,GAAG;EACzB,IAAIE,MAAM,GAAG,EAAE,EAAE;IACf,QAAQA,MAAM;MACZ,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOF,MAAM,GAAG,KAAK;MACvB;QACE,OAAOA,MAAM,GAAG,IAAI;IACxB;EACF;EACA,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}