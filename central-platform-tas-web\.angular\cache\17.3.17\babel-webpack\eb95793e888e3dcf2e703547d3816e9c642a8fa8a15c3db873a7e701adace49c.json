{"ast": null, "code": "'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n/* eslint-disable space-unary-ops */\nvar utils = require('../utils/common');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n//var Z_FILTERED          = 1;\n//var Z_HUFFMAN_ONLY      = 2;\n//var Z_RLE               = 3;\nvar Z_FIXED = 4;\n//var Z_DEFAULT_STRATEGY  = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\nvar Z_BINARY = 0;\nvar Z_TEXT = 1;\n//var Z_ASCII             = 1; // = Z_TEXT\nvar Z_UNKNOWN = 2;\n\n/*============================================================================*/\n\nfunction zero(buf) {\n  var len = buf.length;\n  while (--len >= 0) {\n    buf[len] = 0;\n  }\n}\n\n// From zutil.h\n\nvar STORED_BLOCK = 0;\nvar STATIC_TREES = 1;\nvar DYN_TREES = 2;\n/* The three kinds of block type */\n\nvar MIN_MATCH = 3;\nvar MAX_MATCH = 258;\n/* The minimum and maximum match lengths */\n\n// From deflate.h\n/* ===========================================================================\n * Internal compression state.\n */\n\nvar LENGTH_CODES = 29;\n/* number of length codes, not counting the special END_BLOCK code */\n\nvar LITERALS = 256;\n/* number of literal bytes 0..255 */\n\nvar L_CODES = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\n\nvar D_CODES = 30;\n/* number of distance codes */\n\nvar BL_CODES = 19;\n/* number of codes used to transfer the bit lengths */\n\nvar HEAP_SIZE = 2 * L_CODES + 1;\n/* maximum heap size */\n\nvar MAX_BITS = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar Buf_size = 16;\n/* size of bit buffer in bi_buf */\n\n/* ===========================================================================\n * Constants\n */\n\nvar MAX_BL_BITS = 7;\n/* Bit length codes must not exceed MAX_BL_BITS bits */\n\nvar END_BLOCK = 256;\n/* end of block literal code */\n\nvar REP_3_6 = 16;\n/* repeat previous bit length 3-6 times (2 bits of repeat count) */\n\nvar REPZ_3_10 = 17;\n/* repeat a zero length 3-10 times  (3 bits of repeat count) */\n\nvar REPZ_11_138 = 18;\n/* repeat a zero length 11-138 times  (7 bits of repeat count) */\n\n/* eslint-disable comma-spacing,array-bracket-spacing */\nvar extra_lbits = /* extra bits for each length code */\n[0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0];\nvar extra_dbits = /* extra bits for each distance code */\n[0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13];\nvar extra_blbits = /* extra bits for each bit length code */\n[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7];\nvar bl_order = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15];\n/* eslint-enable comma-spacing,array-bracket-spacing */\n\n/* The lengths of the bit length codes are sent in order of decreasing\n * probability, to avoid transmitting the lengths for unused bit length codes.\n */\n\n/* ===========================================================================\n * Local data. These are initialized only once.\n */\n\n// We pre-fill arrays with 0 to avoid uninitialized gaps\n\nvar DIST_CODE_LEN = 512; /* see definition of array dist_code below */\n\n// !!!! Use flat array instead of structure, Freq = i*2, Len = i*2+1\nvar static_ltree = new Array((L_CODES + 2) * 2);\nzero(static_ltree);\n/* The static literal tree. Since the bit lengths are imposed, there is no\n * need for the L_CODES extra codes used during heap construction. However\n * The codes 286 and 287 are needed to build a canonical tree (see _tr_init\n * below).\n */\n\nvar static_dtree = new Array(D_CODES * 2);\nzero(static_dtree);\n/* The static distance tree. (Actually a trivial tree since all codes use\n * 5 bits.)\n */\n\nvar _dist_code = new Array(DIST_CODE_LEN);\nzero(_dist_code);\n/* Distance codes. The first 256 values correspond to the distances\n * 3 .. 258, the last 256 values correspond to the top 8 bits of\n * the 15 bit distances.\n */\n\nvar _length_code = new Array(MAX_MATCH - MIN_MATCH + 1);\nzero(_length_code);\n/* length code for each normalized match length (0 == MIN_MATCH) */\n\nvar base_length = new Array(LENGTH_CODES);\nzero(base_length);\n/* First normalized length for each code (0 = MIN_MATCH) */\n\nvar base_dist = new Array(D_CODES);\nzero(base_dist);\n/* First normalized distance for each code (0 = distance of 1) */\n\nfunction StaticTreeDesc(static_tree, extra_bits, extra_base, elems, max_length) {\n  this.static_tree = static_tree; /* static tree or NULL */\n  this.extra_bits = extra_bits; /* extra bits for each code or NULL */\n  this.extra_base = extra_base; /* base index for extra_bits */\n  this.elems = elems; /* max number of elements in the tree */\n  this.max_length = max_length; /* max bit length for the codes */\n\n  // show if `static_tree` has data or dummy - needed for monomorphic objects\n  this.has_stree = static_tree && static_tree.length;\n}\nvar static_l_desc;\nvar static_d_desc;\nvar static_bl_desc;\nfunction TreeDesc(dyn_tree, stat_desc) {\n  this.dyn_tree = dyn_tree; /* the dynamic tree */\n  this.max_code = 0; /* largest code with non zero frequency */\n  this.stat_desc = stat_desc; /* the corresponding static tree */\n}\nfunction d_code(dist) {\n  return dist < 256 ? _dist_code[dist] : _dist_code[256 + (dist >>> 7)];\n}\n\n/* ===========================================================================\n * Output a short LSB first on the stream.\n * IN assertion: there is enough room in pendingBuf.\n */\nfunction put_short(s, w) {\n  //    put_byte(s, (uch)((w) & 0xff));\n  //    put_byte(s, (uch)((ush)(w) >> 8));\n  s.pending_buf[s.pending++] = w & 0xff;\n  s.pending_buf[s.pending++] = w >>> 8 & 0xff;\n}\n\n/* ===========================================================================\n * Send a value on a given number of bits.\n * IN assertion: length <= 16 and value fits in length bits.\n */\nfunction send_bits(s, value, length) {\n  if (s.bi_valid > Buf_size - length) {\n    s.bi_buf |= value << s.bi_valid & 0xffff;\n    put_short(s, s.bi_buf);\n    s.bi_buf = value >> Buf_size - s.bi_valid;\n    s.bi_valid += length - Buf_size;\n  } else {\n    s.bi_buf |= value << s.bi_valid & 0xffff;\n    s.bi_valid += length;\n  }\n}\nfunction send_code(s, c, tree) {\n  send_bits(s, tree[c * 2] /*.Code*/, tree[c * 2 + 1] /*.Len*/);\n}\n\n/* ===========================================================================\n * Reverse the first len bits of a code, using straightforward code (a faster\n * method would use a table)\n * IN assertion: 1 <= len <= 15\n */\nfunction bi_reverse(code, len) {\n  var res = 0;\n  do {\n    res |= code & 1;\n    code >>>= 1;\n    res <<= 1;\n  } while (--len > 0);\n  return res >>> 1;\n}\n\n/* ===========================================================================\n * Flush the bit buffer, keeping at most 7 bits in it.\n */\nfunction bi_flush(s) {\n  if (s.bi_valid === 16) {\n    put_short(s, s.bi_buf);\n    s.bi_buf = 0;\n    s.bi_valid = 0;\n  } else if (s.bi_valid >= 8) {\n    s.pending_buf[s.pending++] = s.bi_buf & 0xff;\n    s.bi_buf >>= 8;\n    s.bi_valid -= 8;\n  }\n}\n\n/* ===========================================================================\n * Compute the optimal bit lengths for a tree and update the total bit length\n * for the current block.\n * IN assertion: the fields freq and dad are set, heap[heap_max] and\n *    above are the tree nodes sorted by increasing frequency.\n * OUT assertions: the field len is set to the optimal bit length, the\n *     array bl_count contains the frequencies for each bit length.\n *     The length opt_len is updated; static_len is also updated if stree is\n *     not null.\n */\nfunction gen_bitlen(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc;    /* the tree descriptor */\n{\n  var tree = desc.dyn_tree;\n  var max_code = desc.max_code;\n  var stree = desc.stat_desc.static_tree;\n  var has_stree = desc.stat_desc.has_stree;\n  var extra = desc.stat_desc.extra_bits;\n  var base = desc.stat_desc.extra_base;\n  var max_length = desc.stat_desc.max_length;\n  var h; /* heap index */\n  var n, m; /* iterate over the tree elements */\n  var bits; /* bit length */\n  var xbits; /* extra bits */\n  var f; /* frequency */\n  var overflow = 0; /* number of elements with bit length too large */\n\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    s.bl_count[bits] = 0;\n  }\n\n  /* In a first pass, compute the optimal bit lengths (which may\n   * overflow in the case of the bit length tree).\n   */\n  tree[s.heap[s.heap_max] * 2 + 1] /*.Len*/ = 0; /* root of the heap */\n\n  for (h = s.heap_max + 1; h < HEAP_SIZE; h++) {\n    n = s.heap[h];\n    bits = tree[tree[n * 2 + 1] /*.Dad*/ * 2 + 1] /*.Len*/ + 1;\n    if (bits > max_length) {\n      bits = max_length;\n      overflow++;\n    }\n    tree[n * 2 + 1] /*.Len*/ = bits;\n    /* We overwrite tree[n].Dad which is no longer needed */\n\n    if (n > max_code) {\n      continue;\n    } /* not a leaf node */\n\n    s.bl_count[bits]++;\n    xbits = 0;\n    if (n >= base) {\n      xbits = extra[n - base];\n    }\n    f = tree[n * 2] /*.Freq*/;\n    s.opt_len += f * (bits + xbits);\n    if (has_stree) {\n      s.static_len += f * (stree[n * 2 + 1] /*.Len*/ + xbits);\n    }\n  }\n  if (overflow === 0) {\n    return;\n  }\n\n  // Trace((stderr,\"\\nbit length overflow\\n\"));\n  /* This happens for example on obj2 and pic of the Calgary corpus */\n\n  /* Find the first bit length which could increase: */\n  do {\n    bits = max_length - 1;\n    while (s.bl_count[bits] === 0) {\n      bits--;\n    }\n    s.bl_count[bits]--; /* move one leaf down the tree */\n    s.bl_count[bits + 1] += 2; /* move one overflow item as its brother */\n    s.bl_count[max_length]--;\n    /* The brother of the overflow item also moves one step up,\n     * but this does not affect bl_count[max_length]\n     */\n    overflow -= 2;\n  } while (overflow > 0);\n\n  /* Now recompute all bit lengths, scanning in increasing frequency.\n   * h is still equal to HEAP_SIZE. (It is simpler to reconstruct all\n   * lengths instead of fixing only the wrong ones. This idea is taken\n   * from 'ar' written by Haruhiko Okumura.)\n   */\n  for (bits = max_length; bits !== 0; bits--) {\n    n = s.bl_count[bits];\n    while (n !== 0) {\n      m = s.heap[--h];\n      if (m > max_code) {\n        continue;\n      }\n      if (tree[m * 2 + 1] /*.Len*/ !== bits) {\n        // Trace((stderr,\"code %d bits %d->%d\\n\", m, tree[m].Len, bits));\n        s.opt_len += (bits - tree[m * 2 + 1] /*.Len*/) * tree[m * 2] /*.Freq*/;\n        tree[m * 2 + 1] /*.Len*/ = bits;\n      }\n      n--;\n    }\n  }\n}\n\n/* ===========================================================================\n * Generate the codes for a given tree and bit counts (which need not be\n * optimal).\n * IN assertion: the array bl_count contains the bit length statistics for\n * the given tree and the field len is set for all tree elements.\n * OUT assertion: the field code is set for all tree elements of non\n *     zero code length.\n */\nfunction gen_codes(tree, max_code, bl_count)\n//    ct_data *tree;             /* the tree to decorate */\n//    int max_code;              /* largest code with non zero frequency */\n//    ushf *bl_count;            /* number of codes at each bit length */\n{\n  var next_code = new Array(MAX_BITS + 1); /* next code value for each bit length */\n  var code = 0; /* running code value */\n  var bits; /* bit index */\n  var n; /* code index */\n\n  /* The distribution counts are first used to generate the code values\n   * without bit reversal.\n   */\n  for (bits = 1; bits <= MAX_BITS; bits++) {\n    next_code[bits] = code = code + bl_count[bits - 1] << 1;\n  }\n  /* Check that the bit counts in bl_count are consistent. The last code\n   * must be all ones.\n   */\n  //Assert (code + bl_count[MAX_BITS]-1 == (1<<MAX_BITS)-1,\n  //        \"inconsistent bit counts\");\n  //Tracev((stderr,\"\\ngen_codes: max_code %d \", max_code));\n\n  for (n = 0; n <= max_code; n++) {\n    var len = tree[n * 2 + 1] /*.Len*/;\n    if (len === 0) {\n      continue;\n    }\n    /* Now reverse the bits */\n    tree[n * 2] /*.Code*/ = bi_reverse(next_code[len]++, len);\n\n    //Tracecv(tree != static_ltree, (stderr,\"\\nn %3d %c l %2d c %4x (%x) \",\n    //     n, (isgraph(n) ? n : ' '), len, tree[n].Code, next_code[len]-1));\n  }\n}\n\n/* ===========================================================================\n * Initialize the various 'constant' tables.\n */\nfunction tr_static_init() {\n  var n; /* iterates over tree elements */\n  var bits; /* bit counter */\n  var length; /* length value */\n  var code; /* code value */\n  var dist; /* distance index */\n  var bl_count = new Array(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  // do check in _tr_init()\n  //if (static_init_done) return;\n\n  /* For some embedded targets, global variables are not initialized: */\n  /*#ifdef NO_INIT_GLOBAL_POINTERS\n    static_l_desc.static_tree = static_ltree;\n    static_l_desc.extra_bits = extra_lbits;\n    static_d_desc.static_tree = static_dtree;\n    static_d_desc.extra_bits = extra_dbits;\n    static_bl_desc.extra_bits = extra_blbits;\n  #endif*/\n\n  /* Initialize the mapping length (0..255) -> length code (0..28) */\n  length = 0;\n  for (code = 0; code < LENGTH_CODES - 1; code++) {\n    base_length[code] = length;\n    for (n = 0; n < 1 << extra_lbits[code]; n++) {\n      _length_code[length++] = code;\n    }\n  }\n  //Assert (length == 256, \"tr_static_init: length != 256\");\n  /* Note that the length 255 (match length 258) can be represented\n   * in two different ways: code 284 + 5 bits or code 285, so we\n   * overwrite length_code[255] to use the best encoding:\n   */\n  _length_code[length - 1] = code;\n\n  /* Initialize the mapping dist (0..32K) -> dist code (0..29) */\n  dist = 0;\n  for (code = 0; code < 16; code++) {\n    base_dist[code] = dist;\n    for (n = 0; n < 1 << extra_dbits[code]; n++) {\n      _dist_code[dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: dist != 256\");\n  dist >>= 7; /* from now on, all distances are divided by 128 */\n  for (; code < D_CODES; code++) {\n    base_dist[code] = dist << 7;\n    for (n = 0; n < 1 << extra_dbits[code] - 7; n++) {\n      _dist_code[256 + dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: 256+dist != 512\");\n\n  /* Construct the codes of the static literal tree */\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    bl_count[bits] = 0;\n  }\n  n = 0;\n  while (n <= 143) {\n    static_ltree[n * 2 + 1] /*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  while (n <= 255) {\n    static_ltree[n * 2 + 1] /*.Len*/ = 9;\n    n++;\n    bl_count[9]++;\n  }\n  while (n <= 279) {\n    static_ltree[n * 2 + 1] /*.Len*/ = 7;\n    n++;\n    bl_count[7]++;\n  }\n  while (n <= 287) {\n    static_ltree[n * 2 + 1] /*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  /* Codes 286 and 287 do not exist, but we must include them in the\n   * tree construction to get a canonical Huffman tree (longest code\n   * all ones)\n   */\n  gen_codes(static_ltree, L_CODES + 1, bl_count);\n\n  /* The static distance tree is trivial: */\n  for (n = 0; n < D_CODES; n++) {\n    static_dtree[n * 2 + 1] /*.Len*/ = 5;\n    static_dtree[n * 2] /*.Code*/ = bi_reverse(n, 5);\n  }\n\n  // Now data ready and we can init static trees\n  static_l_desc = new StaticTreeDesc(static_ltree, extra_lbits, LITERALS + 1, L_CODES, MAX_BITS);\n  static_d_desc = new StaticTreeDesc(static_dtree, extra_dbits, 0, D_CODES, MAX_BITS);\n  static_bl_desc = new StaticTreeDesc(new Array(0), extra_blbits, 0, BL_CODES, MAX_BL_BITS);\n\n  //static_init_done = true;\n}\n\n/* ===========================================================================\n * Initialize a new block.\n */\nfunction init_block(s) {\n  var n; /* iterates over tree elements */\n\n  /* Initialize the trees. */\n  for (n = 0; n < L_CODES; n++) {\n    s.dyn_ltree[n * 2] /*.Freq*/ = 0;\n  }\n  for (n = 0; n < D_CODES; n++) {\n    s.dyn_dtree[n * 2] /*.Freq*/ = 0;\n  }\n  for (n = 0; n < BL_CODES; n++) {\n    s.bl_tree[n * 2] /*.Freq*/ = 0;\n  }\n  s.dyn_ltree[END_BLOCK * 2] /*.Freq*/ = 1;\n  s.opt_len = s.static_len = 0;\n  s.last_lit = s.matches = 0;\n}\n\n/* ===========================================================================\n * Flush the bit buffer and align the output on a byte boundary\n */\nfunction bi_windup(s) {\n  if (s.bi_valid > 8) {\n    put_short(s, s.bi_buf);\n  } else if (s.bi_valid > 0) {\n    //put_byte(s, (Byte)s->bi_buf);\n    s.pending_buf[s.pending++] = s.bi_buf;\n  }\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n}\n\n/* ===========================================================================\n * Copy a stored block, storing first the length and its\n * one's complement if requested.\n */\nfunction copy_block(s, buf, len, header)\n//DeflateState *s;\n//charf    *buf;    /* the input data */\n//unsigned len;     /* its length */\n//int      header;  /* true if block header must be written */\n{\n  bi_windup(s); /* align on byte boundary */\n\n  if (header) {\n    put_short(s, len);\n    put_short(s, ~len);\n  }\n  //  while (len--) {\n  //    put_byte(s, *buf++);\n  //  }\n  utils.arraySet(s.pending_buf, s.window, buf, len, s.pending);\n  s.pending += len;\n}\n\n/* ===========================================================================\n * Compares to subtrees, using the tree depth as tie breaker when\n * the subtrees have equal frequency. This minimizes the worst case length.\n */\nfunction smaller(tree, n, m, depth) {\n  var _n2 = n * 2;\n  var _m2 = m * 2;\n  return tree[_n2] /*.Freq*/ < tree[_m2] /*.Freq*/ || tree[_n2] /*.Freq*/ === tree[_m2] /*.Freq*/ && depth[n] <= depth[m];\n}\n\n/* ===========================================================================\n * Restore the heap property by moving down the tree starting at node k,\n * exchanging a node with the smallest of its two sons if necessary, stopping\n * when the heap property is re-established (each father smaller than its\n * two sons).\n */\nfunction pqdownheap(s, tree, k)\n//    deflate_state *s;\n//    ct_data *tree;  /* the tree to restore */\n//    int k;               /* node to move down */\n{\n  var v = s.heap[k];\n  var j = k << 1; /* left son of k */\n  while (j <= s.heap_len) {\n    /* Set j to the smallest of the two sons: */\n    if (j < s.heap_len && smaller(tree, s.heap[j + 1], s.heap[j], s.depth)) {\n      j++;\n    }\n    /* Exit if v is smaller than both sons */\n    if (smaller(tree, v, s.heap[j], s.depth)) {\n      break;\n    }\n\n    /* Exchange v with the smallest son */\n    s.heap[k] = s.heap[j];\n    k = j;\n\n    /* And continue down the tree, setting j to the left son of k */\n    j <<= 1;\n  }\n  s.heap[k] = v;\n}\n\n// inlined manually\n// var SMALLEST = 1;\n\n/* ===========================================================================\n * Send the block data compressed using the given Huffman trees\n */\nfunction compress_block(s, ltree, dtree)\n//    deflate_state *s;\n//    const ct_data *ltree; /* literal tree */\n//    const ct_data *dtree; /* distance tree */\n{\n  var dist; /* distance of matched string */\n  var lc; /* match length or unmatched char (if dist == 0) */\n  var lx = 0; /* running index in l_buf */\n  var code; /* the code to send */\n  var extra; /* number of extra bits to send */\n\n  if (s.last_lit !== 0) {\n    do {\n      dist = s.pending_buf[s.d_buf + lx * 2] << 8 | s.pending_buf[s.d_buf + lx * 2 + 1];\n      lc = s.pending_buf[s.l_buf + lx];\n      lx++;\n      if (dist === 0) {\n        send_code(s, lc, ltree); /* send a literal byte */\n        //Tracecv(isgraph(lc), (stderr,\" '%c' \", lc));\n      } else {\n        /* Here, lc is the match length - MIN_MATCH */\n        code = _length_code[lc];\n        send_code(s, code + LITERALS + 1, ltree); /* send the length code */\n        extra = extra_lbits[code];\n        if (extra !== 0) {\n          lc -= base_length[code];\n          send_bits(s, lc, extra); /* send the extra length bits */\n        }\n        dist--; /* dist is now the match distance - 1 */\n        code = d_code(dist);\n        //Assert (code < D_CODES, \"bad d_code\");\n\n        send_code(s, code, dtree); /* send the distance code */\n        extra = extra_dbits[code];\n        if (extra !== 0) {\n          dist -= base_dist[code];\n          send_bits(s, dist, extra); /* send the extra distance bits */\n        }\n      } /* literal or match pair ? */\n\n      /* Check that the overlay between pending_buf and d_buf+l_buf is ok: */\n      //Assert((uInt)(s->pending) < s->lit_bufsize + 2*lx,\n      //       \"pendingBuf overflow\");\n    } while (lx < s.last_lit);\n  }\n  send_code(s, END_BLOCK, ltree);\n}\n\n/* ===========================================================================\n * Construct one Huffman tree and assigns the code bit strings and lengths.\n * Update the total bit length for the current block.\n * IN assertion: the field freq is set for all tree elements.\n * OUT assertions: the fields len and code are set to the optimal bit length\n *     and corresponding code. The length opt_len is updated; static_len is\n *     also updated if stree is not null. The field max_code is set.\n */\nfunction build_tree(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc; /* the tree descriptor */\n{\n  var tree = desc.dyn_tree;\n  var stree = desc.stat_desc.static_tree;\n  var has_stree = desc.stat_desc.has_stree;\n  var elems = desc.stat_desc.elems;\n  var n, m; /* iterate over heap elements */\n  var max_code = -1; /* largest code with non zero frequency */\n  var node; /* new node being created */\n\n  /* Construct the initial heap, with least frequent element in\n   * heap[SMALLEST]. The sons of heap[n] are heap[2*n] and heap[2*n+1].\n   * heap[0] is not used.\n   */\n  s.heap_len = 0;\n  s.heap_max = HEAP_SIZE;\n  for (n = 0; n < elems; n++) {\n    if (tree[n * 2] /*.Freq*/ !== 0) {\n      s.heap[++s.heap_len] = max_code = n;\n      s.depth[n] = 0;\n    } else {\n      tree[n * 2 + 1] /*.Len*/ = 0;\n    }\n  }\n\n  /* The pkzip format requires that at least one distance code exists,\n   * and that at least one bit should be sent even if there is only one\n   * possible code. So to avoid special checks later on we force at least\n   * two codes of non zero frequency.\n   */\n  while (s.heap_len < 2) {\n    node = s.heap[++s.heap_len] = max_code < 2 ? ++max_code : 0;\n    tree[node * 2] /*.Freq*/ = 1;\n    s.depth[node] = 0;\n    s.opt_len--;\n    if (has_stree) {\n      s.static_len -= stree[node * 2 + 1] /*.Len*/;\n    }\n    /* node is 0 or 1 so it does not have extra bits */\n  }\n  desc.max_code = max_code;\n\n  /* The elements heap[heap_len/2+1 .. heap_len] are leaves of the tree,\n   * establish sub-heaps of increasing lengths:\n   */\n  for (n = s.heap_len >> 1 /*int /2*/; n >= 1; n--) {\n    pqdownheap(s, tree, n);\n  }\n\n  /* Construct the Huffman tree by repeatedly combining the least two\n   * frequent nodes.\n   */\n  node = elems; /* next internal node of the tree */\n  do {\n    //pqremove(s, tree, n);  /* n = node of least frequency */\n    /*** pqremove ***/\n    n = s.heap[1 /*SMALLEST*/];\n    s.heap[1 /*SMALLEST*/] = s.heap[s.heap_len--];\n    pqdownheap(s, tree, 1 /*SMALLEST*/);\n    /***/\n\n    m = s.heap[1 /*SMALLEST*/]; /* m = node of next least frequency */\n\n    s.heap[--s.heap_max] = n; /* keep the nodes sorted by frequency */\n    s.heap[--s.heap_max] = m;\n\n    /* Create a new node father of n and m */\n    tree[node * 2] /*.Freq*/ = tree[n * 2] /*.Freq*/ + tree[m * 2] /*.Freq*/;\n    s.depth[node] = (s.depth[n] >= s.depth[m] ? s.depth[n] : s.depth[m]) + 1;\n    tree[n * 2 + 1] /*.Dad*/ = tree[m * 2 + 1] /*.Dad*/ = node;\n\n    /* and insert the new node in the heap */\n    s.heap[1 /*SMALLEST*/] = node++;\n    pqdownheap(s, tree, 1 /*SMALLEST*/);\n  } while (s.heap_len >= 2);\n  s.heap[--s.heap_max] = s.heap[1 /*SMALLEST*/];\n\n  /* At this point, the fields freq and dad are set. We can now\n   * generate the bit lengths.\n   */\n  gen_bitlen(s, desc);\n\n  /* The field len is now set, we can generate the bit codes */\n  gen_codes(tree, max_code, s.bl_count);\n}\n\n/* ===========================================================================\n * Scan a literal or distance tree to determine the frequencies of the codes\n * in the bit length tree.\n */\nfunction scan_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree;   /* the tree to be scanned */\n//    int max_code;    /* and its largest code of non zero frequency */\n{\n  var n; /* iterates over all tree elements */\n  var prevlen = -1; /* last emitted length */\n  var curlen; /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1] /*.Len*/; /* length of next code */\n\n  var count = 0; /* repeat count of the current code */\n  var max_count = 7; /* max repeat count */\n  var min_count = 4; /* min repeat count */\n\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n  tree[(max_code + 1) * 2 + 1] /*.Len*/ = 0xffff; /* guard */\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1] /*.Len*/;\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n    } else if (count < min_count) {\n      s.bl_tree[curlen * 2] /*.Freq*/ += count;\n    } else if (curlen !== 0) {\n      if (curlen !== prevlen) {\n        s.bl_tree[curlen * 2] /*.Freq*/++;\n      }\n      s.bl_tree[REP_3_6 * 2] /*.Freq*/++;\n    } else if (count <= 10) {\n      s.bl_tree[REPZ_3_10 * 2] /*.Freq*/++;\n    } else {\n      s.bl_tree[REPZ_11_138 * 2] /*.Freq*/++;\n    }\n    count = 0;\n    prevlen = curlen;\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n/* ===========================================================================\n * Send a literal or distance tree in compressed form, using the codes in\n * bl_tree.\n */\nfunction send_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree; /* the tree to be scanned */\n//    int max_code;       /* and its largest code of non zero frequency */\n{\n  var n; /* iterates over all tree elements */\n  var prevlen = -1; /* last emitted length */\n  var curlen; /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1] /*.Len*/; /* length of next code */\n\n  var count = 0; /* repeat count of the current code */\n  var max_count = 7; /* max repeat count */\n  var min_count = 4; /* min repeat count */\n\n  /* tree[max_code+1].Len = -1; */ /* guard already set */\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1] /*.Len*/;\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n    } else if (count < min_count) {\n      do {\n        send_code(s, curlen, s.bl_tree);\n      } while (--count !== 0);\n    } else if (curlen !== 0) {\n      if (curlen !== prevlen) {\n        send_code(s, curlen, s.bl_tree);\n        count--;\n      }\n      //Assert(count >= 3 && count <= 6, \" 3_6?\");\n      send_code(s, REP_3_6, s.bl_tree);\n      send_bits(s, count - 3, 2);\n    } else if (count <= 10) {\n      send_code(s, REPZ_3_10, s.bl_tree);\n      send_bits(s, count - 3, 3);\n    } else {\n      send_code(s, REPZ_11_138, s.bl_tree);\n      send_bits(s, count - 11, 7);\n    }\n    count = 0;\n    prevlen = curlen;\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n/* ===========================================================================\n * Construct the Huffman tree for the bit lengths and return the index in\n * bl_order of the last bit length code to send.\n */\nfunction build_bl_tree(s) {\n  var max_blindex; /* index of last bit length code of non zero freq */\n\n  /* Determine the bit length frequencies for literal and distance trees */\n  scan_tree(s, s.dyn_ltree, s.l_desc.max_code);\n  scan_tree(s, s.dyn_dtree, s.d_desc.max_code);\n\n  /* Build the bit length tree: */\n  build_tree(s, s.bl_desc);\n  /* opt_len now includes the length of the tree representations, except\n   * the lengths of the bit lengths codes and the 5+5+4 bits for the counts.\n   */\n\n  /* Determine the number of bit length codes to send. The pkzip format\n   * requires that at least 4 bit length codes be sent. (appnote.txt says\n   * 3 but the actual value used is 4.)\n   */\n  for (max_blindex = BL_CODES - 1; max_blindex >= 3; max_blindex--) {\n    if (s.bl_tree[bl_order[max_blindex] * 2 + 1] /*.Len*/ !== 0) {\n      break;\n    }\n  }\n  /* Update opt_len to include the bit length tree and counts */\n  s.opt_len += 3 * (max_blindex + 1) + 5 + 5 + 4;\n  //Tracev((stderr, \"\\ndyn trees: dyn %ld, stat %ld\",\n  //        s->opt_len, s->static_len));\n\n  return max_blindex;\n}\n\n/* ===========================================================================\n * Send the header for a block using dynamic Huffman trees: the counts, the\n * lengths of the bit length codes, the literal tree and the distance tree.\n * IN assertion: lcodes >= 257, dcodes >= 1, blcodes >= 4.\n */\nfunction send_all_trees(s, lcodes, dcodes, blcodes)\n//    deflate_state *s;\n//    int lcodes, dcodes, blcodes; /* number of codes for each tree */\n{\n  var rank; /* index in bl_order */\n\n  //Assert (lcodes >= 257 && dcodes >= 1 && blcodes >= 4, \"not enough codes\");\n  //Assert (lcodes <= L_CODES && dcodes <= D_CODES && blcodes <= BL_CODES,\n  //        \"too many codes\");\n  //Tracev((stderr, \"\\nbl counts: \"));\n  send_bits(s, lcodes - 257, 5); /* not +255 as stated in appnote.txt */\n  send_bits(s, dcodes - 1, 5);\n  send_bits(s, blcodes - 4, 4); /* not -3 as stated in appnote.txt */\n  for (rank = 0; rank < blcodes; rank++) {\n    //Tracev((stderr, \"\\nbl code %2d \", bl_order[rank]));\n    send_bits(s, s.bl_tree[bl_order[rank] * 2 + 1] /*.Len*/, 3);\n  }\n  //Tracev((stderr, \"\\nbl tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_ltree, lcodes - 1); /* literal tree */\n  //Tracev((stderr, \"\\nlit tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_dtree, dcodes - 1); /* distance tree */\n  //Tracev((stderr, \"\\ndist tree: sent %ld\", s->bits_sent));\n}\n\n/* ===========================================================================\n * Check if the data type is TEXT or BINARY, using the following algorithm:\n * - TEXT if the two conditions below are satisfied:\n *    a) There are no non-portable control characters belonging to the\n *       \"black list\" (0..6, 14..25, 28..31).\n *    b) There is at least one printable character belonging to the\n *       \"white list\" (9 {TAB}, 10 {LF}, 13 {CR}, 32..255).\n * - BINARY otherwise.\n * - The following partially-portable control characters form a\n *   \"gray list\" that is ignored in this detection algorithm:\n *   (7 {BEL}, 8 {BS}, 11 {VT}, 12 {FF}, 26 {SUB}, 27 {ESC}).\n * IN assertion: the fields Freq of dyn_ltree are set.\n */\nfunction detect_data_type(s) {\n  /* black_mask is the bit mask of black-listed bytes\n   * set bits 0..6, 14..25, and 28..31\n   * 0xf3ffc07f = binary 11110011111111111100000001111111\n   */\n  var black_mask = 0xf3ffc07f;\n  var n;\n\n  /* Check for non-textual (\"black-listed\") bytes. */\n  for (n = 0; n <= 31; n++, black_mask >>>= 1) {\n    if (black_mask & 1 && s.dyn_ltree[n * 2] /*.Freq*/ !== 0) {\n      return Z_BINARY;\n    }\n  }\n\n  /* Check for textual (\"white-listed\") bytes. */\n  if (s.dyn_ltree[9 * 2] /*.Freq*/ !== 0 || s.dyn_ltree[10 * 2] /*.Freq*/ !== 0 || s.dyn_ltree[13 * 2] /*.Freq*/ !== 0) {\n    return Z_TEXT;\n  }\n  for (n = 32; n < LITERALS; n++) {\n    if (s.dyn_ltree[n * 2] /*.Freq*/ !== 0) {\n      return Z_TEXT;\n    }\n  }\n\n  /* There are no \"black-listed\" or \"white-listed\" bytes:\n   * this stream either is empty or has tolerated (\"gray-listed\") bytes only.\n   */\n  return Z_BINARY;\n}\nvar static_init_done = false;\n\n/* ===========================================================================\n * Initialize the tree data structures for a new zlib stream.\n */\nfunction _tr_init(s) {\n  if (!static_init_done) {\n    tr_static_init();\n    static_init_done = true;\n  }\n  s.l_desc = new TreeDesc(s.dyn_ltree, static_l_desc);\n  s.d_desc = new TreeDesc(s.dyn_dtree, static_d_desc);\n  s.bl_desc = new TreeDesc(s.bl_tree, static_bl_desc);\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n\n  /* Initialize the first block of the first file: */\n  init_block(s);\n}\n\n/* ===========================================================================\n * Send a stored block\n */\nfunction _tr_stored_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  send_bits(s, (STORED_BLOCK << 1) + (last ? 1 : 0), 3); /* send block type */\n  copy_block(s, buf, stored_len, true); /* with header */\n}\n\n/* ===========================================================================\n * Send one empty static block to give enough lookahead for inflate.\n * This takes 10 bits, of which 7 may remain in the bit buffer.\n */\nfunction _tr_align(s) {\n  send_bits(s, STATIC_TREES << 1, 3);\n  send_code(s, END_BLOCK, static_ltree);\n  bi_flush(s);\n}\n\n/* ===========================================================================\n * Determine the best encoding for the current block: dynamic trees, static\n * trees or store, and output the encoded block to the zip file.\n */\nfunction _tr_flush_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block, or NULL if too old */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  var opt_lenb, static_lenb; /* opt_len and static_len in bytes */\n  var max_blindex = 0; /* index of last bit length code of non zero freq */\n\n  /* Build the Huffman trees unless a stored block is forced */\n  if (s.level > 0) {\n    /* Check if the file is binary or text */\n    if (s.strm.data_type === Z_UNKNOWN) {\n      s.strm.data_type = detect_data_type(s);\n    }\n\n    /* Construct the literal and distance trees */\n    build_tree(s, s.l_desc);\n    // Tracev((stderr, \"\\nlit data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n\n    build_tree(s, s.d_desc);\n    // Tracev((stderr, \"\\ndist data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n    /* At this point, opt_len and static_len are the total bit lengths of\n     * the compressed block data, excluding the tree representations.\n     */\n\n    /* Build the bit length tree for the above two trees, and get the index\n     * in bl_order of the last bit length code to send.\n     */\n    max_blindex = build_bl_tree(s);\n\n    /* Determine the best encoding. Compute the block lengths in bytes. */\n    opt_lenb = s.opt_len + 3 + 7 >>> 3;\n    static_lenb = s.static_len + 3 + 7 >>> 3;\n\n    // Tracev((stderr, \"\\nopt %lu(%lu) stat %lu(%lu) stored %lu lit %u \",\n    //        opt_lenb, s->opt_len, static_lenb, s->static_len, stored_len,\n    //        s->last_lit));\n\n    if (static_lenb <= opt_lenb) {\n      opt_lenb = static_lenb;\n    }\n  } else {\n    // Assert(buf != (char*)0, \"lost buf\");\n    opt_lenb = static_lenb = stored_len + 5; /* force a stored block */\n  }\n  if (stored_len + 4 <= opt_lenb && buf !== -1) {\n    /* 4: two words for the lengths */\n\n    /* The test buf != NULL is only necessary if LIT_BUFSIZE > WSIZE.\n     * Otherwise we can't have processed more than WSIZE input bytes since\n     * the last block flush, because compression would have been\n     * successful. If LIT_BUFSIZE <= WSIZE, it is never too late to\n     * transform a block into a stored block.\n     */\n    _tr_stored_block(s, buf, stored_len, last);\n  } else if (s.strategy === Z_FIXED || static_lenb === opt_lenb) {\n    send_bits(s, (STATIC_TREES << 1) + (last ? 1 : 0), 3);\n    compress_block(s, static_ltree, static_dtree);\n  } else {\n    send_bits(s, (DYN_TREES << 1) + (last ? 1 : 0), 3);\n    send_all_trees(s, s.l_desc.max_code + 1, s.d_desc.max_code + 1, max_blindex + 1);\n    compress_block(s, s.dyn_ltree, s.dyn_dtree);\n  }\n  // Assert (s->compressed_len == s->bits_sent, \"bad compressed size\");\n  /* The above check is made mod 2^32, for files larger than 512 MB\n   * and uLong implemented on 32 bits.\n   */\n  init_block(s);\n  if (last) {\n    bi_windup(s);\n  }\n  // Tracev((stderr,\"\\ncomprlen %lu(%lu) \", s->compressed_len>>3,\n  //       s->compressed_len-7*last));\n}\n\n/* ===========================================================================\n * Save the match info and tally the frequency counts. Return true if\n * the current block must be flushed.\n */\nfunction _tr_tally(s, dist, lc)\n//    deflate_state *s;\n//    unsigned dist;  /* distance of matched string */\n//    unsigned lc;    /* match length-MIN_MATCH or unmatched char (if dist==0) */\n{\n  //var out_length, in_length, dcode;\n\n  s.pending_buf[s.d_buf + s.last_lit * 2] = dist >>> 8 & 0xff;\n  s.pending_buf[s.d_buf + s.last_lit * 2 + 1] = dist & 0xff;\n  s.pending_buf[s.l_buf + s.last_lit] = lc & 0xff;\n  s.last_lit++;\n  if (dist === 0) {\n    /* lc is the unmatched char */\n    s.dyn_ltree[lc * 2] /*.Freq*/++;\n  } else {\n    s.matches++;\n    /* Here, lc is the match length - MIN_MATCH */\n    dist--; /* dist = match distance - 1 */\n    //Assert((ush)dist < (ush)MAX_DIST(s) &&\n    //       (ush)lc <= (ush)(MAX_MATCH-MIN_MATCH) &&\n    //       (ush)d_code(dist) < (ush)D_CODES,  \"_tr_tally: bad match\");\n\n    s.dyn_ltree[(_length_code[lc] + LITERALS + 1) * 2] /*.Freq*/++;\n    s.dyn_dtree[d_code(dist) * 2] /*.Freq*/++;\n  }\n\n  // (!) This block is disabled in zlib defaults,\n  // don't enable it for binary compatibility\n\n  //#ifdef TRUNCATE_BLOCK\n  //  /* Try to guess if it is profitable to stop the current block here */\n  //  if ((s.last_lit & 0x1fff) === 0 && s.level > 2) {\n  //    /* Compute an upper bound for the compressed length */\n  //    out_length = s.last_lit*8;\n  //    in_length = s.strstart - s.block_start;\n  //\n  //    for (dcode = 0; dcode < D_CODES; dcode++) {\n  //      out_length += s.dyn_dtree[dcode*2]/*.Freq*/ * (5 + extra_dbits[dcode]);\n  //    }\n  //    out_length >>>= 3;\n  //    //Tracev((stderr,\"\\nlast_lit %u, in %ld, out ~%ld(%ld%%) \",\n  //    //       s->last_lit, in_length, out_length,\n  //    //       100L - out_length*100L/in_length));\n  //    if (s.matches < (s.last_lit>>1)/*int /2*/ && out_length < (in_length>>1)/*int /2*/) {\n  //      return true;\n  //    }\n  //  }\n  //#endif\n\n  return s.last_lit === s.lit_bufsize - 1;\n  /* We avoid equality with lit_bufsize because of wraparound at 64K\n   * on 16 bit machines and because stored blocks are restricted to\n   * 64K-1 bytes.\n   */\n}\nexports._tr_init = _tr_init;\nexports._tr_stored_block = _tr_stored_block;\nexports._tr_flush_block = _tr_flush_block;\nexports._tr_tally = _tr_tally;\nexports._tr_align = _tr_align;", "map": {"version": 3, "names": ["utils", "require", "Z_FIXED", "Z_BINARY", "Z_TEXT", "Z_UNKNOWN", "zero", "buf", "len", "length", "STORED_BLOCK", "STATIC_TREES", "DYN_TREES", "MIN_MATCH", "MAX_MATCH", "LENGTH_CODES", "LITERALS", "L_CODES", "D_CODES", "BL_CODES", "HEAP_SIZE", "MAX_BITS", "Buf_size", "MAX_BL_BITS", "END_BLOCK", "REP_3_6", "REPZ_3_10", "REPZ_11_138", "extra_lbits", "extra_dbits", "extra_blbits", "bl_order", "DIST_CODE_LEN", "static_ltree", "Array", "static_dtree", "_dist_code", "_length_code", "base_length", "base_dist", "StaticTreeDesc", "static_tree", "extra_bits", "extra_base", "elems", "max_length", "has_stree", "static_l_desc", "static_d_desc", "static_bl_desc", "TreeDesc", "dyn_tree", "stat_desc", "max_code", "d_code", "dist", "put_short", "s", "w", "pending_buf", "pending", "send_bits", "value", "bi_valid", "bi_buf", "send_code", "c", "tree", "bi_reverse", "code", "res", "bi_flush", "gen_bitlen", "desc", "stree", "extra", "base", "h", "n", "m", "bits", "xbits", "f", "overflow", "bl_count", "heap", "heap_max", "opt_len", "static_len", "gen_codes", "next_code", "tr_static_init", "init_block", "dyn_ltree", "dyn_dtree", "bl_tree", "last_lit", "matches", "bi_windup", "copy_block", "header", "arraySet", "window", "smaller", "depth", "_n2", "_m2", "pqdownheap", "k", "v", "j", "heap_len", "compress_block", "ltree", "dtree", "lc", "lx", "d_buf", "l_buf", "build_tree", "node", "scan_tree", "prevlen", "curlen", "nextlen", "count", "max_count", "min_count", "send_tree", "build_bl_tree", "max_blindex", "l_desc", "d_desc", "bl_desc", "send_all_trees", "lcodes", "dcodes", "blcodes", "rank", "detect_data_type", "black_mask", "static_init_done", "_tr_init", "_tr_stored_block", "stored_len", "last", "_tr_align", "_tr_flush_block", "opt_lenb", "static_lenb", "level", "strm", "data_type", "strategy", "_tr_tally", "lit_bufsize", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/pako/lib/zlib/trees.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n/* eslint-disable space-unary-ops */\n\nvar utils = require('../utils/common');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n//var Z_FILTERED          = 1;\n//var Z_HUFFMAN_ONLY      = 2;\n//var Z_RLE               = 3;\nvar Z_FIXED               = 4;\n//var Z_DEFAULT_STRATEGY  = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\nvar Z_BINARY              = 0;\nvar Z_TEXT                = 1;\n//var Z_ASCII             = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n/*============================================================================*/\n\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n// From zutil.h\n\nvar STORED_BLOCK = 0;\nvar STATIC_TREES = 1;\nvar DYN_TREES    = 2;\n/* The three kinds of block type */\n\nvar MIN_MATCH    = 3;\nvar MAX_MATCH    = 258;\n/* The minimum and maximum match lengths */\n\n// From deflate.h\n/* ===========================================================================\n * Internal compression state.\n */\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\n\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\n\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\n\nvar D_CODES       = 30;\n/* number of distance codes */\n\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\n\nvar HEAP_SIZE     = 2 * L_CODES + 1;\n/* maximum heap size */\n\nvar MAX_BITS      = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar Buf_size      = 16;\n/* size of bit buffer in bi_buf */\n\n\n/* ===========================================================================\n * Constants\n */\n\nvar MAX_BL_BITS = 7;\n/* Bit length codes must not exceed MAX_BL_BITS bits */\n\nvar END_BLOCK   = 256;\n/* end of block literal code */\n\nvar REP_3_6     = 16;\n/* repeat previous bit length 3-6 times (2 bits of repeat count) */\n\nvar REPZ_3_10   = 17;\n/* repeat a zero length 3-10 times  (3 bits of repeat count) */\n\nvar REPZ_11_138 = 18;\n/* repeat a zero length 11-138 times  (7 bits of repeat count) */\n\n/* eslint-disable comma-spacing,array-bracket-spacing */\nvar extra_lbits =   /* extra bits for each length code */\n  [0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0];\n\nvar extra_dbits =   /* extra bits for each distance code */\n  [0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];\n\nvar extra_blbits =  /* extra bits for each bit length code */\n  [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7];\n\nvar bl_order =\n  [16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];\n/* eslint-enable comma-spacing,array-bracket-spacing */\n\n/* The lengths of the bit length codes are sent in order of decreasing\n * probability, to avoid transmitting the lengths for unused bit length codes.\n */\n\n/* ===========================================================================\n * Local data. These are initialized only once.\n */\n\n// We pre-fill arrays with 0 to avoid uninitialized gaps\n\nvar DIST_CODE_LEN = 512; /* see definition of array dist_code below */\n\n// !!!! Use flat array instead of structure, Freq = i*2, Len = i*2+1\nvar static_ltree  = new Array((L_CODES + 2) * 2);\nzero(static_ltree);\n/* The static literal tree. Since the bit lengths are imposed, there is no\n * need for the L_CODES extra codes used during heap construction. However\n * The codes 286 and 287 are needed to build a canonical tree (see _tr_init\n * below).\n */\n\nvar static_dtree  = new Array(D_CODES * 2);\nzero(static_dtree);\n/* The static distance tree. (Actually a trivial tree since all codes use\n * 5 bits.)\n */\n\nvar _dist_code    = new Array(DIST_CODE_LEN);\nzero(_dist_code);\n/* Distance codes. The first 256 values correspond to the distances\n * 3 .. 258, the last 256 values correspond to the top 8 bits of\n * the 15 bit distances.\n */\n\nvar _length_code  = new Array(MAX_MATCH - MIN_MATCH + 1);\nzero(_length_code);\n/* length code for each normalized match length (0 == MIN_MATCH) */\n\nvar base_length   = new Array(LENGTH_CODES);\nzero(base_length);\n/* First normalized length for each code (0 = MIN_MATCH) */\n\nvar base_dist     = new Array(D_CODES);\nzero(base_dist);\n/* First normalized distance for each code (0 = distance of 1) */\n\n\nfunction StaticTreeDesc(static_tree, extra_bits, extra_base, elems, max_length) {\n\n  this.static_tree  = static_tree;  /* static tree or NULL */\n  this.extra_bits   = extra_bits;   /* extra bits for each code or NULL */\n  this.extra_base   = extra_base;   /* base index for extra_bits */\n  this.elems        = elems;        /* max number of elements in the tree */\n  this.max_length   = max_length;   /* max bit length for the codes */\n\n  // show if `static_tree` has data or dummy - needed for monomorphic objects\n  this.has_stree    = static_tree && static_tree.length;\n}\n\n\nvar static_l_desc;\nvar static_d_desc;\nvar static_bl_desc;\n\n\nfunction TreeDesc(dyn_tree, stat_desc) {\n  this.dyn_tree = dyn_tree;     /* the dynamic tree */\n  this.max_code = 0;            /* largest code with non zero frequency */\n  this.stat_desc = stat_desc;   /* the corresponding static tree */\n}\n\n\n\nfunction d_code(dist) {\n  return dist < 256 ? _dist_code[dist] : _dist_code[256 + (dist >>> 7)];\n}\n\n\n/* ===========================================================================\n * Output a short LSB first on the stream.\n * IN assertion: there is enough room in pendingBuf.\n */\nfunction put_short(s, w) {\n//    put_byte(s, (uch)((w) & 0xff));\n//    put_byte(s, (uch)((ush)(w) >> 8));\n  s.pending_buf[s.pending++] = (w) & 0xff;\n  s.pending_buf[s.pending++] = (w >>> 8) & 0xff;\n}\n\n\n/* ===========================================================================\n * Send a value on a given number of bits.\n * IN assertion: length <= 16 and value fits in length bits.\n */\nfunction send_bits(s, value, length) {\n  if (s.bi_valid > (Buf_size - length)) {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    put_short(s, s.bi_buf);\n    s.bi_buf = value >> (Buf_size - s.bi_valid);\n    s.bi_valid += length - Buf_size;\n  } else {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    s.bi_valid += length;\n  }\n}\n\n\nfunction send_code(s, c, tree) {\n  send_bits(s, tree[c * 2]/*.Code*/, tree[c * 2 + 1]/*.Len*/);\n}\n\n\n/* ===========================================================================\n * Reverse the first len bits of a code, using straightforward code (a faster\n * method would use a table)\n * IN assertion: 1 <= len <= 15\n */\nfunction bi_reverse(code, len) {\n  var res = 0;\n  do {\n    res |= code & 1;\n    code >>>= 1;\n    res <<= 1;\n  } while (--len > 0);\n  return res >>> 1;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer, keeping at most 7 bits in it.\n */\nfunction bi_flush(s) {\n  if (s.bi_valid === 16) {\n    put_short(s, s.bi_buf);\n    s.bi_buf = 0;\n    s.bi_valid = 0;\n\n  } else if (s.bi_valid >= 8) {\n    s.pending_buf[s.pending++] = s.bi_buf & 0xff;\n    s.bi_buf >>= 8;\n    s.bi_valid -= 8;\n  }\n}\n\n\n/* ===========================================================================\n * Compute the optimal bit lengths for a tree and update the total bit length\n * for the current block.\n * IN assertion: the fields freq and dad are set, heap[heap_max] and\n *    above are the tree nodes sorted by increasing frequency.\n * OUT assertions: the field len is set to the optimal bit length, the\n *     array bl_count contains the frequencies for each bit length.\n *     The length opt_len is updated; static_len is also updated if stree is\n *     not null.\n */\nfunction gen_bitlen(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc;    /* the tree descriptor */\n{\n  var tree            = desc.dyn_tree;\n  var max_code        = desc.max_code;\n  var stree           = desc.stat_desc.static_tree;\n  var has_stree       = desc.stat_desc.has_stree;\n  var extra           = desc.stat_desc.extra_bits;\n  var base            = desc.stat_desc.extra_base;\n  var max_length      = desc.stat_desc.max_length;\n  var h;              /* heap index */\n  var n, m;           /* iterate over the tree elements */\n  var bits;           /* bit length */\n  var xbits;          /* extra bits */\n  var f;              /* frequency */\n  var overflow = 0;   /* number of elements with bit length too large */\n\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    s.bl_count[bits] = 0;\n  }\n\n  /* In a first pass, compute the optimal bit lengths (which may\n   * overflow in the case of the bit length tree).\n   */\n  tree[s.heap[s.heap_max] * 2 + 1]/*.Len*/ = 0; /* root of the heap */\n\n  for (h = s.heap_max + 1; h < HEAP_SIZE; h++) {\n    n = s.heap[h];\n    bits = tree[tree[n * 2 + 1]/*.Dad*/ * 2 + 1]/*.Len*/ + 1;\n    if (bits > max_length) {\n      bits = max_length;\n      overflow++;\n    }\n    tree[n * 2 + 1]/*.Len*/ = bits;\n    /* We overwrite tree[n].Dad which is no longer needed */\n\n    if (n > max_code) { continue; } /* not a leaf node */\n\n    s.bl_count[bits]++;\n    xbits = 0;\n    if (n >= base) {\n      xbits = extra[n - base];\n    }\n    f = tree[n * 2]/*.Freq*/;\n    s.opt_len += f * (bits + xbits);\n    if (has_stree) {\n      s.static_len += f * (stree[n * 2 + 1]/*.Len*/ + xbits);\n    }\n  }\n  if (overflow === 0) { return; }\n\n  // Trace((stderr,\"\\nbit length overflow\\n\"));\n  /* This happens for example on obj2 and pic of the Calgary corpus */\n\n  /* Find the first bit length which could increase: */\n  do {\n    bits = max_length - 1;\n    while (s.bl_count[bits] === 0) { bits--; }\n    s.bl_count[bits]--;      /* move one leaf down the tree */\n    s.bl_count[bits + 1] += 2; /* move one overflow item as its brother */\n    s.bl_count[max_length]--;\n    /* The brother of the overflow item also moves one step up,\n     * but this does not affect bl_count[max_length]\n     */\n    overflow -= 2;\n  } while (overflow > 0);\n\n  /* Now recompute all bit lengths, scanning in increasing frequency.\n   * h is still equal to HEAP_SIZE. (It is simpler to reconstruct all\n   * lengths instead of fixing only the wrong ones. This idea is taken\n   * from 'ar' written by Haruhiko Okumura.)\n   */\n  for (bits = max_length; bits !== 0; bits--) {\n    n = s.bl_count[bits];\n    while (n !== 0) {\n      m = s.heap[--h];\n      if (m > max_code) { continue; }\n      if (tree[m * 2 + 1]/*.Len*/ !== bits) {\n        // Trace((stderr,\"code %d bits %d->%d\\n\", m, tree[m].Len, bits));\n        s.opt_len += (bits - tree[m * 2 + 1]/*.Len*/) * tree[m * 2]/*.Freq*/;\n        tree[m * 2 + 1]/*.Len*/ = bits;\n      }\n      n--;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Generate the codes for a given tree and bit counts (which need not be\n * optimal).\n * IN assertion: the array bl_count contains the bit length statistics for\n * the given tree and the field len is set for all tree elements.\n * OUT assertion: the field code is set for all tree elements of non\n *     zero code length.\n */\nfunction gen_codes(tree, max_code, bl_count)\n//    ct_data *tree;             /* the tree to decorate */\n//    int max_code;              /* largest code with non zero frequency */\n//    ushf *bl_count;            /* number of codes at each bit length */\n{\n  var next_code = new Array(MAX_BITS + 1); /* next code value for each bit length */\n  var code = 0;              /* running code value */\n  var bits;                  /* bit index */\n  var n;                     /* code index */\n\n  /* The distribution counts are first used to generate the code values\n   * without bit reversal.\n   */\n  for (bits = 1; bits <= MAX_BITS; bits++) {\n    next_code[bits] = code = (code + bl_count[bits - 1]) << 1;\n  }\n  /* Check that the bit counts in bl_count are consistent. The last code\n   * must be all ones.\n   */\n  //Assert (code + bl_count[MAX_BITS]-1 == (1<<MAX_BITS)-1,\n  //        \"inconsistent bit counts\");\n  //Tracev((stderr,\"\\ngen_codes: max_code %d \", max_code));\n\n  for (n = 0;  n <= max_code; n++) {\n    var len = tree[n * 2 + 1]/*.Len*/;\n    if (len === 0) { continue; }\n    /* Now reverse the bits */\n    tree[n * 2]/*.Code*/ = bi_reverse(next_code[len]++, len);\n\n    //Tracecv(tree != static_ltree, (stderr,\"\\nn %3d %c l %2d c %4x (%x) \",\n    //     n, (isgraph(n) ? n : ' '), len, tree[n].Code, next_code[len]-1));\n  }\n}\n\n\n/* ===========================================================================\n * Initialize the various 'constant' tables.\n */\nfunction tr_static_init() {\n  var n;        /* iterates over tree elements */\n  var bits;     /* bit counter */\n  var length;   /* length value */\n  var code;     /* code value */\n  var dist;     /* distance index */\n  var bl_count = new Array(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  // do check in _tr_init()\n  //if (static_init_done) return;\n\n  /* For some embedded targets, global variables are not initialized: */\n/*#ifdef NO_INIT_GLOBAL_POINTERS\n  static_l_desc.static_tree = static_ltree;\n  static_l_desc.extra_bits = extra_lbits;\n  static_d_desc.static_tree = static_dtree;\n  static_d_desc.extra_bits = extra_dbits;\n  static_bl_desc.extra_bits = extra_blbits;\n#endif*/\n\n  /* Initialize the mapping length (0..255) -> length code (0..28) */\n  length = 0;\n  for (code = 0; code < LENGTH_CODES - 1; code++) {\n    base_length[code] = length;\n    for (n = 0; n < (1 << extra_lbits[code]); n++) {\n      _length_code[length++] = code;\n    }\n  }\n  //Assert (length == 256, \"tr_static_init: length != 256\");\n  /* Note that the length 255 (match length 258) can be represented\n   * in two different ways: code 284 + 5 bits or code 285, so we\n   * overwrite length_code[255] to use the best encoding:\n   */\n  _length_code[length - 1] = code;\n\n  /* Initialize the mapping dist (0..32K) -> dist code (0..29) */\n  dist = 0;\n  for (code = 0; code < 16; code++) {\n    base_dist[code] = dist;\n    for (n = 0; n < (1 << extra_dbits[code]); n++) {\n      _dist_code[dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: dist != 256\");\n  dist >>= 7; /* from now on, all distances are divided by 128 */\n  for (; code < D_CODES; code++) {\n    base_dist[code] = dist << 7;\n    for (n = 0; n < (1 << (extra_dbits[code] - 7)); n++) {\n      _dist_code[256 + dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: 256+dist != 512\");\n\n  /* Construct the codes of the static literal tree */\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    bl_count[bits] = 0;\n  }\n\n  n = 0;\n  while (n <= 143) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  while (n <= 255) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 9;\n    n++;\n    bl_count[9]++;\n  }\n  while (n <= 279) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 7;\n    n++;\n    bl_count[7]++;\n  }\n  while (n <= 287) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  /* Codes 286 and 287 do not exist, but we must include them in the\n   * tree construction to get a canonical Huffman tree (longest code\n   * all ones)\n   */\n  gen_codes(static_ltree, L_CODES + 1, bl_count);\n\n  /* The static distance tree is trivial: */\n  for (n = 0; n < D_CODES; n++) {\n    static_dtree[n * 2 + 1]/*.Len*/ = 5;\n    static_dtree[n * 2]/*.Code*/ = bi_reverse(n, 5);\n  }\n\n  // Now data ready and we can init static trees\n  static_l_desc = new StaticTreeDesc(static_ltree, extra_lbits, LITERALS + 1, L_CODES, MAX_BITS);\n  static_d_desc = new StaticTreeDesc(static_dtree, extra_dbits, 0,          D_CODES, MAX_BITS);\n  static_bl_desc = new StaticTreeDesc(new Array(0), extra_blbits, 0,         BL_CODES, MAX_BL_BITS);\n\n  //static_init_done = true;\n}\n\n\n/* ===========================================================================\n * Initialize a new block.\n */\nfunction init_block(s) {\n  var n; /* iterates over tree elements */\n\n  /* Initialize the trees. */\n  for (n = 0; n < L_CODES;  n++) { s.dyn_ltree[n * 2]/*.Freq*/ = 0; }\n  for (n = 0; n < D_CODES;  n++) { s.dyn_dtree[n * 2]/*.Freq*/ = 0; }\n  for (n = 0; n < BL_CODES; n++) { s.bl_tree[n * 2]/*.Freq*/ = 0; }\n\n  s.dyn_ltree[END_BLOCK * 2]/*.Freq*/ = 1;\n  s.opt_len = s.static_len = 0;\n  s.last_lit = s.matches = 0;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer and align the output on a byte boundary\n */\nfunction bi_windup(s)\n{\n  if (s.bi_valid > 8) {\n    put_short(s, s.bi_buf);\n  } else if (s.bi_valid > 0) {\n    //put_byte(s, (Byte)s->bi_buf);\n    s.pending_buf[s.pending++] = s.bi_buf;\n  }\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n}\n\n/* ===========================================================================\n * Copy a stored block, storing first the length and its\n * one's complement if requested.\n */\nfunction copy_block(s, buf, len, header)\n//DeflateState *s;\n//charf    *buf;    /* the input data */\n//unsigned len;     /* its length */\n//int      header;  /* true if block header must be written */\n{\n  bi_windup(s);        /* align on byte boundary */\n\n  if (header) {\n    put_short(s, len);\n    put_short(s, ~len);\n  }\n//  while (len--) {\n//    put_byte(s, *buf++);\n//  }\n  utils.arraySet(s.pending_buf, s.window, buf, len, s.pending);\n  s.pending += len;\n}\n\n/* ===========================================================================\n * Compares to subtrees, using the tree depth as tie breaker when\n * the subtrees have equal frequency. This minimizes the worst case length.\n */\nfunction smaller(tree, n, m, depth) {\n  var _n2 = n * 2;\n  var _m2 = m * 2;\n  return (tree[_n2]/*.Freq*/ < tree[_m2]/*.Freq*/ ||\n         (tree[_n2]/*.Freq*/ === tree[_m2]/*.Freq*/ && depth[n] <= depth[m]));\n}\n\n/* ===========================================================================\n * Restore the heap property by moving down the tree starting at node k,\n * exchanging a node with the smallest of its two sons if necessary, stopping\n * when the heap property is re-established (each father smaller than its\n * two sons).\n */\nfunction pqdownheap(s, tree, k)\n//    deflate_state *s;\n//    ct_data *tree;  /* the tree to restore */\n//    int k;               /* node to move down */\n{\n  var v = s.heap[k];\n  var j = k << 1;  /* left son of k */\n  while (j <= s.heap_len) {\n    /* Set j to the smallest of the two sons: */\n    if (j < s.heap_len &&\n      smaller(tree, s.heap[j + 1], s.heap[j], s.depth)) {\n      j++;\n    }\n    /* Exit if v is smaller than both sons */\n    if (smaller(tree, v, s.heap[j], s.depth)) { break; }\n\n    /* Exchange v with the smallest son */\n    s.heap[k] = s.heap[j];\n    k = j;\n\n    /* And continue down the tree, setting j to the left son of k */\n    j <<= 1;\n  }\n  s.heap[k] = v;\n}\n\n\n// inlined manually\n// var SMALLEST = 1;\n\n/* ===========================================================================\n * Send the block data compressed using the given Huffman trees\n */\nfunction compress_block(s, ltree, dtree)\n//    deflate_state *s;\n//    const ct_data *ltree; /* literal tree */\n//    const ct_data *dtree; /* distance tree */\n{\n  var dist;           /* distance of matched string */\n  var lc;             /* match length or unmatched char (if dist == 0) */\n  var lx = 0;         /* running index in l_buf */\n  var code;           /* the code to send */\n  var extra;          /* number of extra bits to send */\n\n  if (s.last_lit !== 0) {\n    do {\n      dist = (s.pending_buf[s.d_buf + lx * 2] << 8) | (s.pending_buf[s.d_buf + lx * 2 + 1]);\n      lc = s.pending_buf[s.l_buf + lx];\n      lx++;\n\n      if (dist === 0) {\n        send_code(s, lc, ltree); /* send a literal byte */\n        //Tracecv(isgraph(lc), (stderr,\" '%c' \", lc));\n      } else {\n        /* Here, lc is the match length - MIN_MATCH */\n        code = _length_code[lc];\n        send_code(s, code + LITERALS + 1, ltree); /* send the length code */\n        extra = extra_lbits[code];\n        if (extra !== 0) {\n          lc -= base_length[code];\n          send_bits(s, lc, extra);       /* send the extra length bits */\n        }\n        dist--; /* dist is now the match distance - 1 */\n        code = d_code(dist);\n        //Assert (code < D_CODES, \"bad d_code\");\n\n        send_code(s, code, dtree);       /* send the distance code */\n        extra = extra_dbits[code];\n        if (extra !== 0) {\n          dist -= base_dist[code];\n          send_bits(s, dist, extra);   /* send the extra distance bits */\n        }\n      } /* literal or match pair ? */\n\n      /* Check that the overlay between pending_buf and d_buf+l_buf is ok: */\n      //Assert((uInt)(s->pending) < s->lit_bufsize + 2*lx,\n      //       \"pendingBuf overflow\");\n\n    } while (lx < s.last_lit);\n  }\n\n  send_code(s, END_BLOCK, ltree);\n}\n\n\n/* ===========================================================================\n * Construct one Huffman tree and assigns the code bit strings and lengths.\n * Update the total bit length for the current block.\n * IN assertion: the field freq is set for all tree elements.\n * OUT assertions: the fields len and code are set to the optimal bit length\n *     and corresponding code. The length opt_len is updated; static_len is\n *     also updated if stree is not null. The field max_code is set.\n */\nfunction build_tree(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc; /* the tree descriptor */\n{\n  var tree     = desc.dyn_tree;\n  var stree    = desc.stat_desc.static_tree;\n  var has_stree = desc.stat_desc.has_stree;\n  var elems    = desc.stat_desc.elems;\n  var n, m;          /* iterate over heap elements */\n  var max_code = -1; /* largest code with non zero frequency */\n  var node;          /* new node being created */\n\n  /* Construct the initial heap, with least frequent element in\n   * heap[SMALLEST]. The sons of heap[n] are heap[2*n] and heap[2*n+1].\n   * heap[0] is not used.\n   */\n  s.heap_len = 0;\n  s.heap_max = HEAP_SIZE;\n\n  for (n = 0; n < elems; n++) {\n    if (tree[n * 2]/*.Freq*/ !== 0) {\n      s.heap[++s.heap_len] = max_code = n;\n      s.depth[n] = 0;\n\n    } else {\n      tree[n * 2 + 1]/*.Len*/ = 0;\n    }\n  }\n\n  /* The pkzip format requires that at least one distance code exists,\n   * and that at least one bit should be sent even if there is only one\n   * possible code. So to avoid special checks later on we force at least\n   * two codes of non zero frequency.\n   */\n  while (s.heap_len < 2) {\n    node = s.heap[++s.heap_len] = (max_code < 2 ? ++max_code : 0);\n    tree[node * 2]/*.Freq*/ = 1;\n    s.depth[node] = 0;\n    s.opt_len--;\n\n    if (has_stree) {\n      s.static_len -= stree[node * 2 + 1]/*.Len*/;\n    }\n    /* node is 0 or 1 so it does not have extra bits */\n  }\n  desc.max_code = max_code;\n\n  /* The elements heap[heap_len/2+1 .. heap_len] are leaves of the tree,\n   * establish sub-heaps of increasing lengths:\n   */\n  for (n = (s.heap_len >> 1/*int /2*/); n >= 1; n--) { pqdownheap(s, tree, n); }\n\n  /* Construct the Huffman tree by repeatedly combining the least two\n   * frequent nodes.\n   */\n  node = elems;              /* next internal node of the tree */\n  do {\n    //pqremove(s, tree, n);  /* n = node of least frequency */\n    /*** pqremove ***/\n    n = s.heap[1/*SMALLEST*/];\n    s.heap[1/*SMALLEST*/] = s.heap[s.heap_len--];\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n    /***/\n\n    m = s.heap[1/*SMALLEST*/]; /* m = node of next least frequency */\n\n    s.heap[--s.heap_max] = n; /* keep the nodes sorted by frequency */\n    s.heap[--s.heap_max] = m;\n\n    /* Create a new node father of n and m */\n    tree[node * 2]/*.Freq*/ = tree[n * 2]/*.Freq*/ + tree[m * 2]/*.Freq*/;\n    s.depth[node] = (s.depth[n] >= s.depth[m] ? s.depth[n] : s.depth[m]) + 1;\n    tree[n * 2 + 1]/*.Dad*/ = tree[m * 2 + 1]/*.Dad*/ = node;\n\n    /* and insert the new node in the heap */\n    s.heap[1/*SMALLEST*/] = node++;\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n\n  } while (s.heap_len >= 2);\n\n  s.heap[--s.heap_max] = s.heap[1/*SMALLEST*/];\n\n  /* At this point, the fields freq and dad are set. We can now\n   * generate the bit lengths.\n   */\n  gen_bitlen(s, desc);\n\n  /* The field len is now set, we can generate the bit codes */\n  gen_codes(tree, max_code, s.bl_count);\n}\n\n\n/* ===========================================================================\n * Scan a literal or distance tree to determine the frequencies of the codes\n * in the bit length tree.\n */\nfunction scan_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree;   /* the tree to be scanned */\n//    int max_code;    /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n  tree[(max_code + 1) * 2 + 1]/*.Len*/ = 0xffff; /* guard */\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      s.bl_tree[curlen * 2]/*.Freq*/ += count;\n\n    } else if (curlen !== 0) {\n\n      if (curlen !== prevlen) { s.bl_tree[curlen * 2]/*.Freq*/++; }\n      s.bl_tree[REP_3_6 * 2]/*.Freq*/++;\n\n    } else if (count <= 10) {\n      s.bl_tree[REPZ_3_10 * 2]/*.Freq*/++;\n\n    } else {\n      s.bl_tree[REPZ_11_138 * 2]/*.Freq*/++;\n    }\n\n    count = 0;\n    prevlen = curlen;\n\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Send a literal or distance tree in compressed form, using the codes in\n * bl_tree.\n */\nfunction send_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree; /* the tree to be scanned */\n//    int max_code;       /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  /* tree[max_code+1].Len = -1; */  /* guard already set */\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      do { send_code(s, curlen, s.bl_tree); } while (--count !== 0);\n\n    } else if (curlen !== 0) {\n      if (curlen !== prevlen) {\n        send_code(s, curlen, s.bl_tree);\n        count--;\n      }\n      //Assert(count >= 3 && count <= 6, \" 3_6?\");\n      send_code(s, REP_3_6, s.bl_tree);\n      send_bits(s, count - 3, 2);\n\n    } else if (count <= 10) {\n      send_code(s, REPZ_3_10, s.bl_tree);\n      send_bits(s, count - 3, 3);\n\n    } else {\n      send_code(s, REPZ_11_138, s.bl_tree);\n      send_bits(s, count - 11, 7);\n    }\n\n    count = 0;\n    prevlen = curlen;\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Construct the Huffman tree for the bit lengths and return the index in\n * bl_order of the last bit length code to send.\n */\nfunction build_bl_tree(s) {\n  var max_blindex;  /* index of last bit length code of non zero freq */\n\n  /* Determine the bit length frequencies for literal and distance trees */\n  scan_tree(s, s.dyn_ltree, s.l_desc.max_code);\n  scan_tree(s, s.dyn_dtree, s.d_desc.max_code);\n\n  /* Build the bit length tree: */\n  build_tree(s, s.bl_desc);\n  /* opt_len now includes the length of the tree representations, except\n   * the lengths of the bit lengths codes and the 5+5+4 bits for the counts.\n   */\n\n  /* Determine the number of bit length codes to send. The pkzip format\n   * requires that at least 4 bit length codes be sent. (appnote.txt says\n   * 3 but the actual value used is 4.)\n   */\n  for (max_blindex = BL_CODES - 1; max_blindex >= 3; max_blindex--) {\n    if (s.bl_tree[bl_order[max_blindex] * 2 + 1]/*.Len*/ !== 0) {\n      break;\n    }\n  }\n  /* Update opt_len to include the bit length tree and counts */\n  s.opt_len += 3 * (max_blindex + 1) + 5 + 5 + 4;\n  //Tracev((stderr, \"\\ndyn trees: dyn %ld, stat %ld\",\n  //        s->opt_len, s->static_len));\n\n  return max_blindex;\n}\n\n\n/* ===========================================================================\n * Send the header for a block using dynamic Huffman trees: the counts, the\n * lengths of the bit length codes, the literal tree and the distance tree.\n * IN assertion: lcodes >= 257, dcodes >= 1, blcodes >= 4.\n */\nfunction send_all_trees(s, lcodes, dcodes, blcodes)\n//    deflate_state *s;\n//    int lcodes, dcodes, blcodes; /* number of codes for each tree */\n{\n  var rank;                    /* index in bl_order */\n\n  //Assert (lcodes >= 257 && dcodes >= 1 && blcodes >= 4, \"not enough codes\");\n  //Assert (lcodes <= L_CODES && dcodes <= D_CODES && blcodes <= BL_CODES,\n  //        \"too many codes\");\n  //Tracev((stderr, \"\\nbl counts: \"));\n  send_bits(s, lcodes - 257, 5); /* not +255 as stated in appnote.txt */\n  send_bits(s, dcodes - 1,   5);\n  send_bits(s, blcodes - 4,  4); /* not -3 as stated in appnote.txt */\n  for (rank = 0; rank < blcodes; rank++) {\n    //Tracev((stderr, \"\\nbl code %2d \", bl_order[rank]));\n    send_bits(s, s.bl_tree[bl_order[rank] * 2 + 1]/*.Len*/, 3);\n  }\n  //Tracev((stderr, \"\\nbl tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_ltree, lcodes - 1); /* literal tree */\n  //Tracev((stderr, \"\\nlit tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_dtree, dcodes - 1); /* distance tree */\n  //Tracev((stderr, \"\\ndist tree: sent %ld\", s->bits_sent));\n}\n\n\n/* ===========================================================================\n * Check if the data type is TEXT or BINARY, using the following algorithm:\n * - TEXT if the two conditions below are satisfied:\n *    a) There are no non-portable control characters belonging to the\n *       \"black list\" (0..6, 14..25, 28..31).\n *    b) There is at least one printable character belonging to the\n *       \"white list\" (9 {TAB}, 10 {LF}, 13 {CR}, 32..255).\n * - BINARY otherwise.\n * - The following partially-portable control characters form a\n *   \"gray list\" that is ignored in this detection algorithm:\n *   (7 {BEL}, 8 {BS}, 11 {VT}, 12 {FF}, 26 {SUB}, 27 {ESC}).\n * IN assertion: the fields Freq of dyn_ltree are set.\n */\nfunction detect_data_type(s) {\n  /* black_mask is the bit mask of black-listed bytes\n   * set bits 0..6, 14..25, and 28..31\n   * 0xf3ffc07f = binary 11110011111111111100000001111111\n   */\n  var black_mask = 0xf3ffc07f;\n  var n;\n\n  /* Check for non-textual (\"black-listed\") bytes. */\n  for (n = 0; n <= 31; n++, black_mask >>>= 1) {\n    if ((black_mask & 1) && (s.dyn_ltree[n * 2]/*.Freq*/ !== 0)) {\n      return Z_BINARY;\n    }\n  }\n\n  /* Check for textual (\"white-listed\") bytes. */\n  if (s.dyn_ltree[9 * 2]/*.Freq*/ !== 0 || s.dyn_ltree[10 * 2]/*.Freq*/ !== 0 ||\n      s.dyn_ltree[13 * 2]/*.Freq*/ !== 0) {\n    return Z_TEXT;\n  }\n  for (n = 32; n < LITERALS; n++) {\n    if (s.dyn_ltree[n * 2]/*.Freq*/ !== 0) {\n      return Z_TEXT;\n    }\n  }\n\n  /* There are no \"black-listed\" or \"white-listed\" bytes:\n   * this stream either is empty or has tolerated (\"gray-listed\") bytes only.\n   */\n  return Z_BINARY;\n}\n\n\nvar static_init_done = false;\n\n/* ===========================================================================\n * Initialize the tree data structures for a new zlib stream.\n */\nfunction _tr_init(s)\n{\n\n  if (!static_init_done) {\n    tr_static_init();\n    static_init_done = true;\n  }\n\n  s.l_desc  = new TreeDesc(s.dyn_ltree, static_l_desc);\n  s.d_desc  = new TreeDesc(s.dyn_dtree, static_d_desc);\n  s.bl_desc = new TreeDesc(s.bl_tree, static_bl_desc);\n\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n\n  /* Initialize the first block of the first file: */\n  init_block(s);\n}\n\n\n/* ===========================================================================\n * Send a stored block\n */\nfunction _tr_stored_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  send_bits(s, (STORED_BLOCK << 1) + (last ? 1 : 0), 3);    /* send block type */\n  copy_block(s, buf, stored_len, true); /* with header */\n}\n\n\n/* ===========================================================================\n * Send one empty static block to give enough lookahead for inflate.\n * This takes 10 bits, of which 7 may remain in the bit buffer.\n */\nfunction _tr_align(s) {\n  send_bits(s, STATIC_TREES << 1, 3);\n  send_code(s, END_BLOCK, static_ltree);\n  bi_flush(s);\n}\n\n\n/* ===========================================================================\n * Determine the best encoding for the current block: dynamic trees, static\n * trees or store, and output the encoded block to the zip file.\n */\nfunction _tr_flush_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block, or NULL if too old */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  var opt_lenb, static_lenb;  /* opt_len and static_len in bytes */\n  var max_blindex = 0;        /* index of last bit length code of non zero freq */\n\n  /* Build the Huffman trees unless a stored block is forced */\n  if (s.level > 0) {\n\n    /* Check if the file is binary or text */\n    if (s.strm.data_type === Z_UNKNOWN) {\n      s.strm.data_type = detect_data_type(s);\n    }\n\n    /* Construct the literal and distance trees */\n    build_tree(s, s.l_desc);\n    // Tracev((stderr, \"\\nlit data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n\n    build_tree(s, s.d_desc);\n    // Tracev((stderr, \"\\ndist data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n    /* At this point, opt_len and static_len are the total bit lengths of\n     * the compressed block data, excluding the tree representations.\n     */\n\n    /* Build the bit length tree for the above two trees, and get the index\n     * in bl_order of the last bit length code to send.\n     */\n    max_blindex = build_bl_tree(s);\n\n    /* Determine the best encoding. Compute the block lengths in bytes. */\n    opt_lenb = (s.opt_len + 3 + 7) >>> 3;\n    static_lenb = (s.static_len + 3 + 7) >>> 3;\n\n    // Tracev((stderr, \"\\nopt %lu(%lu) stat %lu(%lu) stored %lu lit %u \",\n    //        opt_lenb, s->opt_len, static_lenb, s->static_len, stored_len,\n    //        s->last_lit));\n\n    if (static_lenb <= opt_lenb) { opt_lenb = static_lenb; }\n\n  } else {\n    // Assert(buf != (char*)0, \"lost buf\");\n    opt_lenb = static_lenb = stored_len + 5; /* force a stored block */\n  }\n\n  if ((stored_len + 4 <= opt_lenb) && (buf !== -1)) {\n    /* 4: two words for the lengths */\n\n    /* The test buf != NULL is only necessary if LIT_BUFSIZE > WSIZE.\n     * Otherwise we can't have processed more than WSIZE input bytes since\n     * the last block flush, because compression would have been\n     * successful. If LIT_BUFSIZE <= WSIZE, it is never too late to\n     * transform a block into a stored block.\n     */\n    _tr_stored_block(s, buf, stored_len, last);\n\n  } else if (s.strategy === Z_FIXED || static_lenb === opt_lenb) {\n\n    send_bits(s, (STATIC_TREES << 1) + (last ? 1 : 0), 3);\n    compress_block(s, static_ltree, static_dtree);\n\n  } else {\n    send_bits(s, (DYN_TREES << 1) + (last ? 1 : 0), 3);\n    send_all_trees(s, s.l_desc.max_code + 1, s.d_desc.max_code + 1, max_blindex + 1);\n    compress_block(s, s.dyn_ltree, s.dyn_dtree);\n  }\n  // Assert (s->compressed_len == s->bits_sent, \"bad compressed size\");\n  /* The above check is made mod 2^32, for files larger than 512 MB\n   * and uLong implemented on 32 bits.\n   */\n  init_block(s);\n\n  if (last) {\n    bi_windup(s);\n  }\n  // Tracev((stderr,\"\\ncomprlen %lu(%lu) \", s->compressed_len>>3,\n  //       s->compressed_len-7*last));\n}\n\n/* ===========================================================================\n * Save the match info and tally the frequency counts. Return true if\n * the current block must be flushed.\n */\nfunction _tr_tally(s, dist, lc)\n//    deflate_state *s;\n//    unsigned dist;  /* distance of matched string */\n//    unsigned lc;    /* match length-MIN_MATCH or unmatched char (if dist==0) */\n{\n  //var out_length, in_length, dcode;\n\n  s.pending_buf[s.d_buf + s.last_lit * 2]     = (dist >>> 8) & 0xff;\n  s.pending_buf[s.d_buf + s.last_lit * 2 + 1] = dist & 0xff;\n\n  s.pending_buf[s.l_buf + s.last_lit] = lc & 0xff;\n  s.last_lit++;\n\n  if (dist === 0) {\n    /* lc is the unmatched char */\n    s.dyn_ltree[lc * 2]/*.Freq*/++;\n  } else {\n    s.matches++;\n    /* Here, lc is the match length - MIN_MATCH */\n    dist--;             /* dist = match distance - 1 */\n    //Assert((ush)dist < (ush)MAX_DIST(s) &&\n    //       (ush)lc <= (ush)(MAX_MATCH-MIN_MATCH) &&\n    //       (ush)d_code(dist) < (ush)D_CODES,  \"_tr_tally: bad match\");\n\n    s.dyn_ltree[(_length_code[lc] + LITERALS + 1) * 2]/*.Freq*/++;\n    s.dyn_dtree[d_code(dist) * 2]/*.Freq*/++;\n  }\n\n// (!) This block is disabled in zlib defaults,\n// don't enable it for binary compatibility\n\n//#ifdef TRUNCATE_BLOCK\n//  /* Try to guess if it is profitable to stop the current block here */\n//  if ((s.last_lit & 0x1fff) === 0 && s.level > 2) {\n//    /* Compute an upper bound for the compressed length */\n//    out_length = s.last_lit*8;\n//    in_length = s.strstart - s.block_start;\n//\n//    for (dcode = 0; dcode < D_CODES; dcode++) {\n//      out_length += s.dyn_dtree[dcode*2]/*.Freq*/ * (5 + extra_dbits[dcode]);\n//    }\n//    out_length >>>= 3;\n//    //Tracev((stderr,\"\\nlast_lit %u, in %ld, out ~%ld(%ld%%) \",\n//    //       s->last_lit, in_length, out_length,\n//    //       100L - out_length*100L/in_length));\n//    if (s.matches < (s.last_lit>>1)/*int /2*/ && out_length < (in_length>>1)/*int /2*/) {\n//      return true;\n//    }\n//  }\n//#endif\n\n  return (s.last_lit === s.lit_bufsize - 1);\n  /* We avoid equality with lit_bufsize because of wraparound at 64K\n   * on 16 bit machines and because stored blocks are restricted to\n   * 64K-1 bytes.\n   */\n}\n\nexports._tr_init  = _tr_init;\nexports._tr_stored_block = _tr_stored_block;\nexports._tr_flush_block  = _tr_flush_block;\nexports._tr_tally = _tr_tally;\nexports._tr_align = _tr_align;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA,IAAIA,KAAK,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAEtC;AACA;;AAGA;AACA;AACA;AACA,IAAIC,OAAO,GAAiB,CAAC;AAC7B;;AAEA;AACA,IAAIC,QAAQ,GAAgB,CAAC;AAC7B,IAAIC,MAAM,GAAkB,CAAC;AAC7B;AACA,IAAIC,SAAS,GAAe,CAAC;;AAE7B;;AAGA,SAASC,IAAIA,CAACC,GAAG,EAAE;EAAE,IAAIC,GAAG,GAAGD,GAAG,CAACE,MAAM;EAAE,OAAO,EAAED,GAAG,IAAI,CAAC,EAAE;IAAED,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC;EAAE;AAAE;;AAEhF;;AAEA,IAAIE,YAAY,GAAG,CAAC;AACpB,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,SAAS,GAAM,CAAC;AACpB;;AAEA,IAAIC,SAAS,GAAM,CAAC;AACpB,IAAIC,SAAS,GAAM,GAAG;AACtB;;AAEA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAI,EAAE;AACtB;;AAEA,IAAIC,QAAQ,GAAQ,GAAG;AACvB;;AAEA,IAAIC,OAAO,GAASD,QAAQ,GAAG,CAAC,GAAGD,YAAY;AAC/C;;AAEA,IAAIG,OAAO,GAAS,EAAE;AACtB;;AAEA,IAAIC,QAAQ,GAAQ,EAAE;AACtB;;AAEA,IAAIC,SAAS,GAAO,CAAC,GAAGH,OAAO,GAAG,CAAC;AACnC;;AAEA,IAAII,QAAQ,GAAQ,EAAE;AACtB;;AAEA,IAAIC,QAAQ,GAAQ,EAAE;AACtB;;AAGA;AACA;AACA;;AAEA,IAAIC,WAAW,GAAG,CAAC;AACnB;;AAEA,IAAIC,SAAS,GAAK,GAAG;AACrB;;AAEA,IAAIC,OAAO,GAAO,EAAE;AACpB;;AAEA,IAAIC,SAAS,GAAK,EAAE;AACpB;;AAEA,IAAIC,WAAW,GAAG,EAAE;AACpB;;AAEA;AACA,IAAIC,WAAW,GAAK;AAClB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;AAE7D,IAAIC,WAAW,GAAK;AAClB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC;AAEvE,IAAIC,YAAY,GAAI;AAClB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;AAEzC,IAAIC,QAAQ,GACV,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,CAAC;AAClD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,IAAIC,aAAa,GAAG,GAAG,CAAC,CAAC;;AAEzB;AACA,IAAIC,YAAY,GAAI,IAAIC,KAAK,CAAC,CAACjB,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;AAChDX,IAAI,CAAC2B,YAAY,CAAC;AAClB;AACA;AACA;AACA;AACA;;AAEA,IAAIE,YAAY,GAAI,IAAID,KAAK,CAAChB,OAAO,GAAG,CAAC,CAAC;AAC1CZ,IAAI,CAAC6B,YAAY,CAAC;AAClB;AACA;AACA;;AAEA,IAAIC,UAAU,GAAM,IAAIF,KAAK,CAACF,aAAa,CAAC;AAC5C1B,IAAI,CAAC8B,UAAU,CAAC;AAChB;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAI,IAAIH,KAAK,CAACpB,SAAS,GAAGD,SAAS,GAAG,CAAC,CAAC;AACxDP,IAAI,CAAC+B,YAAY,CAAC;AAClB;;AAEA,IAAIC,WAAW,GAAK,IAAIJ,KAAK,CAACnB,YAAY,CAAC;AAC3CT,IAAI,CAACgC,WAAW,CAAC;AACjB;;AAEA,IAAIC,SAAS,GAAO,IAAIL,KAAK,CAAChB,OAAO,CAAC;AACtCZ,IAAI,CAACiC,SAAS,CAAC;AACf;;AAGA,SAASC,cAAcA,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAE9E,IAAI,CAACJ,WAAW,GAAIA,WAAW,CAAC,CAAE;EAClC,IAAI,CAACC,UAAU,GAAKA,UAAU,CAAC,CAAG;EAClC,IAAI,CAACC,UAAU,GAAKA,UAAU,CAAC,CAAG;EAClC,IAAI,CAACC,KAAK,GAAUA,KAAK,CAAC,CAAQ;EAClC,IAAI,CAACC,UAAU,GAAKA,UAAU,CAAC,CAAG;;EAElC;EACA,IAAI,CAACC,SAAS,GAAML,WAAW,IAAIA,WAAW,CAAChC,MAAM;AACvD;AAGA,IAAIsC,aAAa;AACjB,IAAIC,aAAa;AACjB,IAAIC,cAAc;AAGlB,SAASC,QAAQA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACrC,IAAI,CAACD,QAAQ,GAAGA,QAAQ,CAAC,CAAK;EAC9B,IAAI,CAACE,QAAQ,GAAG,CAAC,CAAC,CAAY;EAC9B,IAAI,CAACD,SAAS,GAAGA,SAAS,CAAC,CAAG;AAChC;AAIA,SAASE,MAAMA,CAACC,IAAI,EAAE;EACpB,OAAOA,IAAI,GAAG,GAAG,GAAGnB,UAAU,CAACmB,IAAI,CAAC,GAAGnB,UAAU,CAAC,GAAG,IAAImB,IAAI,KAAK,CAAC,CAAC,CAAC;AACvE;;AAGA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB;EACA;EACED,CAAC,CAACE,WAAW,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,GAAIF,CAAC,GAAI,IAAI;EACvCD,CAAC,CAACE,WAAW,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,GAAIF,CAAC,KAAK,CAAC,GAAI,IAAI;AAC/C;;AAGA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAACJ,CAAC,EAAEK,KAAK,EAAErD,MAAM,EAAE;EACnC,IAAIgD,CAAC,CAACM,QAAQ,GAAIzC,QAAQ,GAAGb,MAAO,EAAE;IACpCgD,CAAC,CAACO,MAAM,IAAKF,KAAK,IAAIL,CAAC,CAACM,QAAQ,GAAI,MAAM;IAC1CP,SAAS,CAACC,CAAC,EAAEA,CAAC,CAACO,MAAM,CAAC;IACtBP,CAAC,CAACO,MAAM,GAAGF,KAAK,IAAKxC,QAAQ,GAAGmC,CAAC,CAACM,QAAS;IAC3CN,CAAC,CAACM,QAAQ,IAAItD,MAAM,GAAGa,QAAQ;EACjC,CAAC,MAAM;IACLmC,CAAC,CAACO,MAAM,IAAKF,KAAK,IAAIL,CAAC,CAACM,QAAQ,GAAI,MAAM;IAC1CN,CAAC,CAACM,QAAQ,IAAItD,MAAM;EACtB;AACF;AAGA,SAASwD,SAASA,CAACR,CAAC,EAAES,CAAC,EAAEC,IAAI,EAAE;EAC7BN,SAAS,CAACJ,CAAC,EAAEU,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,YAAWC,IAAI,CAACD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAQ,CAAC;AAC7D;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,IAAI,EAAE7D,GAAG,EAAE;EAC7B,IAAI8D,GAAG,GAAG,CAAC;EACX,GAAG;IACDA,GAAG,IAAID,IAAI,GAAG,CAAC;IACfA,IAAI,MAAM,CAAC;IACXC,GAAG,KAAK,CAAC;EACX,CAAC,QAAQ,EAAE9D,GAAG,GAAG,CAAC;EAClB,OAAO8D,GAAG,KAAK,CAAC;AAClB;;AAGA;AACA;AACA;AACA,SAASC,QAAQA,CAACd,CAAC,EAAE;EACnB,IAAIA,CAAC,CAACM,QAAQ,KAAK,EAAE,EAAE;IACrBP,SAAS,CAACC,CAAC,EAAEA,CAAC,CAACO,MAAM,CAAC;IACtBP,CAAC,CAACO,MAAM,GAAG,CAAC;IACZP,CAAC,CAACM,QAAQ,GAAG,CAAC;EAEhB,CAAC,MAAM,IAAIN,CAAC,CAACM,QAAQ,IAAI,CAAC,EAAE;IAC1BN,CAAC,CAACE,WAAW,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,GAAGH,CAAC,CAACO,MAAM,GAAG,IAAI;IAC5CP,CAAC,CAACO,MAAM,KAAK,CAAC;IACdP,CAAC,CAACM,QAAQ,IAAI,CAAC;EACjB;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,UAAUA,CAACf,CAAC,EAAEgB,IAAI;AAC3B;AACA;AACA;EACE,IAAIN,IAAI,GAAcM,IAAI,CAACtB,QAAQ;EACnC,IAAIE,QAAQ,GAAUoB,IAAI,CAACpB,QAAQ;EACnC,IAAIqB,KAAK,GAAaD,IAAI,CAACrB,SAAS,CAACX,WAAW;EAChD,IAAIK,SAAS,GAAS2B,IAAI,CAACrB,SAAS,CAACN,SAAS;EAC9C,IAAI6B,KAAK,GAAaF,IAAI,CAACrB,SAAS,CAACV,UAAU;EAC/C,IAAIkC,IAAI,GAAcH,IAAI,CAACrB,SAAS,CAACT,UAAU;EAC/C,IAAIE,UAAU,GAAQ4B,IAAI,CAACrB,SAAS,CAACP,UAAU;EAC/C,IAAIgC,CAAC,CAAC,CAAc;EACpB,IAAIC,CAAC,EAAEC,CAAC,CAAC,CAAW;EACpB,IAAIC,IAAI,CAAC,CAAW;EACpB,IAAIC,KAAK,CAAC,CAAU;EACpB,IAAIC,CAAC,CAAC,CAAc;EACpB,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAG;;EAEpB,KAAKH,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAI3D,QAAQ,EAAE2D,IAAI,EAAE,EAAE;IACvCvB,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,CAAC,GAAG,CAAC;EACtB;;EAEA;AACF;AACA;EACEb,IAAI,CAACV,CAAC,CAAC4B,IAAI,CAAC5B,CAAC,CAAC6B,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC,CAAC,CAAC;;EAE9C,KAAKT,CAAC,GAAGpB,CAAC,CAAC6B,QAAQ,GAAG,CAAC,EAAET,CAAC,GAAGzD,SAAS,EAAEyD,CAAC,EAAE,EAAE;IAC3CC,CAAC,GAAGrB,CAAC,CAAC4B,IAAI,CAACR,CAAC,CAAC;IACbG,IAAI,GAAGb,IAAI,CAACA,IAAI,CAACW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACxD,IAAIE,IAAI,GAAGnC,UAAU,EAAE;MACrBmC,IAAI,GAAGnC,UAAU;MACjBsC,QAAQ,EAAE;IACZ;IACAhB,IAAI,CAACW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAWE,IAAI;IAC9B;;IAEA,IAAIF,CAAC,GAAGzB,QAAQ,EAAE;MAAE;IAAU,CAAC,CAAC;;IAEhCI,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,CAAC,EAAE;IAClBC,KAAK,GAAG,CAAC;IACT,IAAIH,CAAC,IAAIF,IAAI,EAAE;MACbK,KAAK,GAAGN,KAAK,CAACG,CAAC,GAAGF,IAAI,CAAC;IACzB;IACAM,CAAC,GAAGf,IAAI,CAACW,CAAC,GAAG,CAAC,CAAC;IACfrB,CAAC,CAAC8B,OAAO,IAAIL,CAAC,IAAIF,IAAI,GAAGC,KAAK,CAAC;IAC/B,IAAInC,SAAS,EAAE;MACbW,CAAC,CAAC+B,UAAU,IAAIN,CAAC,IAAIR,KAAK,CAACI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAWG,KAAK,CAAC;IACxD;EACF;EACA,IAAIE,QAAQ,KAAK,CAAC,EAAE;IAAE;EAAQ;;EAE9B;EACA;;EAEA;EACA,GAAG;IACDH,IAAI,GAAGnC,UAAU,GAAG,CAAC;IACrB,OAAOY,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,CAAC,KAAK,CAAC,EAAE;MAAEA,IAAI,EAAE;IAAE;IACzCvB,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,CAAC,EAAE,CAAC,CAAM;IACzBvB,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3BvB,CAAC,CAAC2B,QAAQ,CAACvC,UAAU,CAAC,EAAE;IACxB;AACJ;AACA;IACIsC,QAAQ,IAAI,CAAC;EACf,CAAC,QAAQA,QAAQ,GAAG,CAAC;;EAErB;AACF;AACA;AACA;AACA;EACE,KAAKH,IAAI,GAAGnC,UAAU,EAAEmC,IAAI,KAAK,CAAC,EAAEA,IAAI,EAAE,EAAE;IAC1CF,CAAC,GAAGrB,CAAC,CAAC2B,QAAQ,CAACJ,IAAI,CAAC;IACpB,OAAOF,CAAC,KAAK,CAAC,EAAE;MACdC,CAAC,GAAGtB,CAAC,CAAC4B,IAAI,CAAC,EAAER,CAAC,CAAC;MACf,IAAIE,CAAC,GAAG1B,QAAQ,EAAE;QAAE;MAAU;MAC9B,IAAIc,IAAI,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAaC,IAAI,EAAE;QACpC;QACAvB,CAAC,CAAC8B,OAAO,IAAI,CAACP,IAAI,GAAGb,IAAI,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,aAAYZ,IAAI,CAACY,CAAC,GAAG,CAAC,CAAC;QAC3DZ,IAAI,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAWC,IAAI;MAChC;MACAF,CAAC,EAAE;IACL;EACF;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,SAASA,CAACtB,IAAI,EAAEd,QAAQ,EAAE+B,QAAQ;AAC3C;AACA;AACA;AACA;EACE,IAAIM,SAAS,GAAG,IAAIxD,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,IAAIgD,IAAI,GAAG,CAAC,CAAC,CAAc;EAC3B,IAAIW,IAAI,CAAC,CAAkB;EAC3B,IAAIF,CAAC,CAAC,CAAqB;;EAE3B;AACF;AACA;EACE,KAAKE,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAI3D,QAAQ,EAAE2D,IAAI,EAAE,EAAE;IACvCU,SAAS,CAACV,IAAI,CAAC,GAAGX,IAAI,GAAIA,IAAI,GAAGe,QAAQ,CAACJ,IAAI,GAAG,CAAC,CAAC,IAAK,CAAC;EAC3D;EACA;AACF;AACA;EACE;EACA;EACA;;EAEA,KAAKF,CAAC,GAAG,CAAC,EAAGA,CAAC,IAAIzB,QAAQ,EAAEyB,CAAC,EAAE,EAAE;IAC/B,IAAItE,GAAG,GAAG2D,IAAI,CAACW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzB,IAAItE,GAAG,KAAK,CAAC,EAAE;MAAE;IAAU;IAC3B;IACA2D,IAAI,CAACW,CAAC,GAAG,CAAC,CAAC,aAAYV,UAAU,CAACsB,SAAS,CAAClF,GAAG,CAAC,EAAE,EAAEA,GAAG,CAAC;;IAExD;IACA;EACF;AACF;;AAGA;AACA;AACA;AACA,SAASmF,cAAcA,CAAA,EAAG;EACxB,IAAIb,CAAC,CAAC,CAAQ;EACd,IAAIE,IAAI,CAAC,CAAK;EACd,IAAIvE,MAAM,CAAC,CAAG;EACd,IAAI4D,IAAI,CAAC,CAAK;EACd,IAAId,IAAI,CAAC,CAAK;EACd,IAAI6B,QAAQ,GAAG,IAAIlD,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAC;EACtC;;EAEA;EACA;;EAEA;EACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;EACAZ,MAAM,GAAG,CAAC;EACV,KAAK4D,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGtD,YAAY,GAAG,CAAC,EAAEsD,IAAI,EAAE,EAAE;IAC9C/B,WAAW,CAAC+B,IAAI,CAAC,GAAG5D,MAAM;IAC1B,KAAKqE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAI,CAAC,IAAIlD,WAAW,CAACyC,IAAI,CAAE,EAAES,CAAC,EAAE,EAAE;MAC7CzC,YAAY,CAAC5B,MAAM,EAAE,CAAC,GAAG4D,IAAI;IAC/B;EACF;EACA;EACA;AACF;AACA;AACA;EACEhC,YAAY,CAAC5B,MAAM,GAAG,CAAC,CAAC,GAAG4D,IAAI;;EAE/B;EACAd,IAAI,GAAG,CAAC;EACR,KAAKc,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,EAAE,EAAEA,IAAI,EAAE,EAAE;IAChC9B,SAAS,CAAC8B,IAAI,CAAC,GAAGd,IAAI;IACtB,KAAKuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAI,CAAC,IAAIjD,WAAW,CAACwC,IAAI,CAAE,EAAES,CAAC,EAAE,EAAE;MAC7C1C,UAAU,CAACmB,IAAI,EAAE,CAAC,GAAGc,IAAI;IAC3B;EACF;EACA;EACAd,IAAI,KAAK,CAAC,CAAC,CAAC;EACZ,OAAOc,IAAI,GAAGnD,OAAO,EAAEmD,IAAI,EAAE,EAAE;IAC7B9B,SAAS,CAAC8B,IAAI,CAAC,GAAGd,IAAI,IAAI,CAAC;IAC3B,KAAKuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAI,CAAC,IAAKjD,WAAW,CAACwC,IAAI,CAAC,GAAG,CAAG,EAAES,CAAC,EAAE,EAAE;MACnD1C,UAAU,CAAC,GAAG,GAAGmB,IAAI,EAAE,CAAC,GAAGc,IAAI;IACjC;EACF;EACA;;EAEA;EACA,KAAKW,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAI3D,QAAQ,EAAE2D,IAAI,EAAE,EAAE;IACvCI,QAAQ,CAACJ,IAAI,CAAC,GAAG,CAAC;EACpB;EAEAF,CAAC,GAAG,CAAC;EACL,OAAOA,CAAC,IAAI,GAAG,EAAE;IACf7C,YAAY,CAAC6C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACnCA,CAAC,EAAE;IACHM,QAAQ,CAAC,CAAC,CAAC,EAAE;EACf;EACA,OAAON,CAAC,IAAI,GAAG,EAAE;IACf7C,YAAY,CAAC6C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACnCA,CAAC,EAAE;IACHM,QAAQ,CAAC,CAAC,CAAC,EAAE;EACf;EACA,OAAON,CAAC,IAAI,GAAG,EAAE;IACf7C,YAAY,CAAC6C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACnCA,CAAC,EAAE;IACHM,QAAQ,CAAC,CAAC,CAAC,EAAE;EACf;EACA,OAAON,CAAC,IAAI,GAAG,EAAE;IACf7C,YAAY,CAAC6C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACnCA,CAAC,EAAE;IACHM,QAAQ,CAAC,CAAC,CAAC,EAAE;EACf;EACA;AACF;AACA;AACA;EACEK,SAAS,CAACxD,YAAY,EAAEhB,OAAO,GAAG,CAAC,EAAEmE,QAAQ,CAAC;;EAE9C;EACA,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,OAAO,EAAE4D,CAAC,EAAE,EAAE;IAC5B3C,YAAY,CAAC2C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IACnC3C,YAAY,CAAC2C,CAAC,GAAG,CAAC,CAAC,aAAYV,UAAU,CAACU,CAAC,EAAE,CAAC,CAAC;EACjD;;EAEA;EACA/B,aAAa,GAAG,IAAIP,cAAc,CAACP,YAAY,EAAEL,WAAW,EAAEZ,QAAQ,GAAG,CAAC,EAAEC,OAAO,EAAEI,QAAQ,CAAC;EAC9F2B,aAAa,GAAG,IAAIR,cAAc,CAACL,YAAY,EAAEN,WAAW,EAAE,CAAC,EAAWX,OAAO,EAAEG,QAAQ,CAAC;EAC5F4B,cAAc,GAAG,IAAIT,cAAc,CAAC,IAAIN,KAAK,CAAC,CAAC,CAAC,EAAEJ,YAAY,EAAE,CAAC,EAAUX,QAAQ,EAAEI,WAAW,CAAC;;EAEjG;AACF;;AAGA;AACA;AACA;AACA,SAASqE,UAAUA,CAACnC,CAAC,EAAE;EACrB,IAAIqB,CAAC,CAAC,CAAC;;EAEP;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,OAAO,EAAG6D,CAAC,EAAE,EAAE;IAAErB,CAAC,CAACoC,SAAS,CAACf,CAAC,GAAG,CAAC,CAAC,aAAY,CAAC;EAAE;EAClE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,OAAO,EAAG4D,CAAC,EAAE,EAAE;IAAErB,CAAC,CAACqC,SAAS,CAAChB,CAAC,GAAG,CAAC,CAAC,aAAY,CAAC;EAAE;EAClE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,QAAQ,EAAE2D,CAAC,EAAE,EAAE;IAAErB,CAAC,CAACsC,OAAO,CAACjB,CAAC,GAAG,CAAC,CAAC,aAAY,CAAC;EAAE;EAEhErB,CAAC,CAACoC,SAAS,CAACrE,SAAS,GAAG,CAAC,CAAC,aAAY,CAAC;EACvCiC,CAAC,CAAC8B,OAAO,GAAG9B,CAAC,CAAC+B,UAAU,GAAG,CAAC;EAC5B/B,CAAC,CAACuC,QAAQ,GAAGvC,CAAC,CAACwC,OAAO,GAAG,CAAC;AAC5B;;AAGA;AACA;AACA;AACA,SAASC,SAASA,CAACzC,CAAC,EACpB;EACE,IAAIA,CAAC,CAACM,QAAQ,GAAG,CAAC,EAAE;IAClBP,SAAS,CAACC,CAAC,EAAEA,CAAC,CAACO,MAAM,CAAC;EACxB,CAAC,MAAM,IAAIP,CAAC,CAACM,QAAQ,GAAG,CAAC,EAAE;IACzB;IACAN,CAAC,CAACE,WAAW,CAACF,CAAC,CAACG,OAAO,EAAE,CAAC,GAAGH,CAAC,CAACO,MAAM;EACvC;EACAP,CAAC,CAACO,MAAM,GAAG,CAAC;EACZP,CAAC,CAACM,QAAQ,GAAG,CAAC;AAChB;;AAEA;AACA;AACA;AACA;AACA,SAASoC,UAAUA,CAAC1C,CAAC,EAAElD,GAAG,EAAEC,GAAG,EAAE4F,MAAM;AACvC;AACA;AACA;AACA;AACA;EACEF,SAAS,CAACzC,CAAC,CAAC,CAAC,CAAQ;;EAErB,IAAI2C,MAAM,EAAE;IACV5C,SAAS,CAACC,CAAC,EAAEjD,GAAG,CAAC;IACjBgD,SAAS,CAACC,CAAC,EAAE,CAACjD,GAAG,CAAC;EACpB;EACF;EACA;EACA;EACER,KAAK,CAACqG,QAAQ,CAAC5C,CAAC,CAACE,WAAW,EAAEF,CAAC,CAAC6C,MAAM,EAAE/F,GAAG,EAAEC,GAAG,EAAEiD,CAAC,CAACG,OAAO,CAAC;EAC5DH,CAAC,CAACG,OAAO,IAAIpD,GAAG;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAAS+F,OAAOA,CAACpC,IAAI,EAAEW,CAAC,EAAEC,CAAC,EAAEyB,KAAK,EAAE;EAClC,IAAIC,GAAG,GAAG3B,CAAC,GAAG,CAAC;EACf,IAAI4B,GAAG,GAAG3B,CAAC,GAAG,CAAC;EACf,OAAQZ,IAAI,CAACsC,GAAG,CAAC,aAAYtC,IAAI,CAACuC,GAAG,CAAC,cAC9BvC,IAAI,CAACsC,GAAG,CAAC,eAActC,IAAI,CAACuC,GAAG,CAAC,cAAaF,KAAK,CAAC1B,CAAC,CAAC,IAAI0B,KAAK,CAACzB,CAAC,CAAE;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,UAAUA,CAAClD,CAAC,EAAEU,IAAI,EAAEyC,CAAC;AAC9B;AACA;AACA;AACA;EACE,IAAIC,CAAC,GAAGpD,CAAC,CAAC4B,IAAI,CAACuB,CAAC,CAAC;EACjB,IAAIE,CAAC,GAAGF,CAAC,IAAI,CAAC,CAAC,CAAE;EACjB,OAAOE,CAAC,IAAIrD,CAAC,CAACsD,QAAQ,EAAE;IACtB;IACA,IAAID,CAAC,GAAGrD,CAAC,CAACsD,QAAQ,IAChBR,OAAO,CAACpC,IAAI,EAAEV,CAAC,CAAC4B,IAAI,CAACyB,CAAC,GAAG,CAAC,CAAC,EAAErD,CAAC,CAAC4B,IAAI,CAACyB,CAAC,CAAC,EAAErD,CAAC,CAAC+C,KAAK,CAAC,EAAE;MAClDM,CAAC,EAAE;IACL;IACA;IACA,IAAIP,OAAO,CAACpC,IAAI,EAAE0C,CAAC,EAAEpD,CAAC,CAAC4B,IAAI,CAACyB,CAAC,CAAC,EAAErD,CAAC,CAAC+C,KAAK,CAAC,EAAE;MAAE;IAAO;;IAEnD;IACA/C,CAAC,CAAC4B,IAAI,CAACuB,CAAC,CAAC,GAAGnD,CAAC,CAAC4B,IAAI,CAACyB,CAAC,CAAC;IACrBF,CAAC,GAAGE,CAAC;;IAEL;IACAA,CAAC,KAAK,CAAC;EACT;EACArD,CAAC,CAAC4B,IAAI,CAACuB,CAAC,CAAC,GAAGC,CAAC;AACf;;AAGA;AACA;;AAEA;AACA;AACA;AACA,SAASG,cAAcA,CAACvD,CAAC,EAAEwD,KAAK,EAAEC,KAAK;AACvC;AACA;AACA;AACA;EACE,IAAI3D,IAAI,CAAC,CAAW;EACpB,IAAI4D,EAAE,CAAC,CAAa;EACpB,IAAIC,EAAE,GAAG,CAAC,CAAC,CAAS;EACpB,IAAI/C,IAAI,CAAC,CAAW;EACpB,IAAIM,KAAK,CAAC,CAAU;;EAEpB,IAAIlB,CAAC,CAACuC,QAAQ,KAAK,CAAC,EAAE;IACpB,GAAG;MACDzC,IAAI,GAAIE,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC4D,KAAK,GAAGD,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAK3D,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC4D,KAAK,GAAGD,EAAE,GAAG,CAAC,GAAG,CAAC,CAAE;MACrFD,EAAE,GAAG1D,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC6D,KAAK,GAAGF,EAAE,CAAC;MAChCA,EAAE,EAAE;MAEJ,IAAI7D,IAAI,KAAK,CAAC,EAAE;QACdU,SAAS,CAACR,CAAC,EAAE0D,EAAE,EAAEF,KAAK,CAAC,CAAC,CAAC;QACzB;MACF,CAAC,MAAM;QACL;QACA5C,IAAI,GAAGhC,YAAY,CAAC8E,EAAE,CAAC;QACvBlD,SAAS,CAACR,CAAC,EAAEY,IAAI,GAAGrD,QAAQ,GAAG,CAAC,EAAEiG,KAAK,CAAC,CAAC,CAAC;QAC1CtC,KAAK,GAAG/C,WAAW,CAACyC,IAAI,CAAC;QACzB,IAAIM,KAAK,KAAK,CAAC,EAAE;UACfwC,EAAE,IAAI7E,WAAW,CAAC+B,IAAI,CAAC;UACvBR,SAAS,CAACJ,CAAC,EAAE0D,EAAE,EAAExC,KAAK,CAAC,CAAC,CAAO;QACjC;QACApB,IAAI,EAAE,CAAC,CAAC;QACRc,IAAI,GAAGf,MAAM,CAACC,IAAI,CAAC;QACnB;;QAEAU,SAAS,CAACR,CAAC,EAAEY,IAAI,EAAE6C,KAAK,CAAC,CAAC,CAAO;QACjCvC,KAAK,GAAG9C,WAAW,CAACwC,IAAI,CAAC;QACzB,IAAIM,KAAK,KAAK,CAAC,EAAE;UACfpB,IAAI,IAAIhB,SAAS,CAAC8B,IAAI,CAAC;UACvBR,SAAS,CAACJ,CAAC,EAAEF,IAAI,EAAEoB,KAAK,CAAC,CAAC,CAAG;QAC/B;MACF,CAAC,CAAC;;MAEF;MACA;MACA;IAEF,CAAC,QAAQyC,EAAE,GAAG3D,CAAC,CAACuC,QAAQ;EAC1B;EAEA/B,SAAS,CAACR,CAAC,EAAEjC,SAAS,EAAEyF,KAAK,CAAC;AAChC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAAC9D,CAAC,EAAEgB,IAAI;AAC3B;AACA;AACA;EACE,IAAIN,IAAI,GAAOM,IAAI,CAACtB,QAAQ;EAC5B,IAAIuB,KAAK,GAAMD,IAAI,CAACrB,SAAS,CAACX,WAAW;EACzC,IAAIK,SAAS,GAAG2B,IAAI,CAACrB,SAAS,CAACN,SAAS;EACxC,IAAIF,KAAK,GAAM6B,IAAI,CAACrB,SAAS,CAACR,KAAK;EACnC,IAAIkC,CAAC,EAAEC,CAAC,CAAC,CAAU;EACnB,IAAI1B,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,IAAImE,IAAI,CAAC,CAAU;;EAEnB;AACF;AACA;AACA;EACE/D,CAAC,CAACsD,QAAQ,GAAG,CAAC;EACdtD,CAAC,CAAC6B,QAAQ,GAAGlE,SAAS;EAEtB,KAAK0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlC,KAAK,EAAEkC,CAAC,EAAE,EAAE;IAC1B,IAAIX,IAAI,CAACW,CAAC,GAAG,CAAC,CAAC,eAAc,CAAC,EAAE;MAC9BrB,CAAC,CAAC4B,IAAI,CAAC,EAAE5B,CAAC,CAACsD,QAAQ,CAAC,GAAG1D,QAAQ,GAAGyB,CAAC;MACnCrB,CAAC,CAAC+C,KAAK,CAAC1B,CAAC,CAAC,GAAG,CAAC;IAEhB,CAAC,MAAM;MACLX,IAAI,CAACW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAW,CAAC;IAC7B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOrB,CAAC,CAACsD,QAAQ,GAAG,CAAC,EAAE;IACrBS,IAAI,GAAG/D,CAAC,CAAC4B,IAAI,CAAC,EAAE5B,CAAC,CAACsD,QAAQ,CAAC,GAAI1D,QAAQ,GAAG,CAAC,GAAG,EAAEA,QAAQ,GAAG,CAAE;IAC7Dc,IAAI,CAACqD,IAAI,GAAG,CAAC,CAAC,aAAY,CAAC;IAC3B/D,CAAC,CAAC+C,KAAK,CAACgB,IAAI,CAAC,GAAG,CAAC;IACjB/D,CAAC,CAAC8B,OAAO,EAAE;IAEX,IAAIzC,SAAS,EAAE;MACbW,CAAC,CAAC+B,UAAU,IAAId,KAAK,CAAC8C,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC;IACA;EACF;EACA/C,IAAI,CAACpB,QAAQ,GAAGA,QAAQ;;EAExB;AACF;AACA;EACE,KAAKyB,CAAC,GAAIrB,CAAC,CAACsD,QAAQ,IAAI,CAAC,WAAW,EAAEjC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAAE6B,UAAU,CAAClD,CAAC,EAAEU,IAAI,EAAEW,CAAC,CAAC;EAAE;;EAE7E;AACF;AACA;EACE0C,IAAI,GAAG5E,KAAK,CAAC,CAAc;EAC3B,GAAG;IACD;IACA;IACAkC,CAAC,GAAGrB,CAAC,CAAC4B,IAAI,CAAC,CAAC,cAAa;IACzB5B,CAAC,CAAC4B,IAAI,CAAC,CAAC,cAAa,GAAG5B,CAAC,CAAC4B,IAAI,CAAC5B,CAAC,CAACsD,QAAQ,EAAE,CAAC;IAC5CJ,UAAU,CAAClD,CAAC,EAAEU,IAAI,EAAE,CAAC,aAAY,CAAC;IAClC;;IAEAY,CAAC,GAAGtB,CAAC,CAAC4B,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC;;IAE3B5B,CAAC,CAAC4B,IAAI,CAAC,EAAE5B,CAAC,CAAC6B,QAAQ,CAAC,GAAGR,CAAC,CAAC,CAAC;IAC1BrB,CAAC,CAAC4B,IAAI,CAAC,EAAE5B,CAAC,CAAC6B,QAAQ,CAAC,GAAGP,CAAC;;IAExB;IACAZ,IAAI,CAACqD,IAAI,GAAG,CAAC,CAAC,aAAYrD,IAAI,CAACW,CAAC,GAAG,CAAC,CAAC,aAAYX,IAAI,CAACY,CAAC,GAAG,CAAC,CAAC;IAC5DtB,CAAC,CAAC+C,KAAK,CAACgB,IAAI,CAAC,GAAG,CAAC/D,CAAC,CAAC+C,KAAK,CAAC1B,CAAC,CAAC,IAAIrB,CAAC,CAAC+C,KAAK,CAACzB,CAAC,CAAC,GAAGtB,CAAC,CAAC+C,KAAK,CAAC1B,CAAC,CAAC,GAAGrB,CAAC,CAAC+C,KAAK,CAACzB,CAAC,CAAC,IAAI,CAAC;IACxEZ,IAAI,CAACW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAWX,IAAI,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAWyC,IAAI;;IAExD;IACA/D,CAAC,CAAC4B,IAAI,CAAC,CAAC,cAAa,GAAGmC,IAAI,EAAE;IAC9Bb,UAAU,CAAClD,CAAC,EAAEU,IAAI,EAAE,CAAC,aAAY,CAAC;EAEpC,CAAC,QAAQV,CAAC,CAACsD,QAAQ,IAAI,CAAC;EAExBtD,CAAC,CAAC4B,IAAI,CAAC,EAAE5B,CAAC,CAAC6B,QAAQ,CAAC,GAAG7B,CAAC,CAAC4B,IAAI,CAAC,CAAC,cAAa;;EAE5C;AACF;AACA;EACEb,UAAU,CAACf,CAAC,EAAEgB,IAAI,CAAC;;EAEnB;EACAgB,SAAS,CAACtB,IAAI,EAAEd,QAAQ,EAAEI,CAAC,CAAC2B,QAAQ,CAAC;AACvC;;AAGA;AACA;AACA;AACA;AACA,SAASqC,SAASA,CAAChE,CAAC,EAAEU,IAAI,EAAEd,QAAQ;AACpC;AACA;AACA;AACA;EACE,IAAIyB,CAAC,CAAC,CAAqB;EAC3B,IAAI4C,OAAO,GAAG,CAAC,CAAC,CAAC,CAAU;EAC3B,IAAIC,MAAM,CAAC,CAAgB;;EAE3B,IAAIC,OAAO,GAAGzD,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAS,CAAC;;EAEvC,IAAI0D,KAAK,GAAG,CAAC,CAAC,CAAa;EAC3B,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAS;EAC3B,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAS;;EAE3B,IAAIH,OAAO,KAAK,CAAC,EAAE;IACjBE,SAAS,GAAG,GAAG;IACfC,SAAS,GAAG,CAAC;EACf;EACA5D,IAAI,CAAC,CAACd,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,YAAW,MAAM,CAAC,CAAC;;EAE/C,KAAKyB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzB,QAAQ,EAAEyB,CAAC,EAAE,EAAE;IAC9B6C,MAAM,GAAGC,OAAO;IAChBA,OAAO,GAAGzD,IAAI,CAAC,CAACW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,EAAE+C,KAAK,GAAGC,SAAS,IAAIH,MAAM,KAAKC,OAAO,EAAE;MAC7C;IAEF,CAAC,MAAM,IAAIC,KAAK,GAAGE,SAAS,EAAE;MAC5BtE,CAAC,CAACsC,OAAO,CAAC4B,MAAM,GAAG,CAAC,CAAC,cAAaE,KAAK;IAEzC,CAAC,MAAM,IAAIF,MAAM,KAAK,CAAC,EAAE;MAEvB,IAAIA,MAAM,KAAKD,OAAO,EAAE;QAAEjE,CAAC,CAACsC,OAAO,CAAC4B,MAAM,GAAG,CAAC,CAAC,YAAW;MAAE;MAC5DlE,CAAC,CAACsC,OAAO,CAACtE,OAAO,GAAG,CAAC,CAAC,YAAW;IAEnC,CAAC,MAAM,IAAIoG,KAAK,IAAI,EAAE,EAAE;MACtBpE,CAAC,CAACsC,OAAO,CAACrE,SAAS,GAAG,CAAC,CAAC,YAAW;IAErC,CAAC,MAAM;MACL+B,CAAC,CAACsC,OAAO,CAACpE,WAAW,GAAG,CAAC,CAAC,YAAW;IACvC;IAEAkG,KAAK,GAAG,CAAC;IACTH,OAAO,GAAGC,MAAM;IAEhB,IAAIC,OAAO,KAAK,CAAC,EAAE;MACjBE,SAAS,GAAG,GAAG;MACfC,SAAS,GAAG,CAAC;IAEf,CAAC,MAAM,IAAIJ,MAAM,KAAKC,OAAO,EAAE;MAC7BE,SAAS,GAAG,CAAC;MACbC,SAAS,GAAG,CAAC;IAEf,CAAC,MAAM;MACLD,SAAS,GAAG,CAAC;MACbC,SAAS,GAAG,CAAC;IACf;EACF;AACF;;AAGA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACvE,CAAC,EAAEU,IAAI,EAAEd,QAAQ;AACpC;AACA;AACA;AACA;EACE,IAAIyB,CAAC,CAAC,CAAqB;EAC3B,IAAI4C,OAAO,GAAG,CAAC,CAAC,CAAC,CAAU;EAC3B,IAAIC,MAAM,CAAC,CAAgB;;EAE3B,IAAIC,OAAO,GAAGzD,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAS,CAAC;;EAEvC,IAAI0D,KAAK,GAAG,CAAC,CAAC,CAAa;EAC3B,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAS;EAC3B,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAS;;EAE3B,iCAAkC;EAClC,IAAIH,OAAO,KAAK,CAAC,EAAE;IACjBE,SAAS,GAAG,GAAG;IACfC,SAAS,GAAG,CAAC;EACf;EAEA,KAAKjD,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzB,QAAQ,EAAEyB,CAAC,EAAE,EAAE;IAC9B6C,MAAM,GAAGC,OAAO;IAChBA,OAAO,GAAGzD,IAAI,CAAC,CAACW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,EAAE+C,KAAK,GAAGC,SAAS,IAAIH,MAAM,KAAKC,OAAO,EAAE;MAC7C;IAEF,CAAC,MAAM,IAAIC,KAAK,GAAGE,SAAS,EAAE;MAC5B,GAAG;QAAE9D,SAAS,CAACR,CAAC,EAAEkE,MAAM,EAAElE,CAAC,CAACsC,OAAO,CAAC;MAAE,CAAC,QAAQ,EAAE8B,KAAK,KAAK,CAAC;IAE9D,CAAC,MAAM,IAAIF,MAAM,KAAK,CAAC,EAAE;MACvB,IAAIA,MAAM,KAAKD,OAAO,EAAE;QACtBzD,SAAS,CAACR,CAAC,EAAEkE,MAAM,EAAElE,CAAC,CAACsC,OAAO,CAAC;QAC/B8B,KAAK,EAAE;MACT;MACA;MACA5D,SAAS,CAACR,CAAC,EAAEhC,OAAO,EAAEgC,CAAC,CAACsC,OAAO,CAAC;MAChClC,SAAS,CAACJ,CAAC,EAAEoE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAE5B,CAAC,MAAM,IAAIA,KAAK,IAAI,EAAE,EAAE;MACtB5D,SAAS,CAACR,CAAC,EAAE/B,SAAS,EAAE+B,CAAC,CAACsC,OAAO,CAAC;MAClClC,SAAS,CAACJ,CAAC,EAAEoE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAE5B,CAAC,MAAM;MACL5D,SAAS,CAACR,CAAC,EAAE9B,WAAW,EAAE8B,CAAC,CAACsC,OAAO,CAAC;MACpClC,SAAS,CAACJ,CAAC,EAAEoE,KAAK,GAAG,EAAE,EAAE,CAAC,CAAC;IAC7B;IAEAA,KAAK,GAAG,CAAC;IACTH,OAAO,GAAGC,MAAM;IAChB,IAAIC,OAAO,KAAK,CAAC,EAAE;MACjBE,SAAS,GAAG,GAAG;MACfC,SAAS,GAAG,CAAC;IAEf,CAAC,MAAM,IAAIJ,MAAM,KAAKC,OAAO,EAAE;MAC7BE,SAAS,GAAG,CAAC;MACbC,SAAS,GAAG,CAAC;IAEf,CAAC,MAAM;MACLD,SAAS,GAAG,CAAC;MACbC,SAAS,GAAG,CAAC;IACf;EACF;AACF;;AAGA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACxE,CAAC,EAAE;EACxB,IAAIyE,WAAW,CAAC,CAAE;;EAElB;EACAT,SAAS,CAAChE,CAAC,EAAEA,CAAC,CAACoC,SAAS,EAAEpC,CAAC,CAAC0E,MAAM,CAAC9E,QAAQ,CAAC;EAC5CoE,SAAS,CAAChE,CAAC,EAAEA,CAAC,CAACqC,SAAS,EAAErC,CAAC,CAAC2E,MAAM,CAAC/E,QAAQ,CAAC;;EAE5C;EACAkE,UAAU,CAAC9D,CAAC,EAAEA,CAAC,CAAC4E,OAAO,CAAC;EACxB;AACF;AACA;;EAEE;AACF;AACA;AACA;EACE,KAAKH,WAAW,GAAG/G,QAAQ,GAAG,CAAC,EAAE+G,WAAW,IAAI,CAAC,EAAEA,WAAW,EAAE,EAAE;IAChE,IAAIzE,CAAC,CAACsC,OAAO,CAAChE,QAAQ,CAACmG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAa,CAAC,EAAE;MAC1D;IACF;EACF;EACA;EACAzE,CAAC,CAAC8B,OAAO,IAAI,CAAC,IAAI2C,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC9C;EACA;;EAEA,OAAOA,WAAW;AACpB;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAAC7E,CAAC,EAAE8E,MAAM,EAAEC,MAAM,EAAEC,OAAO;AAClD;AACA;AACA;EACE,IAAIC,IAAI,CAAC,CAAoB;;EAE7B;EACA;EACA;EACA;EACA7E,SAAS,CAACJ,CAAC,EAAE8E,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B1E,SAAS,CAACJ,CAAC,EAAE+E,MAAM,GAAG,CAAC,EAAI,CAAC,CAAC;EAC7B3E,SAAS,CAACJ,CAAC,EAAEgF,OAAO,GAAG,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC;EAC/B,KAAKC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,OAAO,EAAEC,IAAI,EAAE,EAAE;IACrC;IACA7E,SAAS,CAACJ,CAAC,EAAEA,CAAC,CAACsC,OAAO,CAAChE,QAAQ,CAAC2G,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAU,CAAC,CAAC;EAC5D;EACA;;EAEAV,SAAS,CAACvE,CAAC,EAAEA,CAAC,CAACoC,SAAS,EAAE0C,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACvC;;EAEAP,SAAS,CAACvE,CAAC,EAAEA,CAAC,CAACqC,SAAS,EAAE0C,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACvC;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAAClF,CAAC,EAAE;EAC3B;AACF;AACA;AACA;EACE,IAAImF,UAAU,GAAG,UAAU;EAC3B,IAAI9D,CAAC;;EAEL;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE8D,UAAU,MAAM,CAAC,EAAE;IAC3C,IAAKA,UAAU,GAAG,CAAC,IAAMnF,CAAC,CAACoC,SAAS,CAACf,CAAC,GAAG,CAAC,CAAC,eAAc,CAAE,EAAE;MAC3D,OAAO3E,QAAQ;IACjB;EACF;;EAEA;EACA,IAAIsD,CAAC,CAACoC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,eAAc,CAAC,IAAIpC,CAAC,CAACoC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,eAAc,CAAC,IACvEpC,CAAC,CAACoC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,eAAc,CAAC,EAAE;IACtC,OAAOzF,MAAM;EACf;EACA,KAAK0E,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG9D,QAAQ,EAAE8D,CAAC,EAAE,EAAE;IAC9B,IAAIrB,CAAC,CAACoC,SAAS,CAACf,CAAC,GAAG,CAAC,CAAC,eAAc,CAAC,EAAE;MACrC,OAAO1E,MAAM;IACf;EACF;;EAEA;AACF;AACA;EACE,OAAOD,QAAQ;AACjB;AAGA,IAAI0I,gBAAgB,GAAG,KAAK;;AAE5B;AACA;AACA;AACA,SAASC,QAAQA,CAACrF,CAAC,EACnB;EAEE,IAAI,CAACoF,gBAAgB,EAAE;IACrBlD,cAAc,CAAC,CAAC;IAChBkD,gBAAgB,GAAG,IAAI;EACzB;EAEApF,CAAC,CAAC0E,MAAM,GAAI,IAAIjF,QAAQ,CAACO,CAAC,CAACoC,SAAS,EAAE9C,aAAa,CAAC;EACpDU,CAAC,CAAC2E,MAAM,GAAI,IAAIlF,QAAQ,CAACO,CAAC,CAACqC,SAAS,EAAE9C,aAAa,CAAC;EACpDS,CAAC,CAAC4E,OAAO,GAAG,IAAInF,QAAQ,CAACO,CAAC,CAACsC,OAAO,EAAE9C,cAAc,CAAC;EAEnDQ,CAAC,CAACO,MAAM,GAAG,CAAC;EACZP,CAAC,CAACM,QAAQ,GAAG,CAAC;;EAEd;EACA6B,UAAU,CAACnC,CAAC,CAAC;AACf;;AAGA;AACA;AACA;AACA,SAASsF,gBAAgBA,CAACtF,CAAC,EAAElD,GAAG,EAAEyI,UAAU,EAAEC,IAAI;AAClD;AACA;AACA;AACA;AACA;EACEpF,SAAS,CAACJ,CAAC,EAAE,CAAC/C,YAAY,IAAI,CAAC,KAAKuI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAI;EAC1D9C,UAAU,CAAC1C,CAAC,EAAElD,GAAG,EAAEyI,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;AACxC;;AAGA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACzF,CAAC,EAAE;EACpBI,SAAS,CAACJ,CAAC,EAAE9C,YAAY,IAAI,CAAC,EAAE,CAAC,CAAC;EAClCsD,SAAS,CAACR,CAAC,EAAEjC,SAAS,EAAES,YAAY,CAAC;EACrCsC,QAAQ,CAACd,CAAC,CAAC;AACb;;AAGA;AACA;AACA;AACA;AACA,SAAS0F,eAAeA,CAAC1F,CAAC,EAAElD,GAAG,EAAEyI,UAAU,EAAEC,IAAI;AACjD;AACA;AACA;AACA;AACA;EACE,IAAIG,QAAQ,EAAEC,WAAW,CAAC,CAAE;EAC5B,IAAInB,WAAW,GAAG,CAAC,CAAC,CAAQ;;EAE5B;EACA,IAAIzE,CAAC,CAAC6F,KAAK,GAAG,CAAC,EAAE;IAEf;IACA,IAAI7F,CAAC,CAAC8F,IAAI,CAACC,SAAS,KAAKnJ,SAAS,EAAE;MAClCoD,CAAC,CAAC8F,IAAI,CAACC,SAAS,GAAGb,gBAAgB,CAAClF,CAAC,CAAC;IACxC;;IAEA;IACA8D,UAAU,CAAC9D,CAAC,EAAEA,CAAC,CAAC0E,MAAM,CAAC;IACvB;IACA;;IAEAZ,UAAU,CAAC9D,CAAC,EAAEA,CAAC,CAAC2E,MAAM,CAAC;IACvB;IACA;IACA;AACJ;AACA;;IAEI;AACJ;AACA;IACIF,WAAW,GAAGD,aAAa,CAACxE,CAAC,CAAC;;IAE9B;IACA2F,QAAQ,GAAI3F,CAAC,CAAC8B,OAAO,GAAG,CAAC,GAAG,CAAC,KAAM,CAAC;IACpC8D,WAAW,GAAI5F,CAAC,CAAC+B,UAAU,GAAG,CAAC,GAAG,CAAC,KAAM,CAAC;;IAE1C;IACA;IACA;;IAEA,IAAI6D,WAAW,IAAID,QAAQ,EAAE;MAAEA,QAAQ,GAAGC,WAAW;IAAE;EAEzD,CAAC,MAAM;IACL;IACAD,QAAQ,GAAGC,WAAW,GAAGL,UAAU,GAAG,CAAC,CAAC,CAAC;EAC3C;EAEA,IAAKA,UAAU,GAAG,CAAC,IAAII,QAAQ,IAAM7I,GAAG,KAAK,CAAC,CAAE,EAAE;IAChD;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACIwI,gBAAgB,CAACtF,CAAC,EAAElD,GAAG,EAAEyI,UAAU,EAAEC,IAAI,CAAC;EAE5C,CAAC,MAAM,IAAIxF,CAAC,CAACgG,QAAQ,KAAKvJ,OAAO,IAAImJ,WAAW,KAAKD,QAAQ,EAAE;IAE7DvF,SAAS,CAACJ,CAAC,EAAE,CAAC9C,YAAY,IAAI,CAAC,KAAKsI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrDjC,cAAc,CAACvD,CAAC,EAAExB,YAAY,EAAEE,YAAY,CAAC;EAE/C,CAAC,MAAM;IACL0B,SAAS,CAACJ,CAAC,EAAE,CAAC7C,SAAS,IAAI,CAAC,KAAKqI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClDX,cAAc,CAAC7E,CAAC,EAAEA,CAAC,CAAC0E,MAAM,CAAC9E,QAAQ,GAAG,CAAC,EAAEI,CAAC,CAAC2E,MAAM,CAAC/E,QAAQ,GAAG,CAAC,EAAE6E,WAAW,GAAG,CAAC,CAAC;IAChFlB,cAAc,CAACvD,CAAC,EAAEA,CAAC,CAACoC,SAAS,EAAEpC,CAAC,CAACqC,SAAS,CAAC;EAC7C;EACA;EACA;AACF;AACA;EACEF,UAAU,CAACnC,CAAC,CAAC;EAEb,IAAIwF,IAAI,EAAE;IACR/C,SAAS,CAACzC,CAAC,CAAC;EACd;EACA;EACA;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASiG,SAASA,CAACjG,CAAC,EAAEF,IAAI,EAAE4D,EAAE;AAC9B;AACA;AACA;AACA;EACE;;EAEA1D,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC4D,KAAK,GAAG5D,CAAC,CAACuC,QAAQ,GAAG,CAAC,CAAC,GAAQzC,IAAI,KAAK,CAAC,GAAI,IAAI;EACjEE,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC4D,KAAK,GAAG5D,CAAC,CAACuC,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGzC,IAAI,GAAG,IAAI;EAEzDE,CAAC,CAACE,WAAW,CAACF,CAAC,CAAC6D,KAAK,GAAG7D,CAAC,CAACuC,QAAQ,CAAC,GAAGmB,EAAE,GAAG,IAAI;EAC/C1D,CAAC,CAACuC,QAAQ,EAAE;EAEZ,IAAIzC,IAAI,KAAK,CAAC,EAAE;IACd;IACAE,CAAC,CAACoC,SAAS,CAACsB,EAAE,GAAG,CAAC,CAAC,YAAW;EAChC,CAAC,MAAM;IACL1D,CAAC,CAACwC,OAAO,EAAE;IACX;IACA1C,IAAI,EAAE,CAAC,CAAa;IACpB;IACA;IACA;;IAEAE,CAAC,CAACoC,SAAS,CAAC,CAACxD,YAAY,CAAC8E,EAAE,CAAC,GAAGnG,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,YAAW;IAC7DyC,CAAC,CAACqC,SAAS,CAACxC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,YAAW;EAC1C;;EAEF;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEE,OAAQE,CAAC,CAACuC,QAAQ,KAAKvC,CAAC,CAACkG,WAAW,GAAG,CAAC;EACxC;AACF;AACA;AACA;AACA;AAEAC,OAAO,CAACd,QAAQ,GAAIA,QAAQ;AAC5Bc,OAAO,CAACb,gBAAgB,GAAGA,gBAAgB;AAC3Ca,OAAO,CAACT,eAAe,GAAIA,eAAe;AAC1CS,OAAO,CAACF,SAAS,GAAGA,SAAS;AAC7BE,OAAO,CAACV,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}