{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CwfBaseCrud, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\nimport { FormControl, Validators } from '@angular/forms';\nimport { TAS_T_VESSEL } from '@store/TAS/TAS_T_VESSEL';\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\nimport { HttpHeaders, HttpRequest, HttpResponse } from '@angular/common/http';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@service/cwfRestful.service\";\nimport * as i4 from \"@service/cwfuploadService\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ng-zorro-antd/grid\";\nimport * as i9 from \"ng-zorro-antd/form\";\nimport * as i10 from \"ng-zorro-antd/button\";\nimport * as i11 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i12 from \"ng-zorro-antd/core/wave\";\nimport * as i13 from \"ng-zorro-antd/input\";\nimport * as i14 from \"ng-zorro-antd/input-number\";\nimport * as i15 from \"ng-zorro-antd/select\";\nimport * as i16 from \"ng-zorro-antd/card\";\nimport * as i17 from \"ng-zorro-antd/popconfirm\";\nimport * as i18 from \"ng-zorro-antd/table\";\nimport * as i19 from \"ng-zorro-antd/tooltip\";\nimport * as i20 from \"ng-zorro-antd/icon\";\nimport * as i21 from \"ng-zorro-antd/upload\";\nimport * as i22 from \"@layout/components/cms-lookup.component\";\nimport * as i23 from \"@ngx-translate/core\";\nconst _c0 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c1 = () => [8, 4];\nconst _c2 = () => ({});\nconst _c3 = () => ({\n  minRows: 2,\n  maxRows: 4\n});\nconst _c4 = () => ({\n  x: \"1000px\"\n});\nfunction VesselEditComponent_nz_option_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 56);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", item_r2.value)(\"nzLabel\", item_r2.label);\n  }\n}\nfunction VesselEditComponent_nz_option_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 56);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", item_r3.value)(\"nzLabel\", item_r3.label);\n  }\n}\nfunction VesselEditComponent_nz_option_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 56);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", item_r4.value)(\"nzLabel\", item_r4.label);\n  }\n}\nfunction VesselEditComponent_nz_option_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 56);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", item_r5.value)(\"nzLabel\", item_r5.label);\n  }\n}\nfunction VesselEditComponent_nz_option_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 56);\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", item_r6.value)(\"nzLabel\", item_r6.label);\n  }\n}\nfunction VesselEditComponent_tr_181_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 57)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 57)(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 57)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 58)(14, \"a\", 59);\n    i0.ɵɵlistener(\"nzOnConfirm\", function VesselEditComponent_tr_181_Template_a_nzOnConfirm_14_listener() {\n      const data_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onFileDel(data_r8.id));\n    });\n    i0.ɵɵtext(15, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function VesselEditComponent_tr_181_Template_a_click_16_listener() {\n      const data_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.preview(data_r8));\n    });\n    i0.ɵɵtext(17, \"\\u9884\\u89C8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function VesselEditComponent_tr_181_Template_a_click_18_listener() {\n      const data_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onFileDownload(data_r8.attachmentId));\n    });\n    i0.ɵɵtext(19, \"\\u4E0B\\u8F7D\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r8 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r10 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.originalFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.createdUserName || ctx_r8.translate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 8, data_r8.createdTime, \"yyyy-MM-dd HH:mm:ss\"));\n  }\n}\nexport class VesselEditComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, cwfRestfulService, uploadService, http) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.cwfRestfulService = cwfRestfulService;\n    this.uploadService = uploadService;\n    this.http = http;\n    this.mainStore = new TAS_T_VESSEL();\n    this.editStores = [this.mainStore];\n    this.id = \"\";\n    // 定义禁用编辑表单的对象，用于控制表单的禁用状态\n    this.disabledEditForm = {\n      ALL: false\n    };\n    // 数据字典数据\n    this.vesselTypeData = [];\n    this.vesselAttrData = [];\n    this.vesselNatureData = [];\n    this.operationNatureData = [];\n    this.shipLineData = [];\n    // 文件资料相关属性\n    this.fileList = [];\n    this.fileUploading = false;\n    this.fileloading = false;\n    this.fileStore = new BASE_T_PARFILE();\n    this.baseServiceName = '';\n    this.USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\n    this.fileUploadUrl = this.gol.serverUrl + '/' + this.gol.serviceName['tas'].en + '/storage/new/upload'; // 文件上传请求服务地址\n    // ==================== 文件资料相关方法 ====================\n    // 导入文件前\n    this.fileBeforeUpload = file => {\n      console.log('查看id', this.editForm.controls['id'].value);\n      if (this.editForm.controls['id'].value == null || this.editForm.controls['id'].value == '') {\n        this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\n        return false;\n      }\n      this.fileList = [file];\n      console.log(this.fileList[0]);\n      if (this.fileList.length != 1) {\n        this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\n        return false;\n      }\n      // 文件大小验证（限制为50MB）\n      const maxSize = 50 * 1024 * 1024; // 50MB\n      if (file.size > maxSize) {\n        this.showState(ModalTypeEnum.error, '文件大小不能超过50MB');\n        return false;\n      }\n      this.fileUpload();\n      return false;\n    };\n    this.baseServiceName = this.gol.serviceName['tas'].en;\n  }\n  /**\n   * 初始化编辑表单\n   */\n  initEdit() {\n    return {\n      id: new FormControl('', Validators.nullValidator),\n      // 基础字段\n      vesselCd: new FormControl('', [Validators.required, Validators.maxLength(36)]),\n      vesselNm: new FormControl('', [Validators.required, Validators.maxLength(35)]),\n      vesselNmEn: new FormControl('', [Validators.required, Validators.maxLength(105)]),\n      imo: new FormControl('', [Validators.required, Validators.maxLength(18)]),\n      mmsi: new FormControl('', [Validators.maxLength(18)]),\n      callsign: new FormControl('', [Validators.maxLength(18)]),\n      // 下拉选择字段\n      vesselTypeCd: new FormControl('', [Validators.required]),\n      vesselTypeNm: new FormControl(''),\n      vesselTypeNmEn: new FormControl(''),\n      vesselAttrCd: new FormControl(''),\n      vesselAttrNm: new FormControl(''),\n      vesselAttrNmEn: new FormControl(''),\n      vesselNatureCd: new FormControl(''),\n      vesselNatureNm: new FormControl(''),\n      vesselNatureNmEn: new FormControl(''),\n      operationNatureCd: new FormControl(''),\n      operationNatureNm: new FormControl(''),\n      operationNatureNmEn: new FormControl(''),\n      vesselFlagCd: new FormControl(''),\n      vesselFlagNm: new FormControl(''),\n      vesselFlagNmEn: new FormControl(''),\n      // 航线字段\n      shipLineCd: new FormControl(''),\n      shipLineNm: new FormControl(''),\n      shipLineNmEn: new FormControl(''),\n      shipLineClassCd: new FormControl(''),\n      shipLineClassNm: new FormControl(''),\n      shipLineClassNmEn: new FormControl(''),\n      shipLineTypeCd: new FormControl(''),\n      shipLineTypeNm: new FormControl(''),\n      shipLineTypeNmEn: new FormControl(''),\n      // 数字字段\n      cabinNum: new FormControl('', [Validators.pattern(/^\\d*$/)]),\n      tierNum: new FormControl('', [Validators.pattern(/^\\d*$/)]),\n      shipLength: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      shipDepth: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      shipWidth: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      maxDraft: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      dwt: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      cwt: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      bulkCapacity: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      packCapacity: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\n      // 其他字段\n      remark: new FormControl('', [Validators.maxLength(75)]),\n      createdUser: new FormControl('', Validators.nullValidator),\n      createdTime: new FormControl('', Validators.nullValidator),\n      modifiedUser: new FormControl('', Validators.nullValidator),\n      modifiedTime: new FormControl('', Validators.nullValidator),\n      isDelete: new FormControl('', Validators.nullValidator),\n      version: new FormControl('', Validators.nullValidator)\n    };\n  }\n  /**\n   * 页面加载完成后触发\n   */\n  onShow() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // 加载数据字典\n      yield _this.loadDictionaries();\n      if (_this.openParam['state'] === PageModeEnum.Custom) {\n        _this.disabledEditForm['ALL'] = true;\n        _this.cwfRestfulService.get('/vessel/' + _this.openParam['id'], _this.gol.serviceName['tas'].en).then(rps => {\n          if (rps.ok) {\n            _this.editForm.patchValue(rps.data);\n          } else {\n            _this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\n            _this.openMainPage();\n          }\n        });\n      } else if (_this.openParam['state'] === PageModeEnum.Modify) {\n        // 修改模式：根据ID加载数据\n        _this.cwfRestfulService.get('/vessel/' + _this.openParam['id'], _this.gol.serviceName['tas'].en).then(rps => {\n          if (rps.ok) {\n            _this.editForm.patchValue(rps.data);\n            // 加载成功后查询文件列表\n            _this.onQueryData();\n          } else {\n            _this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\n            _this.openMainPage();\n          }\n        });\n      }\n    })();\n  }\n  /**\n   * 加载所有数据字典\n   */\n  loadDictionaries() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield Promise.all([_this2.loadVesselType(), _this2.loadVesselAttr(), _this2.loadVesselNature(), _this2.loadOperationNature(), _this2.loadShipLines()]);\n      } catch (error) {\n        console.error('加载数据字典失败:', error);\n      }\n    })();\n  }\n  /**\n   * 加载船舶类型\n   */\n  loadVesselType() {\n    return new Promise((resolve, reject) => {\n      const rdata = {\n        type: 'system:tas:vesselType'\n      };\n      const requestData = {\n        data: rdata,\n        page: 1,\n        size: 1000\n      };\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en).then(rps => {\n        if (rps.ok) {\n          this.vesselTypeData = rps.data.content.map(item => ({\n            label: item.name,\n            value: item.code,\n            ename: item.englishName\n          }));\n          resolve();\n        } else {\n          reject(rps.msg);\n        }\n      }).catch(reject);\n    });\n  }\n  /**\n   * 加载船舶属性\n   */\n  loadVesselAttr() {\n    return new Promise((resolve, reject) => {\n      const rdata = {\n        type: 'system:tas:vesselAttr'\n      };\n      const requestData = {\n        data: rdata,\n        page: 1,\n        size: 1000\n      };\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en).then(rps => {\n        if (rps.ok) {\n          this.vesselAttrData = rps.data.content.map(item => ({\n            label: item.name,\n            value: item.code,\n            ename: item.englishName\n          }));\n          resolve();\n        } else {\n          reject(rps.msg);\n        }\n      }).catch(reject);\n    });\n  }\n  /**\n   * 加载船舶性质\n   */\n  loadVesselNature() {\n    return new Promise((resolve, reject) => {\n      const rdata = {\n        type: 'system:tas:vesselNature'\n      };\n      const requestData = {\n        data: rdata,\n        page: 1,\n        size: 1000\n      };\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en).then(rps => {\n        if (rps.ok) {\n          this.vesselNatureData = rps.data.content.map(item => ({\n            label: item.name,\n            value: item.code,\n            ename: item.englishName\n          }));\n          resolve();\n        } else {\n          reject(rps.msg);\n        }\n      }).catch(reject);\n    });\n  }\n  /**\n   * 加载营运性质\n   */\n  loadOperationNature() {\n    return new Promise((resolve, reject) => {\n      const rdata = {\n        type: 'system:tas:operationNature'\n      };\n      const requestData = {\n        data: rdata,\n        page: 1,\n        size: 1000\n      };\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en).then(rps => {\n        if (rps.ok) {\n          this.operationNatureData = rps.data.content.map(item => ({\n            label: item.name,\n            value: item.code,\n            ename: item.englishName\n          }));\n          resolve();\n        } else {\n          reject(rps.msg);\n        }\n      }).catch(reject);\n    });\n  }\n  /**\n   * 加载航线数据\n   */\n  loadShipLines() {\n    return new Promise((resolve, reject) => {\n      const requestData = {\n        page: 1,\n        size: 1000\n      };\n      this.cwfRestfulService.post('/shipline/list/page', requestData, this.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          this.shipLineData = rps.data.content.map(item => ({\n            label: item.shipLineNm,\n            value: item.shipLineCd,\n            ...item\n          }));\n          resolve();\n        } else {\n          reject(rps.msg);\n        }\n      }).catch(reject);\n    });\n  }\n  /**\n   * desc:保存数据\n   */\n  saveData() {\n    const url = '/vessel';\n    for (const i in this.editForm.controls) {\n      this.editForm.controls[i].markAsDirty();\n      this.editForm.controls[i].updateValueAndValidity();\n    }\n    if (this.editForm.invalid) {\n      return;\n    }\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\n    this.loading = true;\n    if (this.openParam['state'] === PageModeEnum.Add) {\n      this.editForm.removeControl('id');\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          // 确保在页面跳转前先刷新列表数据\n          this.getMainController()['queryList']();\n          // 延迟跳转，确保数据刷新完成\n          setTimeout(() => {\n            this.openMainPage({});\n          }, 100);\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    } else {\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          // 确保在页面跳转前先刷新列表数据\n          this.getMainController()['queryList']();\n          // 延迟跳转，确保数据刷新完成\n          setTimeout(() => {\n            this.openMainPage({});\n          }, 100);\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    }\n  }\n  onClose() {\n    if (this.checkSave()) {\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\n        switch (state) {\n          case DialogResultEnum.yes:\n            // 是\n            this.saveData();\n            return true;\n          case DialogResultEnum.no:\n            // 否\n            this.openMainPage({});\n            return true;\n          case DialogResultEnum.cancel:\n            // 取消\n            return false;\n        }\n      });\n    } else {\n      this.openMainPage({});\n      return true;\n    }\n  }\n  getEditFromDisabled(feildName) {\n    return this.disabledEditForm[feildName];\n  }\n  /**\n   * 船舶类型选择变化\n   */\n  onVesselTypeChange(value) {\n    const selectedType = this.vesselTypeData.find(item => item.value === value);\n    if (selectedType) {\n      this.editForm.patchValue({\n        vesselTypeCd: selectedType.value,\n        vesselTypeNm: selectedType.label,\n        vesselTypeNmEn: selectedType.ename\n      });\n    }\n  }\n  /**\n   * 船舶属性选择变化\n   */\n  onVesselAttrChange(value) {\n    const selectedAttr = this.vesselAttrData.find(item => item.value === value);\n    if (selectedAttr) {\n      this.editForm.patchValue({\n        vesselAttrCd: selectedAttr.value,\n        vesselAttrNm: selectedAttr.label,\n        vesselAttrNmEn: selectedAttr.ename\n      });\n    }\n  }\n  /**\n   * 船舶性质选择变化\n   */\n  onVesselNatureChange(value) {\n    const selectedNature = this.vesselNatureData.find(item => item.value === value);\n    if (selectedNature) {\n      this.editForm.patchValue({\n        vesselNatureCd: selectedNature.value,\n        vesselNatureNm: selectedNature.label,\n        vesselNatureNmEn: selectedNature.ename\n      });\n    }\n  }\n  /**\n   * 营运性质选择变化\n   */\n  onOperationNatureChange(value) {\n    const selectedOperation = this.operationNatureData.find(item => item.value === value);\n    if (selectedOperation) {\n      this.editForm.patchValue({\n        operationNatureCd: selectedOperation.value,\n        operationNatureNm: selectedOperation.label,\n        operationNatureNmEn: selectedOperation.ename\n      });\n    }\n  }\n  /**\n   * 默认航线选择变化\n   */\n  onShipLineChange(value) {\n    const selectedLine = this.shipLineData.find(item => item.value === value);\n    if (selectedLine) {\n      this.editForm.patchValue({\n        shipLineCd: selectedLine.shipLineCd,\n        shipLineNm: selectedLine.shipLineNm,\n        shipLineNmEn: selectedLine.shipLineNmEn,\n        shipLineClassCd: selectedLine.shipLineClassCd,\n        shipLineClassNm: selectedLine.shipLineClassNm,\n        shipLineClassNmEn: selectedLine.shipLineClassNmEn,\n        shipLineTypeCd: selectedLine.shipLineTypeCd,\n        shipLineTypeNm: selectedLine.shipLineTypeNm,\n        shipLineTypeNmEn: selectedLine.shipLineTypeNmEn\n      });\n    }\n  }\n  /**\n   * 保存成功后触发\n   */\n  afterSave() {\n    // 保存成功后查询文件列表\n    if (this.editForm.controls['id'].value) {\n      this.onQueryData();\n    }\n  }\n  // 文件上传\n  fileUpload() {\n    let self = this;\n    this.fileUploading = true;\n    const formData = new FormData();\n    formData.append('file', this.fileList[0]);\n    formData.append('pkId', this.editForm.controls['id'].value);\n    formData.append('module', 'tasModule');\n    formData.append('businessTypeCode', 'tas_vessel');\n    formData.append('businessTypeName', 'tas_vessel');\n    formData.append('businessTypeNameEn', 'tas_vessel');\n    formData.append('recordTypeCode', 'tas_vessel');\n    formData.append('recordTypeName', 'tas_vessel');\n    formData.append('recordTypeNameEn', 'tas_vessel');\n    formData.append('serviceTypeCode', 'tas_vessel');\n    formData.append('serviceTypeName', 'tas_vessel');\n    formData.append('serviceTypeNameEn', 'tas_vessel');\n    const headers = new HttpHeaders({\n      'x-ccf-token': this.USER_SESSION\n    });\n    const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\n      headers: headers,\n      reportProgress: true,\n      // 启用进度报告\n      withCredentials: false // 携带跨域凭证\n    });\n    this.http.request(req).pipe(filter(e => e instanceof HttpResponse)).subscribe({\n      next(res) {\n        self.fileUploading = false;\n        self.fileList = [];\n        if (res['ok']) {\n          if (res['body']['ok']) {\n            self.showState(ModalTypeEnum.success, '文件上传成功');\n            self.onQueryData();\n          } else {\n            self.showState(ModalTypeEnum.error, res['body']?.['msg'] ?? '文件上传失败');\n          }\n        } else {\n          self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\n        }\n      },\n      error(err) {\n        self.fileUploading = false;\n        self.showState(ModalTypeEnum.error, err.msg);\n      }\n    });\n  }\n  // 查询文件列表\n  onQueryData() {\n    this.fileloading = true;\n    let requestData = {\n      pkId: this.editForm.controls['id'].value,\n      module: 'tasModule',\n      businessTypeCode: 'tas_vessel'\n    };\n    this.fileStore.clearData();\n    this.cwfRestfulService.post('/storage/new/list', {\n      'data': requestData\n    }, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.fileStore.loadDatas(rps.data ?? []);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).finally(() => {\n      this.fileloading = false;\n    });\n  }\n  // 文件删除\n  onFileDel(id) {\n    this.cwfRestfulService.delete('/storage/new/' + id, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '删除成功');\n        this.onQueryData();\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  // 文件下载\n  onFileDownload(id) {\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          window.open(downloadUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  // 文件预览\n  preview(data) {\n    var file_name = data['originalFileName'];\n    const fileExtension = file_name.split('.').pop();\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\n          window.open(previewUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function VesselEditComponent_Factory(t) {\n      return new (t || VesselEditComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.CwfRestfulService), i0.ɵɵdirectiveInject(i4.CwfUploadService), i0.ɵɵdirectiveInject(i5.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VesselEditComponent,\n      selectors: [[\"vessel-edit\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 182,\n      vars: 84,\n      consts: [[\"fileitem\", \"\"], [1, \"system-index-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"24\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\", \"nzLoading\", \"disabled\"], [\"nz-button\", \"\", 2, \"margin-left\", \"10px\", 3, \"click\", \"nzType\", \"nzLoading\"], [\"nz-form\", \"\", 2, \"margin-top\", \"20px\", 3, \"formGroup\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"6\"], [\"nzRequired\", \"\", 2, \"width\", \"120px\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8239\\u8236\\u4EE3\\u7801\\uFF0C\\u6700\\u5927\\u957F\\u5EA636\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8236\\u4EE3\\u7801\", \"formControlName\", \"vesselCd\", \"maxlength\", \"36\", 3, \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8239\\u8236\\u540D\\u79F0\\uFF0C\\u6700\\u5927\\u957F\\u5EA635\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8236\\u540D\\u79F0\", \"formControlName\", \"vesselNm\", \"maxlength\", \"35\", 3, \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8239\\u8236\\u82F1\\u6587\\u540D\\u79F0\\uFF0C\\u6700\\u5927\\u957F\\u5EA6105\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8236\\u82F1\\u6587\\u540D\\u79F0\", \"formControlName\", \"vesselNmEn\", \"maxlength\", \"105\", 3, \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165IMO\\uFF0C\\u6700\\u5927\\u957F\\u5EA618\"], [\"nz-input\", \"\", \"placeholder\", \"IMO\", \"formControlName\", \"imo\", \"maxlength\", \"18\", 3, \"disabled\"], [2, \"width\", \"120px\"], [\"nzErrorTip\", \"\\u6700\\u5927\\u957F\\u5EA618\"], [\"nz-input\", \"\", \"placeholder\", \"MMSI\", \"formControlName\", \"mmsi\", \"maxlength\", \"18\", 3, \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8236\\u547C\\u53F7\", \"formControlName\", \"callsign\", \"maxlength\", \"18\", 3, \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u8236\\u7C7B\\u578B\"], [\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u8236\\u7C7B\\u578B\", \"formControlName\", \"vesselTypeCd\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"disabled\"], [3, \"nzValue\", \"nzLabel\", 4, \"ngFor\", \"ngForOf\"], [\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u8236\\u5C5E\\u6027\", \"formControlName\", \"vesselAttrCd\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"disabled\"], [\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u8236\\u6027\\u8D28\", \"formControlName\", \"vesselNatureCd\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"disabled\"], [\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8425\\u8FD0\\u6027\\u8D28\", \"formControlName\", \"operationNatureCd\", \"nzAllowClear\", \"\", 3, \"ngModelChange\", \"disabled\"], [\"key\", \"BASE_T_NATION\", \"readfield\", \"nationNm,nationCd,nationNmEn\", \"valuefield\", \"vesselFlagNm,vesselFlagCd,vesselFlagNmEn\", \"formControlName\", \"vesselFlagNm\", \"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u8239\\u65D7\\u56FD\\u5BB6\", 3, \"condition\", \"hasAll\", \"formgroup\", \"nzDisabled\"], [\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\\u9ED8\\u8BA4\\u822A\\u7EBF\", \"formControlName\", \"shipLineCd\", \"nzAllowClear\", \"\", \"nzShowSearch\", \"\", 3, \"ngModelChange\", \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u6B63\\u6574\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8231\\u6570\\u91CF\", \"formControlName\", \"cabinNum\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5C42\\u6570\", \"formControlName\", \"tierNum\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u6B63\\u786E\\u7684\\u6570\\u503C\\uFF0C\\u6700\\u591A3\\u4F4D\\u5C0F\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8239\\u8236\\u603B\\u957F\", \"formControlName\", \"shipLength\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u578B\\u6DF1\", \"formControlName\", \"shipDepth\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u578B\\u5BBD\", \"formControlName\", \"shipWidth\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u6700\\u5927\\u5403\\u6C34\", \"formControlName\", \"maxDraft\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u603B\\u8F7D\\u91CD\\u5428\", \"formControlName\", \"dwt\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u51C0\\u8F7D\\u91CD\\u5428\", \"formControlName\", \"cwt\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u6563\\u8D27\\u8231\\u5BB9\", \"formControlName\", \"bulkCapacity\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5305\\u88C5\\u8231\\u5BB9\", \"formControlName\", \"packCapacity\", 3, \"nzPrecision\", \"nzMin\", \"disabled\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"nzErrorTip\", \"\\u6700\\u5927\\u957F\\u5EA675\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5907\\u6CE8\", \"formControlName\", \"remark\", \"maxlength\", \"75\", 3, \"nzAutosize\", \"disabled\"], [1, \"list-button\"], [2, \"width\", \"8%\", \"float\", \"left\", \"margin-top\", \"10px\"], [2, \"height\", \"10px\", \"width\", \"10px\", \"color\", \"rgb(20, 96, 237)\", \"border\", \"2px solid\"], [3, \"nzFileListChange\", \"nzFileList\", \"nzBeforeUpload\", \"nzShowUploadList\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"primary\", 3, \"nzLoading\"], [\"nz-icon\", \"\", \"nzType\", \"import\", \"nzTheme\", \"outline\"], [3, \"nzFrontPagination\", \"nzData\", \"nzScroll\", \"nzLoading\"], [\"nzWidth\", \"75px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"300px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"260px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzRight\", \"\", \"nzWidth\", \"140px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\"], [4, \"ngFor\", \"ngForOf\"], [3, \"nzValue\", \"nzLabel\"], [\"nz-tooltip\", \"\", 3, \"nzAlign\"], [\"nzRight\", \"\"], [\"nz-popconfirm\", \"\", \"nzPopconfirmTitle\", \"\\u662F\\u5426\\u5220\\u9664\\u6B64\\u6570\\u636E\\uFF1F\", 2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"nzOnConfirm\"], [2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"click\"]],\n      template: function VesselEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 1)(1, \"nz-row\")(2, \"nz-col\", 2)(3, \"div\")(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function VesselEditComponent_Template_button_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.saveData());\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function VesselEditComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClose());\n          });\n          i0.ɵɵtext(8);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"form\", 5)(11, \"div\", 6)(12, \"div\", 7)(13, \"nz-form-item\")(14, \"nz-form-label\", 8);\n          i0.ɵɵtext(15, \"\\u8239\\u8236\\u4EE3\\u7801\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nz-form-control\", 9);\n          i0.ɵɵelement(17, \"input\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"nz-form-item\")(20, \"nz-form-label\", 8);\n          i0.ɵɵtext(21, \"\\u8239\\u8236\\u540D\\u79F0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"nz-form-control\", 11);\n          i0.ɵɵelement(23, \"input\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 7)(25, \"nz-form-item\")(26, \"nz-form-label\", 8);\n          i0.ɵɵtext(27, \"\\u8239\\u8236\\u82F1\\u6587\\u540D\\u79F0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nz-form-control\", 13);\n          i0.ɵɵelement(29, \"input\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 7)(31, \"nz-form-item\")(32, \"nz-form-label\", 8);\n          i0.ɵɵtext(33, \"IMO\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nz-form-control\", 15);\n          i0.ɵɵelement(35, \"input\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 7)(37, \"nz-form-item\")(38, \"nz-form-label\", 17);\n          i0.ɵɵtext(39, \"MMSI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nz-form-control\", 18);\n          i0.ɵɵelement(41, \"input\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 7)(43, \"nz-form-item\")(44, \"nz-form-label\", 17);\n          i0.ɵɵtext(45, \"\\u8239\\u8236\\u547C\\u53F7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nz-form-control\", 18);\n          i0.ɵɵelement(47, \"input\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 7)(49, \"nz-form-item\")(50, \"nz-form-label\", 8);\n          i0.ɵɵtext(51, \"\\u8239\\u8236\\u7C7B\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nz-form-control\", 21)(53, \"nz-select\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function VesselEditComponent_Template_nz_select_ngModelChange_53_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onVesselTypeChange($event));\n          });\n          i0.ɵɵtemplate(54, VesselEditComponent_nz_option_54_Template, 1, 2, \"nz-option\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(55, \"div\", 7)(56, \"nz-form-item\")(57, \"nz-form-label\", 17);\n          i0.ɵɵtext(58, \"\\u8239\\u8236\\u5C5E\\u6027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"nz-form-control\")(60, \"nz-select\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function VesselEditComponent_Template_nz_select_ngModelChange_60_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onVesselAttrChange($event));\n          });\n          i0.ɵɵtemplate(61, VesselEditComponent_nz_option_61_Template, 1, 2, \"nz-option\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(62, \"div\", 7)(63, \"nz-form-item\")(64, \"nz-form-label\", 17);\n          i0.ɵɵtext(65, \"\\u8239\\u8236\\u6027\\u8D28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"nz-form-control\")(67, \"nz-select\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function VesselEditComponent_Template_nz_select_ngModelChange_67_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onVesselNatureChange($event));\n          });\n          i0.ɵɵtemplate(68, VesselEditComponent_nz_option_68_Template, 1, 2, \"nz-option\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"div\", 7)(70, \"nz-form-item\")(71, \"nz-form-label\", 17);\n          i0.ɵɵtext(72, \"\\u8425\\u8FD0\\u6027\\u8D28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"nz-form-control\")(74, \"nz-select\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function VesselEditComponent_Template_nz_select_ngModelChange_74_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOperationNatureChange($event));\n          });\n          i0.ɵɵtemplate(75, VesselEditComponent_nz_option_75_Template, 1, 2, \"nz-option\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(76, \"div\", 7)(77, \"nz-form-item\")(78, \"nz-form-label\", 17);\n          i0.ɵɵtext(79, \"\\u8239\\u65D7(\\u56FD\\u5BB6)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"nz-form-control\");\n          i0.ɵɵelement(81, \"cms-select-table\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"div\", 7)(83, \"nz-form-item\")(84, \"nz-form-label\", 17);\n          i0.ɵɵtext(85, \"\\u9ED8\\u8BA4\\u822A\\u7EBF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"nz-form-control\")(87, \"nz-select\", 28);\n          i0.ɵɵlistener(\"ngModelChange\", function VesselEditComponent_Template_nz_select_ngModelChange_87_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onShipLineChange($event));\n          });\n          i0.ɵɵtemplate(88, VesselEditComponent_nz_option_88_Template, 1, 2, \"nz-option\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(89, \"div\", 7)(90, \"nz-form-item\")(91, \"nz-form-label\", 17);\n          i0.ɵɵtext(92, \"\\u8239\\u8231\\u6570\\u91CF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"nz-form-control\", 29);\n          i0.ɵɵelement(94, \"nz-input-number\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(95, \"div\", 7)(96, \"nz-form-item\")(97, \"nz-form-label\", 17);\n          i0.ɵɵtext(98, \"\\u5C42\\u6570(\\u6C7D\\u8F66\\u8239)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"nz-form-control\", 29);\n          i0.ɵɵelement(100, \"nz-input-number\", 31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 7)(102, \"nz-form-item\")(103, \"nz-form-label\", 17);\n          i0.ɵɵtext(104, \"\\u8239\\u8236\\u603B\\u957F(m)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"nz-form-control\", 32);\n          i0.ɵɵelement(106, \"nz-input-number\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\", 7)(108, \"nz-form-item\")(109, \"nz-form-label\", 17);\n          i0.ɵɵtext(110, \"\\u578B\\u6DF1(m)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"nz-form-control\", 32);\n          i0.ɵɵelement(112, \"nz-input-number\", 34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(113, \"div\", 7)(114, \"nz-form-item\")(115, \"nz-form-label\", 17);\n          i0.ɵɵtext(116, \"\\u578B\\u5BBD(m)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"nz-form-control\", 32);\n          i0.ɵɵelement(118, \"nz-input-number\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(119, \"div\", 7)(120, \"nz-form-item\")(121, \"nz-form-label\", 17);\n          i0.ɵɵtext(122, \"\\u6700\\u5927\\u5403\\u6C34(m)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"nz-form-control\", 32);\n          i0.ɵɵelement(124, \"nz-input-number\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(125, \"div\", 7)(126, \"nz-form-item\")(127, \"nz-form-label\", 17);\n          i0.ɵɵtext(128, \"\\u603B\\u8F7D\\u91CD\\u5428(T)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"nz-form-control\", 32);\n          i0.ɵɵelement(130, \"nz-input-number\", 37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(131, \"div\", 7)(132, \"nz-form-item\")(133, \"nz-form-label\", 17);\n          i0.ɵɵtext(134, \"\\u51C0\\u8F7D\\u91CD\\u5428(T)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"nz-form-control\", 32);\n          i0.ɵɵelement(136, \"nz-input-number\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(137, \"div\", 7)(138, \"nz-form-item\")(139, \"nz-form-label\", 17);\n          i0.ɵɵtext(140, \"\\u6563\\u8D27\\u8231\\u5BB9(M3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"nz-form-control\", 32);\n          i0.ɵɵelement(142, \"nz-input-number\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(143, \"div\", 7)(144, \"nz-form-item\")(145, \"nz-form-label\", 17);\n          i0.ɵɵtext(146, \"\\u5305\\u88C5\\u8231\\u5BB9(M3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"nz-form-control\", 32);\n          i0.ɵɵelement(148, \"nz-input-number\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(149, \"div\", 41)(150, \"nz-form-item\")(151, \"nz-form-label\", 17);\n          i0.ɵɵtext(152, \"\\u5907\\u6CE8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"nz-form-control\", 42)(154, \"textarea\", 43);\n          i0.ɵɵtext(155, \"            \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(156, \"h4\")(157, \"div\", 44)(158, \"div\", 45);\n          i0.ɵɵelement(159, \"span\", 46);\n          i0.ɵɵtext(160, \"\\u00A0\\u6587\\u4EF6\\u8D44\\u6599\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"nz-upload\", 47);\n          i0.ɵɵtwoWayListener(\"nzFileListChange\", function VesselEditComponent_Template_nz_upload_nzFileListChange_161_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.fileList, $event) || (ctx.fileList = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(162, \"button\", 48);\n          i0.ɵɵelement(163, \"span\", 49);\n          i0.ɵɵelementStart(164, \"span\");\n          i0.ɵɵtext(165, \"\\u4E0A\\u4F20\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(166, \"nz-table\", 50, 0)(168, \"thead\")(169, \"tr\")(170, \"th\", 51);\n          i0.ɵɵtext(171, \" \\u5E8F\\u53F7 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"th\", 52);\n          i0.ɵɵtext(173, \" \\u6587\\u4EF6\\u540D\\u79F0 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(174, \"th\", 53);\n          i0.ɵɵtext(175, \" \\u4E0A\\u4F20\\u4EBA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"th\", 53);\n          i0.ɵɵtext(177, \" \\u4E0A\\u4F20\\u65F6\\u95F4 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(178, \"th\", 54);\n          i0.ɵɵtext(179, \" \\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(180, \"tbody\");\n          i0.ɵɵtemplate(181, VesselEditComponent_tr_181_Template, 20, 11, \"tr\", 55);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const fileitem_r11 = i0.ɵɵreference(167);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(79, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzType\", \"primary\")(\"nzLoading\", ctx.loading)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 75, \"FP.SAVE\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\")(\"nzLoading\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 77, \"FP.RETURN\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(80, _c1));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.vesselTypeData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.vesselAttrData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.vesselNatureData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.operationNatureData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"condition\", i0.ɵɵpureFunction0(81, _c2))(\"hasAll\", false)(\"formgroup\", ctx.editForm)(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.shipLineData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 0)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 0)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPrecision\", 3)(\"nzMin\", 0)(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzAutosize\", i0.ɵɵpureFunction0(82, _c3))(\"disabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"nzFileList\", ctx.fileList);\n          i0.ɵɵproperty(\"nzBeforeUpload\", ctx.fileBeforeUpload)(\"nzShowUploadList\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzLoading\", ctx.fileUploading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzFrontPagination\", false)(\"nzData\", ctx.fileStore.getDatas())(\"nzScroll\", i0.ɵɵpureFunction0(83, _c4))(\"nzLoading\", ctx.fileloading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", fileitem_r11.data);\n        }\n      },\n      dependencies: [i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.MaxLengthValidator, i7.NgForOf, i6.FormGroupDirective, i6.FormControlName, i8.NzColDirective, i8.NzRowDirective, i9.NzFormDirective, i9.NzFormItemComponent, i9.NzFormLabelComponent, i9.NzFormControlComponent, i10.NzButtonComponent, i11.ɵNzTransitionPatchDirective, i12.NzWaveDirective, i13.NzInputDirective, i13.NzAutosizeDirective, i14.NzInputNumberComponent, i15.NzOptionComponent, i15.NzSelectComponent, i16.NzCardComponent, i17.NzPopconfirmDirective, i18.NzTableComponent, i18.NzTableCellDirective, i18.NzThMeasureDirective, i18.NzTheadComponent, i18.NzTbodyComponent, i18.NzTrDirective, i18.NzCellFixedDirective, i18.NzCellAlignDirective, i19.NzTooltipDirective, i20.NzIconDirective, i21.NzUploadComponent, i22.CmsLookupComponent, i7.DatePipe, i23.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CwfBaseCrud", "DialogResultEnum", "ModalTypeEnum", "PageModeEnum", "FormControl", "Validators", "TAS_T_VESSEL", "BASE_T_PARFILE", "HttpHeaders", "HttpRequest", "HttpResponse", "filter", "i0", "ɵɵelement", "ɵɵproperty", "item_r2", "value", "label", "item_r3", "item_r4", "item_r5", "item_r6", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "VesselEditComponent_tr_181_Template_a_nzOnConfirm_14_listener", "data_r8", "ɵɵrestoreView", "_r7", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onFileDel", "id", "VesselEditComponent_tr_181_Template_a_click_16_listener", "preview", "VesselEditComponent_tr_181_Template_a_click_18_listener", "onFileDownload", "attachmentId", "ɵɵadvance", "ɵɵtextInterpolate", "i_r10", "originalFileName", "createdUserName", "translate", "ɵɵpipeBind2", "createdTime", "VesselEditComponent", "constructor", "cwfBusContextService", "gol", "cwfRestfulService", "uploadService", "http", "mainStore", "editStores", "disabledEditForm", "ALL", "vesselTypeData", "vesselAttrData", "vesselNatureData", "operationNatureData", "shipLineData", "fileList", "fileUploading", "fileloading", "fileStore", "baseServiceName", "USER_SESSION", "cwfBusContext", "getContext", "getSessionId", "fileUploadUrl", "serverUrl", "serviceName", "en", "fileBeforeUpload", "file", "console", "log", "editForm", "controls", "showState", "error", "length", "maxSize", "size", "fileUpload", "initEdit", "nullValidator", "vesselCd", "required", "max<PERSON><PERSON><PERSON>", "vesselNm", "vesselNmEn", "imo", "mmsi", "callsign", "vesselTypeCd", "vesselTypeNm", "vesselTypeNmEn", "vesselAttrCd", "vesselAttrNm", "vesselAttrNmEn", "vesselNatureCd", "vesselNatureNm", "vesselNatureNmEn", "operationNatureCd", "operationNatureNm", "operationNatureNmEn", "vesselFlagCd", "vesselFlagNm", "vesselFlagNmEn", "shipLineCd", "shipLineNm", "shipLineNmEn", "shipLineClassCd", "shipLineClassNm", "shipLineClassNmEn", "shipLineTypeCd", "shipLineTypeNm", "shipLineTypeNmEn", "cabinNum", "pattern", "tierNum", "shipLength", "shipDepth", "shipWidth", "maxDraft", "dwt", "cwt", "bulkCapacity", "packCapacity", "remark", "created<PERSON>ser", "modifiedUser", "modifiedTime", "isDelete", "version", "onShow", "_this", "_asyncToGenerator", "loadDictionaries", "openParam", "Custom", "get", "then", "rps", "ok", "patchValue", "data", "openMainPage", "Modify", "onQueryData", "_this2", "Promise", "all", "loadVesselType", "loadVesselAttr", "loadVesselNature", "loadOperationNature", "loadShipLines", "resolve", "reject", "rdata", "type", "requestData", "page", "post", "content", "map", "item", "name", "code", "ename", "englishName", "msg", "catch", "saveData", "url", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "getNotify", "showLoading", "loading", "Add", "removeControl", "getRawValue", "removeShow", "success", "getMainController", "setTimeout", "put", "onClose", "checkSave", "showConfirm", "getMsgi18nString", "state", "yes", "no", "cancel", "getEditFromDisabled", "feild<PERSON><PERSON>", "onVesselTypeChange", "selectedType", "find", "onVesselAttrChange", "<PERSON><PERSON><PERSON><PERSON>", "onVesselNatureChange", "selectedNature", "onOperationNatureChange", "selectedOperation", "onShipLineChange", "selectedLine", "afterSave", "self", "formData", "FormData", "append", "headers", "req", "reportProgress", "withCredentials", "request", "pipe", "e", "subscribe", "next", "res", "err", "pkId", "module", "businessTypeCode", "clearData", "loadDatas", "finally", "delete", "downloadFile", "downloadUrl", "window", "open", "file_name", "fileExtension", "split", "pop", "previewUrl", "btoa", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "CwfRestfulService", "i4", "CwfUploadService", "i5", "HttpClient", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "VesselEditComponent_Template", "rf", "ctx", "VesselEditComponent_Template_button_click_4_listener", "_r1", "VesselEditComponent_Template_button_click_7_listener", "VesselEditComponent_Template_nz_select_ngModelChange_53_listener", "$event", "ɵɵtemplate", "VesselEditComponent_nz_option_54_Template", "VesselEditComponent_Template_nz_select_ngModelChange_60_listener", "VesselEditComponent_nz_option_61_Template", "VesselEditComponent_Template_nz_select_ngModelChange_67_listener", "VesselEditComponent_nz_option_68_Template", "VesselEditComponent_Template_nz_select_ngModelChange_74_listener", "VesselEditComponent_nz_option_75_Template", "VesselEditComponent_Template_nz_select_ngModelChange_87_listener", "VesselEditComponent_nz_option_88_Template", "ɵɵtwoWayListener", "VesselEditComponent_Template_nz_upload_nzFileListChange_161_listener", "ɵɵtwoWayBindingSet", "VesselEditComponent_tr_181_Template", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "_c1", "_c2", "_c3", "ɵɵtwoWayProperty", "getDatas", "_c4", "fileitem_r11"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-list\\vessel-edit\\vessel-edit.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-list\\vessel-edit\\vessel-edit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CwfBaseCrud, CwfBusContextService, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { TAS_T_VESSEL } from '@store/TAS/TAS_T_VESSEL';\r\nimport { NzUploadFile } from 'ng-zorro-antd/upload';\r\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\r\nimport { CwfUploadService } from '@service/cwfuploadService';\r\nimport { HttpClient, HttpHeaders, HttpRequest, HttpResponse } from '@angular/common/http';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'vessel-edit',\r\n  templateUrl: './vessel-edit.component.html'\r\n})\r\n\r\nexport class VesselEditComponent extends CwfBaseCrud {\r\n  editForm: FormGroup;\r\n  mainStore = new TAS_T_VESSEL();\r\n  editStores = [this.mainStore];\r\n  id = \"\";\r\n\r\n\r\n\r\n  // 定义禁用编辑表单的对象，用于控制表单的禁用状态\r\n  disabledEditForm: Object = {\r\n    ALL: false,\r\n  };\r\n\r\n  // 数据字典数据\r\n  vesselTypeData = [];\r\n  vesselAttrData = [];\r\n  vesselNatureData = [];\r\n  operationNatureData = [];\r\n  shipLineData = [];\r\n\r\n  // 文件资料相关属性\r\n  fileList: NzUploadFile[] = [];\r\n  fileUploading: boolean = false;\r\n  fileloading = false;\r\n  fileStore = new BASE_T_PARFILE();\r\n  baseServiceName = '';\r\n  USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\r\n  fileUploadUrl =\r\n    this.gol.serverUrl +\r\n    '/' +\r\n    this.gol.serviceName['tas'].en +\r\n    '/storage/new/upload'; // 文件上传请求服务地址\r\n\r\n\r\n\r\n  constructor(cwfBusContextService: CwfBusContextService,\r\n    public gol: GlobalDataService,\r\n    private cwfRestfulService: CwfRestfulService,\r\n    private uploadService: CwfUploadService,\r\n    private http: HttpClient) {\r\n    super(cwfBusContextService);\r\n    this.baseServiceName = this.gol.serviceName['tas'].en;\r\n  }\r\n\r\n  /**\r\n   * 初始化编辑表单\r\n   */\r\n  initEdit() {\r\n    return {\r\n      id: new FormControl('', Validators.nullValidator),\r\n      // 基础字段\r\n      vesselCd: new FormControl('', [Validators.required, Validators.maxLength(36)]),\r\n      vesselNm: new FormControl('', [Validators.required, Validators.maxLength(35)]),\r\n      vesselNmEn: new FormControl('', [Validators.required, Validators.maxLength(105)]),\r\n      imo: new FormControl('', [Validators.required, Validators.maxLength(18)]),\r\n      mmsi: new FormControl('', [Validators.maxLength(18)]),\r\n      callsign: new FormControl('', [Validators.maxLength(18)]),\r\n\r\n      // 下拉选择字段\r\n      vesselTypeCd: new FormControl('', [Validators.required]),\r\n      vesselTypeNm: new FormControl(''),\r\n      vesselTypeNmEn: new FormControl(''),\r\n      vesselAttrCd: new FormControl(''),\r\n      vesselAttrNm: new FormControl(''),\r\n      vesselAttrNmEn: new FormControl(''),\r\n      vesselNatureCd: new FormControl(''),\r\n      vesselNatureNm: new FormControl(''),\r\n      vesselNatureNmEn: new FormControl(''),\r\n      operationNatureCd: new FormControl(''),\r\n      operationNatureNm: new FormControl(''),\r\n      operationNatureNmEn: new FormControl(''),\r\n      vesselFlagCd: new FormControl(''),\r\n      vesselFlagNm: new FormControl(''),\r\n      vesselFlagNmEn: new FormControl(''),\r\n\r\n      // 航线字段\r\n      shipLineCd: new FormControl(''),\r\n      shipLineNm: new FormControl(''),\r\n      shipLineNmEn: new FormControl(''),\r\n      shipLineClassCd: new FormControl(''),\r\n      shipLineClassNm: new FormControl(''),\r\n      shipLineClassNmEn: new FormControl(''),\r\n      shipLineTypeCd: new FormControl(''),\r\n      shipLineTypeNm: new FormControl(''),\r\n      shipLineTypeNmEn: new FormControl(''),\r\n\r\n      // 数字字段\r\n      cabinNum: new FormControl('', [Validators.pattern(/^\\d*$/)]),\r\n      tierNum: new FormControl('', [Validators.pattern(/^\\d*$/)]),\r\n      shipLength: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      shipDepth: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      shipWidth: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      maxDraft: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      dwt: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      cwt: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      bulkCapacity: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n      packCapacity: new FormControl('', [Validators.pattern(/^\\d*(\\.\\d{1,3})?$/)]),\r\n\r\n      // 其他字段\r\n      remark: new FormControl('', [Validators.maxLength(75)]),\r\n      createdUser: new FormControl('', Validators.nullValidator),\r\n      createdTime: new FormControl('', Validators.nullValidator),\r\n      modifiedUser: new FormControl('', Validators.nullValidator),\r\n      modifiedTime: new FormControl('', Validators.nullValidator),\r\n      isDelete: new FormControl('', Validators.nullValidator),\r\n      version: new FormControl('', Validators.nullValidator),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 页面加载完成后触发\r\n   */\r\n  async onShow() {\r\n    // 加载数据字典\r\n    await this.loadDictionaries();\r\n\r\n    if (this.openParam['state'] === PageModeEnum.Custom) {\r\n      this.disabledEditForm['ALL'] = true;\r\n      this.cwfRestfulService.get('/vessel/' + this.openParam['id'], this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.editForm.patchValue(rps.data);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\r\n          this.openMainPage();\r\n        }\r\n      });\r\n    } else if (this.openParam['state'] === PageModeEnum.Modify) {\r\n      // 修改模式：根据ID加载数据\r\n      this.cwfRestfulService.get('/vessel/' + this.openParam['id'], this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.editForm.patchValue(rps.data);\r\n          // 加载成功后查询文件列表\r\n          this.onQueryData();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\r\n          this.openMainPage();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载所有数据字典\r\n   */\r\n  async loadDictionaries() {\r\n    try {\r\n      await Promise.all([\r\n        this.loadVesselType(),\r\n        this.loadVesselAttr(),\r\n        this.loadVesselNature(),\r\n        this.loadOperationNature(),\r\n        this.loadShipLines()\r\n      ]);\r\n    } catch (error) {\r\n      console.error('加载数据字典失败:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载船舶类型\r\n   */\r\n  loadVesselType(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const rdata = { type: 'system:tas:vesselType' };\r\n      const requestData = { data: rdata, page: 1, size: 1000 };\r\n\r\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.vesselTypeData = rps.data.content.map((item: any) => ({\r\n              label: item.name,\r\n              value: item.code,\r\n              ename: item.englishName\r\n            }));\r\n            resolve();\r\n          } else {\r\n            reject(rps.msg);\r\n          }\r\n        })\r\n        .catch(reject);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 加载船舶属性\r\n   */\r\n  loadVesselAttr(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const rdata = { type: 'system:tas:vesselAttr' };\r\n      const requestData = { data: rdata, page: 1, size: 1000 };\r\n\r\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.vesselAttrData = rps.data.content.map((item: any) => ({\r\n              label: item.name,\r\n              value: item.code,\r\n              ename: item.englishName\r\n            }));\r\n            resolve();\r\n          } else {\r\n            reject(rps.msg);\r\n          }\r\n        })\r\n        .catch(reject);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 加载船舶性质\r\n   */\r\n  loadVesselNature(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const rdata = { type: 'system:tas:vesselNature' };\r\n      const requestData = { data: rdata, page: 1, size: 1000 };\r\n\r\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.vesselNatureData = rps.data.content.map((item: any) => ({\r\n              label: item.name,\r\n              value: item.code,\r\n              ename: item.englishName\r\n            }));\r\n            resolve();\r\n          } else {\r\n            reject(rps.msg);\r\n          }\r\n        })\r\n        .catch(reject);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 加载营运性质\r\n   */\r\n  loadOperationNature(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const rdata = { type: 'system:tas:operationNature' };\r\n      const requestData = { data: rdata, page: 1, size: 1000 };\r\n\r\n      this.cwfRestfulService.post('/dict/item/list/page', requestData, this.gol.serviceName['main'].en)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.operationNatureData = rps.data.content.map((item: any) => ({\r\n              label: item.name,\r\n              value: item.code,\r\n              ename: item.englishName\r\n            }));\r\n            resolve();\r\n          } else {\r\n            reject(rps.msg);\r\n          }\r\n        })\r\n        .catch(reject);\r\n    });\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * 加载航线数据\r\n   */\r\n  loadShipLines(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const requestData = { page: 1, size: 1000 };\r\n\r\n      this.cwfRestfulService.post('/shipline/list/page', requestData, this.gol.serviceName['tas'].en)\r\n        .then((rps: responseInterface) => {\r\n          if (rps.ok) {\r\n            this.shipLineData = rps.data.content.map((item: any) => ({\r\n              label: item.shipLineNm,\r\n              value: item.shipLineCd,\r\n              ...item\r\n            }));\r\n            resolve();\r\n          } else {\r\n            reject(rps.msg);\r\n          }\r\n        })\r\n        .catch(reject);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * desc:保存数据\r\n   */\r\n  saveData() {\r\n    const url = '/vessel';\r\n    for (const i in this.editForm.controls) {\r\n      this.editForm.controls[i].markAsDirty();\r\n      this.editForm.controls[i].updateValueAndValidity();\r\n    }\r\n    if (this.editForm.invalid) {\r\n      return;\r\n    }\r\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\r\n    this.loading = true;\r\n    if (this.openParam['state'] === PageModeEnum.Add) {\r\n      this.editForm.removeControl('id');\r\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          // 确保在页面跳转前先刷新列表数据\r\n          this.getMainController()['queryList']();\r\n          // 延迟跳转，确保数据刷新完成\r\n          setTimeout(() => {\r\n            this.openMainPage({});\r\n          }, 100);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    } else {\r\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          // 确保在页面跳转前先刷新列表数据\r\n          this.getMainController()['queryList']();\r\n          // 延迟跳转，确保数据刷新完成\r\n          setTimeout(() => {\r\n            this.openMainPage({});\r\n          }, 100);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    if (this.checkSave()) {\r\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\r\n        switch (state) {\r\n          case DialogResultEnum.yes:   // 是\r\n            this.saveData();\r\n            return true;\r\n          case DialogResultEnum.no:    // 否\r\n            this.openMainPage({});\r\n            return true;\r\n          case DialogResultEnum.cancel: // 取消\r\n            return false;\r\n        }\r\n      });\r\n    } else {\r\n      this.openMainPage({});\r\n      return true;\r\n    }\r\n  }\r\n\r\n  getEditFromDisabled(feildName: string) {\r\n    return this.disabledEditForm[feildName];\r\n  }\r\n\r\n  /**\r\n   * 船舶类型选择变化\r\n   */\r\n  onVesselTypeChange(value: string) {\r\n    const selectedType = this.vesselTypeData.find(item => item.value === value);\r\n    if (selectedType) {\r\n      this.editForm.patchValue({\r\n        vesselTypeCd: selectedType.value,\r\n        vesselTypeNm: selectedType.label,\r\n        vesselTypeNmEn: selectedType.ename\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 船舶属性选择变化\r\n   */\r\n  onVesselAttrChange(value: string) {\r\n    const selectedAttr = this.vesselAttrData.find(item => item.value === value);\r\n    if (selectedAttr) {\r\n      this.editForm.patchValue({\r\n        vesselAttrCd: selectedAttr.value,\r\n        vesselAttrNm: selectedAttr.label,\r\n        vesselAttrNmEn: selectedAttr.ename\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 船舶性质选择变化\r\n   */\r\n  onVesselNatureChange(value: string) {\r\n    const selectedNature = this.vesselNatureData.find(item => item.value === value);\r\n    if (selectedNature) {\r\n      this.editForm.patchValue({\r\n        vesselNatureCd: selectedNature.value,\r\n        vesselNatureNm: selectedNature.label,\r\n        vesselNatureNmEn: selectedNature.ename\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 营运性质选择变化\r\n   */\r\n  onOperationNatureChange(value: string) {\r\n    const selectedOperation = this.operationNatureData.find(item => item.value === value);\r\n    if (selectedOperation) {\r\n      this.editForm.patchValue({\r\n        operationNatureCd: selectedOperation.value,\r\n        operationNatureNm: selectedOperation.label,\r\n        operationNatureNmEn: selectedOperation.ename\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * 默认航线选择变化\r\n   */\r\n  onShipLineChange(value: string) {\r\n    const selectedLine = this.shipLineData.find(item => item.value === value);\r\n    if (selectedLine) {\r\n      this.editForm.patchValue({\r\n        shipLineCd: selectedLine.shipLineCd,\r\n        shipLineNm: selectedLine.shipLineNm,\r\n        shipLineNmEn: selectedLine.shipLineNmEn,\r\n        shipLineClassCd: selectedLine.shipLineClassCd,\r\n        shipLineClassNm: selectedLine.shipLineClassNm,\r\n        shipLineClassNmEn: selectedLine.shipLineClassNmEn,\r\n        shipLineTypeCd: selectedLine.shipLineTypeCd,\r\n        shipLineTypeNm: selectedLine.shipLineTypeNm,\r\n        shipLineTypeNmEn: selectedLine.shipLineTypeNmEn\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 保存成功后触发\r\n   */\r\n  afterSave() {\r\n    // 保存成功后查询文件列表\r\n    if (this.editForm.controls['id'].value) {\r\n      this.onQueryData();\r\n    }\r\n  }\r\n\r\n  // ==================== 文件资料相关方法 ====================\r\n\r\n  // 导入文件前\r\n  fileBeforeUpload = (file: NzUploadFile): boolean => {\r\n    console.log('查看id', this.editForm.controls['id'].value);\r\n    if (\r\n      this.editForm.controls['id'].value == null ||\r\n      this.editForm.controls['id'].value == ''\r\n    ) {\r\n      this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\r\n      return false;\r\n    }\r\n    this.fileList = [file];\r\n    console.log(this.fileList[0]);\r\n    if (this.fileList.length != 1) {\r\n      this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\r\n      return false;\r\n    }\r\n    // 文件大小验证（限制为50MB）\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n    if (file.size > maxSize) {\r\n      this.showState(ModalTypeEnum.error, '文件大小不能超过50MB');\r\n      return false;\r\n    }\r\n    this.fileUpload();\r\n    return false;\r\n  };\r\n\r\n  // 文件上传\r\n  fileUpload() {\r\n    let self = this;\r\n    this.fileUploading = true;\r\n    const formData = new FormData();\r\n    formData.append('file', this.fileList[0] as any);\r\n    formData.append('pkId', this.editForm.controls['id'].value);\r\n    formData.append('module', 'tasModule');\r\n    formData.append('businessTypeCode', 'tas_vessel');\r\n    formData.append('businessTypeName', 'tas_vessel');\r\n    formData.append('businessTypeNameEn', 'tas_vessel');\r\n    formData.append('recordTypeCode', 'tas_vessel');\r\n    formData.append('recordTypeName', 'tas_vessel');\r\n    formData.append('recordTypeNameEn', 'tas_vessel');\r\n    formData.append('serviceTypeCode', 'tas_vessel');\r\n    formData.append('serviceTypeName', 'tas_vessel');\r\n    formData.append('serviceTypeNameEn', 'tas_vessel');\r\n    const headers = new HttpHeaders({\r\n      'x-ccf-token': this.USER_SESSION,\r\n    });\r\n    const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\r\n      headers: headers,\r\n      reportProgress: true, // 启用进度报告\r\n      withCredentials: false, // 携带跨域凭证\r\n    });\r\n    this.http\r\n      .request(req)\r\n      .pipe(filter((e) => e instanceof HttpResponse))\r\n      .subscribe({\r\n        next(res): any {\r\n          self.fileUploading = false;\r\n          self.fileList = [];\r\n          if (res['ok']) {\r\n            if (res['body']['ok']) {\r\n              self.showState(ModalTypeEnum.success, '文件上传成功');\r\n              self.onQueryData();\r\n            } else {\r\n              self.showState(\r\n                ModalTypeEnum.error,\r\n                res['body']?.['msg'] ?? '文件上传失败'\r\n              );\r\n            }\r\n          } else {\r\n            self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\r\n          }\r\n        },\r\n        error(err): any {\r\n          self.fileUploading = false;\r\n          self.showState(ModalTypeEnum.error, err.msg);\r\n        },\r\n      });\r\n  }\r\n\r\n  // 查询文件列表\r\n  onQueryData() {\r\n    this.fileloading = true;\r\n    let requestData = {\r\n      pkId: this.editForm.controls['id'].value,\r\n      module: 'tasModule',\r\n      businessTypeCode: 'tas_vessel',\r\n    };\r\n    this.fileStore.clearData();\r\n    this.cwfRestfulService\r\n      .post('/storage/new/list', {'data': requestData}, this.baseServiceName)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.fileStore.loadDatas(rps.data ?? []);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n      .finally(() => {\r\n        this.fileloading = false;\r\n      });\r\n  }\r\n\r\n  // 文件删除\r\n  onFileDel(id) {\r\n    this.cwfRestfulService\r\n      .delete('/storage/new/' + id, this.baseServiceName)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '删除成功');\r\n          this.onQueryData();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 文件下载\r\n  onFileDownload(id) {\r\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then((rps: responseInterface) => {\r\n      if (rps.ok) {\r\n        const downloadUrl = rps.data[0];\r\n        if (downloadUrl) {\r\n          window.open(downloadUrl, '_blank');\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n        }\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 文件预览\r\n  preview(data) {\r\n    var file_name = data['originalFileName'];\r\n    const fileExtension = file_name.split('.').pop();\r\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then((rps: responseInterface) => {\r\n      if (rps.ok) {\r\n        const downloadUrl = rps.data[0];\r\n        if (downloadUrl) {\r\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\r\n          window.open(previewUrl, '_blank');\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n        }\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n}\r\n", "<nz-card class=\"system-index-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div>\r\n        <!-- 保存按钮 -->\r\n        <button nz-button [nzType]=\"'primary'\" (click)=\"saveData()\" [nzLoading]=\"loading\"\r\n                [disabled]=\"getEditFromDisabled('ALL')\">\r\n          {{ 'FP.SAVE' | translate }}\r\n        </button>\r\n\r\n        <!-- 返回按钮 -->\r\n        <button style=\"margin-left: 10px;\" nz-button [nzType]=\"'default'\" (click)=\"onClose()\" [nzLoading]=\"loading\">\r\n          {{ 'FP.RETURN' | translate }}\r\n        </button>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n\r\n  <!-- 编辑表单 -->\r\n  <form style=\"margin-top: 20px;\" nz-form [formGroup]=\"editForm\">\r\n\r\n    <!-- 统一表单布局 -->\r\n    <div nz-row [nzGutter]=\"[8,4]\">\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶代码</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入船舶代码，最大长度36\">\r\n            <input nz-input placeholder=\"船舶代码\" formControlName=\"vesselCd\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"36\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶名称</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入船舶名称，最大长度35\">\r\n            <input nz-input placeholder=\"船舶名称\" formControlName=\"vesselNm\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"35\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶英文名称</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入船舶英文名称，最大长度105\">\r\n            <input nz-input placeholder=\"船舶英文名称\" formControlName=\"vesselNmEn\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"105\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">IMO</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入IMO，最大长度18\">\r\n            <input nz-input placeholder=\"IMO\" formControlName=\"imo\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"18\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">MMSI</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"最大长度18\">\r\n            <input nz-input placeholder=\"MMSI\" formControlName=\"mmsi\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"18\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶呼号</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"最大长度18\">\r\n            <input nz-input placeholder=\"船舶呼号\" formControlName=\"callsign\"\r\n                   [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"18\">\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶类型</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请选择船舶类型\">\r\n            <nz-select nzPlaceHolder=\"请选择船舶类型\" formControlName=\"vesselTypeCd\"\r\n                       [disabled]=\"getEditFromDisabled('ALL')\" nzAllowClear\r\n                       (ngModelChange)=\"onVesselTypeChange($event)\">\r\n              <nz-option *ngFor=\"let item of vesselTypeData\" [nzValue]=\"item.value\" [nzLabel]=\"item.label\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶属性</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select nzPlaceHolder=\"请选择船舶属性\" formControlName=\"vesselAttrCd\"\r\n                       [disabled]=\"getEditFromDisabled('ALL')\" nzAllowClear\r\n                       (ngModelChange)=\"onVesselAttrChange($event)\">\r\n              <nz-option *ngFor=\"let item of vesselAttrData\" [nzValue]=\"item.value\" [nzLabel]=\"item.label\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶性质</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select nzPlaceHolder=\"请选择船舶性质\" formControlName=\"vesselNatureCd\"\r\n                       [disabled]=\"getEditFromDisabled('ALL')\" nzAllowClear\r\n                       (ngModelChange)=\"onVesselNatureChange($event)\">\r\n              <nz-option *ngFor=\"let item of vesselNatureData\" [nzValue]=\"item.value\" [nzLabel]=\"item.label\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">营运性质</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select nzPlaceHolder=\"请选择营运性质\" formControlName=\"operationNatureCd\"\r\n                       [disabled]=\"getEditFromDisabled('ALL')\" nzAllowClear\r\n                       (ngModelChange)=\"onOperationNatureChange($event)\">\r\n              <nz-option *ngFor=\"let item of operationNatureData\" [nzValue]=\"item.value\" [nzLabel]=\"item.label\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船旗(国家)</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BASE_T_NATION\"\r\n                              [condition]=\"{}\"\r\n                              [hasAll]=\"false\"\r\n                              [formgroup]=\"editForm\"\r\n                              readfield=\"nationNm,nationCd,nationNmEn\"\r\n                              valuefield=\"vesselFlagNm,vesselFlagCd,vesselFlagNmEn\"\r\n                              formControlName=\"vesselFlagNm\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              nzPlaceHolder=\"请选择船旗国家\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">默认航线</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select nzPlaceHolder=\"请选择默认航线\" formControlName=\"shipLineCd\"\r\n                       [disabled]=\"getEditFromDisabled('ALL')\" nzAllowClear nzShowSearch\r\n                       (ngModelChange)=\"onShipLineChange($event)\">\r\n              <nz-option *ngFor=\"let item of shipLineData\" [nzValue]=\"item.value\" [nzLabel]=\"item.label\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舱数量</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正整数\">\r\n            <nz-input-number nz-input placeholder=\"船舱数量\" formControlName=\"cabinNum\"\r\n                             [nzPrecision]=\"0\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">层数(汽车船)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正整数\">\r\n            <nz-input-number nz-input placeholder=\"层数\" formControlName=\"tierNum\"\r\n                             [nzPrecision]=\"0\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶总长(m)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"船舶总长\" formControlName=\"shipLength\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">型深(m)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"型深\" formControlName=\"shipDepth\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">型宽(m)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"型宽\" formControlName=\"shipWidth\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">最大吃水(m)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"最大吃水\" formControlName=\"maxDraft\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">总载重吨(T)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"总载重吨\" formControlName=\"dwt\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">净载重吨(T)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"净载重吨\" formControlName=\"cwt\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">散货舱容(M3)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"散货舱容\" formControlName=\"bulkCapacity\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">包装舱容(M3)</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"请输入正确的数值，最多3位小数\">\r\n            <nz-input-number nz-input placeholder=\"包装舱容\" formControlName=\"packCapacity\"\r\n                             [nzPrecision]=\"3\" [nzMin]=\"0\" [disabled]=\"getEditFromDisabled('ALL')\">\r\n            </nz-input-number>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">备注</nz-form-label>\r\n          <nz-form-control nzErrorTip=\"最大长度75\">\r\n            <textarea nz-input placeholder=\"备注\" formControlName=\"remark\"\r\n                      [nzAutosize]=\"{ minRows: 2, maxRows: 4 }\"\r\n                      [disabled]=\"getEditFromDisabled('ALL')\" maxlength=\"75\">\r\n            </textarea>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n    </div>\r\n\r\n  </form>\r\n\r\n  <!-- 文件资料卡片 -->\r\n  <h4>\r\n    <div class=\"list-button\">\r\n      <div style=\"width: 8%;float:left; margin-top: 10px;\"><span style=\"height: 10px;width: 10px;color: rgb(20, 96, 237);border: 2px solid;\"></span>&nbsp;文件资料</div>\r\n      <nz-upload [(nzFileList)]=\"fileList\" [nzBeforeUpload]=\"fileBeforeUpload\" [nzShowUploadList]=\"false\">\r\n        <button type=\"button\" nz-button nzType=\"primary\" [nzLoading]=\"fileUploading\">\r\n          <span nz-icon nzType=\"import\" nzTheme=\"outline\"></span>\r\n          <span>上传</span>\r\n        </button>\r\n      </nz-upload>\r\n    </div>\r\n  </h4>\r\n  <nz-table #fileitem [nzFrontPagination]=\"false\" [nzData]=\"fileStore.getDatas()\"\r\n    [nzScroll]=\"{ x: '1000px' }\" [nzLoading]=\"fileloading\">\r\n    <thead>\r\n      <tr>\r\n        <!--序号-->\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"75px\">\r\n          序号\r\n        </th>\r\n        <!--文件名称-->\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"300px\">\r\n          文件名称\r\n        </th>\r\n\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          上传人\r\n        </th>\r\n\r\n        <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n          上传时间\r\n        </th>\r\n\r\n        <th nzRight nzWidth=\"140px\" style=\"color: #5f8bd3; background-color: #e6edf5\">\r\n          操作\r\n        </th>\r\n        <!-- 操作 -->\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      <tr *ngFor=\"let data of fileitem.data; let i = index\">\r\n        <td nz-tooltip [nzAlign]=\"'center'\">{{ i + 1 }}</td>\r\n        <!-- 文件名称 -->\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.originalFileName }}</span>\r\n        </td>\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.createdUserName || translate }}</span>\r\n        </td>\r\n        <td nz-tooltip [nzAlign]=\"'center'\">\r\n          <span>{{ data.createdTime | date:'yyyy-MM-dd HH:mm:ss' }}</span>\r\n        </td>\r\n        <td nzRight>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" nz-popconfirm\r\n            nzPopconfirmTitle=\"是否删除此数据？\" (nzOnConfirm)=\"onFileDel(data.id)\">删除</a>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"preview(data)\">预览</a>\r\n          <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"onFileDownload(data.attachmentId)\">下载</a>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </nz-table>\r\n</nz-card>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAwBC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB;AACjH,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,YAAY,QAAQ,yBAAyB;AAEtD,SAASC,cAAc,QAAQ,2BAA2B;AAE1D,SAAqBC,WAAW,EAAEC,WAAW,EAAEC,YAAY,QAAQ,sBAAsB;AACzF,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC+EzBC,EAAA,CAAAC,SAAA,oBAAyG;;;;IAAnCD,EAAvB,CAAAE,UAAA,YAAAC,OAAA,CAAAC,KAAA,CAAsB,YAAAD,OAAA,CAAAE,KAAA,CAAuB;;;;;IAa5FL,EAAA,CAAAC,SAAA,oBAAyG;;;;IAAnCD,EAAvB,CAAAE,UAAA,YAAAI,OAAA,CAAAF,KAAA,CAAsB,YAAAE,OAAA,CAAAD,KAAA,CAAuB;;;;;IAa5FL,EAAA,CAAAC,SAAA,oBAA2G;;;;IAAnCD,EAAvB,CAAAE,UAAA,YAAAK,OAAA,CAAAH,KAAA,CAAsB,YAAAG,OAAA,CAAAF,KAAA,CAAuB;;;;;IAa9FL,EAAA,CAAAC,SAAA,oBAA8G;;;;IAAnCD,EAAvB,CAAAE,UAAA,YAAAM,OAAA,CAAAJ,KAAA,CAAsB,YAAAI,OAAA,CAAAH,KAAA,CAAuB;;;;;IA+BjGL,EAAA,CAAAC,SAAA,oBAAuG;;;;IAAnCD,EAAvB,CAAAE,UAAA,YAAAO,OAAA,CAAAL,KAAA,CAAsB,YAAAK,OAAA,CAAAJ,KAAA,CAAuB;;;;;;IA4KhGL,EADF,CAAAU,cAAA,SAAsD,aAChB;IAAAV,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAGlDZ,EADF,CAAAU,cAAA,aAAoC,WAC5B;IAAAV,EAAA,CAAAW,MAAA,GAA2B;IACnCX,EADmC,CAAAY,YAAA,EAAO,EACrC;IAEHZ,EADF,CAAAU,cAAA,aAAoC,WAC5B;IAAAV,EAAA,CAAAW,MAAA,GAAuC;IAC/CX,EAD+C,CAAAY,YAAA,EAAO,EACjD;IAEHZ,EADF,CAAAU,cAAA,aAAoC,YAC5B;IAAAV,EAAA,CAAAW,MAAA,IAAmD;;IAC3DX,EAD2D,CAAAY,YAAA,EAAO,EAC7D;IAEHZ,EADF,CAAAU,cAAA,cAAY,aAEwD;IAAnCV,EAAA,CAAAa,UAAA,yBAAAC,8DAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAeF,MAAA,CAAAG,SAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAkB;IAAA,EAAC;IAACvB,EAAA,CAAAW,MAAA,oBAAE;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxEZ,EAAA,CAAAU,cAAA,aAAuE;IAAxBV,EAAA,CAAAa,UAAA,mBAAAW,wDAAA;MAAA,MAAAT,OAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASF,MAAA,CAAAM,OAAA,CAAAV,OAAA,CAAa;IAAA,EAAC;IAACf,EAAA,CAAAW,MAAA,oBAAE;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAC7EZ,EAAA,CAAAU,cAAA,aAA2F;IAA5CV,EAAA,CAAAa,UAAA,mBAAAa,wDAAA;MAAA,MAAAX,OAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASF,MAAA,CAAAQ,cAAA,CAAAZ,OAAA,CAAAa,YAAA,CAAiC;IAAA,EAAC;IAAC5B,EAAA,CAAAW,MAAA,oBAAE;IAEjGX,EAFiG,CAAAY,YAAA,EAAI,EAC9F,EACF;;;;;;IAjBYZ,EAAA,CAAA6B,SAAA,EAAoB;IAApB7B,EAAA,CAAAE,UAAA,qBAAoB;IAACF,EAAA,CAAA6B,SAAA,EAAW;IAAX7B,EAAA,CAAA8B,iBAAA,CAAAC,KAAA,KAAW;IAEhC/B,EAAA,CAAA6B,SAAA,EAAoB;IAApB7B,EAAA,CAAAE,UAAA,qBAAoB;IAC3BF,EAAA,CAAA6B,SAAA,GAA2B;IAA3B7B,EAAA,CAAA8B,iBAAA,CAAAf,OAAA,CAAAiB,gBAAA,CAA2B;IAEpBhC,EAAA,CAAA6B,SAAA,EAAoB;IAApB7B,EAAA,CAAAE,UAAA,qBAAoB;IAC3BF,EAAA,CAAA6B,SAAA,GAAuC;IAAvC7B,EAAA,CAAA8B,iBAAA,CAAAf,OAAA,CAAAkB,eAAA,IAAAd,MAAA,CAAAe,SAAA,CAAuC;IAEhClC,EAAA,CAAA6B,SAAA,EAAoB;IAApB7B,EAAA,CAAAE,UAAA,qBAAoB;IAC3BF,EAAA,CAAA6B,SAAA,GAAmD;IAAnD7B,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAmC,WAAA,QAAApB,OAAA,CAAAqB,WAAA,yBAAmD;;;ADnUnE,OAAM,MAAOC,mBAAoB,SAAQjD,WAAW;EAmClDkD,YAAYC,oBAA0C,EAC7CC,GAAsB,EACrBC,iBAAoC,EACpCC,aAA+B,EAC/BC,IAAgB;IACxB,KAAK,CAACJ,oBAAoB,CAAC;IAJpB,KAAAC,GAAG,GAAHA,GAAG;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,IAAI,GAAJA,IAAI;IArCd,KAAAC,SAAS,GAAG,IAAIlD,YAAY,EAAE;IAC9B,KAAAmD,UAAU,GAAG,CAAC,IAAI,CAACD,SAAS,CAAC;IAC7B,KAAArB,EAAE,GAAG,EAAE;IAIP;IACA,KAAAuB,gBAAgB,GAAW;MACzBC,GAAG,EAAE;KACN;IAED;IACA,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,QAAQ,GAAmB,EAAE;IAC7B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,SAAS,GAAG,IAAI7D,cAAc,EAAE;IAChC,KAAA8D,eAAe,GAAG,EAAE;IACpB,KAAAC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,UAAU,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC;IAC/D,KAAAC,aAAa,GACX,IAAI,CAACtB,GAAG,CAACuB,SAAS,GAClB,GAAG,GACH,IAAI,CAACvB,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,GAC9B,qBAAqB,CAAC,CAAC;IA8ZzB;IAEA;IACA,KAAAC,gBAAgB,GAAIC,IAAkB,IAAa;MACjDC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK,CAAC;MACvD,IACE,IAAI,CAACkE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK,IAAI,IAAI,IAC1C,IAAI,CAACkE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK,IAAI,EAAE,EACxC;QACA,IAAI,CAACoE,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,YAAY,CAAC;QACjD,OAAO,KAAK;MACd;MACA,IAAI,CAACpB,QAAQ,GAAG,CAACc,IAAI,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI,IAAI,CAACA,QAAQ,CAACqB,MAAM,IAAI,CAAC,EAAE;QAC7B,IAAI,CAACF,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,aAAa,CAAC;QAClD,OAAO,KAAK;MACd;MACA;MACA,MAAME,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;MAClC,IAAIR,IAAI,CAACS,IAAI,GAAGD,OAAO,EAAE;QACvB,IAAI,CAACH,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,cAAc,CAAC;QACnD,OAAO,KAAK;MACd;MACA,IAAI,CAACI,UAAU,EAAE;MACjB,OAAO,KAAK;IACd,CAAC;IA9aC,IAAI,CAACpB,eAAe,GAAG,IAAI,CAACjB,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE;EACvD;EAEA;;;EAGAa,QAAQA,CAAA;IACN,OAAO;MACLvD,EAAE,EAAE,IAAI/B,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MACjD;MACAC,QAAQ,EAAE,IAAIxF,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9EC,QAAQ,EAAE,IAAI3F,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9EE,UAAU,EAAE,IAAI5F,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAACyF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjFG,GAAG,EAAE,IAAI7F,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACzEI,IAAI,EAAE,IAAI9F,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACrDK,QAAQ,EAAE,IAAI/F,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAEzD;MACAM,YAAY,EAAE,IAAIhG,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwF,QAAQ,CAAC,CAAC;MACxDQ,YAAY,EAAE,IAAIjG,WAAW,CAAC,EAAE,CAAC;MACjCkG,cAAc,EAAE,IAAIlG,WAAW,CAAC,EAAE,CAAC;MACnCmG,YAAY,EAAE,IAAInG,WAAW,CAAC,EAAE,CAAC;MACjCoG,YAAY,EAAE,IAAIpG,WAAW,CAAC,EAAE,CAAC;MACjCqG,cAAc,EAAE,IAAIrG,WAAW,CAAC,EAAE,CAAC;MACnCsG,cAAc,EAAE,IAAItG,WAAW,CAAC,EAAE,CAAC;MACnCuG,cAAc,EAAE,IAAIvG,WAAW,CAAC,EAAE,CAAC;MACnCwG,gBAAgB,EAAE,IAAIxG,WAAW,CAAC,EAAE,CAAC;MACrCyG,iBAAiB,EAAE,IAAIzG,WAAW,CAAC,EAAE,CAAC;MACtC0G,iBAAiB,EAAE,IAAI1G,WAAW,CAAC,EAAE,CAAC;MACtC2G,mBAAmB,EAAE,IAAI3G,WAAW,CAAC,EAAE,CAAC;MACxC4G,YAAY,EAAE,IAAI5G,WAAW,CAAC,EAAE,CAAC;MACjC6G,YAAY,EAAE,IAAI7G,WAAW,CAAC,EAAE,CAAC;MACjC8G,cAAc,EAAE,IAAI9G,WAAW,CAAC,EAAE,CAAC;MAEnC;MACA+G,UAAU,EAAE,IAAI/G,WAAW,CAAC,EAAE,CAAC;MAC/BgH,UAAU,EAAE,IAAIhH,WAAW,CAAC,EAAE,CAAC;MAC/BiH,YAAY,EAAE,IAAIjH,WAAW,CAAC,EAAE,CAAC;MACjCkH,eAAe,EAAE,IAAIlH,WAAW,CAAC,EAAE,CAAC;MACpCmH,eAAe,EAAE,IAAInH,WAAW,CAAC,EAAE,CAAC;MACpCoH,iBAAiB,EAAE,IAAIpH,WAAW,CAAC,EAAE,CAAC;MACtCqH,cAAc,EAAE,IAAIrH,WAAW,CAAC,EAAE,CAAC;MACnCsH,cAAc,EAAE,IAAItH,WAAW,CAAC,EAAE,CAAC;MACnCuH,gBAAgB,EAAE,IAAIvH,WAAW,CAAC,EAAE,CAAC;MAErC;MACAwH,QAAQ,EAAE,IAAIxH,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAC5DC,OAAO,EAAE,IAAI1H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAC3DE,UAAU,EAAE,IAAI3H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MAC1EG,SAAS,EAAE,IAAI5H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACzEI,SAAS,EAAE,IAAI7H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACzEK,QAAQ,EAAE,IAAI9H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACxEM,GAAG,EAAE,IAAI/H,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACnEO,GAAG,EAAE,IAAIhI,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MACnEQ,YAAY,EAAE,IAAIjI,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MAC5ES,YAAY,EAAE,IAAIlI,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACwH,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;MAE5E;MACAU,MAAM,EAAE,IAAInI,WAAW,CAAC,EAAE,EAAE,CAACC,UAAU,CAACyF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACvD0C,WAAW,EAAE,IAAIpI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MAC1D3C,WAAW,EAAE,IAAI5C,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MAC1D8C,YAAY,EAAE,IAAIrI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MAC3D+C,YAAY,EAAE,IAAItI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MAC3DgD,QAAQ,EAAE,IAAIvI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa,CAAC;MACvDiD,OAAO,EAAE,IAAIxI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACsF,aAAa;KACtD;EACH;EAEA;;;EAGMkD,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV;MACA,MAAMD,KAAI,CAACE,gBAAgB,EAAE;MAE7B,IAAIF,KAAI,CAACG,SAAS,CAAC,OAAO,CAAC,KAAK9I,YAAY,CAAC+I,MAAM,EAAE;QACnDJ,KAAI,CAACpF,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;QACnCoF,KAAI,CAACzF,iBAAiB,CAAC8F,GAAG,CAAC,UAAU,GAAGL,KAAI,CAACG,SAAS,CAAC,IAAI,CAAC,EAAEH,KAAI,CAAC1F,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACuE,IAAI,CAAEC,GAAsB,IAAI;UAC5H,IAAIA,GAAG,CAACC,EAAE,EAAE;YACVR,KAAI,CAAC5D,QAAQ,CAACqE,UAAU,CAACF,GAAG,CAACG,IAAI,CAAC;UACpC,CAAC,MAAM;YACLV,KAAI,CAAC1D,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,cAAc,CAAC;YACnDyD,KAAI,CAACW,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIX,KAAI,CAACG,SAAS,CAAC,OAAO,CAAC,KAAK9I,YAAY,CAACuJ,MAAM,EAAE;QAC1D;QACAZ,KAAI,CAACzF,iBAAiB,CAAC8F,GAAG,CAAC,UAAU,GAAGL,KAAI,CAACG,SAAS,CAAC,IAAI,CAAC,EAAEH,KAAI,CAAC1F,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACuE,IAAI,CAAEC,GAAsB,IAAI;UAC5H,IAAIA,GAAG,CAACC,EAAE,EAAE;YACVR,KAAI,CAAC5D,QAAQ,CAACqE,UAAU,CAACF,GAAG,CAACG,IAAI,CAAC;YAClC;YACAV,KAAI,CAACa,WAAW,EAAE;UACpB,CAAC,MAAM;YACLb,KAAI,CAAC1D,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,cAAc,CAAC;YACnDyD,KAAI,CAACW,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ;IAAC;EACH;EAEA;;;EAGMT,gBAAgBA,CAAA;IAAA,IAAAY,MAAA;IAAA,OAAAb,iBAAA;MACpB,IAAI;QACF,MAAMc,OAAO,CAACC,GAAG,CAAC,CAChBF,MAAI,CAACG,cAAc,EAAE,EACrBH,MAAI,CAACI,cAAc,EAAE,EACrBJ,MAAI,CAACK,gBAAgB,EAAE,EACvBL,MAAI,CAACM,mBAAmB,EAAE,EAC1BN,MAAI,CAACO,aAAa,EAAE,CACrB,CAAC;MACJ,CAAC,CAAC,OAAO9E,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IAAC;EACH;EAEA;;;EAGA0E,cAAcA,CAAA;IACZ,OAAO,IAAIF,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,KAAK,GAAG;QAAEC,IAAI,EAAE;MAAuB,CAAE;MAC/C,MAAMC,WAAW,GAAG;QAAEhB,IAAI,EAAEc,KAAK;QAAEG,IAAI,EAAE,CAAC;QAAEjF,IAAI,EAAE;MAAI,CAAE;MAExD,IAAI,CAACnC,iBAAiB,CAACqH,IAAI,CAAC,sBAAsB,EAAEF,WAAW,EAAE,IAAI,CAACpH,GAAG,CAACwB,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,CAC9FuE,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAAC1F,cAAc,GAAGyF,GAAG,CAACG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;YACzD5J,KAAK,EAAE4J,IAAI,CAACC,IAAI;YAChB9J,KAAK,EAAE6J,IAAI,CAACE,IAAI;YAChBC,KAAK,EAAEH,IAAI,CAACI;WACb,CAAC,CAAC;UACHb,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAAChB,GAAG,CAAC6B,GAAG,CAAC;QACjB;MACF,CAAC,CAAC,CACDC,KAAK,CAACd,MAAM,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA;;;EAGAL,cAAcA,CAAA;IACZ,OAAO,IAAIH,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,KAAK,GAAG;QAAEC,IAAI,EAAE;MAAuB,CAAE;MAC/C,MAAMC,WAAW,GAAG;QAAEhB,IAAI,EAAEc,KAAK;QAAEG,IAAI,EAAE,CAAC;QAAEjF,IAAI,EAAE;MAAI,CAAE;MAExD,IAAI,CAACnC,iBAAiB,CAACqH,IAAI,CAAC,sBAAsB,EAAEF,WAAW,EAAE,IAAI,CAACpH,GAAG,CAACwB,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,CAC9FuE,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACzF,cAAc,GAAGwF,GAAG,CAACG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;YACzD5J,KAAK,EAAE4J,IAAI,CAACC,IAAI;YAChB9J,KAAK,EAAE6J,IAAI,CAACE,IAAI;YAChBC,KAAK,EAAEH,IAAI,CAACI;WACb,CAAC,CAAC;UACHb,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAAChB,GAAG,CAAC6B,GAAG,CAAC;QACjB;MACF,CAAC,CAAC,CACDC,KAAK,CAACd,MAAM,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA;;;EAGAJ,gBAAgBA,CAAA;IACd,OAAO,IAAIJ,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,KAAK,GAAG;QAAEC,IAAI,EAAE;MAAyB,CAAE;MACjD,MAAMC,WAAW,GAAG;QAAEhB,IAAI,EAAEc,KAAK;QAAEG,IAAI,EAAE,CAAC;QAAEjF,IAAI,EAAE;MAAI,CAAE;MAExD,IAAI,CAACnC,iBAAiB,CAACqH,IAAI,CAAC,sBAAsB,EAAEF,WAAW,EAAE,IAAI,CAACpH,GAAG,CAACwB,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,CAC9FuE,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACxF,gBAAgB,GAAGuF,GAAG,CAACG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;YAC3D5J,KAAK,EAAE4J,IAAI,CAACC,IAAI;YAChB9J,KAAK,EAAE6J,IAAI,CAACE,IAAI;YAChBC,KAAK,EAAEH,IAAI,CAACI;WACb,CAAC,CAAC;UACHb,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAAChB,GAAG,CAAC6B,GAAG,CAAC;QACjB;MACF,CAAC,CAAC,CACDC,KAAK,CAACd,MAAM,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA;;;EAGAH,mBAAmBA,CAAA;IACjB,OAAO,IAAIL,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,KAAK,GAAG;QAAEC,IAAI,EAAE;MAA4B,CAAE;MACpD,MAAMC,WAAW,GAAG;QAAEhB,IAAI,EAAEc,KAAK;QAAEG,IAAI,EAAE,CAAC;QAAEjF,IAAI,EAAE;MAAI,CAAE;MAExD,IAAI,CAACnC,iBAAiB,CAACqH,IAAI,CAAC,sBAAsB,EAAEF,WAAW,EAAE,IAAI,CAACpH,GAAG,CAACwB,WAAW,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,CAC9FuE,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACvF,mBAAmB,GAAGsF,GAAG,CAACG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;YAC9D5J,KAAK,EAAE4J,IAAI,CAACC,IAAI;YAChB9J,KAAK,EAAE6J,IAAI,CAACE,IAAI;YAChBC,KAAK,EAAEH,IAAI,CAACI;WACb,CAAC,CAAC;UACHb,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAAChB,GAAG,CAAC6B,GAAG,CAAC;QACjB;MACF,CAAC,CAAC,CACDC,KAAK,CAACd,MAAM,CAAC;IAClB,CAAC,CAAC;EACJ;EAIA;;;EAGAF,aAAaA,CAAA;IACX,OAAO,IAAIN,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMG,WAAW,GAAG;QAAEC,IAAI,EAAE,CAAC;QAAEjF,IAAI,EAAE;MAAI,CAAE;MAE3C,IAAI,CAACnC,iBAAiB,CAACqH,IAAI,CAAC,qBAAqB,EAAEF,WAAW,EAAE,IAAI,CAACpH,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAC5FuE,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACtF,YAAY,GAAGqF,GAAG,CAACG,IAAI,CAACmB,OAAO,CAACC,GAAG,CAAEC,IAAS,KAAM;YACvD5J,KAAK,EAAE4J,IAAI,CAACzD,UAAU;YACtBpG,KAAK,EAAE6J,IAAI,CAAC1D,UAAU;YACtB,GAAG0D;WACJ,CAAC,CAAC;UACHT,OAAO,EAAE;QACX,CAAC,MAAM;UACLC,MAAM,CAAChB,GAAG,CAAC6B,GAAG,CAAC;QACjB;MACF,CAAC,CAAC,CACDC,KAAK,CAACd,MAAM,CAAC;IAClB,CAAC,CAAC;EACJ;EAEA;;;EAGAe,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,SAAS;IACrB,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACpG,QAAQ,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,QAAQ,CAACC,QAAQ,CAACmG,CAAC,CAAC,CAACC,WAAW,EAAE;MACvC,IAAI,CAACrG,QAAQ,CAACC,QAAQ,CAACmG,CAAC,CAAC,CAACE,sBAAsB,EAAE;IACpD;IACA,IAAI,IAAI,CAACtG,QAAQ,CAACuG,OAAO,EAAE;MACzB;IACF;IACA,MAAMtJ,EAAE,GAAG,IAAI,CAACoC,aAAa,CAACmH,SAAS,EAAE,CAACC,WAAW,CAAC,WAAW,CAAC;IAClE,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAAC3C,SAAS,CAAC,OAAO,CAAC,KAAK9I,YAAY,CAAC0L,GAAG,EAAE;MAChD,IAAI,CAAC3G,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;MACjC,IAAI,CAACzI,iBAAiB,CAACqH,IAAI,CAACW,GAAG,EAAE,IAAI,CAACnG,QAAQ,CAAC6G,WAAW,EAAE,EAAE,IAAI,CAAC3I,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACuE,IAAI,CAAEC,GAAsB,IAAI;QAC5H,IAAI,CAAC9E,aAAa,CAACmH,SAAS,EAAE,CAACM,UAAU,CAAC7J,EAAE,CAAC;QAC7C,IAAI,CAACyJ,OAAO,GAAG,KAAK;QACpB,IAAIvC,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAAClE,SAAS,CAAClF,aAAa,CAAC+L,OAAO,EAAE,OAAO,CAAC;UAC9C;UACA,IAAI,CAACC,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;UACvC;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC1C,YAAY,CAAC,EAAE,CAAC;UACvB,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACL,IAAI,CAACrE,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC7H,iBAAiB,CAAC+I,GAAG,CAACf,GAAG,EAAE,IAAI,CAACnG,QAAQ,CAAC6G,WAAW,EAAE,EAAE,IAAI,CAAC3I,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CAACuE,IAAI,CAAEC,GAAsB,IAAI;QAC3H,IAAI,CAAC9E,aAAa,CAACmH,SAAS,EAAE,CAACM,UAAU,CAAC7J,EAAE,CAAC;QAC7C,IAAI,CAACyJ,OAAO,GAAG,KAAK;QACpB,IAAIvC,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAAClE,SAAS,CAAClF,aAAa,CAAC+L,OAAO,EAAE,OAAO,CAAC;UAC9C;UACA,IAAI,CAACC,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;UACvC;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC1C,YAAY,CAAC,EAAE,CAAC;UACvB,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACL,IAAI,CAACrE,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ;EACF;EAEAmB,OAAOA,CAAA;IACL,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MACpB,OAAO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAACpD,IAAI,CAACqD,KAAK,IAAG;QACxG,QAAQA,KAAK;UACX,KAAKxM,gBAAgB,CAACyM,GAAG;YAAI;YAC3B,IAAI,CAACtB,QAAQ,EAAE;YACf,OAAO,IAAI;UACb,KAAKnL,gBAAgB,CAAC0M,EAAE;YAAK;YAC3B,IAAI,CAAClD,YAAY,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI;UACb,KAAKxJ,gBAAgB,CAAC2M,MAAM;YAAE;YAC5B,OAAO,KAAK;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACnD,YAAY,CAAC,EAAE,CAAC;MACrB,OAAO,IAAI;IACb;EACF;EAEAoD,mBAAmBA,CAACC,SAAiB;IACnC,OAAO,IAAI,CAACpJ,gBAAgB,CAACoJ,SAAS,CAAC;EACzC;EAEA;;;EAGAC,kBAAkBA,CAAC/L,KAAa;IAC9B,MAAMgM,YAAY,GAAG,IAAI,CAACpJ,cAAc,CAACqJ,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAAC7J,KAAK,KAAKA,KAAK,CAAC;IAC3E,IAAIgM,YAAY,EAAE;MAChB,IAAI,CAAC9H,QAAQ,CAACqE,UAAU,CAAC;QACvBnD,YAAY,EAAE4G,YAAY,CAAChM,KAAK;QAChCqF,YAAY,EAAE2G,YAAY,CAAC/L,KAAK;QAChCqF,cAAc,EAAE0G,YAAY,CAAChC;OAC9B,CAAC;IACJ;EACF;EAEA;;;EAGAkC,kBAAkBA,CAAClM,KAAa;IAC9B,MAAMmM,YAAY,GAAG,IAAI,CAACtJ,cAAc,CAACoJ,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAAC7J,KAAK,KAAKA,KAAK,CAAC;IAC3E,IAAImM,YAAY,EAAE;MAChB,IAAI,CAACjI,QAAQ,CAACqE,UAAU,CAAC;QACvBhD,YAAY,EAAE4G,YAAY,CAACnM,KAAK;QAChCwF,YAAY,EAAE2G,YAAY,CAAClM,KAAK;QAChCwF,cAAc,EAAE0G,YAAY,CAACnC;OAC9B,CAAC;IACJ;EACF;EAEA;;;EAGAoC,oBAAoBA,CAACpM,KAAa;IAChC,MAAMqM,cAAc,GAAG,IAAI,CAACvJ,gBAAgB,CAACmJ,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAAC7J,KAAK,KAAKA,KAAK,CAAC;IAC/E,IAAIqM,cAAc,EAAE;MAClB,IAAI,CAACnI,QAAQ,CAACqE,UAAU,CAAC;QACvB7C,cAAc,EAAE2G,cAAc,CAACrM,KAAK;QACpC2F,cAAc,EAAE0G,cAAc,CAACpM,KAAK;QACpC2F,gBAAgB,EAAEyG,cAAc,CAACrC;OAClC,CAAC;IACJ;EACF;EAEA;;;EAGAsC,uBAAuBA,CAACtM,KAAa;IACnC,MAAMuM,iBAAiB,GAAG,IAAI,CAACxJ,mBAAmB,CAACkJ,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAAC7J,KAAK,KAAKA,KAAK,CAAC;IACrF,IAAIuM,iBAAiB,EAAE;MACrB,IAAI,CAACrI,QAAQ,CAACqE,UAAU,CAAC;QACvB1C,iBAAiB,EAAE0G,iBAAiB,CAACvM,KAAK;QAC1C8F,iBAAiB,EAAEyG,iBAAiB,CAACtM,KAAK;QAC1C8F,mBAAmB,EAAEwG,iBAAiB,CAACvC;OACxC,CAAC;IACJ;EACF;EAGA;;;EAGAwC,gBAAgBA,CAACxM,KAAa;IAC5B,MAAMyM,YAAY,GAAG,IAAI,CAACzJ,YAAY,CAACiJ,IAAI,CAACpC,IAAI,IAAIA,IAAI,CAAC7J,KAAK,KAAKA,KAAK,CAAC;IACzE,IAAIyM,YAAY,EAAE;MAChB,IAAI,CAACvI,QAAQ,CAACqE,UAAU,CAAC;QACvBpC,UAAU,EAAEsG,YAAY,CAACtG,UAAU;QACnCC,UAAU,EAAEqG,YAAY,CAACrG,UAAU;QACnCC,YAAY,EAAEoG,YAAY,CAACpG,YAAY;QACvCC,eAAe,EAAEmG,YAAY,CAACnG,eAAe;QAC7CC,eAAe,EAAEkG,YAAY,CAAClG,eAAe;QAC7CC,iBAAiB,EAAEiG,YAAY,CAACjG,iBAAiB;QACjDC,cAAc,EAAEgG,YAAY,CAAChG,cAAc;QAC3CC,cAAc,EAAE+F,YAAY,CAAC/F,cAAc;QAC3CC,gBAAgB,EAAE8F,YAAY,CAAC9F;OAChC,CAAC;IACJ;EACF;EAEA;;;EAGA+F,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAACxI,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK,EAAE;MACtC,IAAI,CAAC2I,WAAW,EAAE;IACpB;EACF;EA8BA;EACAlE,UAAUA,CAAA;IACR,IAAIkI,IAAI,GAAG,IAAI;IACf,IAAI,CAACzJ,aAAa,GAAG,IAAI;IACzB,MAAM0J,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC7J,QAAQ,CAAC,CAAC,CAAQ,CAAC;IAChD2J,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC5I,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK,CAAC;IAC3D4M,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;IACtCF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC;IACjDF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC;IACjDF,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC;IACnDF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,YAAY,CAAC;IAC/CF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,YAAY,CAAC;IAC/CF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC;IACjDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC;IAChDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC;IAChDF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAE,YAAY,CAAC;IAClD,MAAMC,OAAO,GAAG,IAAIvN,WAAW,CAAC;MAC9B,aAAa,EAAE,IAAI,CAAC8D;KACrB,CAAC;IACF,MAAM0J,GAAG,GAAG,IAAIvN,WAAW,CAAC,MAAM,EAAE,IAAI,CAACiE,aAAa,EAAEkJ,QAAQ,EAAE;MAChEG,OAAO,EAAEA,OAAO;MAChBE,cAAc,EAAE,IAAI;MAAE;MACtBC,eAAe,EAAE,KAAK,CAAE;KACzB,CAAC;IACF,IAAI,CAAC3K,IAAI,CACN4K,OAAO,CAACH,GAAG,CAAC,CACZI,IAAI,CAACzN,MAAM,CAAE0N,CAAC,IAAKA,CAAC,YAAY3N,YAAY,CAAC,CAAC,CAC9C4N,SAAS,CAAC;MACTC,IAAIA,CAACC,GAAG;QACNb,IAAI,CAACzJ,aAAa,GAAG,KAAK;QAC1ByJ,IAAI,CAAC1J,QAAQ,GAAG,EAAE;QAClB,IAAIuK,GAAG,CAAC,IAAI,CAAC,EAAE;UACb,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;YACrBb,IAAI,CAACvI,SAAS,CAAClF,aAAa,CAAC+L,OAAO,EAAE,QAAQ,CAAC;YAC/C0B,IAAI,CAAChE,WAAW,EAAE;UACpB,CAAC,MAAM;YACLgE,IAAI,CAACvI,SAAS,CACZlF,aAAa,CAACmF,KAAK,EACnBmJ,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,QAAQ,CACjC;UACH;QACF,CAAC,MAAM;UACLb,IAAI,CAACvI,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEmJ,GAAG,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC;QAC7D;MACF,CAAC;MACDnJ,KAAKA,CAACoJ,GAAG;QACPd,IAAI,CAACzJ,aAAa,GAAG,KAAK;QAC1ByJ,IAAI,CAACvI,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEoJ,GAAG,CAACvD,GAAG,CAAC;MAC9C;KACD,CAAC;EACN;EAEA;EACAvB,WAAWA,CAAA;IACT,IAAI,CAACxF,WAAW,GAAG,IAAI;IACvB,IAAIqG,WAAW,GAAG;MAChBkE,IAAI,EAAE,IAAI,CAACxJ,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAACnE,KAAK;MACxC2N,MAAM,EAAE,WAAW;MACnBC,gBAAgB,EAAE;KACnB;IACD,IAAI,CAACxK,SAAS,CAACyK,SAAS,EAAE;IAC1B,IAAI,CAACxL,iBAAiB,CACnBqH,IAAI,CAAC,mBAAmB,EAAE;MAAC,MAAM,EAAEF;IAAW,CAAC,EAAE,IAAI,CAACnG,eAAe,CAAC,CACtE+E,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAClF,SAAS,CAAC0K,SAAS,CAACzF,GAAG,CAACG,IAAI,IAAI,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACpE,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACD6D,OAAO,CAAC,MAAK;MACZ,IAAI,CAAC5K,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;EACN;EAEA;EACAjC,SAASA,CAACC,EAAE;IACV,IAAI,CAACkB,iBAAiB,CACnB2L,MAAM,CAAC,eAAe,GAAG7M,EAAE,EAAE,IAAI,CAACkC,eAAe,CAAC,CAClD+E,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAClE,SAAS,CAAClF,aAAa,CAAC+L,OAAO,EAAE,MAAM,CAAC;QAC7C,IAAI,CAACtC,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACvE,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;EACA3I,cAAcA,CAACJ,EAAE;IACf,IAAI,CAACmB,aAAa,CAAC2L,YAAY,CAAC,IAAI,CAAC7L,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,EAAE,CAAC1C,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAACiH,IAAI,CAAEC,GAAsB,IAAI;MACjH,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,MAAM4F,WAAW,GAAG7F,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI0F,WAAW,EAAE;UACfC,MAAM,CAACC,IAAI,CAACF,WAAW,EAAE,QAAQ,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAAC9J,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;EAEA;EACA7I,OAAOA,CAACmH,IAAI;IACV,IAAI6F,SAAS,GAAG7F,IAAI,CAAC,kBAAkB,CAAC;IACxC,MAAM8F,aAAa,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAChD,IAAI,CAAClM,aAAa,CAAC2L,YAAY,CAAC,IAAI,CAAC7L,GAAG,CAACwB,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,EAAE,CAAC2E,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAACJ,IAAI,CAAEC,GAAsB,IAAI;MACpI,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,MAAM4F,WAAW,GAAG7F,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI0F,WAAW,EAAE;UACf,IAAIO,UAAU,GAAG,IAAI,CAACrM,GAAG,CAACqM,UAAU,GAAGC,IAAI,CAACR,WAAW,CAAC;UACxDC,MAAM,CAACC,IAAI,CAACK,UAAU,EAAE,QAAQ,CAAC;QACnC,CAAC,MAAM;UACL,IAAI,CAACrK,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAE,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,SAAS,CAAClF,aAAa,CAACmF,KAAK,EAAEgE,GAAG,CAAC6B,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;;;uBAplBWjI,mBAAmB,EAAArC,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAjP,EAAA,CAAA+O,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAnP,EAAA,CAAA+O,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAArP,EAAA,CAAA+O,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvP,EAAA,CAAA+O,iBAAA,CAAAS,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAnBpN,mBAAmB;MAAAqN,SAAA;MAAAC,QAAA,GAAA3P,EAAA,CAAA4P,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCbxBlQ,EALR,CAAAU,cAAA,iBAAwE,aAC9D,gBACc,UACb,gBAG6C;UADTV,EAAA,CAAAa,UAAA,mBAAAuP,qDAAA;YAAApQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAS8O,GAAA,CAAA3F,QAAA,EAAU;UAAA,EAAC;UAEzDxK,EAAA,CAAAW,MAAA,GACF;;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAGTZ,EAAA,CAAAU,cAAA,gBAA4G;UAA1CV,EAAA,CAAAa,UAAA,mBAAAyP,qDAAA;YAAAtQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAS8O,GAAA,CAAA1E,OAAA,EAAS;UAAA,EAAC;UACnFzL,EAAA,CAAAW,MAAA,GACF;;UAGNX,EAHM,CAAAY,YAAA,EAAS,EACL,EACC,EACF;UASDZ,EANR,CAAAU,cAAA,eAA+D,cAG9B,cACN,oBACP,wBACmC;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACnEZ,EAAA,CAAAU,cAAA,0BAA6C;UAC3CV,EAAA,CAAAC,SAAA,iBAC8D;UAGpED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,wBACmC;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACnEZ,EAAA,CAAAU,cAAA,2BAA6C;UAC3CV,EAAA,CAAAC,SAAA,iBAC8D;UAGpED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,wBACmC;UAAAV,EAAA,CAAAW,MAAA,4CAAM;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACrEZ,EAAA,CAAAU,cAAA,2BAAgD;UAC9CV,EAAA,CAAAC,SAAA,iBAC+D;UAGrED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,wBACmC;UAAAV,EAAA,CAAAW,MAAA,WAAG;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAClEZ,EAAA,CAAAU,cAAA,2BAA4C;UAC1CV,EAAA,CAAAC,SAAA,iBAC8D;UAGpED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,YAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACxDZ,EAAA,CAAAU,cAAA,2BAAqC;UACnCV,EAAA,CAAAC,SAAA,iBAC8D;UAGpED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACxDZ,EAAA,CAAAU,cAAA,2BAAqC;UACnCV,EAAA,CAAAC,SAAA,iBAC8D;UAGpED,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,wBACmC;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEjEZ,EADF,CAAAU,cAAA,2BAAsC,qBAGoB;UAA7CV,EAAA,CAAAa,UAAA,2BAAA0P,iEAAAC,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAiB8O,GAAA,CAAAhE,kBAAA,CAAAqE,MAAA,CAA0B;UAAA,EAAC;UACrDxQ,EAAA,CAAAyQ,UAAA,KAAAC,yCAAA,wBAA6F;UAIrG1Q,EAHM,CAAAY,YAAA,EAAY,EACI,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEtDZ,EADF,CAAAU,cAAA,uBAAiB,qBAGyC;UAA7CV,EAAA,CAAAa,UAAA,2BAAA8P,iEAAAH,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAiB8O,GAAA,CAAA7D,kBAAA,CAAAkE,MAAA,CAA0B;UAAA,EAAC;UACrDxQ,EAAA,CAAAyQ,UAAA,KAAAG,yCAAA,wBAA6F;UAIrG5Q,EAHM,CAAAY,YAAA,EAAY,EACI,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEtDZ,EADF,CAAAU,cAAA,uBAAiB,qBAG2C;UAA/CV,EAAA,CAAAa,UAAA,2BAAAgQ,iEAAAL,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAiB8O,GAAA,CAAA3D,oBAAA,CAAAgE,MAAA,CAA4B;UAAA,EAAC;UACvDxQ,EAAA,CAAAyQ,UAAA,KAAAK,yCAAA,wBAA+F;UAIvG9Q,EAHM,CAAAY,YAAA,EAAY,EACI,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEtDZ,EADF,CAAAU,cAAA,uBAAiB,qBAG8C;UAAlDV,EAAA,CAAAa,UAAA,2BAAAkQ,iEAAAP,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAiB8O,GAAA,CAAAzD,uBAAA,CAAA8D,MAAA,CAA+B;UAAA,EAAC;UAC1DxQ,EAAA,CAAAyQ,UAAA,KAAAO,yCAAA,wBAAkG;UAI1GhR,EAHM,CAAAY,YAAA,EAAY,EACI,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,kCAAM;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC1DZ,EAAA,CAAAU,cAAA,uBAAiB;UACfV,EAAA,CAAAC,SAAA,4BASmB;UAGzBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEtDZ,EADF,CAAAU,cAAA,uBAAiB,qBAGuC;UAA3CV,EAAA,CAAAa,UAAA,2BAAAoQ,iEAAAT,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAA,OAAArQ,EAAA,CAAAqB,WAAA,CAAiB8O,GAAA,CAAAvD,gBAAA,CAAA4D,MAAA,CAAwB;UAAA,EAAC;UACnDxQ,EAAA,CAAAyQ,UAAA,KAAAS,yCAAA,wBAA2F;UAInGlR,EAHM,CAAAY,YAAA,EAAY,EACI,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,gCAAI;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACxDZ,EAAA,CAAAU,cAAA,2BAAqC;UACnCV,EAAA,CAAAC,SAAA,2BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,cAAuB,oBACP,yBACwB;UAAAV,EAAA,CAAAW,MAAA,wCAAO;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC3DZ,EAAA,CAAAU,cAAA,2BAAqC;UACnCV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,oCAAO;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC3DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,wBAAK;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACzDZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,wBAAK;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UACzDZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,oCAAO;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC3DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,oCAAO;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC3DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,oCAAO;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC3DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,qCAAQ;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC5DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,eAAuB,qBACP,0BACwB;UAAAV,EAAA,CAAAW,MAAA,qCAAQ;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAC5DZ,EAAA,CAAAU,cAAA,4BAA8C;UAC5CV,EAAA,CAAAC,SAAA,4BAEkB;UAGxBD,EAFI,CAAAY,YAAA,EAAkB,EACL,EACX;UAIFZ,EAFJ,CAAAU,cAAA,gBAAwB,qBACR,0BACwB;UAAAV,EAAA,CAAAW,MAAA,qBAAE;UAAAX,EAAA,CAAAY,YAAA,EAAgB;UAEpDZ,EADF,CAAAU,cAAA,4BAAqC,qBAG8B;UACjEV,EAAA,CAAAW,MAAA;UAMVX,EANU,CAAAY,YAAA,EAAW,EACK,EACL,EACX,EACF,EAED;UAKHZ,EAFJ,CAAAU,cAAA,WAAI,gBACuB,gBAC8B;UAAAV,EAAA,CAAAC,SAAA,iBAAyF;UAAAD,EAAA,CAAAW,MAAA,uCAAU;UAAAX,EAAA,CAAAY,YAAA,EAAM;UAC9JZ,EAAA,CAAAU,cAAA,sBAAoG;UAAzFV,EAAA,CAAAmR,gBAAA,8BAAAC,qEAAAZ,MAAA;YAAAxQ,EAAA,CAAAgB,aAAA,CAAAqP,GAAA;YAAArQ,EAAA,CAAAqR,kBAAA,CAAAlB,GAAA,CAAA9M,QAAA,EAAAmN,MAAA,MAAAL,GAAA,CAAA9M,QAAA,GAAAmN,MAAA;YAAA,OAAAxQ,EAAA,CAAAqB,WAAA,CAAAmP,MAAA;UAAA,EAAyB;UAClCxQ,EAAA,CAAAU,cAAA,mBAA6E;UAC3EV,EAAA,CAAAC,SAAA,iBAAuD;UACvDD,EAAA,CAAAU,cAAA,aAAM;UAAAV,EAAA,CAAAW,MAAA,qBAAE;UAIhBX,EAJgB,CAAAY,YAAA,EAAO,EACR,EACC,EACR,EACH;UAMCZ,EALN,CAAAU,cAAA,wBACyD,cAChD,WACD,eAEwF;UACxFV,EAAA,CAAAW,MAAA,uBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAU,cAAA,eAA2F;UACzFV,EAAA,CAAAW,MAAA,mCACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAU,cAAA,eAA2F;UACzFV,EAAA,CAAAW,MAAA,6BACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAU,cAAA,eAA2F;UACzFV,EAAA,CAAAW,MAAA,mCACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAELZ,EAAA,CAAAU,cAAA,eAA8E;UAC5EV,EAAA,CAAAW,MAAA,uBACF;UAGJX,EAHI,CAAAY,YAAA,EAAK,EAEF,EACC;UACRZ,EAAA,CAAAU,cAAA,cAAO;UACLV,EAAA,CAAAyQ,UAAA,MAAAa,mCAAA,mBAAsD;UAqB5DtR,EAFI,CAAAY,YAAA,EAAQ,EACC,EACH;;;;UAhWyBZ,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAAuR,eAAA,KAAAC,GAAA,EAAoC;UAK7CxR,EAAA,CAAA6B,SAAA,GAAoB;UAC9B7B,EADU,CAAAE,UAAA,qBAAoB,cAAAiQ,GAAA,CAAAnF,OAAA,CAA2C,aAAAmF,GAAA,CAAAlE,mBAAA,QAClC;UAC7CjM,EAAA,CAAA6B,SAAA,EACF;UADE7B,EAAA,CAAAyR,kBAAA,MAAAzR,EAAA,CAAA0R,WAAA,wBACF;UAG6C1R,EAAA,CAAA6B,SAAA,GAAoB;UAAqB7B,EAAzC,CAAAE,UAAA,qBAAoB,cAAAiQ,GAAA,CAAAnF,OAAA,CAA0C;UACzGhL,EAAA,CAAA6B,SAAA,EACF;UADE7B,EAAA,CAAAyR,kBAAA,MAAAzR,EAAA,CAAA0R,WAAA,0BACF;UAMkC1R,EAAA,CAAA6B,SAAA,GAAsB;UAAtB7B,EAAA,CAAAE,UAAA,cAAAiQ,GAAA,CAAA7L,QAAA,CAAsB;UAGhDtE,EAAA,CAAA6B,SAAA,EAAkB;UAAlB7B,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAAuR,eAAA,KAAAI,GAAA,EAAkB;UAMf3R,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUvCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUvCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUvCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUvCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUvCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAUnCjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAEpBjM,EAAA,CAAA6B,SAAA,EAAiB;UAAjB7B,EAAA,CAAAE,UAAA,YAAAiQ,GAAA,CAAAnN,cAAA,CAAiB;UAWpChD,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAEpBjM,EAAA,CAAA6B,SAAA,EAAiB;UAAjB7B,EAAA,CAAAE,UAAA,YAAAiQ,GAAA,CAAAlN,cAAA,CAAiB;UAWpCjD,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAEpBjM,EAAA,CAAA6B,SAAA,EAAmB;UAAnB7B,EAAA,CAAAE,UAAA,YAAAiQ,GAAA,CAAAjN,gBAAA,CAAmB;UAWtClD,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAEpBjM,EAAA,CAAA6B,SAAA,EAAsB;UAAtB7B,EAAA,CAAAE,UAAA,YAAAiQ,GAAA,CAAAhN,mBAAA,CAAsB;UAWlCnD,EAAA,CAAA6B,SAAA,GAAgB;UAMhB7B,EANA,CAAAE,UAAA,cAAAF,EAAA,CAAAuR,eAAA,KAAAK,GAAA,EAAgB,iBACA,cAAAzB,GAAA,CAAA7L,QAAA,CACM,eAAA6L,GAAA,CAAAlE,mBAAA,QAImB;UAYhDjM,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAAE,UAAA,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAuC;UAEpBjM,EAAA,CAAA6B,SAAA,EAAe;UAAf7B,EAAA,CAAAE,UAAA,YAAAiQ,GAAA,CAAA/M,YAAA,CAAe;UAW5BpD,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAWrEjM,EAAA,CAAA6B,SAAA,GAAiB;UAAa7B,EAA9B,CAAAE,UAAA,kBAAiB,YAAY,aAAAiQ,GAAA,CAAAlE,mBAAA,QAAwC;UAW5EjM,EAAA,CAAA6B,SAAA,GAAyC;UACzC7B,EADA,CAAAE,UAAA,eAAAF,EAAA,CAAAuR,eAAA,KAAAM,GAAA,EAAyC,aAAA1B,GAAA,CAAAlE,mBAAA,QACF;UAa5CjM,EAAA,CAAA6B,SAAA,GAAyB;UAAzB7B,EAAA,CAAA8R,gBAAA,eAAA3B,GAAA,CAAA9M,QAAA,CAAyB;UAAqCrD,EAApC,CAAAE,UAAA,mBAAAiQ,GAAA,CAAAjM,gBAAA,CAAmC,2BAA2B;UAChDlE,EAAA,CAAA6B,SAAA,EAA2B;UAA3B7B,EAAA,CAAAE,UAAA,cAAAiQ,GAAA,CAAA7M,aAAA,CAA2B;UAO9DtD,EAAA,CAAA6B,SAAA,GAA2B;UAChB7B,EADX,CAAAE,UAAA,4BAA2B,WAAAiQ,GAAA,CAAA3M,SAAA,CAAAuO,QAAA,GAAgC,aAAA/R,EAAA,CAAAuR,eAAA,KAAAS,GAAA,EACjD,cAAA7B,GAAA,CAAA5M,WAAA,CAA0B;UAI9CvD,EAAA,CAAA6B,SAAA,GAAoB;UAApB7B,EAAA,CAAAE,UAAA,qBAAoB;UAIpBF,EAAA,CAAA6B,SAAA,GAAoB;UAApB7B,EAAA,CAAAE,UAAA,qBAAoB;UAIpBF,EAAA,CAAA6B,SAAA,GAAoB;UAApB7B,EAAA,CAAAE,UAAA,qBAAoB;UAIpBF,EAAA,CAAA6B,SAAA,GAAoB;UAApB7B,EAAA,CAAAE,UAAA,qBAAoB;UAWLF,EAAA,CAAA6B,SAAA,GAAkB;UAAlB7B,EAAA,CAAAE,UAAA,YAAA+R,YAAA,CAAArJ,IAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}