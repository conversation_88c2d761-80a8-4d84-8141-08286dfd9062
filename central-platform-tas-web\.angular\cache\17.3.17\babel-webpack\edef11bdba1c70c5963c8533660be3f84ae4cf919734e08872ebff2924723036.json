{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Most data for localization are taken from this page\n// https://www.unicode.org/cldr/charts/32/summary/ms.html\nvar eraValues = {\n  narrow: ['SM', 'M'],\n  abbreviated: ['<PERSON>', 'M'],\n  wide: ['Se<PERSON><PERSON> Masihi', '<PERSON><PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['S1', 'S2', 'S3', 'S4'],\n  wide: ['Suku pertama', 'Suku kedua', 'Suku ketiga', 'Suku keempat']\n};\n\n// Note: in Malay, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'O', 'S', 'O', 'N', 'D'],\n  abbreviated: ['<PERSON>', 'Feb', '<PERSON>', 'Apr', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>go', '<PERSON>', 'Okt', '<PERSON>', 'Dis'],\n  wide: ['Januari', '<PERSON>ruari', 'Mac', 'April', '<PERSON>', 'Jun', 'Julai', 'O<PERSON>', 'September', 'Oktober', 'November', 'Disember']\n};\nvar dayValues = {\n  narrow: ['A', 'I', 'S', 'R', 'K', 'J', 'S'],\n  short: ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],\n  abbreviated: ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],\n  wide: ['Ahad', 'Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat', 'Sabtu']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'tgh malam',\n    noon: 'tgh hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  // Can't use \"pertama\", \"kedua\" because can't be parsed\n  return 'ke-' + Number(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/ms/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Most data for localization are taken from this page\n// https://www.unicode.org/cldr/charts/32/summary/ms.html\nvar eraValues = {\n  narrow: ['SM', 'M'],\n  abbreviated: ['<PERSON>', 'M'],\n  wide: ['Se<PERSON><PERSON> Masihi', '<PERSON><PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['S1', 'S2', 'S3', 'S4'],\n  wide: ['Suku pertama', 'Suku kedua', 'Suku ketiga', 'Suku keempat']\n};\n\n// Note: in Malay, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'O', 'S', 'O', 'N', 'D'],\n  abbreviated: ['<PERSON>', 'Feb', '<PERSON>', 'Apr', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>go', '<PERSON>', 'Okt', '<PERSON>', 'Dis'],\n  wide: ['Januari', '<PERSON>ruari', 'Mac', 'April', '<PERSON>', 'Jun', 'Julai', 'O<PERSON>', 'September', 'Oktober', 'November', 'Disember']\n};\nvar dayValues = {\n  narrow: ['A', 'I', 'S', 'R', 'K', 'J', 'S'],\n  short: ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],\n  abbreviated: ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],\n  wide: ['Ahad', 'Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat', 'Sabtu']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'tgh malam',\n    noon: 'tgh hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'tengah malam',\n    noon: 'tengah hari',\n    morning: 'pagi',\n    afternoon: 'tengah hari',\n    evening: 'petang',\n    night: 'malam'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  // Can't use \"pertama\", \"kedua\" because can't be parsed\n  return 'ke-' + Number(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE;AACA,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;EACnBC,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;EACxBC,IAAI,EAAE,CAAC,gBAAgB,EAAE,QAAQ;AACnC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC7H,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;AACvE,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE;EACA,OAAO,KAAK,GAAGC,MAAM,CAACF,WAAW,CAAC;AACpC,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}