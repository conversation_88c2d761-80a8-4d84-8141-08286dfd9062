{"ast": null, "code": "// Random number generator - requires a PRNG backend, e.g. prng4.js\nimport { prng_newstate, rng_psize } from \"./prng4\";\nvar rng_state;\nvar rng_pool = null;\nvar rng_pptr;\n// Initialize the pool with junk if needed.\nif (rng_pool == null) {\n  rng_pool = [];\n  rng_pptr = 0;\n  var t = void 0;\n  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {\n    // Extract entropy (2048 bits) from RNG if available\n    var z = new Uint32Array(256);\n    window.crypto.getRandomValues(z);\n    for (t = 0; t < z.length; ++t) {\n      rng_pool[rng_pptr++] = z[t] & 255;\n    }\n  }\n  // Use mouse events for entropy, if we do not have enough entropy by the time\n  // we need it, entropy will be generated by Math.random.\n  var count = 0;\n  var onMouseMoveListener_1 = function (ev) {\n    count = count || 0;\n    if (count >= 256 || rng_pptr >= rng_psize) {\n      if (window.removeEventListener) {\n        window.removeEventListener(\"mousemove\", onMouseMoveListener_1, false);\n      } else if (window.detachEvent) {\n        window.detachEvent(\"onmousemove\", onMouseMoveListener_1);\n      }\n      return;\n    }\n    try {\n      var mouseCoordinates = ev.x + ev.y;\n      rng_pool[rng_pptr++] = mouseCoordinates & 255;\n      count += 1;\n    } catch (e) {\n      // Sometimes Firefox will deny permission to access event properties for some reason. Ignore.\n    }\n  };\n  if (typeof window !== 'undefined') {\n    if (window.addEventListener) {\n      window.addEventListener(\"mousemove\", onMouseMoveListener_1, false);\n    } else if (window.attachEvent) {\n      window.attachEvent(\"onmousemove\", onMouseMoveListener_1);\n    }\n  }\n}\nfunction rng_get_byte() {\n  if (rng_state == null) {\n    rng_state = prng_newstate();\n    // At this point, we may not have collected enough entropy.  If not, fall back to Math.random\n    while (rng_pptr < rng_psize) {\n      var random = Math.floor(65536 * Math.random());\n      rng_pool[rng_pptr++] = random & 255;\n    }\n    rng_state.init(rng_pool);\n    for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) {\n      rng_pool[rng_pptr] = 0;\n    }\n    rng_pptr = 0;\n  }\n  // TODO: allow reseeding after first request\n  return rng_state.next();\n}\nvar SecureRandom = /** @class */function () {\n  function SecureRandom() {}\n  SecureRandom.prototype.nextBytes = function (ba) {\n    for (var i = 0; i < ba.length; ++i) {\n      ba[i] = rng_get_byte();\n    }\n  };\n  return SecureRandom;\n}();\nexport { SecureRandom };", "map": {"version": 3, "names": ["prng_newstate", "rng_psize", "rng_state", "rng_pool", "rng_pptr", "t", "window", "crypto", "getRandomValues", "z", "Uint32Array", "length", "count", "onMouseMoveListener_1", "ev", "removeEventListener", "detachEvent", "mouseCoordinates", "x", "y", "e", "addEventListener", "attachEvent", "rng_get_byte", "random", "Math", "floor", "init", "next", "SecureRandom", "prototype", "nextBytes", "ba", "i"], "sources": ["G:/web/central-platform-tas-web/node_modules/jsencrypt/lib/lib/jsbn/rng.js"], "sourcesContent": ["// Random number generator - requires a PRNG backend, e.g. prng4.js\nimport { prng_newstate, rng_psize } from \"./prng4\";\nvar rng_state;\nvar rng_pool = null;\nvar rng_pptr;\n// Initialize the pool with junk if needed.\nif (rng_pool == null) {\n    rng_pool = [];\n    rng_pptr = 0;\n    var t = void 0;\n    if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {\n        // Extract entropy (2048 bits) from RNG if available\n        var z = new Uint32Array(256);\n        window.crypto.getRandomValues(z);\n        for (t = 0; t < z.length; ++t) {\n            rng_pool[rng_pptr++] = z[t] & 255;\n        }\n    }\n    // Use mouse events for entropy, if we do not have enough entropy by the time\n    // we need it, entropy will be generated by Math.random.\n    var count = 0;\n    var onMouseMoveListener_1 = function (ev) {\n        count = count || 0;\n        if (count >= 256 || rng_pptr >= rng_psize) {\n            if (window.removeEventListener) {\n                window.removeEventListener(\"mousemove\", onMouseMoveListener_1, false);\n            }\n            else if (window.detachEvent) {\n                window.detachEvent(\"onmousemove\", onMouseMoveListener_1);\n            }\n            return;\n        }\n        try {\n            var mouseCoordinates = ev.x + ev.y;\n            rng_pool[rng_pptr++] = mouseCoordinates & 255;\n            count += 1;\n        }\n        catch (e) {\n            // Sometimes Firefox will deny permission to access event properties for some reason. Ignore.\n        }\n    };\n    if (typeof window !== 'undefined') {\n        if (window.addEventListener) {\n            window.addEventListener(\"mousemove\", onMouseMoveListener_1, false);\n        }\n        else if (window.attachEvent) {\n            window.attachEvent(\"onmousemove\", onMouseMoveListener_1);\n        }\n    }\n}\nfunction rng_get_byte() {\n    if (rng_state == null) {\n        rng_state = prng_newstate();\n        // At this point, we may not have collected enough entropy.  If not, fall back to Math.random\n        while (rng_pptr < rng_psize) {\n            var random = Math.floor(65536 * Math.random());\n            rng_pool[rng_pptr++] = random & 255;\n        }\n        rng_state.init(rng_pool);\n        for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) {\n            rng_pool[rng_pptr] = 0;\n        }\n        rng_pptr = 0;\n    }\n    // TODO: allow reseeding after first request\n    return rng_state.next();\n}\nvar SecureRandom = /** @class */ (function () {\n    function SecureRandom() {\n    }\n    SecureRandom.prototype.nextBytes = function (ba) {\n        for (var i = 0; i < ba.length; ++i) {\n            ba[i] = rng_get_byte();\n        }\n    };\n    return SecureRandom;\n}());\nexport { SecureRandom };\n"], "mappings": "AAAA;AACA,SAASA,aAAa,EAAEC,SAAS,QAAQ,SAAS;AAClD,IAAIC,SAAS;AACb,IAAIC,QAAQ,GAAG,IAAI;AACnB,IAAIC,QAAQ;AACZ;AACA,IAAID,QAAQ,IAAI,IAAI,EAAE;EAClBA,QAAQ,GAAG,EAAE;EACbC,QAAQ,GAAG,CAAC;EACZ,IAAIC,CAAC,GAAG,KAAK,CAAC;EACd,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,eAAe,EAAE;IACjF;IACA,IAAIC,CAAC,GAAG,IAAIC,WAAW,CAAC,GAAG,CAAC;IAC5BJ,MAAM,CAACC,MAAM,CAACC,eAAe,CAACC,CAAC,CAAC;IAChC,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,CAAC,CAACE,MAAM,EAAE,EAAEN,CAAC,EAAE;MAC3BF,QAAQ,CAACC,QAAQ,EAAE,CAAC,GAAGK,CAAC,CAACJ,CAAC,CAAC,GAAG,GAAG;IACrC;EACJ;EACA;EACA;EACA,IAAIO,KAAK,GAAG,CAAC;EACb,IAAIC,qBAAqB,GAAG,SAAAA,CAAUC,EAAE,EAAE;IACtCF,KAAK,GAAGA,KAAK,IAAI,CAAC;IAClB,IAAIA,KAAK,IAAI,GAAG,IAAIR,QAAQ,IAAIH,SAAS,EAAE;MACvC,IAAIK,MAAM,CAACS,mBAAmB,EAAE;QAC5BT,MAAM,CAACS,mBAAmB,CAAC,WAAW,EAAEF,qBAAqB,EAAE,KAAK,CAAC;MACzE,CAAC,MACI,IAAIP,MAAM,CAACU,WAAW,EAAE;QACzBV,MAAM,CAACU,WAAW,CAAC,aAAa,EAAEH,qBAAqB,CAAC;MAC5D;MACA;IACJ;IACA,IAAI;MACA,IAAII,gBAAgB,GAAGH,EAAE,CAACI,CAAC,GAAGJ,EAAE,CAACK,CAAC;MAClChB,QAAQ,CAACC,QAAQ,EAAE,CAAC,GAAGa,gBAAgB,GAAG,GAAG;MAC7CL,KAAK,IAAI,CAAC;IACd,CAAC,CACD,OAAOQ,CAAC,EAAE;MACN;IAAA;EAER,CAAC;EACD,IAAI,OAAOd,MAAM,KAAK,WAAW,EAAE;IAC/B,IAAIA,MAAM,CAACe,gBAAgB,EAAE;MACzBf,MAAM,CAACe,gBAAgB,CAAC,WAAW,EAAER,qBAAqB,EAAE,KAAK,CAAC;IACtE,CAAC,MACI,IAAIP,MAAM,CAACgB,WAAW,EAAE;MACzBhB,MAAM,CAACgB,WAAW,CAAC,aAAa,EAAET,qBAAqB,CAAC;IAC5D;EACJ;AACJ;AACA,SAASU,YAAYA,CAAA,EAAG;EACpB,IAAIrB,SAAS,IAAI,IAAI,EAAE;IACnBA,SAAS,GAAGF,aAAa,CAAC,CAAC;IAC3B;IACA,OAAOI,QAAQ,GAAGH,SAAS,EAAE;MACzB,IAAIuB,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,KAAK,GAAGD,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MAC9CrB,QAAQ,CAACC,QAAQ,EAAE,CAAC,GAAGoB,MAAM,GAAG,GAAG;IACvC;IACAtB,SAAS,CAACyB,IAAI,CAACxB,QAAQ,CAAC;IACxB,KAAKC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGD,QAAQ,CAACQ,MAAM,EAAE,EAAEP,QAAQ,EAAE;MACvDD,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC;IAC1B;IACAA,QAAQ,GAAG,CAAC;EAChB;EACA;EACA,OAAOF,SAAS,CAAC0B,IAAI,CAAC,CAAC;AAC3B;AACA,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG,CACxB;EACAA,YAAY,CAACC,SAAS,CAACC,SAAS,GAAG,UAAUC,EAAE,EAAE;IAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,EAAE,CAACrB,MAAM,EAAE,EAAEsB,CAAC,EAAE;MAChCD,EAAE,CAACC,CAAC,CAAC,GAAGV,YAAY,CAAC,CAAC;IAC1B;EACJ,CAAC;EACD,OAAOM,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}