{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { StationComponent } from './station.component';\nimport { StationEditComponent } from '@business/tas/station/station-edit/station-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'list',\n  component: StationComponent,\n  data: {\n    cache: true\n  }\n}, {\n  path: 'list-edit',\n  component: StationEditComponent,\n  data: {\n    cache: true\n  }\n}];\nexport class StationRoutingModule {\n  static {\n    this.ɵfac = function StationRoutingModule_Factory(t) {\n      return new (t || StationRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StationRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StationRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "StationComponent", "StationEditComponent", "routes", "path", "component", "data", "cache", "StationRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\station\\station-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { StationComponent } from './station.component';\r\nimport { StationEditComponent } from '@business/tas/station/station-edit/station-edit.component';\r\nconst routes: Routes = [\r\n  { path: 'list', component: StationComponent, data: { cache: true } },\r\n  { path: 'list-edit', component: StationEditComponent, data: { cache: true } },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class StationRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,2DAA2D;;;AAChG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ,gBAAgB;EAAEK,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,EACpE;EAAEH,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEH,oBAAoB;EAAEI,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI;AAAE,CAAE,CAC9E;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBR,YAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXQ,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFrBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}