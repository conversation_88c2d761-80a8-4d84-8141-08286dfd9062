{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { CwfBaseCrud, DialogResultEnum, ModalTypeEnum } from 'cwf-ng-library';\nimport { TAS_T_VESSEL_BAY } from '@store/TAS/TAS_T_VESSEL_BAY';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@service/cwfRestful.service\";\nimport * as i4 from \"ng-zorro-antd/modal\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-zorro-antd/grid\";\nimport * as i8 from \"ng-zorro-antd/form\";\nimport * as i9 from \"ng-zorro-antd/button\";\nimport * as i10 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i11 from \"ng-zorro-antd/core/wave\";\nimport * as i12 from \"ng-zorro-antd/input\";\nimport * as i13 from \"ng-zorro-antd/input-number\";\nimport * as i14 from \"ng-zorro-antd/alert\";\nimport * as i15 from \"ng-zorro-antd/select\";\nimport * as i16 from \"ng-zorro-antd/card\";\nimport * as i17 from \"ng-zorro-antd/icon\";\nimport * as i18 from \"ng-zorro-antd/divider\";\nimport * as i19 from \"ng-zorro-antd/radio\";\nimport * as i20 from \"ng-zorro-antd/empty\";\nconst _c0 = () => ({\n  \"padding\": \"16px 20px\"\n});\nconst _c1 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c2 = () => ({\n  \"padding\": \"12px 20px\"\n});\nconst _c3 = () => ({\n  \"padding\": \"0\"\n});\nconst _c4 = () => ({\n  \"height\": \"550px\",\n  \"overflow\": \"hidden\"\n});\nconst _c5 = () => [24, 0];\nconst _c6 = () => [16, 8];\nconst _c7 = () => ({\n  minRows: 2,\n  maxRows: 3\n});\nconst _c8 = () => ({\n  minRows: 3,\n  maxRows: 6\n});\nconst _c9 = () => [16, 16];\nfunction VesselBayComponent_nz_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 21);\n  }\n  if (rf & 2) {\n    const bay_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzValue\", bay_r1.bayNo)(\"nzLabel\", bay_r1.bayNo);\n  }\n}\nfunction VesselBayComponent_div_31_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowLabel_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rowLabel_r2, \" \");\n  }\n}\nfunction VesselBayComponent_div_31_div_1_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_div_31_div_1_div_5_div_4_Template_div_click_0_listener($event) {\n      const ctx_r3 = i0.ɵɵrestoreView(_r3);\n      const position_r5 = ctx_r3.$implicit;\n      const posIndex_r6 = ctx_r3.index;\n      const tierIndex_r7 = i0.ɵɵnextContext().index;\n      const ctx_r7 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r7.onPositionClick(position_r5, \"deck\", tierIndex_r7, posIndex_r6, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r5 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", position_r5.selected)(\"deleted\", position_r5.deleted)(\"drag-highlight\", position_r5.dragHighlight);\n    i0.ɵɵattribute(\"data-position-id\", position_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r5.label, \" \");\n  }\n}\nfunction VesselBayComponent_div_31_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtemplate(4, VesselBayComponent_div_31_div_1_div_5_div_4_Template, 2, 8, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tier_r9 = ctx.$implicit;\n    const tierIndex_r7 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tier_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckPositions[tierIndex_r7]);\n  }\n}\nfunction VesselBayComponent_div_31_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtemplate(3, VesselBayComponent_div_31_div_1_div_3_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵtemplate(5, VesselBayComponent_div_31_div_1_div_5_Template, 5, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckRowLabels);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckTierLabels);\n  }\n}\nfunction VesselBayComponent_div_31_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"div\", 40);\n    i0.ɵɵelementStart(2, \"div\", 41);\n    i0.ɵɵtext(3, \"\\u7532\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction VesselBayComponent_div_31_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_div_31_div_3_div_2_div_4_Template_div_click_0_listener($event) {\n      const ctx_r10 = i0.ɵɵrestoreView(_r10);\n      const position_r12 = ctx_r10.$implicit;\n      const posIndex_r13 = ctx_r10.index;\n      const tierIndex_r14 = i0.ɵɵnextContext().index;\n      const ctx_r7 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r7.onPositionClick(position_r12, \"hold\", tierIndex_r14, posIndex_r13, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r12 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", position_r12.selected)(\"deleted\", position_r12.deleted)(\"drag-highlight\", position_r12.dragHighlight);\n    i0.ɵɵattribute(\"data-position-id\", position_r12.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r12.label, \" \");\n  }\n}\nfunction VesselBayComponent_div_31_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtemplate(4, VesselBayComponent_div_31_div_3_div_2_div_4_Template, 2, 8, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tier_r15 = ctx.$implicit;\n    const tierIndex_r14 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tier_r15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdPositions[tierIndex_r14]);\n  }\n}\nfunction VesselBayComponent_div_31_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowLabel_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rowLabel_r16, \" \");\n  }\n}\nfunction VesselBayComponent_div_31_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵtemplate(2, VesselBayComponent_div_31_div_3_div_2_Template, 5, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45)(4, \"div\", 29);\n    i0.ɵɵtemplate(5, VesselBayComponent_div_31_div_3_div_5_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdTierLabels);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdRowLabels);\n  }\n}\nfunction VesselBayComponent_div_31_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"nz-empty\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselBayComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, VesselBayComponent_div_31_div_1_Template, 6, 2, \"div\", 23)(2, VesselBayComponent_div_31_div_2_Template, 4, 0, \"div\", 24)(3, VesselBayComponent_div_31_div_3_Template, 6, 2, \"div\", 25)(4, VesselBayComponent_div_31_div_4_Template, 2, 0, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.deckTiers > 0 && ctx_r7.previewBayData.deckRows > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.deckTiers > 0 && ctx_r7.previewBayData.holdTiers > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.holdTiers > 0 && ctx_r7.previewBayData.holdRows > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.previewBayData && ctx_r7.selectedBayData);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowLabel_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rowLabel_r18, \" \");\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_1_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_33_div_93_div_1_div_5_div_4_Template_div_click_0_listener($event) {\n      const ctx_r19 = i0.ɵɵrestoreView(_r19);\n      const position_r21 = ctx_r19.$implicit;\n      const posIndex_r22 = ctx_r19.index;\n      const tierIndex_r23 = i0.ɵɵnextContext().index;\n      const ctx_r7 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r7.onPositionClick(position_r21, \"deck\", tierIndex_r23, posIndex_r22, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r21 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", position_r21.selected)(\"deleted\", position_r21.deleted);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r21.label, \" \");\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtemplate(4, VesselBayComponent_ng_container_33_div_93_div_1_div_5_div_4_Template, 2, 5, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tier_r24 = ctx.$implicit;\n    const tierIndex_r23 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tier_r24);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckPositions[tierIndex_r23]);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtemplate(3, VesselBayComponent_ng_container_33_div_93_div_1_div_3_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵtemplate(5, VesselBayComponent_ng_container_33_div_93_div_1_div_5_Template, 5, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckRowLabels);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.deckTierLabels);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"div\", 40);\n    i0.ɵɵelementStart(2, \"div\", 41);\n    i0.ɵɵtext(3, \"\\u7532\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_33_div_93_div_3_div_2_div_4_Template_div_click_0_listener($event) {\n      const ctx_r25 = i0.ɵɵrestoreView(_r25);\n      const position_r27 = ctx_r25.$implicit;\n      const posIndex_r28 = ctx_r25.index;\n      const tierIndex_r29 = i0.ɵɵnextContext().index;\n      const ctx_r7 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r7.onPositionClick(position_r27, \"hold\", tierIndex_r29, posIndex_r28, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const position_r27 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", position_r27.selected)(\"deleted\", position_r27.deleted);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", position_r27.label, \" \");\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtemplate(4, VesselBayComponent_ng_container_33_div_93_div_3_div_2_div_4_Template, 2, 5, \"div\", 89);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tier_r30 = ctx.$implicit;\n    const tierIndex_r29 = ctx.index;\n    const ctx_r7 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tier_r30);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdPositions[tierIndex_r29]);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowLabel_r31 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", rowLabel_r31, \" \");\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵtemplate(2, VesselBayComponent_ng_container_33_div_93_div_3_div_2_Template, 5, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45)(4, \"div\", 29);\n    i0.ɵɵtemplate(5, VesselBayComponent_ng_container_33_div_93_div_3_div_5_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdTierLabels);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.previewBayData.holdRowLabels);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, VesselBayComponent_ng_container_33_div_93_div_1_Template, 6, 2, \"div\", 23)(2, VesselBayComponent_ng_container_33_div_93_div_2_Template, 4, 0, \"div\", 24)(3, VesselBayComponent_ng_container_33_div_93_div_3_Template, 6, 2, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.deckTiers > 0 && ctx_r7.previewBayData.deckRows > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.deckTiers > 0 && ctx_r7.previewBayData.holdTiers > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData.holdTiers > 0 && ctx_r7.previewBayData.holdRows > 0);\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_94_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_33_div_94_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r7 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r7.deleteSelectedPositions());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5220\\u9664\\u9009\\u4E2D\\u4F4D\\u7F6E (\", ctx_r7.getSelectedCount(), \") \");\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_94_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_33_div_94_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r7 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r7.clearSelection());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u9009\\u62E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselBayComponent_ng_container_33_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"nz-space\");\n    i0.ɵɵtemplate(2, VesselBayComponent_ng_container_33_div_94_button_2_Template, 2, 1, \"button\", 90)(3, VesselBayComponent_ng_container_33_div_94_button_3_Template, 2, 0, \"button\", 91);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction VesselBayComponent_ng_container_33_nz_empty_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-empty\", 94);\n  }\n}\nfunction VesselBayComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 51)(2, \"div\", 52)(3, \"nz-card\", 53)(4, \"form\", 54)(5, \"div\", 51)(6, \"div\", 55)(7, \"nz-form-item\")(8, \"nz-form-label\", 56);\n    i0.ɵɵtext(9, \"\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nz-form-control\", 57);\n    i0.ɵɵelement(11, \"input\", 58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 55);\n    i0.ɵɵelement(13, \"nz-divider\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 60)(15, \"nz-form-item\")(16, \"nz-form-label\", 56);\n    i0.ɵɵtext(17, \"\\u7532\\u677F\\u57FA\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nz-form-control\", 61)(19, \"nz-input-number\", 62);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_19_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 60)(21, \"nz-form-item\")(22, \"nz-form-label\", 56);\n    i0.ɵɵtext(23, \"\\u7532\\u677F\\u6B65\\u957F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"nz-form-control\", 63)(25, \"nz-input-number\", 64);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_25_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(26, \"div\", 60)(27, \"nz-form-item\")(28, \"nz-form-label\", 56);\n    i0.ɵɵtext(29, \"\\u7532\\u677F\\u5C42\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"nz-form-control\", 65)(31, \"nz-input-number\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_31_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 60)(33, \"nz-form-item\")(34, \"nz-form-label\", 56);\n    i0.ɵɵtext(35, \"\\u7532\\u677F\\u884C\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"nz-form-control\", 67)(37, \"nz-input-number\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_37_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(38, \"div\", 55)(39, \"nz-form-item\")(40, \"nz-form-label\");\n    i0.ɵɵtext(41, \"\\u7532\\u677F\\u884C\\u6570\\u662F\\u5426\\u4ECE0\\u5F00\\u59CB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"nz-form-control\")(43, \"nz-radio-group\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_radio_group_ngModelChange_43_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementStart(44, \"label\", 70);\n    i0.ɵɵtext(45, \"\\u662F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"label\", 71);\n    i0.ɵɵtext(47, \"\\u5426\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(48, \"div\", 55);\n    i0.ɵɵelement(49, \"nz-divider\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 60)(51, \"nz-form-item\")(52, \"nz-form-label\", 56);\n    i0.ɵɵtext(53, \"\\u8231\\u5E95\\u57FA\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"nz-form-control\", 73)(55, \"nz-input-number\", 74);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_55_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(56, \"div\", 60)(57, \"nz-form-item\")(58, \"nz-form-label\", 56);\n    i0.ɵɵtext(59, \"\\u8231\\u5E95\\u6B65\\u957F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"nz-form-control\", 75)(61, \"nz-input-number\", 76);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_61_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(62, \"div\", 60)(63, \"nz-form-item\")(64, \"nz-form-label\", 56);\n    i0.ɵɵtext(65, \"\\u8231\\u5E95\\u5C42\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"nz-form-control\", 77)(67, \"nz-input-number\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_67_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(68, \"div\", 60)(69, \"nz-form-item\")(70, \"nz-form-label\", 56);\n    i0.ɵɵtext(71, \"\\u8231\\u5E95\\u884C\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"nz-form-control\", 79)(73, \"nz-input-number\", 80);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_73_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(74, \"div\", 55)(75, \"nz-form-item\")(76, \"nz-form-label\");\n    i0.ɵɵtext(77, \"\\u8231\\u5E95\\u884C\\u6570\\u662F\\u5426\\u4ECE0\\u5F00\\u59CB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"nz-form-control\")(79, \"nz-radio-group\", 81);\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_33_Template_nz_radio_group_ngModelChange_79_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onParameterChange());\n    });\n    i0.ɵɵelementStart(80, \"label\", 70);\n    i0.ɵɵtext(81, \"\\u662F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"label\", 71);\n    i0.ɵɵtext(83, \"\\u5426\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(84, \"div\", 55)(85, \"nz-form-item\")(86, \"nz-form-label\");\n    i0.ɵɵtext(87, \"\\u5907\\u6CE8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"nz-form-control\");\n    i0.ɵɵelement(89, \"textarea\", 82);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(90, \"div\", 83)(91, \"nz-card\", 84)(92, \"div\", 85);\n    i0.ɵɵtemplate(93, VesselBayComponent_ng_container_33_div_93_Template, 4, 3, \"div\", 15)(94, VesselBayComponent_ng_container_33_div_94_Template, 4, 0, \"div\", 86)(95, VesselBayComponent_ng_container_33_nz_empty_95_Template, 1, 0, \"nz-empty\", 87);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(25, _c5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzSize\", \"small\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r7.newBayForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(26, _c6));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"nzMin\", 1)(\"nzMax\", 99);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 1)(\"nzMax\", 10);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0)(\"nzMax\", 10);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0)(\"nzMax\", 20);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"nzMin\", 1)(\"nzMax\", 99);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 1)(\"nzMax\", 10);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0)(\"nzMax\", 10);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzMin\", 0)(\"nzMax\", 20);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"nzAutosize\", i0.ɵɵpureFunction0(27, _c7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzSize\", \"small\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.previewBayData && ctx_r7.hasSelectedPositions());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.previewBayData || ctx_r7.previewBayData.deckTiers === 0 && ctx_r7.previewBayData.holdTiers === 0);\n  }\n}\nfunction VesselBayComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 54)(2, \"nz-form-item\")(3, \"nz-form-label\", 56);\n    i0.ɵɵtext(4, \"\\u63D2\\u5165\\u8D1D\\u4F4D\\u4FE1\\u606F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nz-form-control\", 95);\n    i0.ɵɵelement(6, \"textarea\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"nz-alert\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r7.insertBayForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nzAutosize\", i0.ɵɵpureFunction0(2, _c8));\n  }\n}\nfunction VesselBayComponent_ng_container_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 54)(2, \"nz-form-item\")(3, \"nz-form-label\");\n    i0.ɵɵtext(4, \"\\u539F\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nz-form-control\");\n    i0.ɵɵelement(6, \"input\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nz-form-item\")(8, \"nz-form-label\", 56);\n    i0.ɵɵtext(9, \"\\u65B0\\u8D1D\\u53F7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nz-form-control\", 99);\n    i0.ɵɵelement(11, \"input\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"nz-alert\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r7.copyBayForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nfunction VesselBayComponent_ng_container_39_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_39_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.selectAllRestorePositions());\n    });\n    i0.ɵɵelement(1, \"i\", 121);\n    i0.ɵɵtext(2, \" \\u5168\\u9009 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselBayComponent_ng_container_39_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_39_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.deselectAllRestorePositions());\n    });\n    i0.ɵɵelement(1, \"i\", 122);\n    i0.ɵɵtext(2, \" \\u53CD\\u9009 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselBayComponent_ng_container_39_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_39_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.clearRestoreSelection());\n    });\n    i0.ɵɵelement(1, \"i\", 123);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VesselBayComponent_ng_container_39_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 124);\n    i0.ɵɵtext(1, \" \\u5DF2\\u9009\\u62E9: \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedDeletedPositions.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" / \", ctx_r7.filteredDeletedPositions.length, \" \");\n  }\n}\nfunction VesselBayComponent_ng_container_39_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵlistener(\"click\", function VesselBayComponent_ng_container_39_div_19_div_1_Template_div_click_0_listener() {\n      const position_r39 = i0.ɵɵrestoreView(_r38).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r7.togglePositionSelection(position_r39.value));\n    });\n    i0.ɵɵelementStart(1, \"div\", 128)(2, \"div\", 129);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 130);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"i\", 131);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const position_r39 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r7.isPositionSelected(position_r39.value));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(position_r39.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getPositionArea(position_r39.value));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", ctx_r7.isPositionSelected(position_r39.value) ? \"check-circle\" : \"plus-circle\")(\"nzTheme\", ctx_r7.isPositionSelected(position_r39.value) ? \"fill\" : \"outline\");\n  }\n}\nfunction VesselBayComponent_ng_container_39_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵtemplate(1, VesselBayComponent_ng_container_39_div_19_div_1_Template, 7, 6, \"div\", 126);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.filteredDeletedPositions)(\"ngForTrackBy\", ctx_r7.trackByPositionId);\n  }\n}\nfunction VesselBayComponent_ng_container_39_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132);\n    i0.ɵɵelement(1, \"nz-empty\", 133);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzNotFoundContent\", ctx_r7.restoreSearchText || ctx_r7.restoreAreaFilter ? \"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u8D1D\\u4F4D\" : \"\\u6682\\u65E0\\u5DF2\\u5220\\u9664\\u7684\\u8D1D\\u4F4D\");\n  }\n}\nfunction VesselBayComponent_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 102)(2, \"div\", 103)(3, \"nz-row\", 104)(4, \"nz-col\", 105)(5, \"nz-input-group\", 106)(6, \"input\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function VesselBayComponent_ng_container_39_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r7 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r7.restoreSearchText, $event) || (ctx_r7.restoreSearchText = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_39_Template_input_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onRestoreSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"nz-col\", 105)(8, \"nz-select\", 108);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function VesselBayComponent_ng_container_39_Template_nz_select_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r7 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r7.restoreAreaFilter, $event) || (ctx_r7.restoreAreaFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_ng_container_39_Template_nz_select_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onRestoreAreaFilter());\n    });\n    i0.ɵɵelement(9, \"nz-option\", 109)(10, \"nz-option\", 110)(11, \"nz-option\", 111);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 112)(13, \"nz-space\");\n    i0.ɵɵtemplate(14, VesselBayComponent_ng_container_39_button_14_Template, 3, 0, \"button\", 113)(15, VesselBayComponent_ng_container_39_button_15_Template, 3, 0, \"button\", 113)(16, VesselBayComponent_ng_container_39_button_16_Template, 3, 0, \"button\", 113)(17, VesselBayComponent_ng_container_39_span_17_Template, 5, 2, \"span\", 114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 115);\n    i0.ɵɵtemplate(19, VesselBayComponent_ng_container_39_div_19_Template, 2, 2, \"div\", 116)(20, VesselBayComponent_ng_container_39_div_20_Template, 2, 1, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 118);\n    i0.ɵɵelement(22, \"nz-alert\", 119);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(5, _c9));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r7.restoreSearchText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r7.restoreAreaFilter);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.filteredDeletedPositions.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.filteredDeletedPositions.length === 0);\n  }\n}\nexport class VesselBayComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, cwfRestfulService, modal, cdr) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.cwfRestfulService = cwfRestfulService;\n    this.modal = modal;\n    this.cdr = cdr;\n    this.mainStore = new TAS_T_VESSEL_BAY();\n    this.vesselId = '';\n    this.vesselNm = '';\n    this.selectedBayNo = '';\n    this.selectedBayData = null;\n    this.bayList = [];\n    this.bayPositions = [];\n    this.selectedPositions = [];\n    // 模态框相关\n    this.isNewBayModalVisible = false;\n    this.isInsertBayModalVisible = false;\n    this.isCopyBayModalVisible = false;\n    this.isRestorePositionModalVisible = false;\n    // 新增属性\n    this.previewBayData = null;\n    this.deletedPositions = new Set();\n    // 恢复贝位功能相关属性\n    this.selectedDeletedPositions = [];\n    this.deletedPositionOptions = [];\n    // 恢复贝位弹出框优化相关属性\n    this.restoreSearchText = '';\n    this.restoreAreaFilter = '';\n    this.filteredDeletedPositions = [];\n    // 拖拽多选相关属性\n    this.isDragging = false;\n    this.dragStartX = 0;\n    this.dragStartY = 0;\n    this.dragCurrentX = 0;\n    this.dragCurrentY = 0;\n    this.dragStartPosition = null;\n    this.dragSelectionBox = null;\n    this.ctrlKeyPressed = false;\n    // 路径拖拽相关属性\n    this.pathDragMode = false; // 是否为路径拖拽模式\n    this.pathSelectedPositions = new Set(); // 路径拖拽中已选择的贝位ID\n    this.lastPathPosition = null; // 上一个路径经过的贝位\n    // 拖拽检测相关属性\n    this.dragStartTime = 0; // 拖拽开始时间\n    this.hasMoved = false; // 是否已经移动\n    this.dragThreshold = 150; // 拖拽时间阈值（毫秒）\n    this.moveThreshold = 5; // 鼠标移动阈值（像素）\n    // 潜在拖拽状态\n    this.potentialPathDrag = false; // 潜在的路径拖拽\n    this.potentialRectDrag = false; // 潜在的矩形拖拽\n    this.potentialPathTarget = null; // 潜在路径拖拽的目标元素\n  }\n  /**\n   * 页面加载后处理\n   */\n  onShow() {\n    this.vesselId = this.openParam['vesselId'] || '';\n    this.vesselNm = this.openParam['vesselNm'] || '';\n    console.log('船舶贝图 - 接收到的参数:', {\n      vesselId: this.vesselId,\n      vesselNm: this.vesselNm,\n      allParams: this.openParam\n    });\n    if (!this.vesselId) {\n      this.showState(ModalTypeEnum.error, '船舶ID参数缺失！');\n      this.openMainPage();\n      return;\n    }\n    this.initForms();\n    this.loadBayList();\n    this.initDragEvents();\n  }\n  /**\n   * 初始化表单\n   */\n  initForms() {\n    this.newBayForm = new FormGroup({\n      bayNo: new FormControl('', [Validators.required, Validators.maxLength(12)]),\n      deckBase: new FormControl(82, [Validators.required, Validators.min(1), Validators.max(99)]),\n      deckStep: new FormControl(2, [Validators.required, Validators.min(1), Validators.max(10)]),\n      deckTiers: new FormControl(3, [Validators.required, Validators.min(0), Validators.max(10)]),\n      deckRows: new FormControl(9, [Validators.required, Validators.min(0), Validators.max(20)]),\n      deckFromZero: new FormControl('Y'),\n      holdBase: new FormControl(81, [Validators.required, Validators.min(1), Validators.max(99)]),\n      holdStep: new FormControl(2, [Validators.required, Validators.min(1), Validators.max(10)]),\n      holdTiers: new FormControl(4, [Validators.required, Validators.min(0), Validators.max(10)]),\n      holdRows: new FormControl(9, [Validators.required, Validators.min(0), Validators.max(20)]),\n      holdFromZero: new FormControl('Y'),\n      remark: new FormControl('', [Validators.maxLength(255)])\n    });\n    this.insertBayForm = new FormGroup({\n      insertBayNos: new FormControl('', [Validators.required])\n    });\n    this.copyBayForm = new FormGroup({\n      originalBayNo: new FormControl(''),\n      newBayNo: new FormControl('', [Validators.required, Validators.maxLength(12)])\n    });\n    // 监听表单变化，实时生成预览\n    this.newBayForm.valueChanges.subscribe(() => {\n      this.generatePreviewBayData();\n    });\n  }\n  /**\n   * 加载贝位列表\n   */\n  loadBayList() {\n    // 清除之前的数据，确保不会显示缓存的数据\n    this.bayList = [];\n    this.selectedBayNo = '';\n    this.selectedBayData = null;\n    this.previewBayData = null;\n    this.selectedPositions = [];\n    // 按照正确的参数格式构建请求参数\n    const requestData = {\n      data: {\n        vesselId: this.vesselId\n      }\n    };\n    console.log('船舶贝图 - 请求参数:', requestData);\n    this.cwfRestfulService.post('/vessel-bay/list', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      console.log('船舶贝图 - API响应:', rps);\n      if (rps.ok === true) {\n        this.bayList = rps.data || [];\n        console.log('船舶贝图 - 加载贝位列表成功:', {\n          vesselId: this.vesselId,\n          bayCount: this.bayList.length,\n          bayList: this.bayList\n        });\n        if (this.bayList.length > 0) {\n          this.selectedBayNo = this.bayList[0].bayNo;\n          this.onBaySelect();\n        } else {\n          console.log('船舶贝图 - 该船舶没有贝位数据');\n          this.selectedBayNo = '';\n          this.selectedBayData = null;\n          this.previewBayData = null;\n        }\n      } else {\n        console.log('船舶贝图 - API错误:', rps.msg);\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).catch(error => {\n      console.error('船舶贝图 - API调用失败:', error);\n      this.showState(ModalTypeEnum.error, '加载贝位列表失败');\n    });\n  }\n  /**\n   * 加载贝位列表并选择指定贝号\n   */\n  loadBayListAndSelect(bayNo) {\n    // 按照正确的参数格式构建请求参数\n    const requestData = {\n      data: {\n        vesselId: this.vesselId\n      }\n    };\n    console.log('船舶贝图 - 请求参数:', requestData);\n    this.cwfRestfulService.post('/vessel-bay/list', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      console.log('船舶贝图 - API响应:', rps);\n      if (rps.ok === true) {\n        this.bayList = rps.data || [];\n        console.log('船舶贝图 - 贝位列表:', this.bayList);\n        // 查找指定的贝号\n        const targetBay = this.bayList.find(bay => bay.bayNo === bayNo);\n        if (targetBay) {\n          this.selectedBayNo = bayNo;\n          console.log('船舶贝图 - 选择贝号:', bayNo);\n          this.onBaySelect();\n        } else if (this.bayList.length > 0) {\n          // 如果找不到指定贝号，选择第一个\n          this.selectedBayNo = this.bayList[0].bayNo;\n          this.onBaySelect();\n        } else {\n          console.log('船舶贝图 - 贝位列表为空');\n        }\n      } else {\n        console.log('船舶贝图 - API错误:', rps.msg);\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).catch(error => {\n      console.error('船舶贝图 - API调用失败:', error);\n    });\n  }\n  /**\n   * 贝号选择事件\n   */\n  onBaySelect() {\n    console.log('船舶贝图 - 选择贝号:', this.selectedBayNo);\n    console.log('船舶贝图 - 当前贝位列表:', this.bayList);\n    // 清空之前的选中状态\n    this.selectedPositions = [];\n    this.selectedBayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\n    console.log('船舶贝图 - 找到的贝位数据:', this.selectedBayData);\n    if (this.selectedBayData) {\n      this.generateBayPositions();\n    } else {\n      console.log('船舶贝图 - 未找到对应的贝位数据');\n    }\n  }\n  /**\n   * 生成贝位图（使用与预览相同的数据结构）\n   */\n  generateBayPositions() {\n    if (!this.selectedBayData) {\n      console.log('船舶贝图 - selectedBayData为空，无法生成贝位图');\n      return;\n    }\n    const data = this.selectedBayData;\n    console.log('船舶贝图 - 开始生成贝位图，数据:', data);\n    // 先更新deletedPositions集合，确保生成贝位时有正确的删除状态\n    this.deletedPositions.clear();\n    if (data.rowtier) {\n      const validPositions = data.rowtier.split(',');\n      console.log('船舶贝图 - 有效贝位列表:', validPositions);\n      // 生成所有可能的贝位ID，然后找出已删除的\n      const allPossiblePositions = this.generateAllPossiblePositionIds(data);\n      console.log('船舶贝图 - 所有可能的贝位ID:', allPossiblePositions);\n      allPossiblePositions.forEach(positionId => {\n        if (!validPositions.includes(positionId)) {\n          this.deletedPositions.add(positionId);\n        }\n      });\n      console.log('船舶贝图 - 已删除贝位集合:', Array.from(this.deletedPositions));\n    }\n    // 使用与预览相同的数据生成逻辑（此时deletedPositions已经正确设置）\n    this.previewBayData = {\n      deckTiers: data.dtier || 0,\n      deckRows: data.drow || 0,\n      holdTiers: data.htier || 0,\n      holdRows: data.hrow || 0,\n      deckTierLabels: this.generateDeckTierLabels(data.dfrom, data.dstep, data.dtier),\n      deckRowLabels: this.generateRowLabels(data.drow, data.drowFromZeroTag === 'Y'),\n      holdTierLabels: this.generateHoldTierLabels(data.hfrom, data.hstep, data.htier),\n      holdRowLabels: this.generateRowLabels(data.hrow, data.hrowFromZeroTag === 'Y'),\n      deckPositions: this.generateDeckPositions(data.dfrom, data.dstep, data.dtier, data.drow, data.drowFromZeroTag === 'Y'),\n      holdPositions: this.generateHoldPositions(data.hfrom, data.hstep, data.htier, data.hrow, data.hrowFromZeroTag === 'Y')\n    };\n    console.log('船舶贝图 - 生成的预览数据:', this.previewBayData);\n    this.selectedPositions = [];\n    // 更新已删除贝位的下拉选项\n    this.updateDeletedPositionOptions();\n  }\n  /**\n   * 更新已删除贝位的下拉选项\n   */\n  updateDeletedPositionOptions() {\n    this.deletedPositionOptions = Array.from(this.deletedPositions).map(positionId => ({\n      label: positionId,\n      value: positionId\n    }));\n    // 更新筛选后的贝位列表\n    this.updateFilteredDeletedPositions();\n    console.log('船舶贝图 - 已删除贝位选项:', this.deletedPositionOptions);\n  }\n  /**\n   * 显示恢复贝位模态框\n   */\n  showRestorePositionModal() {\n    if (!this.selectedBayData) {\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\n      return;\n    }\n    if (this.deletedPositions.size === 0) {\n      this.showState(ModalTypeEnum.info, '当前没有已删除的贝位可以恢复！');\n      return;\n    }\n    // 重置搜索和筛选条件\n    this.restoreSearchText = '';\n    this.restoreAreaFilter = '';\n    // 更新下拉选项和筛选列表\n    this.updateDeletedPositionOptions();\n    // 清空之前的选择\n    this.selectedDeletedPositions = [];\n    // 显示模态框\n    this.isRestorePositionModalVisible = true;\n  }\n  /**\n   * 取消恢复贝位操作\n   */\n  cancelRestorePosition() {\n    this.isRestorePositionModalVisible = false;\n    this.selectedDeletedPositions = [];\n    this.restoreSearchText = '';\n    this.restoreAreaFilter = '';\n  }\n  /**\n   * 更新筛选后的已删除贝位列表\n   */\n  updateFilteredDeletedPositions() {\n    let filtered = [...this.deletedPositionOptions];\n    // 按搜索文本筛选\n    if (this.restoreSearchText) {\n      const searchText = this.restoreSearchText.toLowerCase();\n      filtered = filtered.filter(position => position.label.toLowerCase().includes(searchText));\n    }\n    // 按区域筛选\n    if (this.restoreAreaFilter) {\n      filtered = filtered.filter(position => {\n        const area = this.getPositionArea(position.value);\n        console.log('船舶贝图 - 筛选调试:', {\n          positionId: position.value,\n          area: area,\n          filter: this.restoreAreaFilter,\n          match: this.matchAreaFilter(area, this.restoreAreaFilter)\n        });\n        return this.matchAreaFilter(area, this.restoreAreaFilter);\n      });\n    }\n    this.filteredDeletedPositions = filtered;\n    console.log('船舶贝图 - 筛选结果:', {\n      total: this.deletedPositionOptions.length,\n      filtered: filtered.length,\n      searchText: this.restoreSearchText,\n      areaFilter: this.restoreAreaFilter\n    });\n  }\n  /**\n   * 搜索贝位\n   */\n  onRestoreSearch() {\n    this.updateFilteredDeletedPositions();\n  }\n  /**\n   * 区域筛选\n   */\n  onRestoreAreaFilter() {\n    this.updateFilteredDeletedPositions();\n  }\n  /**\n   * 匹配区域筛选条件\n   */\n  matchAreaFilter(area, filter) {\n    if (!filter) return true; // 空筛选条件匹配所有\n    // 精确匹配区域\n    if (filter === 'deck' && area === '甲板') return true;\n    if (filter === 'hold' && area === '舱底') return true;\n    return false;\n  }\n  /**\n   * 获取贝位所属区域\n   */\n  getPositionArea(positionId) {\n    if (!this.previewBayData) {\n      return '未知';\n    }\n    // 在甲板区域查找\n    if (this.previewBayData.deckPositions) {\n      for (const tierPositions of this.previewBayData.deckPositions) {\n        const position = tierPositions.find(p => p.id === positionId);\n        if (position) return '甲板';\n      }\n    }\n    // 在舱底区域查找\n    if (this.previewBayData.holdPositions) {\n      for (const tierPositions of this.previewBayData.holdPositions) {\n        const position = tierPositions.find(p => p.id === positionId);\n        if (position) return '舱底';\n      }\n    }\n    return '未知';\n  }\n  /**\n   * 切换贝位选择状态\n   */\n  togglePositionSelection(positionId) {\n    const index = this.selectedDeletedPositions.indexOf(positionId);\n    if (index > -1) {\n      this.selectedDeletedPositions.splice(index, 1);\n    } else {\n      this.selectedDeletedPositions.push(positionId);\n    }\n  }\n  /**\n   * 检查贝位是否已选中\n   */\n  isPositionSelected(positionId) {\n    return this.selectedDeletedPositions.includes(positionId);\n  }\n  /**\n   * 全选当前筛选的贝位\n   */\n  selectAllRestorePositions() {\n    this.filteredDeletedPositions.forEach(position => {\n      if (!this.isPositionSelected(position.value)) {\n        this.selectedDeletedPositions.push(position.value);\n      }\n    });\n  }\n  /**\n   * 反选当前筛选的贝位\n   */\n  deselectAllRestorePositions() {\n    this.filteredDeletedPositions.forEach(position => {\n      const index = this.selectedDeletedPositions.indexOf(position.value);\n      if (index > -1) {\n        this.selectedDeletedPositions.splice(index, 1);\n      } else {\n        this.selectedDeletedPositions.push(position.value);\n      }\n    });\n  }\n  /**\n   * 清空选择\n   */\n  clearRestoreSelection() {\n    this.selectedDeletedPositions = [];\n  }\n  /**\n   * TrackBy函数用于优化ngFor性能\n   */\n  trackByPositionId(index, item) {\n    return item.value;\n  }\n  /**\n   * 恢复选中的已删除贝位\n   */\n  restoreSelectedPositions() {\n    if (!this.selectedBayData) {\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\n      return;\n    }\n    if (this.selectedDeletedPositions.length === 0) {\n      this.showState(ModalTypeEnum.error, '请选择要恢复的贝位！');\n      return;\n    }\n    const bayData = this.selectedBayData;\n    console.log('船舶贝图 - 恢复贝位，贝数据:', bayData);\n    // 根据接口文档规范构建请求数据（使用插入贝位接口）\n    const requestData = {\n      bayId: bayData.id,\n      // 船舶贝的ID\n      bayNos: this.selectedDeletedPositions // 要恢复的贝位编号列表\n    };\n    console.log('船舶贝图 - 恢复贝位请求数据:', requestData);\n    // 调用插入贝位接口\n    this.cwfRestfulService.post('/vessel-bay/vessel/bay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en).then(response => {\n      console.log('船舶贝图 - 恢复贝位响应:', response);\n      if (response && response.ok) {\n        this.showState(ModalTypeEnum.success, '恢复成功！');\n        // 将恢复的贝位从已删除状态移除\n        this.selectedDeletedPositions.forEach(positionId => {\n          this.deletedPositions.delete(positionId);\n          // 在界面上更新贝位状态\n          this.previewBayData.deckPositions.forEach(tierPositions => {\n            tierPositions.forEach(position => {\n              if (position.id === positionId) {\n                position.deleted = false;\n              }\n            });\n          });\n          this.previewBayData.holdPositions.forEach(tierPositions => {\n            tierPositions.forEach(position => {\n              if (position.id === positionId) {\n                position.deleted = false;\n              }\n            });\n          });\n        });\n        // 清空选中的已删除贝位\n        this.selectedDeletedPositions = [];\n        // 更新下拉选项\n        this.updateDeletedPositionOptions();\n        // 关闭模态框\n        this.isRestorePositionModalVisible = false;\n        console.log('船舶贝图 - 恢复后的已删除贝位集合:', Array.from(this.deletedPositions));\n      } else {\n        this.showState(ModalTypeEnum.error, response?.msg || '恢复失败！');\n      }\n    }).catch(error => {\n      console.error('船舶贝图 - 恢复贝位失败:', error);\n      this.showState(ModalTypeEnum.error, '恢复失败！');\n    });\n  }\n  /**\n   * 贝位点击事件 - 默认多选模式\n   */\n  onPositionClick(position, area, tierIndex, posIndex, event) {\n    console.log('船舶贝图 - 贝位点击事件触发:', {\n      positionId: position.id,\n      currentSelected: position.selected,\n      isDragging: this.isDragging,\n      dragStartTime: this.dragStartTime,\n      hasEvent: !!event\n    });\n    if (position.deleted) {\n      console.log('船舶贝图 - 已删除的贝位，忽略点击');\n      return; // 已删除的位置不能点击\n    }\n    // 阻止事件冒泡和默认行为，避免触发容器的mousedown事件\n    if (event) {\n      event.stopPropagation();\n      event.preventDefault();\n    }\n    // 如果正在拖拽过程中，忽略点击事件\n    if (this.isDragging) {\n      console.log('船舶贝图 - 正在拖拽中，忽略点击事件');\n      return;\n    }\n    // 如果刚刚结束拖拽操作（防止拖拽结束后立即触发点击）\n    const timeSinceMouseDown = Date.now() - this.dragStartTime;\n    if (this.dragStartTime > 0 && timeSinceMouseDown > 50 && timeSinceMouseDown < 200) {\n      console.log('船舶贝图 - 刚结束拖拽操作，忽略点击事件，时间差:', timeSinceMouseDown);\n      return;\n    }\n    // 检查是否按住Ctrl键\n    const isCtrlPressed = event ? event.ctrlKey || event.metaKey : this.ctrlKeyPressed;\n    // 记录原始状态\n    const wasSelected = position.selected;\n    // 默认多选模式：普通点击也支持多选\n    // Ctrl+点击保留作为备选操作方式\n    position.selected = !position.selected;\n    if (position.selected) {\n      // 添加到选中列表\n      if (!this.selectedPositions.find(p => p.id === position.id)) {\n        this.selectedPositions.push(position);\n      }\n    } else {\n      // 从选中列表移除\n      this.selectedPositions = this.selectedPositions.filter(p => p.id !== position.id);\n    }\n    // 立即触发变更检测，确保视觉反馈\n    this.cdr.detectChanges();\n    console.log('船舶贝图 - 贝位选择状态更新:', {\n      positionId: position.id,\n      wasSelected: wasSelected,\n      nowSelected: position.selected,\n      selectedCount: this.selectedPositions.length,\n      selectedPositions: this.selectedPositions.map(p => p.id)\n    });\n  }\n  /**\n   * 新建贝\n   */\n  showNewBayModal() {\n    this.newBayForm.reset();\n    this.newBayForm.patchValue({\n      deckBase: 82,\n      deckStep: 2,\n      deckTiers: 3,\n      deckRows: 9,\n      deckFromZero: 'Y',\n      holdBase: 81,\n      holdStep: 2,\n      holdTiers: 4,\n      holdRows: 9,\n      holdFromZero: 'Y'\n    });\n    this.deletedPositions.clear();\n    this.generatePreviewBayData();\n    this.isNewBayModalVisible = true;\n  }\n  /**\n   * 取消新建贝\n   */\n  cancelNewBay() {\n    this.isNewBayModalVisible = false;\n    this.previewBayData = null;\n    this.deletedPositions.clear();\n  }\n  /**\n   * 参数变化时重新生成预览\n   */\n  onParameterChange() {\n    setTimeout(() => {\n      this.generatePreviewBayData();\n    }, 100);\n  }\n  /**\n   * 生成预览贝图数据\n   */\n  generatePreviewBayData() {\n    const formValue = this.newBayForm.getRawValue();\n    if (!formValue.deckBase || !formValue.holdBase) {\n      this.previewBayData = null;\n      return;\n    }\n    const deckTiers = formValue.deckTiers || 0;\n    const deckRows = formValue.deckRows || 0;\n    const holdTiers = formValue.holdTiers || 0;\n    const holdRows = formValue.holdRows || 0;\n    if (deckTiers === 0 && holdTiers === 0) {\n      this.previewBayData = null;\n      return;\n    }\n    this.previewBayData = {\n      deckTiers,\n      deckRows,\n      holdTiers,\n      holdRows,\n      deckTierLabels: this.generateDeckTierLabels(formValue.deckBase, formValue.deckStep, deckTiers),\n      deckRowLabels: this.generateRowLabels(deckRows, formValue.deckFromZero === 'Y'),\n      holdTierLabels: this.generateHoldTierLabels(formValue.holdBase, formValue.holdStep, holdTiers),\n      holdRowLabels: this.generateRowLabels(holdRows, formValue.holdFromZero === 'Y'),\n      deckPositions: this.generateDeckPositions(formValue.deckBase, formValue.deckStep, deckTiers, deckRows, formValue.deckFromZero === 'Y'),\n      holdPositions: this.generateHoldPositions(formValue.holdBase, formValue.holdStep, holdTiers, holdRows, formValue.holdFromZero === 'Y')\n    };\n  }\n  /**\n   * 生成所有可能的贝位ID（用于确定删除状态）\n   */\n  generateAllPossiblePositionIds(data) {\n    const allPositions = [];\n    // 生成甲板区域的所有可能贝位ID\n    if (data.dtier > 0 && data.drow > 0) {\n      const deckRowLabels = this.generateRowLabels(data.drow, data.drowFromZeroTag === 'Y');\n      for (let tierIndex = 0; tierIndex < data.dtier; tierIndex++) {\n        const tierLabel = String(data.dfrom + tierIndex * data.dstep).padStart(2, '0');\n        for (let rowIndex = 0; rowIndex < data.drow; rowIndex++) {\n          const rowLabel = deckRowLabels[rowIndex];\n          allPositions.push(`${rowLabel}${tierLabel}`);\n        }\n      }\n    }\n    // 生成舱底区域的所有可能贝位ID\n    if (data.htier > 0 && data.hrow > 0) {\n      const holdRowLabels = this.generateRowLabels(data.hrow, data.hrowFromZeroTag === 'Y');\n      for (let tierIndex = 0; tierIndex < data.htier; tierIndex++) {\n        const tierLabel = String(data.hfrom + tierIndex * data.hstep).padStart(2, '0');\n        for (let rowIndex = 0; rowIndex < data.hrow; rowIndex++) {\n          const rowLabel = holdRowLabels[rowIndex];\n          allPositions.push(`${rowLabel}${tierLabel}`);\n        }\n      }\n    }\n    return allPositions;\n  }\n  /**\n   * 生成甲板层标签\n   */\n  generateDeckTierLabels(base, step, tiers) {\n    const labels = [];\n    for (let i = 0; i < tiers; i++) {\n      labels.push(String(base + i * step).padStart(2, '0'));\n    }\n    return labels;\n  }\n  /**\n   * 生成舱底层标签（从下到上排列）\n   */\n  generateHoldTierLabels(base, step, tiers) {\n    const labels = [];\n    // 舱底区域从下到上排列，所以需要反向生成标签\n    for (let i = tiers - 1; i >= 0; i--) {\n      labels.push(String(base + i * step).padStart(2, '0'));\n    }\n    return labels;\n  }\n  /**\n   * 生成行标签（按照中间对称的方式）\n   * 规则：中间轴开始，右侧为从01开始的奇数，左侧为从02开始的偶数\n   * 示例：行数9，从0开始=是：08/06/04/02/00/01/03/05/07\n   * 示例：行数9，从0开始=否：08/06/04/02/01/03/05/07/09\n   * 示例：行数8：08/06/04/02/01/03/05/07\n   */\n  generateRowLabels(rows, fromZero) {\n    if (rows === 0) return [];\n    const labels = [];\n    const isOdd = rows % 2 === 1;\n    const halfRows = Math.floor(rows / 2);\n    // 左侧偶数（从大到小）\n    for (let i = halfRows; i >= 1; i--) {\n      labels.push(String(i * 2).padStart(2, '0'));\n    }\n    // 中间位置（仅当行数为奇数时）\n    if (isOdd) {\n      if (fromZero) {\n        labels.push('00'); // 从0开始，中间为00\n      } else {\n        labels.push('01'); // 不从0开始，中间为01\n      }\n    }\n    // 右侧奇数（从小到大）\n    if (isOdd && fromZero) {\n      // 奇数行且从0开始：右侧为01/03/05/07...\n      for (let i = 1; i <= halfRows; i++) {\n        labels.push(String(i * 2 - 1).padStart(2, '0'));\n      }\n    } else if (isOdd && !fromZero) {\n      // 奇数行且不从0开始：右侧为03/05/07/09...\n      for (let i = 1; i <= halfRows; i++) {\n        labels.push(String(i * 2 + 1).padStart(2, '0'));\n      }\n    } else {\n      // 偶数行：右侧为01/03/05/07...\n      for (let i = 1; i <= halfRows; i++) {\n        labels.push(String(i * 2 - 1).padStart(2, '0'));\n      }\n    }\n    return labels;\n  }\n  /**\n   * 生成甲板位置\n   */\n  generateDeckPositions(base, step, tiers, rows, fromZero) {\n    const positions = [];\n    const rowLabels = this.generateRowLabels(rows, fromZero);\n    for (let tierIndex = 0; tierIndex < tiers; tierIndex++) {\n      const tierPositions = [];\n      const tierLabel = String(base + tierIndex * step).padStart(2, '0');\n      for (let rowIndex = 0; rowIndex < rows; rowIndex++) {\n        const rowLabel = rowLabels[rowIndex];\n        const positionId = `${rowLabel}${tierLabel}`;\n        tierPositions.push({\n          id: positionId,\n          label: positionId,\n          selected: false,\n          deleted: this.deletedPositions.has(positionId),\n          tierIndex,\n          rowIndex\n        });\n      }\n      positions.push(tierPositions);\n    }\n    return positions;\n  }\n  /**\n   * 生成舱底位置\n   */\n  generateHoldPositions(base, step, tiers, rows, fromZero) {\n    const positions = [];\n    const rowLabels = this.generateRowLabels(rows, fromZero);\n    // 舱底区域从下到上排列，所以需要反向生成层级\n    for (let tierIndex = tiers - 1; tierIndex >= 0; tierIndex--) {\n      const tierPositions = [];\n      const tierLabel = String(base + tierIndex * step).padStart(2, '0');\n      for (let rowIndex = 0; rowIndex < rows; rowIndex++) {\n        const rowLabel = rowLabels[rowIndex];\n        const positionId = `${rowLabel}${tierLabel}`;\n        tierPositions.push({\n          id: positionId,\n          label: positionId,\n          selected: false,\n          deleted: this.deletedPositions.has(positionId),\n          tierIndex,\n          rowIndex\n        });\n      }\n      positions.push(tierPositions);\n    }\n    return positions;\n  }\n  /**\n   * 保存新建贝\n   */\n  saveNewBay() {\n    for (const i in this.newBayForm.controls) {\n      this.newBayForm.controls[i].markAsDirty();\n      this.newBayForm.controls[i].updateValueAndValidity();\n    }\n    if (this.newBayForm.invalid) {\n      return;\n    }\n    // 检查贝号唯一性\n    const bayNo = this.newBayForm.get('bayNo')?.value;\n    if (this.bayList.some(bay => bay.bayNo === bayNo)) {\n      this.showState(ModalTypeEnum.error, '贝号已存在，请重新输入！');\n      return;\n    }\n    // 生成有效贝位列表（排除已删除的位置）\n    const validPositions = this.generateValidPositionsList();\n    const formValue = this.newBayForm.getRawValue();\n    const requestData = {\n      vesselId: this.vesselId,\n      bayNo: formValue.bayNo,\n      rowtier: validPositions.join(','),\n      dtier: formValue.deckTiers,\n      htier: formValue.holdTiers,\n      drow: formValue.deckRows,\n      hrow: formValue.holdRows,\n      dfrom: formValue.deckBase,\n      dstep: formValue.deckStep,\n      hfrom: formValue.holdBase,\n      hstep: formValue.holdStep,\n      drowFromZeroTag: formValue.deckFromZero,\n      hrowFromZeroTag: formValue.holdFromZero,\n      remark: formValue.remark || ''\n    };\n    this.cwfRestfulService.post('/vessel-bay', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '保存成功！');\n        this.isNewBayModalVisible = false;\n        this.previewBayData = null;\n        this.deletedPositions.clear();\n        // 重新加载贝位列表，并在完成后选择新创建的贝位\n        this.loadBayListAndSelect(formValue.bayNo);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  /**\n   * 生成有效贝位列表\n   */\n  generateValidPositionsList() {\n    const validPositions = [];\n    if (this.previewBayData) {\n      // 甲板位置\n      for (const tierPositions of this.previewBayData.deckPositions) {\n        for (const position of tierPositions) {\n          if (!position.deleted) {\n            validPositions.push(position.id);\n          }\n        }\n      }\n      // 舱底位置\n      for (const tierPositions of this.previewBayData.holdPositions) {\n        for (const position of tierPositions) {\n          if (!position.deleted) {\n            validPositions.push(position.id);\n          }\n        }\n      }\n    }\n    return validPositions;\n  }\n  /**\n   * 检查是否有选中的位置\n   */\n  hasSelectedPositions() {\n    if (!this.previewBayData) return false;\n    // 检查甲板位置\n    for (const tierPositions of this.previewBayData.deckPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          return true;\n        }\n      }\n    }\n    // 检查舱底位置\n    for (const tierPositions of this.previewBayData.holdPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  /**\n   * 获取选中位置数量\n   */\n  getSelectedCount() {\n    if (!this.previewBayData) return 0;\n    let count = 0;\n    // 统计甲板位置\n    for (const tierPositions of this.previewBayData.deckPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          count++;\n        }\n      }\n    }\n    // 统计舱底位置\n    for (const tierPositions of this.previewBayData.holdPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          count++;\n        }\n      }\n    }\n    return count;\n  }\n  /**\n   * 删除选中位置\n   */\n  deleteSelectedPositions() {\n    if (!this.previewBayData) return;\n    // 删除甲板选中位置\n    for (const tierPositions of this.previewBayData.deckPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          position.deleted = true;\n          position.selected = false;\n          this.deletedPositions.add(position.id);\n        }\n      }\n    }\n    // 删除舱底选中位置\n    for (const tierPositions of this.previewBayData.holdPositions) {\n      for (const position of tierPositions) {\n        if (position.selected && !position.deleted) {\n          position.deleted = true;\n          position.selected = false;\n          this.deletedPositions.add(position.id);\n        }\n      }\n    }\n  }\n  /**\n   * 清除选择\n   */\n  clearSelection() {\n    if (!this.previewBayData) return;\n    // 清除甲板位置选择\n    for (const tierPositions of this.previewBayData.deckPositions) {\n      for (const position of tierPositions) {\n        position.selected = false;\n      }\n    }\n    // 清除舱底位置选择\n    for (const tierPositions of this.previewBayData.holdPositions) {\n      for (const position of tierPositions) {\n        position.selected = false;\n      }\n    }\n  }\n  /**\n   * 删除贝\n   */\n  deleteBay() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.selectedBayNo) {\n        _this.showState(ModalTypeEnum.error, '请选择要删除的贝！');\n        return;\n      }\n      const state = yield _this.showConfirm('确认', '是否确认删除？');\n      if (state !== DialogResultEnum.yes) {\n        return;\n      }\n      const bayData = _this.bayList.find(bay => bay.bayNo === _this.selectedBayNo);\n      if (!bayData) return;\n      _this.cwfRestfulService.delete(`/vessel-bay/${bayData.id}`, _this.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          _this.showState(ModalTypeEnum.success, '删除成功！');\n          _this.loadBayList();\n        } else {\n          _this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 插入贝\n   */\n  showInsertBayModal() {\n    if (!this.selectedBayNo) {\n      this.showState(ModalTypeEnum.error, '请选择要插入的贝！');\n      return;\n    }\n    this.insertBayForm.reset();\n    this.isInsertBayModalVisible = true;\n  }\n  /**\n   * 保存插入贝\n   */\n  saveInsertBay() {\n    for (const i in this.insertBayForm.controls) {\n      this.insertBayForm.controls[i].markAsDirty();\n      this.insertBayForm.controls[i].updateValueAndValidity();\n    }\n    if (this.insertBayForm.invalid) {\n      return;\n    }\n    const insertBayNos = this.insertBayForm.get('insertBayNos')?.value;\n    // 验证格式\n    const regex = /^[\\d,，\\s]+$/;\n    if (!regex.test(insertBayNos)) {\n      this.showState(ModalTypeEnum.error, '插入贝位信息格式不正确，请检查后重新输入！');\n      return;\n    }\n    const bayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\n    if (!bayData) return;\n    // 将输入的贝位编号转换为数组格式\n    const bayNos = insertBayNos.replace(/，/g, ',').replace(/\\s/g, '').split(',').filter(pos => pos.trim() !== '');\n    const requestData = {\n      bayId: bayData.id,\n      // 船舶贝的ID\n      bayNos: bayNos // 要插入的贝位编号列表\n    };\n    console.log('船舶贝图 - 插入贝位请求数据:', requestData);\n    this.cwfRestfulService.post('/vessel-bay/vessel/bay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '插入成功！');\n        this.isInsertBayModalVisible = false;\n        this.loadBayList();\n        setTimeout(() => this.onBaySelect(), 100);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).catch(error => {\n      console.error('船舶贝图 - 插入贝位失败:', error);\n      this.showState(ModalTypeEnum.error, '插入贝位失败，请重试');\n    });\n  }\n  /**\n   * 删除贝位\n   */\n  deletePositions() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.selectedBayNo) {\n        _this2.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\n        return;\n      }\n      if (_this2.selectedPositions.length === 0) {\n        _this2.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\n        return;\n      }\n      const bayData = _this2.bayList.find(bay => bay.bayNo === _this2.selectedBayNo);\n      if (!bayData) return;\n      // 根据接口文档规范构建请求数据\n      const requestData = {\n        bayId: bayData.id,\n        // 船舶贝的ID\n        bayNos: _this2.selectedPositions.map(p => p.id) // 要删除的贝位编号列表（字符串数组）\n      };\n      console.log('船舶贝图 - 删除贝位请求数据:', requestData);\n      // 使用正确的接口路径和HTTP方法\n      _this2.cwfRestfulService.delete('/vessel-bay/vessel/bay/batch-delete-positions', _this2.gol.serviceName['tas'].en, {\n        body: requestData\n      }).then(rps => {\n        if (rps.ok) {\n          _this2.showState(ModalTypeEnum.success, '删除成功！');\n          // 将被删除的贝位标记为已删除状态，而不是从界面移除\n          _this2.selectedPositions.forEach(position => {\n            position.deleted = true;\n            position.selected = false; // 取消选中状态\n            _this2.deletedPositions.add(position.id); // 添加到已删除集合\n          });\n          // 清空选中状态\n          _this2.selectedPositions = [];\n          // 更新已删除贝位的下拉选项\n          _this2.updateDeletedPositionOptions();\n          console.log('船舶贝图 - 已删除贝位集合:', Array.from(_this2.deletedPositions));\n        } else {\n          _this2.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 复制贝\n   */\n  showCopyBayModal() {\n    if (!this.selectedBayNo) {\n      this.showState(ModalTypeEnum.error, '请选择要复制的贝位信息！');\n      return;\n    }\n    this.copyBayForm.reset();\n    this.copyBayForm.patchValue({\n      originalBayNo: this.selectedBayNo\n    });\n    this.isCopyBayModalVisible = true;\n  }\n  /**\n   * 保存复制贝\n   */\n  saveCopyBay() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      for (const i in _this3.copyBayForm.controls) {\n        _this3.copyBayForm.controls[i].markAsDirty();\n        _this3.copyBayForm.controls[i].updateValueAndValidity();\n      }\n      if (_this3.copyBayForm.invalid) {\n        return;\n      }\n      const newBayNo = _this3.copyBayForm.get('newBayNo')?.value;\n      // 验证格式 - 允许数字和字母\n      const regex = /^[a-zA-Z0-9]+$/;\n      if (!regex.test(newBayNo.replace(/\\s/g, ''))) {\n        _this3.showState(ModalTypeEnum.error, '新贝号格式不正确，只能包含数字和字母，请检查后重新输入！');\n        return;\n      }\n      // 检查唯一性\n      if (_this3.bayList.some(bay => bay.bayNo === newBayNo)) {\n        const state = yield _this3.showConfirm('确认', '新贝号已存在，是否覆盖？');\n        if (state !== DialogResultEnum.yes) {\n          return;\n        }\n      }\n      const originalBayData = _this3.bayList.find(bay => bay.bayNo === _this3.selectedBayNo);\n      if (!originalBayData) return;\n      // 使用正确的接口路径，根据接口文档使用targetBayNo作为查询参数\n      const apiPath = `/vessel-bay/vessel/${_this3.vesselId}/bay/${_this3.selectedBayNo}/copy?targetBayNo=${encodeURIComponent(newBayNo)}`;\n      _this3.cwfRestfulService.post(apiPath, {}, _this3.gol.serviceName['tas'].en).then(rps => {\n        if (rps.ok) {\n          _this3.showState(ModalTypeEnum.success, '复制成功！');\n          _this3.isCopyBayModalVisible = false;\n          // 重新加载贝位列表，并在完成后选择新复制的贝位\n          _this3.loadBayListAndSelect(newBayNo);\n        } else {\n          _this3.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    })();\n  }\n  /**\n   * 返回主页面\n   */\n  goBack() {\n    this.openPage('/tas/vessel/list');\n  }\n  // ==================== 拖拽多选功能 ====================\n  /**\n   * 初始化拖拽事件监听\n   */\n  initDragEvents() {\n    // 创建事件监听器函数\n    this.keydownListener = e => {\n      if (e.key === 'Control' || e.key === 'Meta') {\n        this.ctrlKeyPressed = true;\n      }\n    };\n    this.keyupListener = e => {\n      if (e.key === 'Control' || e.key === 'Meta') {\n        this.ctrlKeyPressed = false;\n      }\n    };\n    this.mousemoveListener = e => this.onMouseMove(e);\n    this.mouseupListener = e => this.onMouseUp(e);\n    // 添加事件监听器\n    document.addEventListener('keydown', this.keydownListener);\n    document.addEventListener('keyup', this.keyupListener);\n    document.addEventListener('mousemove', this.mousemoveListener);\n    document.addEventListener('mouseup', this.mouseupListener);\n  }\n  /**\n   * 鼠标按下事件（在贝位图容器上）\n   */\n  onMouseDown(event) {\n    // 只处理左键\n    if (event.button !== 0) return;\n    console.log('船舶贝图 - 鼠标按下事件:', {\n      target: event.target.className,\n      isPositionCell: event.target.classList.contains('position-cell'),\n      currentTime: Date.now()\n    });\n    // 记录拖拽开始时间和位置\n    this.dragStartTime = Date.now();\n    this.dragStartX = event.clientX;\n    this.dragStartY = event.clientY;\n    this.hasMoved = false;\n    // 检查是否点击在贝位上\n    const target = event.target;\n    if (target.classList.contains('position-cell')) {\n      // 点击在贝位上，标记为潜在的路径拖拽，但不立即启动\n      this.potentialPathDrag = true;\n      this.potentialPathTarget = target;\n    } else {\n      // 点击在空白区域，标记为潜在的矩形选择\n      this.potentialRectDrag = true;\n    }\n  }\n  /**\n   * 开始路径拖拽选择\n   */\n  startPathDragSelection(event, target) {\n    console.log('船舶贝图 - 开始路径拖拽选择');\n    this.isDragging = true;\n    this.pathDragMode = true;\n    this.pathSelectedPositions.clear();\n    // 获取起始贝位\n    const positionId = target.getAttribute('data-position-id');\n    if (positionId) {\n      this.pathSelectedPositions.add(positionId);\n      this.lastPathPosition = positionId;\n      // 注意：路径拖拽不自动清除之前的选择，保持默认多选模式的一致性\n      // 只有在Ctrl+拖拽时才是追加模式，普通拖拽也是追加模式\n      // 设置起始贝位的拖拽高亮状态，而不是最终选中状态\n      const position = this.findPositionById(positionId);\n      if (position && !position.deleted) {\n        position.dragHighlight = true;\n        this.cdr.detectChanges();\n        console.log('船舶贝图 - 设置起始贝位拖拽高亮:', positionId);\n      }\n    }\n    // 阻止默认行为\n    event.preventDefault();\n  }\n  /**\n   * 开始矩形拖拽选择\n   */\n  startRectangleDragSelection(event) {\n    this.isDragging = true;\n    this.pathDragMode = false;\n    this.dragStartX = event.clientX;\n    this.dragStartY = event.clientY;\n    this.dragCurrentX = event.clientX;\n    this.dragCurrentY = event.clientY;\n    // 创建选择框\n    this.createSelectionBox();\n    // 阻止默认行为\n    event.preventDefault();\n  }\n  /**\n   * 创建选择框\n   */\n  createSelectionBox() {\n    if (this.dragSelectionBox) {\n      this.dragSelectionBox.remove();\n    }\n    this.dragSelectionBox = document.createElement('div');\n    this.dragSelectionBox.className = 'drag-selection-box';\n    this.dragSelectionBox.style.cssText = `\n      position: fixed;\n      border: 2px dashed #1890ff;\n      background: rgba(24, 144, 255, 0.1);\n      pointer-events: none;\n      z-index: 1000;\n      left: ${this.dragStartX}px;\n      top: ${this.dragStartY}px;\n      width: 0px;\n      height: 0px;\n    `;\n    document.body.appendChild(this.dragSelectionBox);\n  }\n  /**\n   * 鼠标移动事件\n   */\n  onMouseMove(event) {\n    // 检查是否需要启动拖拽\n    if (!this.isDragging && (this.potentialPathDrag || this.potentialRectDrag)) {\n      const deltaX = Math.abs(event.clientX - this.dragStartX);\n      const deltaY = Math.abs(event.clientY - this.dragStartY);\n      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n      // 如果鼠标移动超过阈值，启动拖拽\n      if (distance > this.moveThreshold) {\n        this.hasMoved = true;\n        if (this.potentialPathDrag && this.potentialPathTarget) {\n          console.log('船舶贝图 - 启动路径拖拽模式，移动距离:', distance);\n          this.startPathDragSelection(event, this.potentialPathTarget);\n        } else if (this.potentialRectDrag) {\n          console.log('船舶贝图 - 启动矩形选择模式，移动距离:', distance);\n          this.startRectangleDragSelection(event);\n        }\n        // 清除潜在拖拽状态\n        this.potentialPathDrag = false;\n        this.potentialRectDrag = false;\n        this.potentialPathTarget = null;\n      }\n    }\n    // 处理正在进行的拖拽\n    if (this.isDragging) {\n      if (this.pathDragMode) {\n        // 路径拖拽模式\n        this.handlePathDragMove(event);\n      } else {\n        // 矩形选择模式\n        this.handleRectangleDragMove(event);\n      }\n    }\n  }\n  /**\n   * 处理路径拖拽移动\n   */\n  handlePathDragMove(event) {\n    // 获取鼠标位置下的元素\n    const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY);\n    if (elementUnderMouse && elementUnderMouse.classList.contains('position-cell')) {\n      const positionId = elementUnderMouse.getAttribute('data-position-id');\n      if (positionId && positionId !== this.lastPathPosition && !this.pathSelectedPositions.has(positionId)) {\n        // 检查是否为有效贝位（未删除）\n        const position = this.findPositionById(positionId);\n        if (position && !position.deleted) {\n          this.pathSelectedPositions.add(positionId);\n          this.lastPathPosition = positionId;\n          // 设置拖拽高亮状态，而不是最终选中状态\n          position.dragHighlight = true;\n          // 立即触发变更检测，确保视觉反馈\n          this.cdr.detectChanges();\n          console.log('船舶贝图 - 路径拖拽经过贝位:', positionId, '当前路径选择数量:', this.pathSelectedPositions.size);\n        }\n      }\n    }\n  }\n  /**\n   * 处理矩形拖拽移动\n   */\n  handleRectangleDragMove(event) {\n    if (!this.dragSelectionBox) return;\n    this.dragCurrentX = event.clientX;\n    this.dragCurrentY = event.clientY;\n    // 更新选择框位置和大小\n    this.updateSelectionBox();\n    // 高亮选择区域内的贝位\n    this.highlightPositionsInSelection();\n  }\n  /**\n   * 更新选择框\n   */\n  updateSelectionBox() {\n    if (!this.dragSelectionBox) return;\n    const left = Math.min(this.dragStartX, this.dragCurrentX);\n    const top = Math.min(this.dragStartY, this.dragCurrentY);\n    const width = Math.abs(this.dragCurrentX - this.dragStartX);\n    const height = Math.abs(this.dragCurrentY - this.dragStartY);\n    this.dragSelectionBox.style.left = `${left}px`;\n    this.dragSelectionBox.style.top = `${top}px`;\n    this.dragSelectionBox.style.width = `${width}px`;\n    this.dragSelectionBox.style.height = `${height}px`;\n  }\n  /**\n   * 高亮选择区域内的贝位\n   */\n  highlightPositionsInSelection() {\n    if (!this.previewBayData) return;\n    const selectionRect = this.getSelectionRect();\n    // 检查甲板区域的贝位\n    if (this.previewBayData.deckPositions) {\n      this.previewBayData.deckPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          if (position.deleted) return;\n          const positionElement = this.getPositionElement(position.id);\n          if (positionElement && this.isElementInSelection(positionElement, selectionRect)) {\n            position.dragHighlight = true;\n          } else {\n            position.dragHighlight = false;\n          }\n        });\n      });\n    }\n    // 检查舱底区域的贝位\n    if (this.previewBayData.holdPositions) {\n      this.previewBayData.holdPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          if (position.deleted) return;\n          const positionElement = this.getPositionElement(position.id);\n          if (positionElement && this.isElementInSelection(positionElement, selectionRect)) {\n            position.dragHighlight = true;\n          } else {\n            position.dragHighlight = false;\n          }\n        });\n      });\n    }\n  }\n  /**\n   * 获取选择区域的矩形\n   */\n  getSelectionRect() {\n    return {\n      left: Math.min(this.dragStartX, this.dragCurrentX),\n      top: Math.min(this.dragStartY, this.dragCurrentY),\n      right: Math.max(this.dragStartX, this.dragCurrentX),\n      bottom: Math.max(this.dragStartY, this.dragCurrentY)\n    };\n  }\n  /**\n   * 获取贝位元素\n   */\n  getPositionElement(positionId) {\n    return document.querySelector(`[data-position-id=\"${positionId}\"]`);\n  }\n  /**\n   * 检查元素是否在选择区域内\n   */\n  isElementInSelection(element, selectionRect) {\n    const elementRect = element.getBoundingClientRect();\n    return !(elementRect.right < selectionRect.left || elementRect.left > selectionRect.right || elementRect.bottom < selectionRect.top || elementRect.top > selectionRect.bottom);\n  }\n  /**\n   * 鼠标释放事件\n   */\n  onMouseUp(event) {\n    console.log('船舶贝图 - 鼠标释放事件:', {\n      isDragging: this.isDragging,\n      pathDragMode: this.pathDragMode,\n      potentialPathDrag: this.potentialPathDrag,\n      potentialRectDrag: this.potentialRectDrag,\n      hasMoved: this.hasMoved,\n      dragStartTime: this.dragStartTime\n    });\n    // 处理正在进行的拖拽\n    if (this.isDragging) {\n      // 计算拖拽持续时间\n      const dragDuration = Date.now() - this.dragStartTime;\n      console.log('船舶贝图 - 拖拽持续时间:', dragDuration, 'ms');\n      // 完成拖拽操作\n      if (this.pathDragMode) {\n        // 路径拖拽完成\n        this.completePathDragSelection();\n      } else {\n        // 矩形选择完成\n        this.completeRectangleDragSelection();\n      }\n    }\n    // 清理所有拖拽相关状态\n    this.isDragging = false;\n    this.pathDragMode = false;\n    this.pathSelectedPositions.clear();\n    this.lastPathPosition = null;\n    this.potentialPathDrag = false;\n    this.potentialRectDrag = false;\n    this.potentialPathTarget = null;\n    this.hasMoved = false;\n    this.dragStartTime = 0;\n    // 确保清除所有拖拽高亮状态（防止遗漏）\n    this.clearDragHighlight();\n    if (this.dragSelectionBox) {\n      this.dragSelectionBox.remove();\n      this.dragSelectionBox = null;\n    }\n  }\n  /**\n   * 完成路径拖拽选择\n   */\n  completePathDragSelection() {\n    console.log('船舶贝图 - 开始完成路径拖拽选择，路径选择数量:', this.pathSelectedPositions.size);\n    // 先清除所有拖拽高亮状态\n    this.clearDragHighlight();\n    // 将路径选择的贝位转换为最终选中状态\n    this.pathSelectedPositions.forEach(positionId => {\n      const position = this.findPositionById(positionId);\n      if (position && !position.deleted) {\n        // 设置为最终选中状态\n        if (!position.selected) {\n          position.selected = true;\n          if (!this.selectedPositions.find(p => p.id === position.id)) {\n            this.selectedPositions.push(position);\n          }\n        }\n      }\n    });\n    // 触发变更检测，确保视觉状态更新\n    this.cdr.detectChanges();\n    console.log('船舶贝图 - 路径拖拽选择完成，当前选中的贝位:', this.selectedPositions.map(p => p.id));\n  }\n  /**\n   * 完成矩形拖拽选择\n   */\n  completeRectangleDragSelection() {\n    if (!this.previewBayData) return;\n    // 如果不是Ctrl键，先清除之前的选择\n    if (!this.ctrlKeyPressed) {\n      this.clearAllSelections();\n    }\n    // 选择拖拽高亮的贝位\n    this.selectDragHighlightedPositions();\n    // 清除拖拽高亮状态\n    this.clearDragHighlight();\n  }\n  /**\n   * 清除所有选择\n   */\n  clearAllSelections() {\n    this.selectedPositions = [];\n    if (this.previewBayData.deckPositions) {\n      this.previewBayData.deckPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          position.selected = false;\n        });\n      });\n    }\n    if (this.previewBayData.holdPositions) {\n      this.previewBayData.holdPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          position.selected = false;\n        });\n      });\n    }\n  }\n  /**\n   * 选择拖拽高亮的贝位\n   */\n  selectDragHighlightedPositions() {\n    if (!this.previewBayData) return;\n    // 处理甲板区域\n    if (this.previewBayData.deckPositions) {\n      this.previewBayData.deckPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          if (position.dragHighlight && !position.deleted) {\n            position.selected = true;\n            if (!this.selectedPositions.find(p => p.id === position.id)) {\n              this.selectedPositions.push(position);\n            }\n          }\n        });\n      });\n    }\n    // 处理舱底区域\n    if (this.previewBayData.holdPositions) {\n      this.previewBayData.holdPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          if (position.dragHighlight && !position.deleted) {\n            position.selected = true;\n            if (!this.selectedPositions.find(p => p.id === position.id)) {\n              this.selectedPositions.push(position);\n            }\n          }\n        });\n      });\n    }\n    console.log('船舶贝图 - 拖拽选择完成，当前选中的贝位:', this.selectedPositions);\n  }\n  /**\n   * 清除拖拽高亮状态\n   */\n  clearDragHighlight() {\n    if (!this.previewBayData) return;\n    if (this.previewBayData.deckPositions) {\n      this.previewBayData.deckPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          position.dragHighlight = false;\n        });\n      });\n    }\n    if (this.previewBayData.holdPositions) {\n      this.previewBayData.holdPositions.forEach(tierPositions => {\n        tierPositions.forEach(position => {\n          position.dragHighlight = false;\n        });\n      });\n    }\n  }\n  /**\n   * 组件销毁时清理事件监听器\n   */\n  ngOnDestroy() {\n    // 清理事件监听器\n    if (this.keydownListener) {\n      document.removeEventListener('keydown', this.keydownListener);\n    }\n    if (this.keyupListener) {\n      document.removeEventListener('keyup', this.keyupListener);\n    }\n    if (this.mousemoveListener) {\n      document.removeEventListener('mousemove', this.mousemoveListener);\n    }\n    if (this.mouseupListener) {\n      document.removeEventListener('mouseup', this.mouseupListener);\n    }\n    // 清理选择框\n    if (this.dragSelectionBox) {\n      this.dragSelectionBox.remove();\n      this.dragSelectionBox = null;\n    }\n  }\n  /**\n   * 根据ID查找贝位对象\n   */\n  findPositionById(positionId) {\n    if (!this.previewBayData) return null;\n    // 在甲板区域查找\n    if (this.previewBayData.deckPositions) {\n      for (const tierPositions of this.previewBayData.deckPositions) {\n        const position = tierPositions.find(p => p.id === positionId);\n        if (position) return position;\n      }\n    }\n    // 在舱底区域查找\n    if (this.previewBayData.holdPositions) {\n      for (const tierPositions of this.previewBayData.holdPositions) {\n        const position = tierPositions.find(p => p.id === positionId);\n        if (position) return position;\n      }\n    }\n    return null;\n  }\n  /**\n   * 根据ID选择贝位\n   */\n  selectPositionById(positionId) {\n    const position = this.findPositionById(positionId);\n    if (position && !position.deleted) {\n      // 如果贝位未选中，则选中它\n      if (!position.selected) {\n        position.selected = true;\n        if (!this.selectedPositions.find(p => p.id === position.id)) {\n          this.selectedPositions.push(position);\n        }\n        // 立即触发变更检测，确保视觉反馈\n        this.cdr.detectChanges();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function VesselBayComponent_Factory(t) {\n      return new (t || VesselBayComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.CwfRestfulService), i0.ɵɵdirectiveInject(i4.NzModalService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VesselBayComponent,\n      selectors: [[\"tas-vessel-bay-app\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 40,\n      vars: 27,\n      consts: [[1, \"title-card\", 3, \"nzBodyStyle\"], [1, \"page-title\"], [1, \"operations-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"24\"], [1, \"bay-operations\"], [\"nz-button\", \"\", 3, \"click\", \"nzType\"], [\"nz-button\", \"\", 1, \"return-button\", 3, \"click\", \"nzType\"], [1, \"selector-card\", 3, \"nzBodyStyle\"], [1, \"bay-selector-container\"], [1, \"bay-selector\"], [1, \"bay-selector-label\"], [1, \"bay-selector-dropdown\", 3, \"ngModelChange\", \"ngModel\"], [3, \"nzValue\", \"nzLabel\", 4, \"ngFor\", \"ngForOf\"], [1, \"diagram-card\", 3, \"nzBodyStyle\"], [1, \"bay-diagram-container\", 3, \"mousedown\"], [\"class\", \"bay-diagram\", 4, \"ngIf\"], [\"nzTitle\", \"\\u65B0\\u5EFA\\u8D1D\", \"nzOkText\", \"\\u4FDD\\u5B58\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\"], [4, \"nzModalContent\"], [\"nzTitle\", \"\\u63D2\\u5165\\u8D1D\", \"nzOkText\", \"\\u4FDD\\u5B58\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\"], [\"nzTitle\", \"\\u590D\\u5236\\u8D1D\", \"nzOkText\", \"\\u4FDD\\u5B58\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\"], [\"nzTitle\", \"\\u6062\\u590D\\u8D1D\\u4F4D\", \"nzOkText\", \"\\u6062\\u590D\\u9009\\u4E2D\\u8D1D\\u4F4D\", \"nzCancelText\", \"\\u53D6\\u6D88\", 3, \"nzVisibleChange\", \"nzOnCancel\", \"nzOnOk\", \"nzVisible\", \"nzWidth\", \"nzBodyStyle\"], [3, \"nzValue\", \"nzLabel\"], [1, \"bay-diagram\"], [\"class\", \"deck-area\", 4, \"ngIf\"], [\"class\", \"deck-separator\", 4, \"ngIf\"], [\"class\", \"hold-area\", 4, \"ngIf\"], [\"class\", \"no-positions\", 4, \"ngIf\"], [1, \"deck-area\"], [1, \"deck-header\"], [1, \"row-labels\"], [\"class\", \"row-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"deck-content\"], [\"class\", \"deck-tier\", 4, \"ngFor\", \"ngForOf\"], [1, \"row-label\"], [1, \"deck-tier\"], [1, \"tier-label\"], [1, \"tier-positions\"], [\"class\", \"position-cell deck-position\", 3, \"selected\", \"deleted\", \"drag-highlight\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"position-cell\", \"deck-position\", 3, \"click\"], [1, \"deck-separator\"], [1, \"separator-line\"], [1, \"separator-text\"], [1, \"hold-area\"], [1, \"hold-content\"], [\"class\", \"hold-tier\", 4, \"ngFor\", \"ngForOf\"], [1, \"hold-footer\"], [1, \"hold-tier\"], [\"class\", \"position-cell hold-position\", 3, \"selected\", \"deleted\", \"drag-highlight\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"position-cell\", \"hold-position\", 3, \"click\"], [1, \"no-positions\"], [\"nzNotFoundContent\", \"\\u6682\\u65E0\\u8D1D\\u4F4D\\u6570\\u636E\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"10\"], [\"nzTitle\", \"\\u53C2\\u6570\\u914D\\u7F6E\", 3, \"nzSize\"], [\"nz-form\", \"\", 3, \"formGroup\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"nzRequired\", \"\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8D1D\\u53F7\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8D1D\\u53F7\", \"formControlName\", \"bayNo\"], [\"nzText\", \"\\u7532\\u677F\\u53C2\\u6570\", \"nzOrientation\", \"left\"], [\"nz-col\", \"\", \"nzSpan\", \"12\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u7532\\u677F\\u57FA\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u7532\\u677F\\u57FA\\u6570\", \"formControlName\", \"deckBase\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u7532\\u677F\\u6B65\\u957F\"], [\"nz-input\", \"\", \"placeholder\", \"\\u7532\\u677F\\u6B65\\u957F\", \"formControlName\", \"deckStep\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u7532\\u677F\\u5C42\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u7532\\u677F\\u5C42\\u6570\", \"formControlName\", \"deckTiers\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u7532\\u677F\\u884C\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u7532\\u677F\\u884C\\u6570\", \"formControlName\", \"deckRows\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"formControlName\", \"deckFromZero\", 3, \"ngModelChange\"], [\"nz-radio\", \"\", \"nzValue\", \"Y\"], [\"nz-radio\", \"\", \"nzValue\", \"N\"], [\"nzText\", \"\\u8231\\u5E95\\u53C2\\u6570\", \"nzOrientation\", \"left\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8231\\u5E95\\u57FA\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8231\\u5E95\\u57FA\\u6570\", \"formControlName\", \"holdBase\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8231\\u5E95\\u6B65\\u957F\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8231\\u5E95\\u6B65\\u957F\", \"formControlName\", \"holdStep\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8231\\u5E95\\u5C42\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8231\\u5E95\\u5C42\\u6570\", \"formControlName\", \"holdTiers\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u8231\\u5E95\\u884C\\u6570\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8231\\u5E95\\u884C\\u6570\", \"formControlName\", \"holdRows\", 3, \"ngModelChange\", \"nzMin\", \"nzMax\"], [\"formControlName\", \"holdFromZero\", 3, \"ngModelChange\"], [\"nz-input\", \"\", \"placeholder\", \"\\u5907\\u6CE8\", \"formControlName\", \"remark\", 3, \"nzAutosize\"], [\"nz-col\", \"\", \"nzSpan\", \"14\"], [\"nzTitle\", \"\\u8D1D\\u56FE\\u9884\\u89C8\", 3, \"nzSize\"], [1, \"bay-diagram-container\"], [\"class\", \"bay-operations\", 4, \"ngIf\"], [\"nzNotFoundImage\", \"simple\", \"nzNotFoundContent\", \"\\u8BF7\\u914D\\u7F6E\\u53C2\\u6570\\u4EE5\\u751F\\u6210\\u8D1D\\u56FE\\u9884\\u89C8\", 4, \"ngIf\"], [\"class\", \"position-cell deck-position\", 3, \"selected\", \"deleted\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"position-cell hold-position\", 3, \"selected\", \"deleted\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzDanger\", \"\", 3, \"click\", 4, \"nzSpaceItem\"], [\"nz-button\", \"\", 3, \"click\", 4, \"nzSpaceItem\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzDanger\", \"\", 3, \"click\"], [\"nz-button\", \"\", 3, \"click\"], [\"nzNotFoundImage\", \"simple\", \"nzNotFoundContent\", \"\\u8BF7\\u914D\\u7F6E\\u53C2\\u6570\\u4EE5\\u751F\\u6210\\u8D1D\\u56FE\\u9884\\u89C8\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u63D2\\u5165\\u8D1D\\u4F4D\\u4FE1\\u606F\"], [\"nz-input\", \"\", \"placeholder\", \"\\u8BF7\\u8F93\\u5165\\u8D1D\\u4F4D\\u4FE1\\u606F\\uFF0C\\u7528\\u9017\\u53F7\\u5206\\u9694\\uFF0C\\u5982\\uFF1A0101,0103,0105\", \"formControlName\", \"insertBayNos\", 3, \"nzAutosize\"], [\"nzType\", \"info\", \"nzMessage\", \"\\u683C\\u5F0F\\u8BF4\\u660E\\uFF1A\\u53EA\\u80FD\\u5305\\u542B\\u6570\\u5B57\\u548C\\u9017\\u53F7\\uFF08\\u4E2D\\u82F1\\u6587\\u9017\\u53F7\\u90FD\\u53EF\\u4EE5\\uFF09\\uFF0C\\u7CFB\\u7EDF\\u4F1A\\u81EA\\u52A8\\u5254\\u9664\\u7A7A\\u683C\", \"nzShowIcon\", \"\"], [\"nz-input\", \"\", \"formControlName\", \"originalBayNo\", 3, \"disabled\"], [\"nzErrorTip\", \"\\u8BF7\\u8F93\\u5165\\u65B0\\u8D1D\\u53F7\"], [\"nz-input\", \"\", \"placeholder\", \"\\u65B0\\u8D1D\\u53F7\", \"formControlName\", \"newBayNo\"], [\"nzType\", \"info\", \"nzMessage\", \"\\u683C\\u5F0F\\u8BF4\\u660E\\uFF1A\\u53EF\\u4EE5\\u5305\\u542B\\u6570\\u5B57\\u548C\\u5B57\\u6BCD\\uFF0C\\u7CFB\\u7EDF\\u4F1A\\u81EA\\u52A8\\u5254\\u9664\\u7A7A\\u683C\", \"nzShowIcon\", \"\"], [1, \"restore-position-container\"], [1, \"restore-search-section\"], [3, \"nzGutter\"], [\"nzSpan\", \"12\"], [\"nzPrefixIcon\", \"search\"], [\"nz-input\", \"\", \"placeholder\", \"\\u641C\\u7D22\\u8D1D\\u4F4D\\u7F16\\u53F7...\", 3, \"ngModelChange\", \"ngModel\"], [\"nzPlaceHolder\", \"\\u7B5B\\u9009\\u533A\\u57DF\", 2, \"width\", \"100%\", 3, \"ngModelChange\", \"ngModel\"], [\"nzValue\", \"\", \"nzLabel\", \"\\u5168\\u90E8\\u533A\\u57DF\"], [\"nzValue\", \"deck\", \"nzLabel\", \"\\u7532\\u677F\\u533A\\u57DF\"], [\"nzValue\", \"hold\", \"nzLabel\", \"\\u8231\\u5E95\\u533A\\u57DF\"], [1, \"restore-batch-operations\"], [\"nz-button\", \"\", \"nzSize\", \"small\", 3, \"click\", 4, \"nzSpaceItem\"], [\"class\", \"restore-selection-count\", 4, \"nzSpaceItem\"], [1, \"restore-positions-grid-container\"], [\"class\", \"restore-positions-grid\", 4, \"ngIf\"], [\"class\", \"restore-empty-state\", 4, \"ngIf\"], [1, \"restore-info-section\"], [\"nzType\", \"info\", \"nzShowIcon\", \"\", \"nzMessage\", \"\\u9009\\u62E9\\u5DF2\\u5220\\u9664\\u7684\\u8D1D\\u4F4D\\u8FDB\\u884C\\u6062\\u590D\\uFF0C\\u6062\\u590D\\u540E\\u8D1D\\u4F4D\\u5C06\\u91CD\\u65B0\\u663E\\u793A\\u5728\\u8D1D\\u56FE\\u4E2D\\u3002\\u63D0\\u793A\\uFF1A\\u70B9\\u51FB\\u8D1D\\u4F4D\\u5361\\u7247\\u8FDB\\u884C\\u9009\\u62E9\\uFF0C\\u652F\\u6301\\u591A\\u9009\\u64CD\\u4F5C\\u3002\"], [\"nz-button\", \"\", \"nzSize\", \"small\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"check-square\", \"nzTheme\", \"outline\"], [\"nz-icon\", \"\", \"nzType\", \"border\", \"nzTheme\", \"outline\"], [\"nz-icon\", \"\", \"nzType\", \"close-square\", \"nzTheme\", \"outline\"], [1, \"restore-selection-count\"], [1, \"restore-positions-grid\"], [\"class\", \"restore-position-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"restore-position-card\", 3, \"click\"], [1, \"position-card-content\"], [1, \"position-id\"], [1, \"position-area\"], [\"nz-icon\", \"\", 1, \"position-check-icon\", 3, \"nzType\", \"nzTheme\"], [1, \"restore-empty-state\"], [\"nzNotFoundImage\", \"simple\", 3, \"nzNotFoundContent\"]],\n      template: function VesselBayComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nz-card\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"\\u8239\\u8236\\u8D1D\\u56FE\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(4, \"nz-card\", 2)(5, \"nz-row\")(6, \"nz-col\", 3)(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_8_listener() {\n            return ctx.showNewBayModal();\n          });\n          i0.ɵɵtext(9, \" \\u65B0\\u5EFA\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_10_listener() {\n            return ctx.showCopyBayModal();\n          });\n          i0.ɵɵtext(11, \" \\u590D\\u5236\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_12_listener() {\n            return ctx.deleteBay();\n          });\n          i0.ɵɵtext(13, \" \\u5220\\u9664\\u8D1D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_14_listener() {\n            return ctx.showRestorePositionModal();\n          });\n          i0.ɵɵtext(15, \" \\u6062\\u590D\\u8D1D\\u4F4D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_16_listener() {\n            return ctx.deletePositions();\n          });\n          i0.ɵɵtext(17, \" \\u5220\\u9664\\u8D1D\\u4F4D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function VesselBayComponent_Template_button_click_18_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(19, \" \\u8FD4\\u56DE \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(20, \"nz-card\", 7)(21, \"nz-row\")(22, \"nz-col\", 3)(23, \"div\", 8)(24, \"div\", 9)(25, \"span\", 10);\n          i0.ɵɵtext(26, \"\\u8D1D\\u53F7\\u9009\\u62E9:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nz-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function VesselBayComponent_Template_nz_select_ngModelChange_27_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBayNo, $event) || (ctx.selectedBayNo = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function VesselBayComponent_Template_nz_select_ngModelChange_27_listener() {\n            return ctx.onBaySelect();\n          });\n          i0.ɵɵtemplate(28, VesselBayComponent_nz_option_28_Template, 1, 2, \"nz-option\", 12);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(29, \"nz-card\", 13)(30, \"div\", 14);\n          i0.ɵɵlistener(\"mousedown\", function VesselBayComponent_Template_div_mousedown_30_listener($event) {\n            return ctx.onMouseDown($event);\n          });\n          i0.ɵɵtemplate(31, VesselBayComponent_div_31_Template, 5, 4, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"nz-modal\", 16);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselBayComponent_Template_nz_modal_nzVisibleChange_32_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isNewBayModalVisible, $event) || (ctx.isNewBayModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselBayComponent_Template_nz_modal_nzOnCancel_32_listener() {\n            return ctx.cancelNewBay();\n          })(\"nzOnOk\", function VesselBayComponent_Template_nz_modal_nzOnOk_32_listener() {\n            return ctx.saveNewBay();\n          });\n          i0.ɵɵtemplate(33, VesselBayComponent_ng_container_33_Template, 96, 28, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nz-modal\", 18);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselBayComponent_Template_nz_modal_nzVisibleChange_34_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isInsertBayModalVisible, $event) || (ctx.isInsertBayModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselBayComponent_Template_nz_modal_nzOnCancel_34_listener() {\n            return ctx.isInsertBayModalVisible = false;\n          })(\"nzOnOk\", function VesselBayComponent_Template_nz_modal_nzOnOk_34_listener() {\n            return ctx.saveInsertBay();\n          });\n          i0.ɵɵtemplate(35, VesselBayComponent_ng_container_35_Template, 8, 3, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"nz-modal\", 19);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselBayComponent_Template_nz_modal_nzVisibleChange_36_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isCopyBayModalVisible, $event) || (ctx.isCopyBayModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselBayComponent_Template_nz_modal_nzOnCancel_36_listener() {\n            return ctx.isCopyBayModalVisible = false;\n          })(\"nzOnOk\", function VesselBayComponent_Template_nz_modal_nzOnOk_36_listener() {\n            return ctx.saveCopyBay();\n          });\n          i0.ɵɵtemplate(37, VesselBayComponent_ng_container_37_Template, 13, 2, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nz-modal\", 20);\n          i0.ɵɵtwoWayListener(\"nzVisibleChange\", function VesselBayComponent_Template_nz_modal_nzVisibleChange_38_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isRestorePositionModalVisible, $event) || (ctx.isRestorePositionModalVisible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"nzOnCancel\", function VesselBayComponent_Template_nz_modal_nzOnCancel_38_listener() {\n            return ctx.cancelRestorePosition();\n          })(\"nzOnOk\", function VesselBayComponent_Template_nz_modal_nzOnOk_38_listener() {\n            return ctx.restoreSelectedPositions();\n          });\n          i0.ɵɵtemplate(39, VesselBayComponent_ng_container_39_Template, 23, 6, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(22, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(23, _c1));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzType\", \"primary\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzType\", \"default\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(24, _c2));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBayNo);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.bayList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(25, _c3));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.previewBayData);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isNewBayModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 1200);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isInsertBayModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 600);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isCopyBayModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 500);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"nzVisible\", ctx.isRestorePositionModalVisible);\n          i0.ɵɵproperty(\"nzWidth\", 900)(\"nzBodyStyle\", i0.ɵɵpureFunction0(26, _c4));\n        }\n      },\n      dependencies: [i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.NgModel, i6.NgForOf, i6.NgIf, i5.FormGroupDirective, i5.FormControlName, i7.NzColDirective, i7.NzRowDirective, i8.NzFormDirective, i8.NzFormItemComponent, i8.NzFormLabelComponent, i8.NzFormControlComponent, i9.NzButtonComponent, i10.ɵNzTransitionPatchDirective, i11.NzWaveDirective, i12.NzInputDirective, i12.NzInputGroupComponent, i12.NzAutosizeDirective, i13.NzInputNumberComponent, i14.NzAlertComponent, i15.NzOptionComponent, i15.NzSelectComponent, i16.NzCardComponent, i4.NzModalComponent, i4.NzModalContentDirective, i17.NzIconDirective, i18.NzDividerComponent, i19.NzRadioComponent, i19.NzRadioGroupComponent, i20.NzEmptyComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n[_nghost-%COMP%]   .title-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .operations-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .selector-card[_ngcontent-%COMP%], [_nghost-%COMP%]   .diagram-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n[_nghost-%COMP%]   .title-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .operations-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .selector-card[_ngcontent-%COMP%]:last-child, [_nghost-%COMP%]   .diagram-card[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.title-card[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #262626;\\n  line-height: 1.4;\\n}\\n.operations-card[_ngcontent-%COMP%]   .bay-operations[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 0;\\n  flex-wrap: wrap;\\n}\\n.operations-card[_ngcontent-%COMP%]   .bay-operations[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  height: 32px;\\n  padding: 0 16px;\\n  font-size: 14px;\\n  border-radius: 4px;\\n}\\n.operations-card[_ngcontent-%COMP%]   .bay-operations[_ngcontent-%COMP%]   button[nz-button][nzType=\\\"primary\\\"][_ngcontent-%COMP%] {\\n  background: #1890ff;\\n  border-color: #1890ff;\\n}\\n.operations-card[_ngcontent-%COMP%]   .bay-operations[_ngcontent-%COMP%]   button[nz-button][nzType=\\\"primary\\\"][_ngcontent-%COMP%]:hover {\\n  background: #40a9ff;\\n  border-color: #40a9ff;\\n}\\n.operations-card[_ngcontent-%COMP%]   .bay-operations[_ngcontent-%COMP%]   .return-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n.selector-card[_ngcontent-%COMP%]   .bay-selector-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n}\\n.selector-card[_ngcontent-%COMP%]   .bay-selector-container[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.selector-card[_ngcontent-%COMP%]   .bay-selector-container[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%]   .bay-selector-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #595959;\\n  font-weight: 500;\\n  white-space: nowrap;\\n}\\n.selector-card[_ngcontent-%COMP%]   .bay-selector-container[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%]   .bay-selector-dropdown[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n.diagram-card[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n.bay-diagram-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #fafafa;\\n  border-radius: 6px;\\n  min-height: 500px;\\n  max-height: 700px;\\n  overflow: auto;\\n}\\n.bay-diagram-container[_ngcontent-%COMP%]   .bay-diagram[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  padding: 20px;\\n  border-radius: 4px;\\n  border: 1px solid #d9d9d9;\\n}\\n.deck-label-common[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  line-height: 1;\\n  box-sizing: border-box;\\n  vertical-align: middle;\\n  font-weight: 600;\\n  color: #1890ff;\\n  background: #f0f8ff;\\n  border: none;\\n  border-radius: 4px;\\n}\\n.row-label.ng-star-inserted[_ngcontent-%COMP%], .tier-label.ng-star-inserted[_ngcontent-%COMP%] {\\n  @extend .deck-label-common;\\n}\\n.deck-area[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 16px;\\n  background: white;\\n  border-radius: 12px;\\n  padding: 16px;\\n  border: 1px solid #d9d9d9;\\n  position: relative;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 4px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-header[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  margin-left: 44px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-header[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%]   .row-label[_ngcontent-%COMP%] {\\n  @extend .deck-label-common;\\n  width: 36px;\\n  height: 28px;\\n  font-size: 8px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column-reverse;\\n  gap: 2px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-content[_ngcontent-%COMP%]   .deck-tier[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-content[_ngcontent-%COMP%]   .deck-tier[_ngcontent-%COMP%]   .tier-label[_ngcontent-%COMP%] {\\n  @extend .deck-label-common;\\n  width: 40px;\\n  height: 32px;\\n  font-size: 9px;\\n}\\n.deck-area[_ngcontent-%COMP%]   .deck-content[_ngcontent-%COMP%]   .deck-tier[_ngcontent-%COMP%]   .tier-positions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.hold-area[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-top: 16px;\\n  background: white;\\n  border-radius: 12px;\\n  padding: 16px;\\n  border: 1px solid #d9d9d9;\\n  position: relative;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-content[_ngcontent-%COMP%]   .hold-tier[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-content[_ngcontent-%COMP%]   .hold-tier[_ngcontent-%COMP%]   .tier-label[_ngcontent-%COMP%] {\\n  @extend .deck-label-common;\\n  width: 40px;\\n  height: 32px;\\n  font-size: 9px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-content[_ngcontent-%COMP%]   .hold-tier[_ngcontent-%COMP%]   .tier-positions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 4px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-footer[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  margin-left: 44px;\\n}\\n.hold-area[_ngcontent-%COMP%]   .hold-footer[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%]   .row-label[_ngcontent-%COMP%] {\\n  @extend .deck-label-common;\\n  width: 36px;\\n  height: 24px;\\n  font-size: 9px;\\n}\\n.position-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n  justify-content: flex-start;\\n  align-items: flex-start;\\n  padding: 8px;\\n  background: #fafafa;\\n  border-radius: 4px;\\n  border: 1px solid #e8e8e8;\\n  min-height: 60px;\\n}\\n.deck-separator[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n  margin: 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.deck-separator[_ngcontent-%COMP%]   .separator-line[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: #000000;\\n  width: 100%;\\n  border-radius: 1px;\\n  position: relative;\\n}\\n.deck-separator[_ngcontent-%COMP%]   .separator-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #000000;\\n  color: white;\\n  padding: 2px 8px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  border-radius: 8px;\\n  letter-spacing: 0.3px;\\n}\\n.deck-separator[_ngcontent-%COMP%]   .separator-text[_ngcontent-%COMP%]::before {\\n  content: '\\u2693';\\n  margin-right: 3px;\\n}\\n.position-cell[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 13px;\\n  font-weight: 600;\\n  border: 1px solid #000000;\\n  cursor: pointer;\\n  background: white;\\n  color: #333;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n.position-cell[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n.position-cell.deck-position.selected[_ngcontent-%COMP%] {\\n  background: #1890ff;\\n  color: white;\\n  border-color: #1890ff;\\n}\\n.position-cell.deck-position.deleted[_ngcontent-%COMP%] {\\n  background: white;\\n  color: transparent;\\n  border: 1px solid #000000;\\n  cursor: default;\\n}\\n.position-cell.deck-position.deleted[_ngcontent-%COMP%]:hover {\\n  background: white;\\n}\\n.position-cell.hold-position.selected[_ngcontent-%COMP%] {\\n  background: #52c41a;\\n  color: white;\\n  border-color: #52c41a;\\n}\\n.position-cell.hold-position.deleted[_ngcontent-%COMP%] {\\n  background: white;\\n  color: transparent;\\n  border: 1px solid #000000;\\n  cursor: default;\\n}\\n.position-cell.hold-position.deleted[_ngcontent-%COMP%]:hover {\\n  background: white;\\n}\\n.position-cell.hold-position.drag-highlight[_ngcontent-%COMP%] {\\n  background: #52c41a;\\n  color: white;\\n  border-color: #52c41a;\\n  opacity: 0.7;\\n}\\n.position-cell.deck-position.drag-highlight[_ngcontent-%COMP%] {\\n  background: #1890ff;\\n  color: white;\\n  border-color: #1890ff;\\n  opacity: 0.7;\\n}\\n.drag-selection-box[_ngcontent-%COMP%] {\\n  position: fixed;\\n  border: 2px dashed #1890ff;\\n  background: rgba(24, 144, 255, 0.1);\\n  pointer-events: none;\\n  z-index: 1000;\\n}\\n.bay-diagram-container[_ngcontent-%COMP%] {\\n  -webkit-user-select: none;\\n          user-select: none;\\n  position: relative;\\n}\\n.position-cell.path-highlight[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #1890ff, #40a9ff) !important;\\n  color: white !important;\\n  border-color: #1890ff !important;\\n  box-shadow: 0 0 8px rgba(24, 144, 255, 0.6);\\n  transform: scale(1.05);\\n  transition: all 0.1s ease;\\n}\\n.position-cell.path-trail[_ngcontent-%COMP%] {\\n  background: rgba(24, 144, 255, 0.3) !important;\\n  border-color: #1890ff !important;\\n  transition: all 0.2s ease;\\n}\\n.restore-position-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  gap: 16px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-search-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding-bottom: 12px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  padding: 8px 0;\\n  background: #fafafa;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%]   .restore-selection-count[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 13px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%]   .restore-selection-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1890ff;\\n  font-size: 14px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  min-height: 300px;\\n  max-height: 350px;\\n  padding: 8px 0;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));\\n  gap: 8px;\\n  padding: 4px;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%] {\\n  border: 2px solid #d9d9d9;\\n  border-radius: 6px;\\n  padding: 8px 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  background: white;\\n  position: relative;\\n  min-height: 50px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]:hover {\\n  border-color: #40a9ff;\\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\\n  transform: translateY(-1px);\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1890ff;\\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card.selected[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n  color: #1890ff;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 100%;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-id[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-area[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #8c8c8c;\\n  margin-bottom: 4px;\\n  line-height: 1;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #d9d9d9;\\n  transition: color 0.2s ease;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  color: #999;\\n}\\n.restore-position-container[_ngcontent-%COMP%]   .restore-info-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-top: auto;\\n}\\n.bay-operations[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding: 12px;\\n  background: #fafafa;\\n  border-radius: 4px;\\n  border: 1px solid #d9d9d9;\\n}\\n@media (max-width: 768px) {\\n  .deck-area[_ngcontent-%COMP%], .hold-area[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    margin: 12px 0;\\n  }\\n  .bay-diagram-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .bay-diagram-container[_ngcontent-%COMP%]   .position-cell[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 28px;\\n    font-size: 9px;\\n  }\\n  .bay-diagram-container[_ngcontent-%COMP%]   .tier-label[_ngcontent-%COMP%] {\\n    width: 34px !important;\\n    height: 28px !important;\\n    font-size: 10px;\\n  }\\n  .bay-diagram-container[_ngcontent-%COMP%]   .row-label[_ngcontent-%COMP%] {\\n    width: 30px !important;\\n    height: 24px !important;\\n    font-size: 10px;\\n  }\\n  .bay-diagram-container[_ngcontent-%COMP%]   .row-labels[_ngcontent-%COMP%] {\\n    margin-left: 38px !important;\\n  }\\n  .deck-separator[_ngcontent-%COMP%] {\\n    margin: 12px 0;\\n  }\\n  .deck-separator[_ngcontent-%COMP%]   .separator-text[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    padding: 2px 6px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .bay-operations[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%] {\\n    margin-left: 8px;\\n    margin-right: 8px;\\n  }\\n  .bay-operations[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%]   .bay-selector-dropdown[_ngcontent-%COMP%] {\\n    width: 150px;\\n  }\\n  .bay-operations[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    font-size: 13px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));\\n    gap: 6px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%] {\\n    min-height: 45px;\\n    padding: 6px 4px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-id[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-area[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .bay-operations[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 8px;\\n  }\\n  .bay-operations[_ngcontent-%COMP%]   .bay-selector[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-right: 0;\\n    justify-content: space-between;\\n  }\\n  .bay-operations[_ngcontent-%COMP%]   .return-button[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n  .bay-operations[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-search-section[_ngcontent-%COMP%]   nz-row[_ngcontent-%COMP%]   nz-col[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%]   nz-space[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-batch-operations[_ngcontent-%COMP%]   nz-space[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    margin-bottom: 4px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));\\n    gap: 6px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%] {\\n    min-height: 45px;\\n    padding: 6px 4px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-id[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-area[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n  .restore-position-container[_ngcontent-%COMP%]   .restore-positions-grid-container[_ngcontent-%COMP%]   .restore-positions-grid[_ngcontent-%COMP%]   .restore-position-card[_ngcontent-%COMP%]   .position-card-content[_ngcontent-%COMP%]   .position-check-icon[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .bay-info-card[_ngcontent-%COMP%]   .bay-info-header[_ngcontent-%COMP%]   .bay-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .position-cell[_ngcontent-%COMP%] {\\n    width: 26px !important;\\n    height: 24px !important;\\n    font-size: 8px !important;\\n  }\\n  .tier-label[_ngcontent-%COMP%] {\\n    width: 30px !important;\\n    height: 24px !important;\\n    font-size: 9px !important;\\n  }\\n  .row-label[_ngcontent-%COMP%] {\\n    width: 26px !important;\\n    height: 20px !important;\\n    font-size: 9px !important;\\n  }\\n  .row-labels[_ngcontent-%COMP%] {\\n    margin-left: 34px !important;\\n  }\\n}\\n.ant-modal-body[_ngcontent-%COMP%]   .ant-form-item[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.ant-modal-body[_ngcontent-%COMP%]   .ant-alert[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "CwfBaseCrud", "DialogResultEnum", "ModalTypeEnum", "TAS_T_VESSEL_BAY", "i0", "ɵɵelement", "ɵɵproperty", "bay_r1", "bayNo", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "rowLabel_r2", "ɵɵlistener", "VesselBayComponent_div_31_div_1_div_5_div_4_Template_div_click_0_listener", "$event", "ctx_r3", "ɵɵrestoreView", "_r3", "position_r5", "$implicit", "posIndex_r6", "index", "tierIndex_r7", "ɵɵnextContext", "ctx_r7", "ɵɵresetView", "onPositionClick", "ɵɵclassProp", "selected", "deleted", "dragHighlight", "label", "ɵɵtemplate", "VesselBayComponent_div_31_div_1_div_5_div_4_Template", "ɵɵtextInterpolate", "tier_r9", "previewBayData", "deckPositions", "VesselBayComponent_div_31_div_1_div_3_Template", "VesselBayComponent_div_31_div_1_div_5_Template", "deckRowLabels", "deckTierLabels", "VesselBayComponent_div_31_div_3_div_2_div_4_Template_div_click_0_listener", "ctx_r10", "_r10", "position_r12", "posIndex_r13", "tierIndex_r14", "VesselBayComponent_div_31_div_3_div_2_div_4_Template", "tier_r15", "holdPositions", "rowLabel_r16", "VesselBayComponent_div_31_div_3_div_2_Template", "VesselBayComponent_div_31_div_3_div_5_Template", "hold<PERSON>ier<PERSON><PERSON><PERSON>", "holdRowLabels", "VesselBayComponent_div_31_div_1_Template", "VesselBayComponent_div_31_div_2_Template", "VesselBayComponent_div_31_div_3_Template", "VesselBayComponent_div_31_div_4_Template", "deckTiers", "deckRows", "holdTiers", "holdRows", "selectedBayData", "rowLabel_r18", "VesselBayComponent_ng_container_33_div_93_div_1_div_5_div_4_Template_div_click_0_listener", "ctx_r19", "_r19", "position_r21", "posIndex_r22", "tierIndex_r23", "VesselBayComponent_ng_container_33_div_93_div_1_div_5_div_4_Template", "tier_r24", "VesselBayComponent_ng_container_33_div_93_div_1_div_3_Template", "VesselBayComponent_ng_container_33_div_93_div_1_div_5_Template", "VesselBayComponent_ng_container_33_div_93_div_3_div_2_div_4_Template_div_click_0_listener", "ctx_r25", "_r25", "position_r27", "posIndex_r28", "tierIndex_r29", "VesselBayComponent_ng_container_33_div_93_div_3_div_2_div_4_Template", "tier_r30", "rowLabel_r31", "VesselBayComponent_ng_container_33_div_93_div_3_div_2_Template", "VesselBayComponent_ng_container_33_div_93_div_3_div_5_Template", "VesselBayComponent_ng_container_33_div_93_div_1_Template", "VesselBayComponent_ng_container_33_div_93_div_2_Template", "VesselBayComponent_ng_container_33_div_93_div_3_Template", "VesselBayComponent_ng_container_33_div_94_button_2_Template_button_click_0_listener", "_r32", "deleteSelectedPositions", "getSelectedCount", "VesselBayComponent_ng_container_33_div_94_button_3_Template_button_click_0_listener", "_r33", "clearSelection", "VesselBayComponent_ng_container_33_div_94_button_2_Template", "VesselBayComponent_ng_container_33_div_94_button_3_Template", "ɵɵelementContainerStart", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_19_listener", "_r17", "onParameterChange", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_25_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_31_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_37_listener", "VesselBayComponent_ng_container_33_Template_nz_radio_group_ngModelChange_43_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_55_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_61_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_67_listener", "VesselBayComponent_ng_container_33_Template_nz_input_number_ngModelChange_73_listener", "VesselBayComponent_ng_container_33_Template_nz_radio_group_ngModelChange_79_listener", "VesselBayComponent_ng_container_33_div_93_Template", "VesselBayComponent_ng_container_33_div_94_Template", "VesselBayComponent_ng_container_33_nz_empty_95_Template", "ɵɵpureFunction0", "_c5", "newBayForm", "_c6", "_c7", "hasSelectedPositions", "insertBayForm", "_c8", "copyBayForm", "VesselBayComponent_ng_container_39_button_14_Template_button_click_0_listener", "_r35", "selectAllRestorePositions", "VesselBayComponent_ng_container_39_button_15_Template_button_click_0_listener", "_r36", "deselectAllRestorePositions", "VesselBayComponent_ng_container_39_button_16_Template_button_click_0_listener", "_r37", "clearRestoreSelection", "selectedDeletedPositions", "length", "filteredDeletedPositions", "VesselBayComponent_ng_container_39_div_19_div_1_Template_div_click_0_listener", "position_r39", "_r38", "togglePositionSelection", "value", "isPositionSelected", "getPositionArea", "VesselBayComponent_ng_container_39_div_19_div_1_Template", "trackByPositionId", "restoreSearchText", "restore<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtwoWayListener", "VesselBayComponent_ng_container_39_Template_input_ngModelChange_6_listener", "_r34", "ɵɵtwoWayBindingSet", "onRestoreSearch", "VesselBayComponent_ng_container_39_Template_nz_select_ngModelChange_8_listener", "onRestoreAreaFilter", "VesselBayComponent_ng_container_39_button_14_Template", "VesselBayComponent_ng_container_39_button_15_Template", "VesselBayComponent_ng_container_39_button_16_Template", "VesselBayComponent_ng_container_39_span_17_Template", "VesselBayComponent_ng_container_39_div_19_Template", "VesselBayComponent_ng_container_39_div_20_Template", "_c9", "ɵɵtwoWayProperty", "VesselBayComponent", "constructor", "cwfBusContextService", "gol", "cwfRestfulService", "modal", "cdr", "mainStore", "vesselId", "vesselNm", "selectedBay<PERSON>o", "bayList", "bayPositions", "selectedPositions", "isNewBayModalVisible", "isInsertBayModalVisible", "isCopyBayModalVisible", "isRestorePositionModalVisible", "deletedPositions", "Set", "deletedPositionOptions", "isDragging", "dragStartX", "dragStartY", "dragCurrentX", "dragCurrentY", "dragStartPosition", "dragSelectionBox", "ctrlKeyPressed", "pathDragMode", "pathSelectedPositions", "lastPathPosition", "dragStartTime", "hasMoved", "drag<PERSON><PERSON><PERSON><PERSON>", "moveT<PERSON><PERSON>old", "potentialPathDrag", "potentialRectDrag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onShow", "openParam", "console", "log", "allParams", "showState", "error", "openMainPage", "initForms", "loadBayList", "initDragEvents", "required", "max<PERSON><PERSON><PERSON>", "deckBase", "min", "max", "deckStep", "deckFromZero", "holdBase", "holdStep", "holdFromZero", "remark", "insertBayNos", "originalBayNo", "newBayNo", "valueChanges", "subscribe", "generatePreviewBayData", "requestData", "data", "post", "serviceName", "en", "then", "rps", "ok", "bayCount", "onBaySelect", "msg", "catch", "loadBayListAndSelect", "targetBay", "find", "bay", "generateBayPositions", "clear", "rowtier", "validPositions", "split", "allPossiblePositions", "generateAllPossiblePositionIds", "for<PERSON>ach", "positionId", "includes", "add", "Array", "from", "dtier", "drow", "htier", "hrow", "generateDeckTierLabels", "dfrom", "dstep", "generateRowLabels", "drowFromZeroTag", "generateHoldTierLabels", "hfrom", "hstep", "hrowFromZeroTag", "generateDeckPositions", "generateHoldPositions", "updateDeletedPositionOptions", "map", "updateFilteredDeletedPositions", "showRestorePositionModal", "size", "info", "cancelRestorePosition", "filtered", "searchText", "toLowerCase", "filter", "position", "area", "match", "matchArea<PERSON><PERSON>er", "total", "areaFilter", "tierPositions", "p", "id", "indexOf", "splice", "push", "item", "restoreSelectedPositions", "bayData", "bayId", "bayNos", "response", "success", "delete", "tierIndex", "posIndex", "event", "currentSelected", "hasEvent", "stopPropagation", "preventDefault", "timeSinceMouseDown", "Date", "now", "isCtrlPressed", "ctrl<PERSON>ey", "metaKey", "wasSelected", "detectChanges", "nowSelected", "selectedCount", "showNewBayModal", "reset", "patchValue", "cancelNewBay", "setTimeout", "formValue", "getRawValue", "allPositions", "tierLabel", "String", "padStart", "rowIndex", "row<PERSON>abel", "base", "step", "tiers", "labels", "i", "rows", "fromZero", "isOdd", "halfRows", "Math", "floor", "positions", "<PERSON><PERSON><PERSON><PERSON>", "has", "saveNewBay", "controls", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "get", "some", "generateValidPositionsList", "join", "count", "deleteBay", "_this", "_asyncToGenerator", "state", "showConfirm", "yes", "showInsertBayModal", "saveInsertBay", "regex", "test", "replace", "pos", "trim", "deletePositions", "_this2", "body", "showCopyBayModal", "saveCopyBay", "_this3", "originalBayData", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "goBack", "openPage", "keydownListener", "e", "key", "keyupListener", "mousemoveListener", "onMouseMove", "mouseupListener", "onMouseUp", "document", "addEventListener", "onMouseDown", "button", "target", "className", "isPositionCell", "classList", "contains", "currentTime", "clientX", "clientY", "startPathDragSelection", "getAttribute", "findPositionById", "startRectangleDragSelection", "createSelectionBox", "remove", "createElement", "style", "cssText", "append<PERSON><PERSON><PERSON>", "deltaX", "abs", "deltaY", "distance", "sqrt", "handlePathDragMove", "handleRectangleDragMove", "elementUnderMouse", "elementFromPoint", "updateSelectionBox", "highlightPositionsInSelection", "left", "top", "width", "height", "selectionRect", "getSelectionRect", "positionElement", "getPositionElement", "isElementInSelection", "right", "bottom", "querySelector", "element", "elementRect", "getBoundingClientRect", "dragDuration", "completePathDragSelection", "completeRectangleDragSelection", "clearDragHighlight", "clearAllSelections", "selectDragHighlightedPositions", "ngOnDestroy", "removeEventListener", "selectPositionById", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "CwfRestfulService", "i4", "NzModalService", "ChangeDetectorRef", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "VesselBayComponent_Template", "rf", "ctx", "VesselBayComponent_Template_button_click_8_listener", "VesselBayComponent_Template_button_click_10_listener", "VesselBayComponent_Template_button_click_12_listener", "VesselBayComponent_Template_button_click_14_listener", "VesselBayComponent_Template_button_click_16_listener", "VesselBayComponent_Template_button_click_18_listener", "VesselBayComponent_Template_nz_select_ngModelChange_27_listener", "VesselBayComponent_nz_option_28_Template", "VesselBayComponent_Template_div_mousedown_30_listener", "VesselBayComponent_div_31_Template", "VesselBayComponent_Template_nz_modal_nzVisibleChange_32_listener", "VesselBayComponent_Template_nz_modal_nzOnCancel_32_listener", "VesselBayComponent_Template_nz_modal_nzOnOk_32_listener", "VesselBayComponent_ng_container_33_Template", "VesselBayComponent_Template_nz_modal_nzVisibleChange_34_listener", "VesselBayComponent_Template_nz_modal_nzOnCancel_34_listener", "VesselBayComponent_Template_nz_modal_nzOnOk_34_listener", "VesselBayComponent_ng_container_35_Template", "VesselBayComponent_Template_nz_modal_nzVisibleChange_36_listener", "VesselBayComponent_Template_nz_modal_nzOnCancel_36_listener", "VesselBayComponent_Template_nz_modal_nzOnOk_36_listener", "VesselBayComponent_ng_container_37_Template", "VesselBayComponent_Template_nz_modal_nzVisibleChange_38_listener", "VesselBayComponent_Template_nz_modal_nzOnCancel_38_listener", "VesselBayComponent_Template_nz_modal_nzOnOk_38_listener", "VesselBayComponent_ng_container_39_Template", "_c0", "_c1", "_c2", "_c3", "_c4"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-bay\\vessel-bay.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vessel\\vessel-bay\\vessel-bay.component.html"], "sourcesContent": ["import { Component, OnDestroy, ChangeDetectorRef } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport {CwfBaseCrud, CwfBusContextService, DialogResultEnum, ModalTypeEnum} from 'cwf-ng-library';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { responseInterface } from 'app/interface/request.interface';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport { TAS_T_VESSEL_BAY } from '@store/TAS/TAS_T_VESSEL_BAY';\r\nimport { NzModalService } from 'ng-zorro-antd/modal';\r\n\r\n@Component({\r\n  selector: 'tas-vessel-bay-app',\r\n  templateUrl: './vessel-bay.component.html',\r\n  styleUrls: ['./vessel-bay.component.less']\r\n})\r\nexport class VesselBayComponent extends CwfBaseCrud implements OnDestroy {\r\n  mainStore = new TAS_T_VESSEL_BAY();\r\n  vesselId: string = '';\r\n  vesselNm: string = '';\r\n  selectedBayNo: string = '';\r\n  selectedBayData: any = null;\r\n  bayList: any[] = [];\r\n  bayPositions: any[] = [];\r\n  selectedPositions: any[] = [];\r\n  \r\n  // 模态框相关\r\n  isNewBayModalVisible = false;\r\n  isInsertBayModalVisible = false;\r\n  isCopyBayModalVisible = false;\r\n  isRestorePositionModalVisible = false;\r\n  newBayForm: FormGroup;\r\n  insertBayForm: FormGroup;\r\n  copyBayForm: FormGroup;\r\n\r\n  constructor(\r\n    cwfBusContextService: CwfBusContextService,\r\n    private gol: GlobalDataService,\r\n    private cwfRestfulService: CwfRestfulService,\r\n    private modal: NzModalService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  /**\r\n   * 页面加载后处理\r\n   */\r\n  onShow() {\r\n    this.vesselId = this.openParam['vesselId'] || '';\r\n    this.vesselNm = this.openParam['vesselNm'] || '';\r\n\r\n    console.log('船舶贝图 - 接收到的参数:', {\r\n      vesselId: this.vesselId,\r\n      vesselNm: this.vesselNm,\r\n      allParams: this.openParam\r\n    });\r\n\r\n    if (!this.vesselId) {\r\n      this.showState(ModalTypeEnum.error, '船舶ID参数缺失！');\r\n      this.openMainPage();\r\n      return;\r\n    }\r\n\r\n    this.initForms();\r\n    this.loadBayList();\r\n    this.initDragEvents();\r\n  }\r\n\r\n  // 新增属性\r\n  previewBayData: any = null;\r\n  deletedPositions: Set<string> = new Set();\r\n\r\n  // 恢复贝位功能相关属性\r\n  selectedDeletedPositions: string[] = [];\r\n  deletedPositionOptions: any[] = [];\r\n\r\n  // 恢复贝位弹出框优化相关属性\r\n  restoreSearchText: string = '';\r\n  restoreAreaFilter: string = '';\r\n  filteredDeletedPositions: any[] = [];\r\n\r\n  // 拖拽多选相关属性\r\n  isDragging = false;\r\n  dragStartX = 0;\r\n  dragStartY = 0;\r\n  dragCurrentX = 0;\r\n  dragCurrentY = 0;\r\n  dragStartPosition: any = null;\r\n  dragSelectionBox: HTMLElement | null = null;\r\n  ctrlKeyPressed = false;\r\n\r\n  // 路径拖拽相关属性\r\n  pathDragMode = false;  // 是否为路径拖拽模式\r\n  pathSelectedPositions = new Set<string>();  // 路径拖拽中已选择的贝位ID\r\n  lastPathPosition: string | null = null;  // 上一个路径经过的贝位\r\n\r\n  // 拖拽检测相关属性\r\n  dragStartTime = 0;  // 拖拽开始时间\r\n  hasMoved = false;   // 是否已经移动\r\n  dragThreshold = 150;  // 拖拽时间阈值（毫秒）\r\n  moveThreshold = 5;    // 鼠标移动阈值（像素）\r\n\r\n  // 潜在拖拽状态\r\n  potentialPathDrag = false;      // 潜在的路径拖拽\r\n  potentialRectDrag = false;      // 潜在的矩形拖拽\r\n  potentialPathTarget: HTMLElement | null = null;  // 潜在路径拖拽的目标元素\r\n\r\n  // 事件监听器引用（用于清理）\r\n  private keydownListener: (e: KeyboardEvent) => void;\r\n  private keyupListener: (e: KeyboardEvent) => void;\r\n  private mousemoveListener: (e: MouseEvent) => void;\r\n  private mouseupListener: (e: MouseEvent) => void;\r\n\r\n  /**\r\n   * 初始化表单\r\n   */\r\n  initForms() {\r\n    this.newBayForm = new FormGroup({\r\n      bayNo: new FormControl('', [Validators.required, Validators.maxLength(12)]),\r\n      deckBase: new FormControl(82, [Validators.required, Validators.min(1), Validators.max(99)]),\r\n      deckStep: new FormControl(2, [Validators.required, Validators.min(1), Validators.max(10)]),\r\n      deckTiers: new FormControl(3, [Validators.required, Validators.min(0), Validators.max(10)]),\r\n      deckRows: new FormControl(9, [Validators.required, Validators.min(0), Validators.max(20)]),\r\n      deckFromZero: new FormControl('Y'),\r\n      holdBase: new FormControl(81, [Validators.required, Validators.min(1), Validators.max(99)]),\r\n      holdStep: new FormControl(2, [Validators.required, Validators.min(1), Validators.max(10)]),\r\n      holdTiers: new FormControl(4, [Validators.required, Validators.min(0), Validators.max(10)]),\r\n      holdRows: new FormControl(9, [Validators.required, Validators.min(0), Validators.max(20)]),\r\n      holdFromZero: new FormControl('Y'),\r\n      remark: new FormControl('', [Validators.maxLength(255)])\r\n    });\r\n\r\n    this.insertBayForm = new FormGroup({\r\n      insertBayNos: new FormControl('', [Validators.required])\r\n    });\r\n\r\n    this.copyBayForm = new FormGroup({\r\n      originalBayNo: new FormControl(''),\r\n      newBayNo: new FormControl('', [Validators.required, Validators.maxLength(12)])\r\n    });\r\n\r\n    // 监听表单变化，实时生成预览\r\n    this.newBayForm.valueChanges.subscribe(() => {\r\n      this.generatePreviewBayData();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 加载贝位列表\r\n   */\r\n  loadBayList() {\r\n    // 清除之前的数据，确保不会显示缓存的数据\r\n    this.bayList = [];\r\n    this.selectedBayNo = '';\r\n    this.selectedBayData = null;\r\n    this.previewBayData = null;\r\n    this.selectedPositions = [];\r\n\r\n    // 按照正确的参数格式构建请求参数\r\n    const requestData = {\r\n      data: {\r\n        vesselId: this.vesselId\r\n      }\r\n    };\r\n\r\n    console.log('船舶贝图 - 请求参数:', requestData);\r\n\r\n    this.cwfRestfulService.post('/vessel-bay/list', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        console.log('船舶贝图 - API响应:', rps);\r\n        if (rps.ok === true) {\r\n          this.bayList = rps.data || [];\r\n          console.log('船舶贝图 - 加载贝位列表成功:', {\r\n            vesselId: this.vesselId,\r\n            bayCount: this.bayList.length,\r\n            bayList: this.bayList\r\n          });\r\n\r\n          if (this.bayList.length > 0) {\r\n            this.selectedBayNo = this.bayList[0].bayNo;\r\n            this.onBaySelect();\r\n          } else {\r\n            console.log('船舶贝图 - 该船舶没有贝位数据');\r\n            this.selectedBayNo = '';\r\n            this.selectedBayData = null;\r\n            this.previewBayData = null;\r\n          }\r\n        } else {\r\n          console.log('船舶贝图 - API错误:', rps.msg);\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('船舶贝图 - API调用失败:', error);\r\n        this.showState(ModalTypeEnum.error, '加载贝位列表失败');\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 加载贝位列表并选择指定贝号\r\n   */\r\n  loadBayListAndSelect(bayNo: string) {\r\n    // 按照正确的参数格式构建请求参数\r\n    const requestData = {\r\n      data: {\r\n        vesselId: this.vesselId\r\n      }\r\n    };\r\n\r\n    console.log('船舶贝图 - 请求参数:', requestData);\r\n\r\n    this.cwfRestfulService.post('/vessel-bay/list', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        console.log('船舶贝图 - API响应:', rps);\r\n        if (rps.ok === true) {\r\n          this.bayList = rps.data || [];\r\n          console.log('船舶贝图 - 贝位列表:', this.bayList);\r\n\r\n          // 查找指定的贝号\r\n          const targetBay = this.bayList.find(bay => bay.bayNo === bayNo);\r\n          if (targetBay) {\r\n            this.selectedBayNo = bayNo;\r\n            console.log('船舶贝图 - 选择贝号:', bayNo);\r\n            this.onBaySelect();\r\n          } else if (this.bayList.length > 0) {\r\n            // 如果找不到指定贝号，选择第一个\r\n            this.selectedBayNo = this.bayList[0].bayNo;\r\n            this.onBaySelect();\r\n          } else {\r\n            console.log('船舶贝图 - 贝位列表为空');\r\n          }\r\n        } else {\r\n          console.log('船舶贝图 - API错误:', rps.msg);\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('船舶贝图 - API调用失败:', error);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 贝号选择事件\r\n   */\r\n  onBaySelect() {\r\n    console.log('船舶贝图 - 选择贝号:', this.selectedBayNo);\r\n    console.log('船舶贝图 - 当前贝位列表:', this.bayList);\r\n\r\n    // 清空之前的选中状态\r\n    this.selectedPositions = [];\r\n\r\n    this.selectedBayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\r\n    console.log('船舶贝图 - 找到的贝位数据:', this.selectedBayData);\r\n\r\n    if (this.selectedBayData) {\r\n      this.generateBayPositions();\r\n    } else {\r\n      console.log('船舶贝图 - 未找到对应的贝位数据');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成贝位图（使用与预览相同的数据结构）\r\n   */\r\n  generateBayPositions() {\r\n    if (!this.selectedBayData) {\r\n      console.log('船舶贝图 - selectedBayData为空，无法生成贝位图');\r\n      return;\r\n    }\r\n\r\n    const data = this.selectedBayData;\r\n    console.log('船舶贝图 - 开始生成贝位图，数据:', data);\r\n\r\n    // 先更新deletedPositions集合，确保生成贝位时有正确的删除状态\r\n    this.deletedPositions.clear();\r\n    if (data.rowtier) {\r\n      const validPositions = data.rowtier.split(',');\r\n      console.log('船舶贝图 - 有效贝位列表:', validPositions);\r\n\r\n      // 生成所有可能的贝位ID，然后找出已删除的\r\n      const allPossiblePositions = this.generateAllPossiblePositionIds(data);\r\n      console.log('船舶贝图 - 所有可能的贝位ID:', allPossiblePositions);\r\n\r\n      allPossiblePositions.forEach(positionId => {\r\n        if (!validPositions.includes(positionId)) {\r\n          this.deletedPositions.add(positionId);\r\n        }\r\n      });\r\n\r\n      console.log('船舶贝图 - 已删除贝位集合:', Array.from(this.deletedPositions));\r\n    }\r\n\r\n    // 使用与预览相同的数据生成逻辑（此时deletedPositions已经正确设置）\r\n    this.previewBayData = {\r\n      deckTiers: data.dtier || 0,\r\n      deckRows: data.drow || 0,\r\n      holdTiers: data.htier || 0,\r\n      holdRows: data.hrow || 0,\r\n      deckTierLabels: this.generateDeckTierLabels(data.dfrom, data.dstep, data.dtier),\r\n      deckRowLabels: this.generateRowLabels(data.drow, data.drowFromZeroTag === 'Y'),\r\n      holdTierLabels: this.generateHoldTierLabels(data.hfrom, data.hstep, data.htier),\r\n      holdRowLabels: this.generateRowLabels(data.hrow, data.hrowFromZeroTag === 'Y'),\r\n      deckPositions: this.generateDeckPositions(data.dfrom, data.dstep, data.dtier, data.drow, data.drowFromZeroTag === 'Y'),\r\n      holdPositions: this.generateHoldPositions(data.hfrom, data.hstep, data.htier, data.hrow, data.hrowFromZeroTag === 'Y')\r\n    };\r\n\r\n    console.log('船舶贝图 - 生成的预览数据:', this.previewBayData);\r\n    this.selectedPositions = [];\r\n\r\n    // 更新已删除贝位的下拉选项\r\n    this.updateDeletedPositionOptions();\r\n  }\r\n\r\n  /**\r\n   * 更新已删除贝位的下拉选项\r\n   */\r\n  updateDeletedPositionOptions() {\r\n    this.deletedPositionOptions = Array.from(this.deletedPositions).map(positionId => ({\r\n      label: positionId,\r\n      value: positionId\r\n    }));\r\n\r\n    // 更新筛选后的贝位列表\r\n    this.updateFilteredDeletedPositions();\r\n\r\n    console.log('船舶贝图 - 已删除贝位选项:', this.deletedPositionOptions);\r\n  }\r\n\r\n  /**\r\n   * 显示恢复贝位模态框\r\n   */\r\n  showRestorePositionModal() {\r\n    if (!this.selectedBayData) {\r\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\r\n      return;\r\n    }\r\n\r\n    if (this.deletedPositions.size === 0) {\r\n      this.showState(ModalTypeEnum.info, '当前没有已删除的贝位可以恢复！');\r\n      return;\r\n    }\r\n\r\n    // 重置搜索和筛选条件\r\n    this.restoreSearchText = '';\r\n    this.restoreAreaFilter = '';\r\n\r\n    // 更新下拉选项和筛选列表\r\n    this.updateDeletedPositionOptions();\r\n\r\n    // 清空之前的选择\r\n    this.selectedDeletedPositions = [];\r\n\r\n    // 显示模态框\r\n    this.isRestorePositionModalVisible = true;\r\n  }\r\n\r\n  /**\r\n   * 取消恢复贝位操作\r\n   */\r\n  cancelRestorePosition() {\r\n    this.isRestorePositionModalVisible = false;\r\n    this.selectedDeletedPositions = [];\r\n    this.restoreSearchText = '';\r\n    this.restoreAreaFilter = '';\r\n  }\r\n\r\n  /**\r\n   * 更新筛选后的已删除贝位列表\r\n   */\r\n  updateFilteredDeletedPositions() {\r\n    let filtered = [...this.deletedPositionOptions];\r\n\r\n    // 按搜索文本筛选\r\n    if (this.restoreSearchText) {\r\n      const searchText = this.restoreSearchText.toLowerCase();\r\n      filtered = filtered.filter(position =>\r\n        position.label.toLowerCase().includes(searchText)\r\n      );\r\n    }\r\n\r\n    // 按区域筛选\r\n    if (this.restoreAreaFilter) {\r\n      filtered = filtered.filter(position => {\r\n        const area = this.getPositionArea(position.value);\r\n        console.log('船舶贝图 - 筛选调试:', {\r\n          positionId: position.value,\r\n          area: area,\r\n          filter: this.restoreAreaFilter,\r\n          match: this.matchAreaFilter(area, this.restoreAreaFilter)\r\n        });\r\n        return this.matchAreaFilter(area, this.restoreAreaFilter);\r\n      });\r\n    }\r\n\r\n    this.filteredDeletedPositions = filtered;\r\n    console.log('船舶贝图 - 筛选结果:', {\r\n      total: this.deletedPositionOptions.length,\r\n      filtered: filtered.length,\r\n      searchText: this.restoreSearchText,\r\n      areaFilter: this.restoreAreaFilter\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 搜索贝位\r\n   */\r\n  onRestoreSearch() {\r\n    this.updateFilteredDeletedPositions();\r\n  }\r\n\r\n  /**\r\n   * 区域筛选\r\n   */\r\n  onRestoreAreaFilter() {\r\n    this.updateFilteredDeletedPositions();\r\n  }\r\n\r\n  /**\r\n   * 匹配区域筛选条件\r\n   */\r\n  matchAreaFilter(area: string, filter: string): boolean {\r\n    if (!filter) return true; // 空筛选条件匹配所有\r\n\r\n    // 精确匹配区域\r\n    if (filter === 'deck' && area === '甲板') return true;\r\n    if (filter === 'hold' && area === '舱底') return true;\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * 获取贝位所属区域\r\n   */\r\n  getPositionArea(positionId: string): string {\r\n    if (!this.previewBayData) {\r\n      return '未知';\r\n    }\r\n\r\n    // 在甲板区域查找\r\n    if (this.previewBayData.deckPositions) {\r\n      for (const tierPositions of this.previewBayData.deckPositions) {\r\n        const position = tierPositions.find((p: any) => p.id === positionId);\r\n        if (position) return '甲板';\r\n      }\r\n    }\r\n\r\n    // 在舱底区域查找\r\n    if (this.previewBayData.holdPositions) {\r\n      for (const tierPositions of this.previewBayData.holdPositions) {\r\n        const position = tierPositions.find((p: any) => p.id === positionId);\r\n        if (position) return '舱底';\r\n      }\r\n    }\r\n\r\n    return '未知';\r\n  }\r\n\r\n  /**\r\n   * 切换贝位选择状态\r\n   */\r\n  togglePositionSelection(positionId: string) {\r\n    const index = this.selectedDeletedPositions.indexOf(positionId);\r\n    if (index > -1) {\r\n      this.selectedDeletedPositions.splice(index, 1);\r\n    } else {\r\n      this.selectedDeletedPositions.push(positionId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 检查贝位是否已选中\r\n   */\r\n  isPositionSelected(positionId: string): boolean {\r\n    return this.selectedDeletedPositions.includes(positionId);\r\n  }\r\n\r\n  /**\r\n   * 全选当前筛选的贝位\r\n   */\r\n  selectAllRestorePositions() {\r\n    this.filteredDeletedPositions.forEach(position => {\r\n      if (!this.isPositionSelected(position.value)) {\r\n        this.selectedDeletedPositions.push(position.value);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 反选当前筛选的贝位\r\n   */\r\n  deselectAllRestorePositions() {\r\n    this.filteredDeletedPositions.forEach(position => {\r\n      const index = this.selectedDeletedPositions.indexOf(position.value);\r\n      if (index > -1) {\r\n        this.selectedDeletedPositions.splice(index, 1);\r\n      } else {\r\n        this.selectedDeletedPositions.push(position.value);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 清空选择\r\n   */\r\n  clearRestoreSelection() {\r\n    this.selectedDeletedPositions = [];\r\n  }\r\n\r\n  /**\r\n   * TrackBy函数用于优化ngFor性能\r\n   */\r\n  trackByPositionId(index: number, item: any): string {\r\n    return item.value;\r\n  }\r\n\r\n  /**\r\n   * 恢复选中的已删除贝位\r\n   */\r\n  restoreSelectedPositions() {\r\n    if (!this.selectedBayData) {\r\n      this.showState(ModalTypeEnum.error, '请先选择贝号！');\r\n      return;\r\n    }\r\n\r\n    if (this.selectedDeletedPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.error, '请选择要恢复的贝位！');\r\n      return;\r\n    }\r\n\r\n    const bayData = this.selectedBayData;\r\n    console.log('船舶贝图 - 恢复贝位，贝数据:', bayData);\r\n\r\n    // 根据接口文档规范构建请求数据（使用插入贝位接口）\r\n    const requestData = {\r\n      bayId: bayData.id,  // 船舶贝的ID\r\n      bayNos: this.selectedDeletedPositions  // 要恢复的贝位编号列表\r\n    };\r\n\r\n    console.log('船舶贝图 - 恢复贝位请求数据:', requestData);\r\n\r\n    // 调用插入贝位接口\r\n    this.cwfRestfulService.post('/vessel-bay/vessel/bay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en)\r\n      .then((response: any) => {\r\n        console.log('船舶贝图 - 恢复贝位响应:', response);\r\n        if (response && response.ok) {\r\n          this.showState(ModalTypeEnum.success, '恢复成功！');\r\n\r\n          // 将恢复的贝位从已删除状态移除\r\n          this.selectedDeletedPositions.forEach(positionId => {\r\n            this.deletedPositions.delete(positionId);\r\n\r\n            // 在界面上更新贝位状态\r\n            this.previewBayData.deckPositions.forEach((tierPositions: any[]) => {\r\n              tierPositions.forEach(position => {\r\n                if (position.id === positionId) {\r\n                  position.deleted = false;\r\n                }\r\n              });\r\n            });\r\n\r\n            this.previewBayData.holdPositions.forEach((tierPositions: any[]) => {\r\n              tierPositions.forEach(position => {\r\n                if (position.id === positionId) {\r\n                  position.deleted = false;\r\n                }\r\n              });\r\n            });\r\n          });\r\n\r\n          // 清空选中的已删除贝位\r\n          this.selectedDeletedPositions = [];\r\n\r\n          // 更新下拉选项\r\n          this.updateDeletedPositionOptions();\r\n\r\n          // 关闭模态框\r\n          this.isRestorePositionModalVisible = false;\r\n\r\n          console.log('船舶贝图 - 恢复后的已删除贝位集合:', Array.from(this.deletedPositions));\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, response?.msg || '恢复失败！');\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('船舶贝图 - 恢复贝位失败:', error);\r\n        this.showState(ModalTypeEnum.error, '恢复失败！');\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 贝位点击事件 - 默认多选模式\r\n   */\r\n  onPositionClick(position: any, area?: string, tierIndex?: number, posIndex?: number, event?: MouseEvent) {\r\n    console.log('船舶贝图 - 贝位点击事件触发:', {\r\n      positionId: position.id,\r\n      currentSelected: position.selected,\r\n      isDragging: this.isDragging,\r\n      dragStartTime: this.dragStartTime,\r\n      hasEvent: !!event\r\n    });\r\n\r\n    if (position.deleted) {\r\n      console.log('船舶贝图 - 已删除的贝位，忽略点击');\r\n      return; // 已删除的位置不能点击\r\n    }\r\n\r\n    // 阻止事件冒泡和默认行为，避免触发容器的mousedown事件\r\n    if (event) {\r\n      event.stopPropagation();\r\n      event.preventDefault();\r\n    }\r\n\r\n    // 如果正在拖拽过程中，忽略点击事件\r\n    if (this.isDragging) {\r\n      console.log('船舶贝图 - 正在拖拽中，忽略点击事件');\r\n      return;\r\n    }\r\n\r\n    // 如果刚刚结束拖拽操作（防止拖拽结束后立即触发点击）\r\n    const timeSinceMouseDown = Date.now() - this.dragStartTime;\r\n    if (this.dragStartTime > 0 && timeSinceMouseDown > 50 && timeSinceMouseDown < 200) {\r\n      console.log('船舶贝图 - 刚结束拖拽操作，忽略点击事件，时间差:', timeSinceMouseDown);\r\n      return;\r\n    }\r\n\r\n    // 检查是否按住Ctrl键\r\n    const isCtrlPressed = event ? (event.ctrlKey || event.metaKey) : this.ctrlKeyPressed;\r\n\r\n    // 记录原始状态\r\n    const wasSelected = position.selected;\r\n\r\n    // 默认多选模式：普通点击也支持多选\r\n    // Ctrl+点击保留作为备选操作方式\r\n    position.selected = !position.selected;\r\n\r\n    if (position.selected) {\r\n      // 添加到选中列表\r\n      if (!this.selectedPositions.find(p => p.id === position.id)) {\r\n        this.selectedPositions.push(position);\r\n      }\r\n    } else {\r\n      // 从选中列表移除\r\n      this.selectedPositions = this.selectedPositions.filter(p => p.id !== position.id);\r\n    }\r\n\r\n    // 立即触发变更检测，确保视觉反馈\r\n    this.cdr.detectChanges();\r\n\r\n    console.log('船舶贝图 - 贝位选择状态更新:', {\r\n      positionId: position.id,\r\n      wasSelected: wasSelected,\r\n      nowSelected: position.selected,\r\n      selectedCount: this.selectedPositions.length,\r\n      selectedPositions: this.selectedPositions.map(p => p.id)\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 新建贝\r\n   */\r\n  showNewBayModal() {\r\n    this.newBayForm.reset();\r\n    this.newBayForm.patchValue({\r\n      deckBase: 82,\r\n      deckStep: 2,\r\n      deckTiers: 3,\r\n      deckRows: 9,\r\n      deckFromZero: 'Y',\r\n      holdBase: 81,\r\n      holdStep: 2,\r\n      holdTiers: 4,\r\n      holdRows: 9,\r\n      holdFromZero: 'Y'\r\n    });\r\n    this.deletedPositions.clear();\r\n    this.generatePreviewBayData();\r\n    this.isNewBayModalVisible = true;\r\n  }\r\n\r\n  /**\r\n   * 取消新建贝\r\n   */\r\n  cancelNewBay() {\r\n    this.isNewBayModalVisible = false;\r\n    this.previewBayData = null;\r\n    this.deletedPositions.clear();\r\n  }\r\n\r\n  /**\r\n   * 参数变化时重新生成预览\r\n   */\r\n  onParameterChange() {\r\n    setTimeout(() => {\r\n      this.generatePreviewBayData();\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * 生成预览贝图数据\r\n   */\r\n  generatePreviewBayData() {\r\n    const formValue = this.newBayForm.getRawValue();\r\n\r\n    if (!formValue.deckBase || !formValue.holdBase) {\r\n      this.previewBayData = null;\r\n      return;\r\n    }\r\n\r\n    const deckTiers = formValue.deckTiers || 0;\r\n    const deckRows = formValue.deckRows || 0;\r\n    const holdTiers = formValue.holdTiers || 0;\r\n    const holdRows = formValue.holdRows || 0;\r\n\r\n    if (deckTiers === 0 && holdTiers === 0) {\r\n      this.previewBayData = null;\r\n      return;\r\n    }\r\n\r\n    this.previewBayData = {\r\n      deckTiers,\r\n      deckRows,\r\n      holdTiers,\r\n      holdRows,\r\n      deckTierLabels: this.generateDeckTierLabels(formValue.deckBase, formValue.deckStep, deckTiers),\r\n      deckRowLabels: this.generateRowLabels(deckRows, formValue.deckFromZero === 'Y'),\r\n      holdTierLabels: this.generateHoldTierLabels(formValue.holdBase, formValue.holdStep, holdTiers),\r\n      holdRowLabels: this.generateRowLabels(holdRows, formValue.holdFromZero === 'Y'),\r\n      deckPositions: this.generateDeckPositions(formValue.deckBase, formValue.deckStep, deckTiers, deckRows, formValue.deckFromZero === 'Y'),\r\n      holdPositions: this.generateHoldPositions(formValue.holdBase, formValue.holdStep, holdTiers, holdRows, formValue.holdFromZero === 'Y')\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 生成所有可能的贝位ID（用于确定删除状态）\r\n   */\r\n  generateAllPossiblePositionIds(data: any): string[] {\r\n    const allPositions = [];\r\n\r\n    // 生成甲板区域的所有可能贝位ID\r\n    if (data.dtier > 0 && data.drow > 0) {\r\n      const deckRowLabels = this.generateRowLabels(data.drow, data.drowFromZeroTag === 'Y');\r\n      for (let tierIndex = 0; tierIndex < data.dtier; tierIndex++) {\r\n        const tierLabel = String(data.dfrom + tierIndex * data.dstep).padStart(2, '0');\r\n        for (let rowIndex = 0; rowIndex < data.drow; rowIndex++) {\r\n          const rowLabel = deckRowLabels[rowIndex];\r\n          allPositions.push(`${rowLabel}${tierLabel}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    // 生成舱底区域的所有可能贝位ID\r\n    if (data.htier > 0 && data.hrow > 0) {\r\n      const holdRowLabels = this.generateRowLabels(data.hrow, data.hrowFromZeroTag === 'Y');\r\n      for (let tierIndex = 0; tierIndex < data.htier; tierIndex++) {\r\n        const tierLabel = String(data.hfrom + tierIndex * data.hstep).padStart(2, '0');\r\n        for (let rowIndex = 0; rowIndex < data.hrow; rowIndex++) {\r\n          const rowLabel = holdRowLabels[rowIndex];\r\n          allPositions.push(`${rowLabel}${tierLabel}`);\r\n        }\r\n      }\r\n    }\r\n\r\n    return allPositions;\r\n  }\r\n\r\n  /**\r\n   * 生成甲板层标签\r\n   */\r\n  generateDeckTierLabels(base: number, step: number, tiers: number): string[] {\r\n    const labels = [];\r\n    for (let i = 0; i < tiers; i++) {\r\n      labels.push(String(base + i * step).padStart(2, '0'));\r\n    }\r\n    return labels;\r\n  }\r\n\r\n  /**\r\n   * 生成舱底层标签（从下到上排列）\r\n   */\r\n  generateHoldTierLabels(base: number, step: number, tiers: number): string[] {\r\n    const labels = [];\r\n    // 舱底区域从下到上排列，所以需要反向生成标签\r\n    for (let i = tiers - 1; i >= 0; i--) {\r\n      labels.push(String(base + i * step).padStart(2, '0'));\r\n    }\r\n    return labels;\r\n  }\r\n\r\n  /**\r\n   * 生成行标签（按照中间对称的方式）\r\n   * 规则：中间轴开始，右侧为从01开始的奇数，左侧为从02开始的偶数\r\n   * 示例：行数9，从0开始=是：08/06/04/02/00/01/03/05/07\r\n   * 示例：行数9，从0开始=否：08/06/04/02/01/03/05/07/09\r\n   * 示例：行数8：08/06/04/02/01/03/05/07\r\n   */\r\n  generateRowLabels(rows: number, fromZero: boolean): string[] {\r\n    if (rows === 0) return [];\r\n\r\n    const labels = [];\r\n    const isOdd = rows % 2 === 1;\r\n    const halfRows = Math.floor(rows / 2);\r\n\r\n    // 左侧偶数（从大到小）\r\n    for (let i = halfRows; i >= 1; i--) {\r\n      labels.push(String(i * 2).padStart(2, '0'));\r\n    }\r\n\r\n    // 中间位置（仅当行数为奇数时）\r\n    if (isOdd) {\r\n      if (fromZero) {\r\n        labels.push('00'); // 从0开始，中间为00\r\n      } else {\r\n        labels.push('01'); // 不从0开始，中间为01\r\n      }\r\n    }\r\n\r\n    // 右侧奇数（从小到大）\r\n    if (isOdd && fromZero) {\r\n      // 奇数行且从0开始：右侧为01/03/05/07...\r\n      for (let i = 1; i <= halfRows; i++) {\r\n        labels.push(String(i * 2 - 1).padStart(2, '0'));\r\n      }\r\n    } else if (isOdd && !fromZero) {\r\n      // 奇数行且不从0开始：右侧为03/05/07/09...\r\n      for (let i = 1; i <= halfRows; i++) {\r\n        labels.push(String(i * 2 + 1).padStart(2, '0'));\r\n      }\r\n    } else {\r\n      // 偶数行：右侧为01/03/05/07...\r\n      for (let i = 1; i <= halfRows; i++) {\r\n        labels.push(String(i * 2 - 1).padStart(2, '0'));\r\n      }\r\n    }\r\n\r\n    return labels;\r\n  }\r\n\r\n  /**\r\n   * 生成甲板位置\r\n   */\r\n  generateDeckPositions(base: number, step: number, tiers: number, rows: number, fromZero: boolean): any[][] {\r\n    const positions = [];\r\n    const rowLabels = this.generateRowLabels(rows, fromZero);\r\n\r\n    for (let tierIndex = 0; tierIndex < tiers; tierIndex++) {\r\n      const tierPositions = [];\r\n      const tierLabel = String(base + tierIndex * step).padStart(2, '0');\r\n\r\n      for (let rowIndex = 0; rowIndex < rows; rowIndex++) {\r\n        const rowLabel = rowLabels[rowIndex];\r\n        const positionId = `${rowLabel}${tierLabel}`;\r\n\r\n        tierPositions.push({\r\n          id: positionId,\r\n          label: positionId,\r\n          selected: false,\r\n          deleted: this.deletedPositions.has(positionId),\r\n          tierIndex,\r\n          rowIndex\r\n        });\r\n      }\r\n\r\n      positions.push(tierPositions);\r\n    }\r\n\r\n    return positions;\r\n  }\r\n\r\n  /**\r\n   * 生成舱底位置\r\n   */\r\n  generateHoldPositions(base: number, step: number, tiers: number, rows: number, fromZero: boolean): any[][] {\r\n    const positions = [];\r\n    const rowLabels = this.generateRowLabels(rows, fromZero);\r\n\r\n    // 舱底区域从下到上排列，所以需要反向生成层级\r\n    for (let tierIndex = tiers - 1; tierIndex >= 0; tierIndex--) {\r\n      const tierPositions = [];\r\n      const tierLabel = String(base + tierIndex * step).padStart(2, '0');\r\n\r\n      for (let rowIndex = 0; rowIndex < rows; rowIndex++) {\r\n        const rowLabel = rowLabels[rowIndex];\r\n        const positionId = `${rowLabel}${tierLabel}`;\r\n\r\n        tierPositions.push({\r\n          id: positionId,\r\n          label: positionId,\r\n          selected: false,\r\n          deleted: this.deletedPositions.has(positionId),\r\n          tierIndex,\r\n          rowIndex\r\n        });\r\n      }\r\n\r\n      positions.push(tierPositions);\r\n    }\r\n\r\n    return positions;\r\n  }\r\n\r\n  /**\r\n   * 保存新建贝\r\n   */\r\n  saveNewBay() {\r\n    for (const i in this.newBayForm.controls) {\r\n      this.newBayForm.controls[i].markAsDirty();\r\n      this.newBayForm.controls[i].updateValueAndValidity();\r\n    }\r\n\r\n    if (this.newBayForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    // 检查贝号唯一性\r\n    const bayNo = this.newBayForm.get('bayNo')?.value;\r\n    if (this.bayList.some(bay => bay.bayNo === bayNo)) {\r\n      this.showState(ModalTypeEnum.error, '贝号已存在，请重新输入！');\r\n      return;\r\n    }\r\n\r\n    // 生成有效贝位列表（排除已删除的位置）\r\n    const validPositions = this.generateValidPositionsList();\r\n    const formValue = this.newBayForm.getRawValue();\r\n\r\n    const requestData = {\r\n      vesselId: this.vesselId,\r\n      bayNo: formValue.bayNo,\r\n      rowtier: validPositions.join(','),\r\n      dtier: formValue.deckTiers,\r\n      htier: formValue.holdTiers,\r\n      drow: formValue.deckRows,\r\n      hrow: formValue.holdRows,\r\n      dfrom: formValue.deckBase,\r\n      dstep: formValue.deckStep,\r\n      hfrom: formValue.holdBase,\r\n      hstep: formValue.holdStep,\r\n      drowFromZeroTag: formValue.deckFromZero,\r\n      hrowFromZeroTag: formValue.holdFromZero,\r\n      remark: formValue.remark || ''\r\n    };\r\n\r\n    this.cwfRestfulService.post('/vessel-bay', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.isNewBayModalVisible = false;\r\n          this.previewBayData = null;\r\n          this.deletedPositions.clear();\r\n          // 重新加载贝位列表，并在完成后选择新创建的贝位\r\n          this.loadBayListAndSelect(formValue.bayNo);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 生成有效贝位列表\r\n   */\r\n  generateValidPositionsList(): string[] {\r\n    const validPositions = [];\r\n\r\n    if (this.previewBayData) {\r\n      // 甲板位置\r\n      for (const tierPositions of this.previewBayData.deckPositions) {\r\n        for (const position of tierPositions) {\r\n          if (!position.deleted) {\r\n            validPositions.push(position.id);\r\n          }\r\n        }\r\n      }\r\n\r\n      // 舱底位置\r\n      for (const tierPositions of this.previewBayData.holdPositions) {\r\n        for (const position of tierPositions) {\r\n          if (!position.deleted) {\r\n            validPositions.push(position.id);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return validPositions;\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * 检查是否有选中的位置\r\n   */\r\n  hasSelectedPositions(): boolean {\r\n    if (!this.previewBayData) return false;\r\n\r\n    // 检查甲板位置\r\n    for (const tierPositions of this.previewBayData.deckPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 检查舱底位置\r\n    for (const tierPositions of this.previewBayData.holdPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * 获取选中位置数量\r\n   */\r\n  getSelectedCount(): number {\r\n    if (!this.previewBayData) return 0;\r\n\r\n    let count = 0;\r\n\r\n    // 统计甲板位置\r\n    for (const tierPositions of this.previewBayData.deckPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          count++;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 统计舱底位置\r\n    for (const tierPositions of this.previewBayData.holdPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          count++;\r\n        }\r\n      }\r\n    }\r\n\r\n    return count;\r\n  }\r\n\r\n  /**\r\n   * 删除选中位置\r\n   */\r\n  deleteSelectedPositions() {\r\n    if (!this.previewBayData) return;\r\n\r\n    // 删除甲板选中位置\r\n    for (const tierPositions of this.previewBayData.deckPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          position.deleted = true;\r\n          position.selected = false;\r\n          this.deletedPositions.add(position.id);\r\n        }\r\n      }\r\n    }\r\n\r\n    // 删除舱底选中位置\r\n    for (const tierPositions of this.previewBayData.holdPositions) {\r\n      for (const position of tierPositions) {\r\n        if (position.selected && !position.deleted) {\r\n          position.deleted = true;\r\n          position.selected = false;\r\n          this.deletedPositions.add(position.id);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 清除选择\r\n   */\r\n  clearSelection() {\r\n    if (!this.previewBayData) return;\r\n\r\n    // 清除甲板位置选择\r\n    for (const tierPositions of this.previewBayData.deckPositions) {\r\n      for (const position of tierPositions) {\r\n        position.selected = false;\r\n      }\r\n    }\r\n\r\n    // 清除舱底位置选择\r\n    for (const tierPositions of this.previewBayData.holdPositions) {\r\n      for (const position of tierPositions) {\r\n        position.selected = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 删除贝\r\n   */\r\n  async deleteBay() {\r\n    if (!this.selectedBayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要删除的贝！');\r\n      return;\r\n    }\r\n\r\n    const state = await this.showConfirm('确认', '是否确认删除？');\r\n    if (state !== DialogResultEnum.yes) {\r\n      return;\r\n    }\r\n\r\n    const bayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\r\n    if (!bayData) return;\r\n\r\n    this.cwfRestfulService.delete(`/vessel-bay/${bayData.id}`, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '删除成功！');\r\n          this.loadBayList();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 插入贝\r\n   */\r\n  showInsertBayModal() {\r\n    if (!this.selectedBayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要插入的贝！');\r\n      return;\r\n    }\r\n    this.insertBayForm.reset();\r\n    this.isInsertBayModalVisible = true;\r\n  }\r\n\r\n  /**\r\n   * 保存插入贝\r\n   */\r\n  saveInsertBay() {\r\n    for (const i in this.insertBayForm.controls) {\r\n      this.insertBayForm.controls[i].markAsDirty();\r\n      this.insertBayForm.controls[i].updateValueAndValidity();\r\n    }\r\n    \r\n    if (this.insertBayForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    const insertBayNos = this.insertBayForm.get('insertBayNos')?.value;\r\n    \r\n    // 验证格式\r\n    const regex = /^[\\d,，\\s]+$/;\r\n    if (!regex.test(insertBayNos)) {\r\n      this.showState(ModalTypeEnum.error, '插入贝位信息格式不正确，请检查后重新输入！');\r\n      return;\r\n    }\r\n\r\n    const bayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\r\n    if (!bayData) return;\r\n\r\n    // 将输入的贝位编号转换为数组格式\r\n    const bayNos = insertBayNos.replace(/，/g, ',').replace(/\\s/g, '').split(',').filter(pos => pos.trim() !== '');\r\n\r\n    const requestData = {\r\n      bayId: bayData.id,  // 船舶贝的ID\r\n      bayNos: bayNos      // 要插入的贝位编号列表\r\n    };\r\n\r\n    console.log('船舶贝图 - 插入贝位请求数据:', requestData);\r\n\r\n    this.cwfRestfulService.post('/vessel-bay/vessel/bay/batch-insert-positions', requestData, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '插入成功！');\r\n          this.isInsertBayModalVisible = false;\r\n          this.loadBayList();\r\n          setTimeout(() => this.onBaySelect(), 100);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('船舶贝图 - 插入贝位失败:', error);\r\n        this.showState(ModalTypeEnum.error, '插入贝位失败，请重试');\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 删除贝位\r\n   */\r\n  async deletePositions() {\r\n    if (!this.selectedBayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\r\n      return;\r\n    }\r\n\r\n    if (this.selectedPositions.length === 0) {\r\n      this.showState(ModalTypeEnum.error, '请选择要删除的贝位信息！');\r\n      return;\r\n    }\r\n\r\n    const bayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\r\n    if (!bayData) return;\r\n\r\n    // 根据接口文档规范构建请求数据\r\n    const requestData = {\r\n      bayId: bayData.id,  // 船舶贝的ID\r\n      bayNos: this.selectedPositions.map(p => p.id)  // 要删除的贝位编号列表（字符串数组）\r\n    };\r\n\r\n    console.log('船舶贝图 - 删除贝位请求数据:', requestData);\r\n\r\n    // 使用正确的接口路径和HTTP方法\r\n    this.cwfRestfulService.delete('/vessel-bay/vessel/bay/batch-delete-positions', this.gol.serviceName['tas'].en, { body: requestData })\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '删除成功！');\r\n\r\n          // 将被删除的贝位标记为已删除状态，而不是从界面移除\r\n          this.selectedPositions.forEach(position => {\r\n            position.deleted = true;\r\n            position.selected = false; // 取消选中状态\r\n            this.deletedPositions.add(position.id); // 添加到已删除集合\r\n          });\r\n\r\n          // 清空选中状态\r\n          this.selectedPositions = [];\r\n\r\n          // 更新已删除贝位的下拉选项\r\n          this.updateDeletedPositionOptions();\r\n\r\n          console.log('船舶贝图 - 已删除贝位集合:', Array.from(this.deletedPositions));\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 复制贝\r\n   */\r\n  showCopyBayModal() {\r\n    if (!this.selectedBayNo) {\r\n      this.showState(ModalTypeEnum.error, '请选择要复制的贝位信息！');\r\n      return;\r\n    }\r\n    \r\n    this.copyBayForm.reset();\r\n    this.copyBayForm.patchValue({\r\n      originalBayNo: this.selectedBayNo\r\n    });\r\n    this.isCopyBayModalVisible = true;\r\n  }\r\n\r\n  /**\r\n   * 保存复制贝\r\n   */\r\n  async saveCopyBay() {\r\n    for (const i in this.copyBayForm.controls) {\r\n      this.copyBayForm.controls[i].markAsDirty();\r\n      this.copyBayForm.controls[i].updateValueAndValidity();\r\n    }\r\n    \r\n    if (this.copyBayForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    const newBayNo = this.copyBayForm.get('newBayNo')?.value;\r\n    \r\n    // 验证格式 - 允许数字和字母\r\n    const regex = /^[a-zA-Z0-9]+$/;\r\n    if (!regex.test(newBayNo.replace(/\\s/g, ''))) {\r\n      this.showState(ModalTypeEnum.error, '新贝号格式不正确，只能包含数字和字母，请检查后重新输入！');\r\n      return;\r\n    }\r\n\r\n    // 检查唯一性\r\n    if (this.bayList.some(bay => bay.bayNo === newBayNo)) {\r\n      const state = await this.showConfirm('确认', '新贝号已存在，是否覆盖？');\r\n      if (state !== DialogResultEnum.yes) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    const originalBayData = this.bayList.find(bay => bay.bayNo === this.selectedBayNo);\r\n    if (!originalBayData) return;\r\n\r\n    // 使用正确的接口路径，根据接口文档使用targetBayNo作为查询参数\r\n    const apiPath = `/vessel-bay/vessel/${this.vesselId}/bay/${this.selectedBayNo}/copy?targetBayNo=${encodeURIComponent(newBayNo)}`;\r\n\r\n    this.cwfRestfulService.post(apiPath, {}, this.gol.serviceName['tas'].en)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '复制成功！');\r\n          this.isCopyBayModalVisible = false;\r\n          // 重新加载贝位列表，并在完成后选择新复制的贝位\r\n          this.loadBayListAndSelect(newBayNo);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * 返回主页面\r\n   */\r\n  goBack() {\r\n    this.openPage('/tas/vessel/list');\r\n  }\r\n\r\n  // ==================== 拖拽多选功能 ====================\r\n\r\n  /**\r\n   * 初始化拖拽事件监听\r\n   */\r\n  initDragEvents() {\r\n    // 创建事件监听器函数\r\n    this.keydownListener = (e) => {\r\n      if (e.key === 'Control' || e.key === 'Meta') {\r\n        this.ctrlKeyPressed = true;\r\n      }\r\n    };\r\n\r\n    this.keyupListener = (e) => {\r\n      if (e.key === 'Control' || e.key === 'Meta') {\r\n        this.ctrlKeyPressed = false;\r\n      }\r\n    };\r\n\r\n    this.mousemoveListener = (e) => this.onMouseMove(e);\r\n    this.mouseupListener = (e) => this.onMouseUp(e);\r\n\r\n    // 添加事件监听器\r\n    document.addEventListener('keydown', this.keydownListener);\r\n    document.addEventListener('keyup', this.keyupListener);\r\n    document.addEventListener('mousemove', this.mousemoveListener);\r\n    document.addEventListener('mouseup', this.mouseupListener);\r\n  }\r\n\r\n  /**\r\n   * 鼠标按下事件（在贝位图容器上）\r\n   */\r\n  onMouseDown(event: MouseEvent) {\r\n    // 只处理左键\r\n    if (event.button !== 0) return;\r\n\r\n    console.log('船舶贝图 - 鼠标按下事件:', {\r\n      target: (event.target as HTMLElement).className,\r\n      isPositionCell: (event.target as HTMLElement).classList.contains('position-cell'),\r\n      currentTime: Date.now()\r\n    });\r\n\r\n    // 记录拖拽开始时间和位置\r\n    this.dragStartTime = Date.now();\r\n    this.dragStartX = event.clientX;\r\n    this.dragStartY = event.clientY;\r\n    this.hasMoved = false;\r\n\r\n    // 检查是否点击在贝位上\r\n    const target = event.target as HTMLElement;\r\n    if (target.classList.contains('position-cell')) {\r\n      // 点击在贝位上，标记为潜在的路径拖拽，但不立即启动\r\n      this.potentialPathDrag = true;\r\n      this.potentialPathTarget = target;\r\n    } else {\r\n      // 点击在空白区域，标记为潜在的矩形选择\r\n      this.potentialRectDrag = true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 开始路径拖拽选择\r\n   */\r\n  startPathDragSelection(event: MouseEvent, target: HTMLElement) {\r\n    console.log('船舶贝图 - 开始路径拖拽选择');\r\n\r\n    this.isDragging = true;\r\n    this.pathDragMode = true;\r\n    this.pathSelectedPositions.clear();\r\n\r\n    // 获取起始贝位\r\n    const positionId = target.getAttribute('data-position-id');\r\n    if (positionId) {\r\n      this.pathSelectedPositions.add(positionId);\r\n      this.lastPathPosition = positionId;\r\n\r\n      // 注意：路径拖拽不自动清除之前的选择，保持默认多选模式的一致性\r\n      // 只有在Ctrl+拖拽时才是追加模式，普通拖拽也是追加模式\r\n\r\n      // 设置起始贝位的拖拽高亮状态，而不是最终选中状态\r\n      const position = this.findPositionById(positionId);\r\n      if (position && !position.deleted) {\r\n        position.dragHighlight = true;\r\n        this.cdr.detectChanges();\r\n        console.log('船舶贝图 - 设置起始贝位拖拽高亮:', positionId);\r\n      }\r\n    }\r\n\r\n    // 阻止默认行为\r\n    event.preventDefault();\r\n  }\r\n\r\n  /**\r\n   * 开始矩形拖拽选择\r\n   */\r\n  startRectangleDragSelection(event: MouseEvent) {\r\n    this.isDragging = true;\r\n    this.pathDragMode = false;\r\n    this.dragStartX = event.clientX;\r\n    this.dragStartY = event.clientY;\r\n    this.dragCurrentX = event.clientX;\r\n    this.dragCurrentY = event.clientY;\r\n\r\n    // 创建选择框\r\n    this.createSelectionBox();\r\n\r\n    // 阻止默认行为\r\n    event.preventDefault();\r\n  }\r\n\r\n  /**\r\n   * 创建选择框\r\n   */\r\n  createSelectionBox() {\r\n    if (this.dragSelectionBox) {\r\n      this.dragSelectionBox.remove();\r\n    }\r\n\r\n    this.dragSelectionBox = document.createElement('div');\r\n    this.dragSelectionBox.className = 'drag-selection-box';\r\n    this.dragSelectionBox.style.cssText = `\r\n      position: fixed;\r\n      border: 2px dashed #1890ff;\r\n      background: rgba(24, 144, 255, 0.1);\r\n      pointer-events: none;\r\n      z-index: 1000;\r\n      left: ${this.dragStartX}px;\r\n      top: ${this.dragStartY}px;\r\n      width: 0px;\r\n      height: 0px;\r\n    `;\r\n\r\n    document.body.appendChild(this.dragSelectionBox);\r\n  }\r\n\r\n  /**\r\n   * 鼠标移动事件\r\n   */\r\n  onMouseMove(event: MouseEvent) {\r\n    // 检查是否需要启动拖拽\r\n    if (!this.isDragging && (this.potentialPathDrag || this.potentialRectDrag)) {\r\n      const deltaX = Math.abs(event.clientX - this.dragStartX);\r\n      const deltaY = Math.abs(event.clientY - this.dragStartY);\r\n      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n      // 如果鼠标移动超过阈值，启动拖拽\r\n      if (distance > this.moveThreshold) {\r\n        this.hasMoved = true;\r\n\r\n        if (this.potentialPathDrag && this.potentialPathTarget) {\r\n          console.log('船舶贝图 - 启动路径拖拽模式，移动距离:', distance);\r\n          this.startPathDragSelection(event, this.potentialPathTarget);\r\n        } else if (this.potentialRectDrag) {\r\n          console.log('船舶贝图 - 启动矩形选择模式，移动距离:', distance);\r\n          this.startRectangleDragSelection(event);\r\n        }\r\n\r\n        // 清除潜在拖拽状态\r\n        this.potentialPathDrag = false;\r\n        this.potentialRectDrag = false;\r\n        this.potentialPathTarget = null;\r\n      }\r\n    }\r\n\r\n    // 处理正在进行的拖拽\r\n    if (this.isDragging) {\r\n      if (this.pathDragMode) {\r\n        // 路径拖拽模式\r\n        this.handlePathDragMove(event);\r\n      } else {\r\n        // 矩形选择模式\r\n        this.handleRectangleDragMove(event);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理路径拖拽移动\r\n   */\r\n  handlePathDragMove(event: MouseEvent) {\r\n    // 获取鼠标位置下的元素\r\n    const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY) as HTMLElement;\r\n\r\n    if (elementUnderMouse && elementUnderMouse.classList.contains('position-cell')) {\r\n      const positionId = elementUnderMouse.getAttribute('data-position-id');\r\n\r\n      if (positionId && positionId !== this.lastPathPosition && !this.pathSelectedPositions.has(positionId)) {\r\n        // 检查是否为有效贝位（未删除）\r\n        const position = this.findPositionById(positionId);\r\n        if (position && !position.deleted) {\r\n          this.pathSelectedPositions.add(positionId);\r\n          this.lastPathPosition = positionId;\r\n\r\n          // 设置拖拽高亮状态，而不是最终选中状态\r\n          position.dragHighlight = true;\r\n\r\n          // 立即触发变更检测，确保视觉反馈\r\n          this.cdr.detectChanges();\r\n\r\n          console.log('船舶贝图 - 路径拖拽经过贝位:', positionId, '当前路径选择数量:', this.pathSelectedPositions.size);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理矩形拖拽移动\r\n   */\r\n  handleRectangleDragMove(event: MouseEvent) {\r\n    if (!this.dragSelectionBox) return;\r\n\r\n    this.dragCurrentX = event.clientX;\r\n    this.dragCurrentY = event.clientY;\r\n\r\n    // 更新选择框位置和大小\r\n    this.updateSelectionBox();\r\n\r\n    // 高亮选择区域内的贝位\r\n    this.highlightPositionsInSelection();\r\n  }\r\n\r\n  /**\r\n   * 更新选择框\r\n   */\r\n  updateSelectionBox() {\r\n    if (!this.dragSelectionBox) return;\r\n\r\n    const left = Math.min(this.dragStartX, this.dragCurrentX);\r\n    const top = Math.min(this.dragStartY, this.dragCurrentY);\r\n    const width = Math.abs(this.dragCurrentX - this.dragStartX);\r\n    const height = Math.abs(this.dragCurrentY - this.dragStartY);\r\n\r\n    this.dragSelectionBox.style.left = `${left}px`;\r\n    this.dragSelectionBox.style.top = `${top}px`;\r\n    this.dragSelectionBox.style.width = `${width}px`;\r\n    this.dragSelectionBox.style.height = `${height}px`;\r\n  }\r\n\r\n  /**\r\n   * 高亮选择区域内的贝位\r\n   */\r\n  highlightPositionsInSelection() {\r\n    if (!this.previewBayData) return;\r\n\r\n    const selectionRect = this.getSelectionRect();\r\n\r\n    // 检查甲板区域的贝位\r\n    if (this.previewBayData.deckPositions) {\r\n      this.previewBayData.deckPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          if (position.deleted) return;\r\n\r\n          const positionElement = this.getPositionElement(position.id);\r\n          if (positionElement && this.isElementInSelection(positionElement, selectionRect)) {\r\n            position.dragHighlight = true;\r\n          } else {\r\n            position.dragHighlight = false;\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    // 检查舱底区域的贝位\r\n    if (this.previewBayData.holdPositions) {\r\n      this.previewBayData.holdPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          if (position.deleted) return;\r\n\r\n          const positionElement = this.getPositionElement(position.id);\r\n          if (positionElement && this.isElementInSelection(positionElement, selectionRect)) {\r\n            position.dragHighlight = true;\r\n          } else {\r\n            position.dragHighlight = false;\r\n          }\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取选择区域的矩形\r\n   */\r\n  getSelectionRect() {\r\n    return {\r\n      left: Math.min(this.dragStartX, this.dragCurrentX),\r\n      top: Math.min(this.dragStartY, this.dragCurrentY),\r\n      right: Math.max(this.dragStartX, this.dragCurrentX),\r\n      bottom: Math.max(this.dragStartY, this.dragCurrentY)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取贝位元素\r\n   */\r\n  getPositionElement(positionId: string): HTMLElement | null {\r\n    return document.querySelector(`[data-position-id=\"${positionId}\"]`) as HTMLElement;\r\n  }\r\n\r\n  /**\r\n   * 检查元素是否在选择区域内\r\n   */\r\n  isElementInSelection(element: HTMLElement, selectionRect: any): boolean {\r\n    const elementRect = element.getBoundingClientRect();\r\n\r\n    return !(elementRect.right < selectionRect.left ||\r\n             elementRect.left > selectionRect.right ||\r\n             elementRect.bottom < selectionRect.top ||\r\n             elementRect.top > selectionRect.bottom);\r\n  }\r\n\r\n  /**\r\n   * 鼠标释放事件\r\n   */\r\n  onMouseUp(event: MouseEvent) {\r\n    console.log('船舶贝图 - 鼠标释放事件:', {\r\n      isDragging: this.isDragging,\r\n      pathDragMode: this.pathDragMode,\r\n      potentialPathDrag: this.potentialPathDrag,\r\n      potentialRectDrag: this.potentialRectDrag,\r\n      hasMoved: this.hasMoved,\r\n      dragStartTime: this.dragStartTime\r\n    });\r\n\r\n    // 处理正在进行的拖拽\r\n    if (this.isDragging) {\r\n      // 计算拖拽持续时间\r\n      const dragDuration = Date.now() - this.dragStartTime;\r\n      console.log('船舶贝图 - 拖拽持续时间:', dragDuration, 'ms');\r\n\r\n      // 完成拖拽操作\r\n      if (this.pathDragMode) {\r\n        // 路径拖拽完成\r\n        this.completePathDragSelection();\r\n      } else {\r\n        // 矩形选择完成\r\n        this.completeRectangleDragSelection();\r\n      }\r\n    }\r\n\r\n    // 清理所有拖拽相关状态\r\n    this.isDragging = false;\r\n    this.pathDragMode = false;\r\n    this.pathSelectedPositions.clear();\r\n    this.lastPathPosition = null;\r\n    this.potentialPathDrag = false;\r\n    this.potentialRectDrag = false;\r\n    this.potentialPathTarget = null;\r\n    this.hasMoved = false;\r\n    this.dragStartTime = 0;\r\n\r\n    // 确保清除所有拖拽高亮状态（防止遗漏）\r\n    this.clearDragHighlight();\r\n\r\n    if (this.dragSelectionBox) {\r\n      this.dragSelectionBox.remove();\r\n      this.dragSelectionBox = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 完成路径拖拽选择\r\n   */\r\n  completePathDragSelection() {\r\n    console.log('船舶贝图 - 开始完成路径拖拽选择，路径选择数量:', this.pathSelectedPositions.size);\r\n\r\n    // 先清除所有拖拽高亮状态\r\n    this.clearDragHighlight();\r\n\r\n    // 将路径选择的贝位转换为最终选中状态\r\n    this.pathSelectedPositions.forEach(positionId => {\r\n      const position = this.findPositionById(positionId);\r\n      if (position && !position.deleted) {\r\n        // 设置为最终选中状态\r\n        if (!position.selected) {\r\n          position.selected = true;\r\n          if (!this.selectedPositions.find(p => p.id === position.id)) {\r\n            this.selectedPositions.push(position);\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    // 触发变更检测，确保视觉状态更新\r\n    this.cdr.detectChanges();\r\n\r\n    console.log('船舶贝图 - 路径拖拽选择完成，当前选中的贝位:', this.selectedPositions.map(p => p.id));\r\n  }\r\n\r\n  /**\r\n   * 完成矩形拖拽选择\r\n   */\r\n  completeRectangleDragSelection() {\r\n    if (!this.previewBayData) return;\r\n\r\n    // 如果不是Ctrl键，先清除之前的选择\r\n    if (!this.ctrlKeyPressed) {\r\n      this.clearAllSelections();\r\n    }\r\n\r\n    // 选择拖拽高亮的贝位\r\n    this.selectDragHighlightedPositions();\r\n\r\n    // 清除拖拽高亮状态\r\n    this.clearDragHighlight();\r\n  }\r\n\r\n  /**\r\n   * 清除所有选择\r\n   */\r\n  clearAllSelections() {\r\n    this.selectedPositions = [];\r\n\r\n    if (this.previewBayData.deckPositions) {\r\n      this.previewBayData.deckPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          position.selected = false;\r\n        });\r\n      });\r\n    }\r\n\r\n    if (this.previewBayData.holdPositions) {\r\n      this.previewBayData.holdPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          position.selected = false;\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 选择拖拽高亮的贝位\r\n   */\r\n  selectDragHighlightedPositions() {\r\n    if (!this.previewBayData) return;\r\n\r\n    // 处理甲板区域\r\n    if (this.previewBayData.deckPositions) {\r\n      this.previewBayData.deckPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          if (position.dragHighlight && !position.deleted) {\r\n            position.selected = true;\r\n            if (!this.selectedPositions.find(p => p.id === position.id)) {\r\n              this.selectedPositions.push(position);\r\n            }\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    // 处理舱底区域\r\n    if (this.previewBayData.holdPositions) {\r\n      this.previewBayData.holdPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          if (position.dragHighlight && !position.deleted) {\r\n            position.selected = true;\r\n            if (!this.selectedPositions.find(p => p.id === position.id)) {\r\n              this.selectedPositions.push(position);\r\n            }\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    console.log('船舶贝图 - 拖拽选择完成，当前选中的贝位:', this.selectedPositions);\r\n  }\r\n\r\n  /**\r\n   * 清除拖拽高亮状态\r\n   */\r\n  clearDragHighlight() {\r\n    if (!this.previewBayData) return;\r\n\r\n    if (this.previewBayData.deckPositions) {\r\n      this.previewBayData.deckPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          position.dragHighlight = false;\r\n        });\r\n      });\r\n    }\r\n\r\n    if (this.previewBayData.holdPositions) {\r\n      this.previewBayData.holdPositions.forEach((tierPositions: any[]) => {\r\n        tierPositions.forEach(position => {\r\n          position.dragHighlight = false;\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 组件销毁时清理事件监听器\r\n   */\r\n  ngOnDestroy() {\r\n    // 清理事件监听器\r\n    if (this.keydownListener) {\r\n      document.removeEventListener('keydown', this.keydownListener);\r\n    }\r\n    if (this.keyupListener) {\r\n      document.removeEventListener('keyup', this.keyupListener);\r\n    }\r\n    if (this.mousemoveListener) {\r\n      document.removeEventListener('mousemove', this.mousemoveListener);\r\n    }\r\n    if (this.mouseupListener) {\r\n      document.removeEventListener('mouseup', this.mouseupListener);\r\n    }\r\n\r\n    // 清理选择框\r\n    if (this.dragSelectionBox) {\r\n      this.dragSelectionBox.remove();\r\n      this.dragSelectionBox = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 根据ID查找贝位对象\r\n   */\r\n  findPositionById(positionId: string): any | null {\r\n    if (!this.previewBayData) return null;\r\n\r\n    // 在甲板区域查找\r\n    if (this.previewBayData.deckPositions) {\r\n      for (const tierPositions of this.previewBayData.deckPositions) {\r\n        const position = tierPositions.find((p: any) => p.id === positionId);\r\n        if (position) return position;\r\n      }\r\n    }\r\n\r\n    // 在舱底区域查找\r\n    if (this.previewBayData.holdPositions) {\r\n      for (const tierPositions of this.previewBayData.holdPositions) {\r\n        const position = tierPositions.find((p: any) => p.id === positionId);\r\n        if (position) return position;\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * 根据ID选择贝位\r\n   */\r\n  selectPositionById(positionId: string) {\r\n    const position = this.findPositionById(positionId);\r\n    if (position && !position.deleted) {\r\n      // 如果贝位未选中，则选中它\r\n      if (!position.selected) {\r\n        position.selected = true;\r\n        if (!this.selectedPositions.find(p => p.id === position.id)) {\r\n          this.selectedPositions.push(position);\r\n        }\r\n\r\n        // 立即触发变更检测，确保视觉反馈\r\n        this.cdr.detectChanges();\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- 顶部标题卡片 -->\r\n<nz-card class=\"title-card\" [nzBodyStyle]=\"{'padding': '16px 20px' }\">\r\n  <div class=\"page-title\">\r\n    <h2>船舶贝图管理</h2>\r\n  </div>\r\n</nz-card>\r\n\r\n<!-- 功能按钮卡片 -->\r\n<nz-card class=\"operations-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <!-- 功能按钮栏 - 重新排列在同一行 -->\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div class=\"bay-operations\">\r\n        <!-- 新建贝按钮（最左侧） -->\r\n        <button nz-button [nzType]=\"'primary'\" (click)=\"showNewBayModal()\">\r\n          新建贝\r\n        </button>\r\n\r\n        <!-- 复制贝按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"showCopyBayModal()\">\r\n          复制贝\r\n        </button>\r\n\r\n        <!-- 删除贝按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"deleteBay()\">\r\n          删除贝\r\n        </button>\r\n\r\n        <!-- 恢复贝位按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"showRestorePositionModal()\">\r\n          恢复贝位\r\n        </button>\r\n\r\n        <!-- 删除贝位按钮 -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"deletePositions()\">\r\n          删除贝位\r\n        </button>\r\n\r\n        <!-- 返回按钮（最右侧） -->\r\n        <button nz-button [nzType]=\"'default'\" (click)=\"goBack()\" class=\"return-button\">\r\n          返回\r\n        </button>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n</nz-card>\r\n\r\n<!-- 贝号选择卡片 -->\r\n<nz-card class=\"selector-card\" [nzBodyStyle]=\"{'padding': '12px 20px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"24\">\r\n      <div class=\"bay-selector-container\">\r\n        <div class=\"bay-selector\">\r\n          <span class=\"bay-selector-label\">贝号选择:</span>\r\n          <nz-select [(ngModel)]=\"selectedBayNo\" (ngModelChange)=\"onBaySelect()\" class=\"bay-selector-dropdown\">\r\n            <nz-option *ngFor=\"let bay of bayList\" [nzValue]=\"bay.bayNo\" [nzLabel]=\"bay.bayNo\"></nz-option>\r\n          </nz-select>\r\n        </div>\r\n      </div>\r\n    </nz-col>\r\n  </nz-row>\r\n</nz-card>\r\n\r\n<!-- 贝图展示卡片 -->\r\n<nz-card class=\"diagram-card\" [nzBodyStyle]=\"{'padding': '0' }\">\r\n  <div class=\"bay-diagram-container\" (mousedown)=\"onMouseDown($event)\">\r\n    <div class=\"bay-diagram\" *ngIf=\"previewBayData\">\r\n        <!-- 甲板区域 -->\r\n        <div class=\"deck-area\" *ngIf=\"previewBayData.deckTiers > 0 && previewBayData.deckRows > 0\">\r\n          <div class=\"deck-header\">\r\n            <div class=\"row-labels\">\r\n              <div class=\"row-label\" *ngFor=\"let rowLabel of previewBayData.deckRowLabels\">\r\n                {{rowLabel}}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"deck-content\">\r\n            <div class=\"deck-tier\" *ngFor=\"let tier of previewBayData.deckTierLabels; let tierIndex = index\">\r\n              <div class=\"tier-label\">{{tier}}</div>\r\n              <div class=\"tier-positions\">\r\n                <div class=\"position-cell deck-position\"\r\n                     *ngFor=\"let position of previewBayData.deckPositions[tierIndex]; let posIndex = index\"\r\n                     [class.selected]=\"position.selected\"\r\n                     [class.deleted]=\"position.deleted\"\r\n                     [class.drag-highlight]=\"position.dragHighlight\"\r\n                     [attr.data-position-id]=\"position.id\"\r\n                     (click)=\"onPositionClick(position, 'deck', tierIndex, posIndex, $event)\">\r\n                  {{position.label}}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 甲板分隔线 -->\r\n        <div class=\"deck-separator\" *ngIf=\"previewBayData.deckTiers > 0 && previewBayData.holdTiers > 0\">\r\n          <div class=\"separator-line\"></div>\r\n          <div class=\"separator-text\">甲板</div>\r\n        </div>\r\n\r\n        <!-- 舱底区域 -->\r\n        <div class=\"hold-area\" *ngIf=\"previewBayData.holdTiers > 0 && previewBayData.holdRows > 0\">\r\n          <div class=\"hold-content\">\r\n            <div class=\"hold-tier\" *ngFor=\"let tier of previewBayData.holdTierLabels; let tierIndex = index\">\r\n              <div class=\"tier-label\">{{tier}}</div>\r\n              <div class=\"tier-positions\">\r\n                <div class=\"position-cell hold-position\"\r\n                     *ngFor=\"let position of previewBayData.holdPositions[tierIndex]; let posIndex = index\"\r\n                     [class.selected]=\"position.selected\"\r\n                     [class.deleted]=\"position.deleted\"\r\n                     [class.drag-highlight]=\"position.dragHighlight\"\r\n                     [attr.data-position-id]=\"position.id\"\r\n                     (click)=\"onPositionClick(position, 'hold', tierIndex, posIndex, $event)\">\r\n                  {{position.label}}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"hold-footer\">\r\n            <div class=\"row-labels\">\r\n              <div class=\"row-label\" *ngFor=\"let rowLabel of previewBayData.holdRowLabels\">\r\n                {{rowLabel}}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      <div *ngIf=\"!previewBayData && selectedBayData\" class=\"no-positions\">\r\n        <nz-empty nzNotFoundContent=\"暂无贝位数据\"></nz-empty>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</nz-card>\r\n\r\n<!-- 新建贝模态框 -->\r\n<nz-modal [(nzVisible)]=\"isNewBayModalVisible\" nzTitle=\"新建贝\" (nzOnCancel)=\"cancelNewBay()\"\r\n          (nzOnOk)=\"saveNewBay()\" nzOkText=\"保存\" nzCancelText=\"取消\" [nzWidth]=\"1200\">\r\n  <ng-container *nzModalContent>\r\n    <div nz-row [nzGutter]=\"[24, 0]\">\r\n      <!-- 左侧参数配置 -->\r\n      <div nz-col nzSpan=\"10\">\r\n        <nz-card nzTitle=\"参数配置\" [nzSize]=\"'small'\">\r\n          <form nz-form [formGroup]=\"newBayForm\">\r\n            <div nz-row [nzGutter]=\"[16, 8]\">\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>贝号</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入贝号\">\r\n                    <input nz-input placeholder=\"贝号\" formControlName=\"bayNo\">\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <!-- 甲板参数 -->\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-divider nzText=\"甲板参数\" nzOrientation=\"left\"></nz-divider>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>甲板基数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入甲板基数\">\r\n                    <nz-input-number nz-input placeholder=\"甲板基数\" formControlName=\"deckBase\"\r\n                                     [nzMin]=\"1\" [nzMax]=\"99\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>甲板步长</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入甲板步长\">\r\n                    <nz-input-number nz-input placeholder=\"甲板步长\" formControlName=\"deckStep\"\r\n                                     [nzMin]=\"1\" [nzMax]=\"10\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>甲板层数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入甲板层数\">\r\n                    <nz-input-number nz-input placeholder=\"甲板层数\" formControlName=\"deckTiers\"\r\n                                     [nzMin]=\"0\" [nzMax]=\"10\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>甲板行数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入甲板行数\">\r\n                    <nz-input-number nz-input placeholder=\"甲板行数\" formControlName=\"deckRows\"\r\n                                     [nzMin]=\"0\" [nzMax]=\"20\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label>甲板行数是否从0开始</nz-form-label>\r\n                  <nz-form-control>\r\n                    <nz-radio-group formControlName=\"deckFromZero\" (ngModelChange)=\"onParameterChange()\">\r\n                      <label nz-radio nzValue=\"Y\">是</label>\r\n                      <label nz-radio nzValue=\"N\">否</label>\r\n                    </nz-radio-group>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <!-- 舱底参数 -->\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-divider nzText=\"舱底参数\" nzOrientation=\"left\"></nz-divider>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>舱底基数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入舱底基数\">\r\n                    <nz-input-number nz-input placeholder=\"舱底基数\" formControlName=\"holdBase\"\r\n                                     [nzMin]=\"1\" [nzMax]=\"99\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>舱底步长</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入舱底步长\">\r\n                    <nz-input-number nz-input placeholder=\"舱底步长\" formControlName=\"holdStep\"\r\n                                     [nzMin]=\"1\" [nzMax]=\"10\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>舱底层数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入舱底层数\">\r\n                    <nz-input-number nz-input placeholder=\"舱底层数\" formControlName=\"holdTiers\"\r\n                                     [nzMin]=\"0\" [nzMax]=\"10\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"12\">\r\n                <nz-form-item>\r\n                  <nz-form-label nzRequired>舱底行数</nz-form-label>\r\n                  <nz-form-control nzErrorTip=\"请输入舱底行数\">\r\n                    <nz-input-number nz-input placeholder=\"舱底行数\" formControlName=\"holdRows\"\r\n                                     [nzMin]=\"0\" [nzMax]=\"20\" (ngModelChange)=\"onParameterChange()\"></nz-input-number>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label>舱底行数是否从0开始</nz-form-label>\r\n                  <nz-form-control>\r\n                    <nz-radio-group formControlName=\"holdFromZero\" (ngModelChange)=\"onParameterChange()\">\r\n                      <label nz-radio nzValue=\"Y\">是</label>\r\n                      <label nz-radio nzValue=\"N\">否</label>\r\n                    </nz-radio-group>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n\r\n              <div nz-col nzSpan=\"24\">\r\n                <nz-form-item>\r\n                  <nz-form-label>备注</nz-form-label>\r\n                  <nz-form-control>\r\n                    <textarea nz-input placeholder=\"备注\" formControlName=\"remark\" [nzAutosize]=\"{ minRows: 2, maxRows: 3 }\"></textarea>\r\n                  </nz-form-control>\r\n                </nz-form-item>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </nz-card>\r\n      </div>\r\n\r\n      <!-- 右侧贝图预览 -->\r\n      <div nz-col nzSpan=\"14\">\r\n        <nz-card nzTitle=\"贝图预览\" [nzSize]=\"'small'\">\r\n          <div class=\"bay-diagram-container\">\r\n            <div class=\"bay-diagram\" *ngIf=\"previewBayData\">\r\n              <!-- 甲板区域 -->\r\n              <div class=\"deck-area\" *ngIf=\"previewBayData.deckTiers > 0 && previewBayData.deckRows > 0\">\r\n                <div class=\"deck-header\">\r\n                  <div class=\"row-labels\">\r\n                    <div class=\"row-label\" *ngFor=\"let rowLabel of previewBayData.deckRowLabels\">\r\n                      {{rowLabel}}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"deck-content\">\r\n                  <div class=\"deck-tier\" *ngFor=\"let tier of previewBayData.deckTierLabels; let tierIndex = index\">\r\n                    <div class=\"tier-label\">{{tier}}</div>\r\n                    <div class=\"tier-positions\">\r\n                      <div class=\"position-cell deck-position\"\r\n                           *ngFor=\"let position of previewBayData.deckPositions[tierIndex]; let posIndex = index\"\r\n                           [class.selected]=\"position.selected\"\r\n                           [class.deleted]=\"position.deleted\"\r\n                           (click)=\"onPositionClick(position, 'deck', tierIndex, posIndex, $event)\">\r\n                        {{position.label}}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 甲板分隔线 -->\r\n              <div class=\"deck-separator\" *ngIf=\"previewBayData.deckTiers > 0 && previewBayData.holdTiers > 0\">\r\n                <div class=\"separator-line\"></div>\r\n                <div class=\"separator-text\">甲板</div>\r\n              </div>\r\n\r\n              <!-- 舱底区域 -->\r\n              <div class=\"hold-area\" *ngIf=\"previewBayData.holdTiers > 0 && previewBayData.holdRows > 0\">\r\n                <div class=\"hold-content\">\r\n                  <div class=\"hold-tier\" *ngFor=\"let tier of previewBayData.holdTierLabels; let tierIndex = index\">\r\n                    <div class=\"tier-label\">{{tier}}</div>\r\n                    <div class=\"tier-positions\">\r\n                      <div class=\"position-cell hold-position\"\r\n                           *ngFor=\"let position of previewBayData.holdPositions[tierIndex]; let posIndex = index\"\r\n                           [class.selected]=\"position.selected\"\r\n                           [class.deleted]=\"position.deleted\"\r\n                           (click)=\"onPositionClick(position, 'hold', tierIndex, posIndex, $event)\">\r\n                        {{position.label}}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"hold-footer\">\r\n                  <div class=\"row-labels\">\r\n                    <div class=\"row-label\" *ngFor=\"let rowLabel of previewBayData.holdRowLabels\">\r\n                      {{rowLabel}}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 操作按钮 -->\r\n            <div class=\"bay-operations\" *ngIf=\"previewBayData && hasSelectedPositions()\">\r\n              <nz-space>\r\n                <button *nzSpaceItem nz-button nzType=\"primary\" nzDanger (click)=\"deleteSelectedPositions()\">\r\n                  删除选中位置 ({{getSelectedCount()}})\r\n                </button>\r\n                <button *nzSpaceItem nz-button (click)=\"clearSelection()\">\r\n                  清除选择\r\n                </button>\r\n              </nz-space>\r\n            </div>\r\n\r\n            <!-- 空状态提示 -->\r\n            <nz-empty *ngIf=\"!previewBayData || (previewBayData.deckTiers === 0 && previewBayData.holdTiers === 0)\"\r\n                      nzNotFoundImage=\"simple\"\r\n                      nzNotFoundContent=\"请配置参数以生成贝图预览\">\r\n            </nz-empty>\r\n          </div>\r\n        </nz-card>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</nz-modal>\r\n\r\n<!-- 插入贝模态框 -->\r\n<nz-modal [(nzVisible)]=\"isInsertBayModalVisible\" nzTitle=\"插入贝\" (nzOnCancel)=\"isInsertBayModalVisible = false\"\r\n          (nzOnOk)=\"saveInsertBay()\" nzOkText=\"保存\" nzCancelText=\"取消\" [nzWidth]=\"600\">\r\n  <ng-container *nzModalContent>\r\n    <form nz-form [formGroup]=\"insertBayForm\">\r\n      <nz-form-item>\r\n        <nz-form-label nzRequired>插入贝位信息</nz-form-label>\r\n        <nz-form-control nzErrorTip=\"请输入插入贝位信息\">\r\n          <textarea nz-input placeholder=\"请输入贝位信息，用逗号分隔，如：0101,0103,0105\"\r\n                    formControlName=\"insertBayNos\" [nzAutosize]=\"{ minRows: 3, maxRows: 6 }\"></textarea>\r\n        </nz-form-control>\r\n      </nz-form-item>\r\n      <nz-alert nzType=\"info\" nzMessage=\"格式说明：只能包含数字和逗号（中英文逗号都可以），系统会自动剔除空格\" nzShowIcon></nz-alert>\r\n    </form>\r\n  </ng-container>\r\n</nz-modal>\r\n\r\n<!-- 复制贝模态框 -->\r\n<nz-modal [(nzVisible)]=\"isCopyBayModalVisible\" nzTitle=\"复制贝\" (nzOnCancel)=\"isCopyBayModalVisible = false\"\r\n          (nzOnOk)=\"saveCopyBay()\" nzOkText=\"保存\" nzCancelText=\"取消\" [nzWidth]=\"500\">\r\n  <ng-container *nzModalContent>\r\n    <form nz-form [formGroup]=\"copyBayForm\">\r\n      <nz-form-item>\r\n        <nz-form-label>原贝号</nz-form-label>\r\n        <nz-form-control>\r\n          <input nz-input formControlName=\"originalBayNo\" [disabled]=\"true\">\r\n        </nz-form-control>\r\n      </nz-form-item>\r\n\r\n      <nz-form-item>\r\n        <nz-form-label nzRequired>新贝号</nz-form-label>\r\n        <nz-form-control nzErrorTip=\"请输入新贝号\">\r\n          <input nz-input placeholder=\"新贝号\" formControlName=\"newBayNo\">\r\n        </nz-form-control>\r\n      </nz-form-item>\r\n\r\n      <nz-alert nzType=\"info\" nzMessage=\"格式说明：可以包含数字和字母，系统会自动剔除空格\" nzShowIcon></nz-alert>\r\n    </form>\r\n  </ng-container>\r\n</nz-modal>\r\n\r\n<!-- 恢复贝位模态框 - 优化版 -->\r\n<nz-modal [(nzVisible)]=\"isRestorePositionModalVisible\" nzTitle=\"恢复贝位\" (nzOnCancel)=\"cancelRestorePosition()\"\r\n          (nzOnOk)=\"restoreSelectedPositions()\" nzOkText=\"恢复选中贝位\" nzCancelText=\"取消\" [nzWidth]=\"900\" [nzBodyStyle]=\"{'height': '550px', 'overflow': 'hidden'}\">\r\n  <ng-container *nzModalContent>\r\n    <div class=\"restore-position-container\">\r\n      <!-- 搜索和筛选区域 -->\r\n      <div class=\"restore-search-section\">\r\n        <nz-row [nzGutter]=\"[16, 16]\">\r\n          <nz-col nzSpan=\"12\">\r\n            <nz-input-group nzPrefixIcon=\"search\">\r\n              <input nz-input placeholder=\"搜索贝位编号...\" [(ngModel)]=\"restoreSearchText\" (ngModelChange)=\"onRestoreSearch()\">\r\n            </nz-input-group>\r\n          </nz-col>\r\n          <nz-col nzSpan=\"12\">\r\n            <nz-select nzPlaceHolder=\"筛选区域\" [(ngModel)]=\"restoreAreaFilter\" (ngModelChange)=\"onRestoreAreaFilter()\" style=\"width: 100%;\">\r\n              <nz-option nzValue=\"\" nzLabel=\"全部区域\"></nz-option>\r\n              <nz-option nzValue=\"deck\" nzLabel=\"甲板区域\"></nz-option>\r\n              <nz-option nzValue=\"hold\" nzLabel=\"舱底区域\"></nz-option>\r\n            </nz-select>\r\n          </nz-col>\r\n        </nz-row>\r\n      </div>\r\n\r\n      <!-- 批量操作区域 -->\r\n      <div class=\"restore-batch-operations\">\r\n        <nz-space>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"selectAllRestorePositions()\">\r\n            <i nz-icon nzType=\"check-square\" nzTheme=\"outline\"></i>\r\n            全选\r\n          </button>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"deselectAllRestorePositions()\">\r\n            <i nz-icon nzType=\"border\" nzTheme=\"outline\"></i>\r\n            反选\r\n          </button>\r\n          <button *nzSpaceItem nz-button nzSize=\"small\" (click)=\"clearRestoreSelection()\">\r\n            <i nz-icon nzType=\"close-square\" nzTheme=\"outline\"></i>\r\n            清空\r\n          </button>\r\n          <span *nzSpaceItem class=\"restore-selection-count\">\r\n            已选择: <strong>{{ selectedDeletedPositions.length }}</strong> / {{ filteredDeletedPositions.length }}\r\n          </span>\r\n        </nz-space>\r\n      </div>\r\n\r\n      <!-- 贝位网格显示区域 -->\r\n      <div class=\"restore-positions-grid-container\">\r\n        <div class=\"restore-positions-grid\" *ngIf=\"filteredDeletedPositions.length > 0\">\r\n          <div\r\n            class=\"restore-position-card\"\r\n            *ngFor=\"let position of filteredDeletedPositions; trackBy: trackByPositionId\"\r\n            [class.selected]=\"isPositionSelected(position.value)\"\r\n            (click)=\"togglePositionSelection(position.value)\">\r\n            <div class=\"position-card-content\">\r\n              <div class=\"position-id\">{{ position.label }}</div>\r\n              <div class=\"position-area\">{{ getPositionArea(position.value) }}</div>\r\n              <i class=\"position-check-icon\" nz-icon\r\n                 [nzType]=\"isPositionSelected(position.value) ? 'check-circle' : 'plus-circle'\"\r\n                 [nzTheme]=\"isPositionSelected(position.value) ? 'fill' : 'outline'\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div class=\"restore-empty-state\" *ngIf=\"filteredDeletedPositions.length === 0\">\r\n          <nz-empty\r\n            [nzNotFoundContent]=\"restoreSearchText || restoreAreaFilter ? '没有找到匹配的贝位' : '暂无已删除的贝位'\"\r\n            nzNotFoundImage=\"simple\">\r\n          </nz-empty>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部信息提示 -->\r\n      <div class=\"restore-info-section\">\r\n        <nz-alert nzType=\"info\" nzShowIcon\r\n                  nzMessage=\"选择已删除的贝位进行恢复，恢复后贝位将重新显示在贝图中。提示：点击贝位卡片进行选择，支持多选操作。\">\r\n        </nz-alert>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</nz-modal>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AACnE,SAAQC,WAAW,EAAwBC,gBAAgB,EAAEC,aAAa,QAAO,gBAAgB;AAIjG,SAASC,gBAAgB,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICiDlDC,EAAA,CAAAC,SAAA,oBAA+F;;;;IAAlCD,EAAtB,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAqB,YAAAD,MAAA,CAAAC,KAAA,CAAsB;;;;;IAgBhFJ,EAAA,CAAAK,cAAA,cAA6E;IAC3EL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,WAAA,MACF;;;;;;IAOEV,EAAA,CAAAK,cAAA,cAM8E;IAAzEL,EAAA,CAAAW,UAAA,mBAAAC,0EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,WAAA,GAAAH,MAAA,CAAAI,SAAA;MAAA,MAAAC,WAAA,GAAAL,MAAA,CAAAM,KAAA;MAAA,MAAAC,YAAA,GAAArB,EAAA,CAAAsB,aAAA,GAAAF,KAAA;MAAA,MAAAG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAR,WAAA,EAA0B,MAAM,EAAAI,YAAA,EAAAF,WAAA,EAAAN,MAAA,CAA8B;IAAA,EAAC;IAC3Eb,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IAJDP,EAFA,CAAA0B,WAAA,aAAAT,WAAA,CAAAU,QAAA,CAAoC,YAAAV,WAAA,CAAAW,OAAA,CACF,mBAAAX,WAAA,CAAAY,aAAA,CACa;;IAGlD7B,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAQ,WAAA,CAAAa,KAAA,MACF;;;;;IAVF9B,EADF,CAAAK,cAAA,cAAiG,cACvE;IAAAL,EAAA,CAAAM,MAAA,GAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtCP,EAAA,CAAAK,cAAA,cAA4B;IAC1BL,EAAA,CAAA+B,UAAA,IAAAC,oDAAA,kBAM8E;IAIlFhC,EADE,CAAAO,YAAA,EAAM,EACF;;;;;;IAZoBP,EAAA,CAAAQ,SAAA,GAAQ;IAARR,EAAA,CAAAiC,iBAAA,CAAAC,OAAA,CAAQ;IAGJlC,EAAA,CAAAQ,SAAA,GAA4C;IAA5CR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAC,aAAA,CAAAf,YAAA,EAA4C;;;;;IAX1ErB,EAFJ,CAAAK,cAAA,cAA2F,cAChE,cACC;IACtBL,EAAA,CAAA+B,UAAA,IAAAM,8CAAA,kBAA6E;IAIjFrC,EADE,CAAAO,YAAA,EAAM,EACF;IACNP,EAAA,CAAAK,cAAA,cAA0B;IACxBL,EAAA,CAAA+B,UAAA,IAAAO,8CAAA,kBAAiG;IAerGtC,EADE,CAAAO,YAAA,EAAM,EACF;;;;IArB4CP,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAI,aAAA,CAA+B;IAMrCvC,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAK,cAAA,CAAkC;;;;;IAkB9ExC,EAAA,CAAAK,cAAA,cAAiG;IAC/FL,EAAA,CAAAC,SAAA,cAAkC;IAClCD,EAAA,CAAAK,cAAA,cAA4B;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAChCN,EADgC,CAAAO,YAAA,EAAM,EAChC;;;;;;IAQEP,EAAA,CAAAK,cAAA,cAM8E;IAAzEL,EAAA,CAAAW,UAAA,mBAAA8B,0EAAA5B,MAAA;MAAA,MAAA6B,OAAA,GAAA1C,EAAA,CAAAe,aAAA,CAAA4B,IAAA;MAAA,MAAAC,YAAA,GAAAF,OAAA,CAAAxB,SAAA;MAAA,MAAA2B,YAAA,GAAAH,OAAA,CAAAtB,KAAA;MAAA,MAAA0B,aAAA,GAAA9C,EAAA,CAAAsB,aAAA,GAAAF,KAAA;MAAA,MAAAG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAmB,YAAA,EAA0B,MAAM,EAAAE,aAAA,EAAAD,YAAA,EAAAhC,MAAA,CAA8B;IAAA,EAAC;IAC3Eb,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IAJDP,EAFA,CAAA0B,WAAA,aAAAkB,YAAA,CAAAjB,QAAA,CAAoC,YAAAiB,YAAA,CAAAhB,OAAA,CACF,mBAAAgB,YAAA,CAAAf,aAAA,CACa;;IAGlD7B,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAmC,YAAA,CAAAd,KAAA,MACF;;;;;IAVF9B,EADF,CAAAK,cAAA,cAAiG,cACvE;IAAAL,EAAA,CAAAM,MAAA,GAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtCP,EAAA,CAAAK,cAAA,cAA4B;IAC1BL,EAAA,CAAA+B,UAAA,IAAAgB,oDAAA,kBAM8E;IAIlF/C,EADE,CAAAO,YAAA,EAAM,EACF;;;;;;IAZoBP,EAAA,CAAAQ,SAAA,GAAQ;IAARR,EAAA,CAAAiC,iBAAA,CAAAe,QAAA,CAAQ;IAGJhD,EAAA,CAAAQ,SAAA,GAA4C;IAA5CR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAc,aAAA,CAAAH,aAAA,EAA4C;;;;;IAaxE9C,EAAA,CAAAK,cAAA,cAA6E;IAC3EL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAyC,YAAA,MACF;;;;;IApBJlD,EADF,CAAAK,cAAA,cAA2F,cAC/D;IACxBL,EAAA,CAAA+B,UAAA,IAAAoB,8CAAA,kBAAiG;IAcnGnD,EAAA,CAAAO,YAAA,EAAM;IAEJP,EADF,CAAAK,cAAA,cAAyB,cACC;IACtBL,EAAA,CAAA+B,UAAA,IAAAqB,8CAAA,kBAA6E;IAKnFpD,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;IAtBsCP,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAkB,cAAA,CAAkC;IAiB5BrD,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAmB,aAAA,CAA+B;;;;;IAOnFtD,EAAA,CAAAK,cAAA,cAAqE;IACnEL,EAAA,CAAAC,SAAA,mBAAgD;IAClDD,EAAA,CAAAO,YAAA,EAAM;;;;;IA/DRP,EAAA,CAAAK,cAAA,cAAgD;IA6D9CL,EA3DE,CAAA+B,UAAA,IAAAwB,wCAAA,kBAA2F,IAAAC,wCAAA,kBA2BM,IAAAC,wCAAA,kBAMN,IAAAC,wCAAA,kBA0BxB;IAGvE1D,EAAA,CAAAO,YAAA,EAAM;;;;IA9DsBP,EAAA,CAAAQ,SAAA,EAAiE;IAAjER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAAwB,SAAA,QAAApC,MAAA,CAAAY,cAAA,CAAAyB,QAAA,KAAiE;IA2B5D5D,EAAA,CAAAQ,SAAA,EAAkE;IAAlER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAAwB,SAAA,QAAApC,MAAA,CAAAY,cAAA,CAAA0B,SAAA,KAAkE;IAMvE7D,EAAA,CAAAQ,SAAA,EAAiE;IAAjER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAA0B,SAAA,QAAAtC,MAAA,CAAAY,cAAA,CAAA2B,QAAA,KAAiE;IA0BrF9D,EAAA,CAAAQ,SAAA,EAAwC;IAAxCR,EAAA,CAAAE,UAAA,UAAAqB,MAAA,CAAAY,cAAA,IAAAZ,MAAA,CAAAwC,eAAA,CAAwC;;;;;IAkKhC/D,EAAA,CAAAK,cAAA,cAA6E;IAC3EL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAuD,YAAA,MACF;;;;;;IAOEhE,EAAA,CAAAK,cAAA,cAI8E;IAAzEL,EAAA,CAAAW,UAAA,mBAAAsD,0FAAApD,MAAA;MAAA,MAAAqD,OAAA,GAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAC,YAAA,GAAAF,OAAA,CAAAhD,SAAA;MAAA,MAAAmD,YAAA,GAAAH,OAAA,CAAA9C,KAAA;MAAA,MAAAkD,aAAA,GAAAtE,EAAA,CAAAsB,aAAA,GAAAF,KAAA;MAAA,MAAAG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAA2C,YAAA,EAA0B,MAAM,EAAAE,aAAA,EAAAD,YAAA,EAAAxD,MAAA,CAA8B;IAAA,EAAC;IAC3Eb,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IAHDP,EADA,CAAA0B,WAAA,aAAA0C,YAAA,CAAAzC,QAAA,CAAoC,YAAAyC,YAAA,CAAAxC,OAAA,CACF;IAErC5B,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAA2D,YAAA,CAAAtC,KAAA,MACF;;;;;IARF9B,EADF,CAAAK,cAAA,cAAiG,cACvE;IAAAL,EAAA,CAAAM,MAAA,GAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtCP,EAAA,CAAAK,cAAA,cAA4B;IAC1BL,EAAA,CAAA+B,UAAA,IAAAwC,oEAAA,kBAI8E;IAIlFvE,EADE,CAAAO,YAAA,EAAM,EACF;;;;;;IAVoBP,EAAA,CAAAQ,SAAA,GAAQ;IAARR,EAAA,CAAAiC,iBAAA,CAAAuC,QAAA,CAAQ;IAGJxE,EAAA,CAAAQ,SAAA,GAA4C;IAA5CR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAC,aAAA,CAAAkC,aAAA,EAA4C;;;;;IAX1EtE,EAFJ,CAAAK,cAAA,cAA2F,cAChE,cACC;IACtBL,EAAA,CAAA+B,UAAA,IAAA0C,8DAAA,kBAA6E;IAIjFzE,EADE,CAAAO,YAAA,EAAM,EACF;IACNP,EAAA,CAAAK,cAAA,cAA0B;IACxBL,EAAA,CAAA+B,UAAA,IAAA2C,8DAAA,kBAAiG;IAarG1E,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAnB4CP,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAI,aAAA,CAA+B;IAMrCvC,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAK,cAAA,CAAkC;;;;;IAgB9ExC,EAAA,CAAAK,cAAA,cAAiG;IAC/FL,EAAA,CAAAC,SAAA,cAAkC;IAClCD,EAAA,CAAAK,cAAA,cAA4B;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAChCN,EADgC,CAAAO,YAAA,EAAM,EAChC;;;;;;IAQEP,EAAA,CAAAK,cAAA,cAI8E;IAAzEL,EAAA,CAAAW,UAAA,mBAAAgE,0FAAA9D,MAAA;MAAA,MAAA+D,OAAA,GAAA5E,EAAA,CAAAe,aAAA,CAAA8D,IAAA;MAAA,MAAAC,YAAA,GAAAF,OAAA,CAAA1D,SAAA;MAAA,MAAA6D,YAAA,GAAAH,OAAA,CAAAxD,KAAA;MAAA,MAAA4D,aAAA,GAAAhF,EAAA,CAAAsB,aAAA,GAAAF,KAAA;MAAA,MAAAG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAqD,YAAA,EAA0B,MAAM,EAAAE,aAAA,EAAAD,YAAA,EAAAlE,MAAA,CAA8B;IAAA,EAAC;IAC3Eb,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IAHDP,EADA,CAAA0B,WAAA,aAAAoD,YAAA,CAAAnD,QAAA,CAAoC,YAAAmD,YAAA,CAAAlD,OAAA,CACF;IAErC5B,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAAqE,YAAA,CAAAhD,KAAA,MACF;;;;;IARF9B,EADF,CAAAK,cAAA,cAAiG,cACvE;IAAAL,EAAA,CAAAM,MAAA,GAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtCP,EAAA,CAAAK,cAAA,cAA4B;IAC1BL,EAAA,CAAA+B,UAAA,IAAAkD,oEAAA,kBAI8E;IAIlFjF,EADE,CAAAO,YAAA,EAAM,EACF;;;;;;IAVoBP,EAAA,CAAAQ,SAAA,GAAQ;IAARR,EAAA,CAAAiC,iBAAA,CAAAiD,QAAA,CAAQ;IAGJlF,EAAA,CAAAQ,SAAA,GAA4C;IAA5CR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAc,aAAA,CAAA+B,aAAA,EAA4C;;;;;IAWxEhF,EAAA,CAAAK,cAAA,cAA6E;IAC3EL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,MAAA0E,YAAA,MACF;;;;;IAlBJnF,EADF,CAAAK,cAAA,cAA2F,cAC/D;IACxBL,EAAA,CAAA+B,UAAA,IAAAqD,8DAAA,kBAAiG;IAYnGpF,EAAA,CAAAO,YAAA,EAAM;IAEJP,EADF,CAAAK,cAAA,cAAyB,cACC;IACtBL,EAAA,CAAA+B,UAAA,IAAAsD,8DAAA,kBAA6E;IAKnFrF,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;IApBsCP,EAAA,CAAAQ,SAAA,GAAkC;IAAlCR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAkB,cAAA,CAAkC;IAe5BrD,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAY,cAAA,CAAAmB,aAAA,CAA+B;;;;;IAlDnFtD,EAAA,CAAAK,cAAA,cAAgD;IAiC9CL,EA/BA,CAAA+B,UAAA,IAAAuD,wDAAA,kBAA2F,IAAAC,wDAAA,kBAyBM,IAAAC,wDAAA,kBAMN;IAuB7FxF,EAAA,CAAAO,YAAA,EAAM;;;;IAtDoBP,EAAA,CAAAQ,SAAA,EAAiE;IAAjER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAAwB,SAAA,QAAApC,MAAA,CAAAY,cAAA,CAAAyB,QAAA,KAAiE;IAyB5D5D,EAAA,CAAAQ,SAAA,EAAkE;IAAlER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAAwB,SAAA,QAAApC,MAAA,CAAAY,cAAA,CAAA0B,SAAA,KAAkE;IAMvE7D,EAAA,CAAAQ,SAAA,EAAiE;IAAjER,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAA0B,SAAA,QAAAtC,MAAA,CAAAY,cAAA,CAAA2B,QAAA,KAAiE;;;;;;IA4BvF9D,EAAA,CAAAK,cAAA,iBAA6F;IAApCL,EAAA,CAAAW,UAAA,mBAAA8E,oFAAA;MAAAzF,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAnE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAoE,uBAAA,EAAyB;IAAA,EAAC;IAC1F3F,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;IADPP,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAS,kBAAA,4CAAAc,MAAA,CAAAqE,gBAAA,SACF;;;;;;IACA5F,EAAA,CAAAK,cAAA,iBAA0D;IAA3BL,EAAA,CAAAW,UAAA,mBAAAkF,oFAAA;MAAA7F,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAAvE,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAwE,cAAA,EAAgB;IAAA,EAAC;IACvD/F,EAAA,CAAAM,MAAA,iCACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;IANXP,EADF,CAAAK,cAAA,aAA6E,eACjE;IAIRL,EAHA,CAAA+B,UAAA,IAAAiE,2DAAA,qBAA6F,IAAAC,2DAAA,qBAGnC;IAI9DjG,EADE,CAAAO,YAAA,EAAW,EACP;;;;;IAGNP,EAAA,CAAAC,SAAA,mBAGW;;;;;;IA7NrBD,EAAA,CAAAkG,uBAAA,GAA8B;IASdlG,EARd,CAAAK,cAAA,cAAiC,cAEP,kBACqB,eACF,cACJ,cACP,mBACR,wBACc;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAC5CP,EAAA,CAAAK,cAAA,2BAAoC;IAClCL,EAAA,CAAAC,SAAA,iBAAyD;IAG/DD,EAFI,CAAAO,YAAA,EAAkB,EACL,EACX;IAGNP,EAAA,CAAAK,cAAA,eAAwB;IACtBL,EAAA,CAAAC,SAAA,sBAA4D;IAC9DD,EAAA,CAAAO,YAAA,EAAM;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAAwF,sFAAA;MAAAnG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAA2F,sFAAA;MAAAtG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAA4F,sFAAA;MAAAvG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAA6F,sFAAA;MAAAxG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,qBACG;IAAAL,EAAA,CAAAM,MAAA,+DAAU;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAEvCP,EADF,CAAAK,cAAA,uBAAiB,0BACsE;IAAtCL,EAAA,CAAAW,UAAA,2BAAA8F,qFAAA;MAAAzG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAClFrG,EAAA,CAAAK,cAAA,iBAA4B;IAAAL,EAAA,CAAAM,MAAA,cAAC;IAAAN,EAAA,CAAAO,YAAA,EAAQ;IACrCP,EAAA,CAAAK,cAAA,iBAA4B;IAAAL,EAAA,CAAAM,MAAA,cAAC;IAIrCN,EAJqC,CAAAO,YAAA,EAAQ,EACtB,EACD,EACL,EACX;IAGNP,EAAA,CAAAK,cAAA,eAAwB;IACtBL,EAAA,CAAAC,SAAA,sBAA4D;IAC9DD,EAAA,CAAAO,YAAA,EAAM;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAA+F,sFAAA;MAAA1G,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAAgG,sFAAA;MAAA3G,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAAiG,sFAAA;MAAA5G,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,yBACc;IAAAL,EAAA,CAAAM,MAAA,gCAAI;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAE5CP,EADF,CAAAK,cAAA,2BAAsC,2BAE4C;IAAtCL,EAAA,CAAAW,UAAA,2BAAAkG,sFAAA;MAAA7G,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAGrFrG,EAHsF,CAAAO,YAAA,EAAkB,EAClF,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,qBACG;IAAAL,EAAA,CAAAM,MAAA,+DAAU;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAEvCP,EADF,CAAAK,cAAA,uBAAiB,0BACsE;IAAtCL,EAAA,CAAAW,UAAA,2BAAAmG,qFAAA;MAAA9G,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAA7E,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAClFrG,EAAA,CAAAK,cAAA,iBAA4B;IAAAL,EAAA,CAAAM,MAAA,cAAC;IAAAN,EAAA,CAAAO,YAAA,EAAQ;IACrCP,EAAA,CAAAK,cAAA,iBAA4B;IAAAL,EAAA,CAAAM,MAAA,cAAC;IAIrCN,EAJqC,CAAAO,YAAA,EAAQ,EACtB,EACD,EACL,EACX;IAIFP,EAFJ,CAAAK,cAAA,eAAwB,oBACR,qBACG;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IACjCP,EAAA,CAAAK,cAAA,uBAAiB;IACfL,EAAA,CAAAC,SAAA,oBAAkH;IAOhID,EANY,CAAAO,YAAA,EAAkB,EACL,EACX,EACF,EACD,EACC,EACN;IAKFP,EAFJ,CAAAK,cAAA,eAAwB,mBACqB,eACN;IAwEjCL,EAvEA,CAAA+B,UAAA,KAAAgF,kDAAA,kBAAgD,KAAAC,kDAAA,kBA2D6B,KAAAC,uDAAA,uBAclC;IAKnDjH,EAHM,CAAAO,YAAA,EAAM,EACE,EACN,EACF;;;;;IAhOMP,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAAkH,eAAA,KAAAC,GAAA,EAAoB;IAGJnH,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAE,UAAA,mBAAkB;IAC1BF,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAE,UAAA,cAAAqB,MAAA,CAAA6F,UAAA,CAAwB;IACxBpH,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAAkH,eAAA,KAAAG,GAAA,EAAoB;IAoBPrH,EAAA,CAAAQ,SAAA,IAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IA2BxBF,EAAA,CAAAQ,SAAA,IAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAUxBF,EAAA,CAAAQ,SAAA,GAAW;IAACR,EAAZ,CAAAE,UAAA,YAAW,aAAa;IAqBoBF,EAAA,CAAAQ,SAAA,IAAyC;IAAzCR,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAkH,eAAA,KAAAI,GAAA,EAAyC;IAW1FtH,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAE,UAAA,mBAAkB;IAEZF,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,CAAoB;IA2DjBnC,EAAA,CAAAQ,SAAA,EAA8C;IAA9CR,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAAY,cAAA,IAAAZ,MAAA,CAAAgG,oBAAA,GAA8C;IAYhEvH,EAAA,CAAAQ,SAAA,EAA2F;IAA3FR,EAAA,CAAAE,UAAA,UAAAqB,MAAA,CAAAY,cAAA,IAAAZ,MAAA,CAAAY,cAAA,CAAAwB,SAAA,UAAApC,MAAA,CAAAY,cAAA,CAAA0B,SAAA,OAA2F;;;;;IAchH7D,EAAA,CAAAkG,uBAAA,GAA8B;IAGxBlG,EAFJ,CAAAK,cAAA,eAA0C,mBAC1B,wBACc;IAAAL,EAAA,CAAAM,MAAA,2CAAM;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAChDP,EAAA,CAAAK,cAAA,0BAAwC;IACtCL,EAAA,CAAAC,SAAA,mBAC8F;IAElGD,EADE,CAAAO,YAAA,EAAkB,EACL;IACfP,EAAA,CAAAC,SAAA,mBAA6F;IAC/FD,EAAA,CAAAO,YAAA,EAAO;;;;;IATOP,EAAA,CAAAQ,SAAA,EAA2B;IAA3BR,EAAA,CAAAE,UAAA,cAAAqB,MAAA,CAAAiG,aAAA,CAA2B;IAKMxH,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAkH,eAAA,IAAAO,GAAA,EAAyC;;;;;IAW1FzH,EAAA,CAAAkG,uBAAA,GAA8B;IAGxBlG,EAFJ,CAAAK,cAAA,eAAwC,mBACxB,oBACG;IAAAL,EAAA,CAAAM,MAAA,yBAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAClCP,EAAA,CAAAK,cAAA,sBAAiB;IACfL,EAAA,CAAAC,SAAA,gBAAkE;IAEtED,EADE,CAAAO,YAAA,EAAkB,EACL;IAGbP,EADF,CAAAK,cAAA,mBAAc,wBACc;IAAAL,EAAA,CAAAM,MAAA,yBAAG;IAAAN,EAAA,CAAAO,YAAA,EAAgB;IAC7CP,EAAA,CAAAK,cAAA,2BAAqC;IACnCL,EAAA,CAAAC,SAAA,kBAA6D;IAEjED,EADE,CAAAO,YAAA,EAAkB,EACL;IAEfP,EAAA,CAAAC,SAAA,qBAAmF;IACrFD,EAAA,CAAAO,YAAA,EAAO;;;;;IAhBOP,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAE,UAAA,cAAAqB,MAAA,CAAAmG,WAAA,CAAyB;IAIe1H,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAE,UAAA,kBAAiB;;;;;;IA0CjEF,EAAA,CAAAK,cAAA,kBAAoF;IAAtCL,EAAA,CAAAW,UAAA,mBAAAgH,8EAAA;MAAA3H,EAAA,CAAAe,aAAA,CAAA6G,IAAA;MAAA,MAAArG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAsG,yBAAA,EAA2B;IAAA,EAAC;IACjF7H,EAAA,CAAAC,SAAA,aAAuD;IACvDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;;IACTP,EAAA,CAAAK,cAAA,kBAAsF;IAAxCL,EAAA,CAAAW,UAAA,mBAAAmH,8EAAA;MAAA9H,EAAA,CAAAe,aAAA,CAAAgH,IAAA;MAAA,MAAAxG,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAyG,2BAAA,EAA6B;IAAA,EAAC;IACnFhI,EAAA,CAAAC,SAAA,aAAiD;IACjDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;;IACTP,EAAA,CAAAK,cAAA,kBAAgF;IAAlCL,EAAA,CAAAW,UAAA,mBAAAsH,8EAAA;MAAAjI,EAAA,CAAAe,aAAA,CAAAmH,IAAA;MAAA,MAAA3G,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAA4G,qBAAA,EAAuB;IAAA,EAAC;IAC7EnI,EAAA,CAAAC,SAAA,aAAuD;IACvDD,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;;;IACTP,EAAA,CAAAK,cAAA,gBAAmD;IACjDL,EAAA,CAAAM,MAAA,4BAAK;IAAAN,EAAA,CAAAK,cAAA,aAAQ;IAAAL,EAAA,CAAAM,MAAA,GAAqC;IAAAN,EAAA,CAAAO,YAAA,EAAS;IAACP,EAAA,CAAAM,MAAA,GAC9D;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IADQP,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAiC,iBAAA,CAAAV,MAAA,CAAA6G,wBAAA,CAAAC,MAAA,CAAqC;IAAUrI,EAAA,CAAAQ,SAAA,EAC9D;IAD8DR,EAAA,CAAAS,kBAAA,QAAAc,MAAA,CAAA+G,wBAAA,CAAAD,MAAA,MAC9D;;;;;;IAOArI,EAAA,CAAAK,cAAA,eAIoD;IAAlDL,EAAA,CAAAW,UAAA,mBAAA4H,8EAAA;MAAA,MAAAC,YAAA,GAAAxI,EAAA,CAAAe,aAAA,CAAA0H,IAAA,EAAAvH,SAAA;MAAA,MAAAK,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAASD,MAAA,CAAAmH,uBAAA,CAAAF,YAAA,CAAAG,KAAA,CAAuC;IAAA,EAAC;IAE/C3I,EADF,CAAAK,cAAA,eAAmC,eACR;IAAAL,EAAA,CAAAM,MAAA,GAAoB;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnDP,EAAA,CAAAK,cAAA,eAA2B;IAAAL,EAAA,CAAAM,MAAA,GAAqC;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACtEP,EAAA,CAAAC,SAAA,aAE2E;IAE/ED,EADE,CAAAO,YAAA,EAAM,EACF;;;;;IATJP,EAAA,CAAA0B,WAAA,aAAAH,MAAA,CAAAqH,kBAAA,CAAAJ,YAAA,CAAAG,KAAA,EAAqD;IAG1B3I,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAiC,iBAAA,CAAAuG,YAAA,CAAA1G,KAAA,CAAoB;IAClB9B,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAiC,iBAAA,CAAAV,MAAA,CAAAsH,eAAA,CAAAL,YAAA,CAAAG,KAAA,EAAqC;IAE7D3I,EAAA,CAAAQ,SAAA,EAA8E;IAC9ER,EADA,CAAAE,UAAA,WAAAqB,MAAA,CAAAqH,kBAAA,CAAAJ,YAAA,CAAAG,KAAA,mCAA8E,YAAApH,MAAA,CAAAqH,kBAAA,CAAAJ,YAAA,CAAAG,KAAA,uBACX;;;;;IAX5E3I,EAAA,CAAAK,cAAA,eAAgF;IAC9EL,EAAA,CAAA+B,UAAA,IAAA+G,wDAAA,mBAIoD;IAStD9I,EAAA,CAAAO,YAAA,EAAM;;;;IAXmBP,EAAA,CAAAQ,SAAA,EAA6B;IAAAR,EAA7B,CAAAE,UAAA,YAAAqB,MAAA,CAAA+G,wBAAA,CAA6B,iBAAA/G,MAAA,CAAAwH,iBAAA,CAA0B;;;;;IAchF/I,EAAA,CAAAK,cAAA,eAA+E;IAC7EL,EAAA,CAAAC,SAAA,oBAGW;IACbD,EAAA,CAAAO,YAAA,EAAM;;;;IAHFP,EAAA,CAAAQ,SAAA,EAAuF;IAAvFR,EAAA,CAAAE,UAAA,sBAAAqB,MAAA,CAAAyH,iBAAA,IAAAzH,MAAA,CAAA0H,iBAAA,iHAAuF;;;;;;IA9DjGjJ,EAAA,CAAAkG,uBAAA,GAA8B;IAOlBlG,EANV,CAAAK,cAAA,eAAwC,eAEF,kBACJ,kBACR,0BACoB,iBACwE;IAApEL,EAAA,CAAAkJ,gBAAA,2BAAAC,2EAAAtI,MAAA;MAAAb,EAAA,CAAAe,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAqJ,kBAAA,CAAA9H,MAAA,CAAAyH,iBAAA,EAAAnI,MAAA,MAAAU,MAAA,CAAAyH,iBAAA,GAAAnI,MAAA;MAAA,OAAAb,EAAA,CAAAwB,WAAA,CAAAX,MAAA;IAAA,EAA+B;IAACb,EAAA,CAAAW,UAAA,2BAAAwI,2EAAA;MAAAnJ,EAAA,CAAAe,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAA+H,eAAA,EAAiB;IAAA,EAAC;IAE/GtJ,EAFI,CAAAO,YAAA,EAA4G,EAC7F,EACV;IAEPP,EADF,CAAAK,cAAA,kBAAoB,qBAC2G;IAA7FL,EAAA,CAAAkJ,gBAAA,2BAAAK,+EAAA1I,MAAA;MAAAb,EAAA,CAAAe,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAqJ,kBAAA,CAAA9H,MAAA,CAAA0H,iBAAA,EAAApI,MAAA,MAAAU,MAAA,CAAA0H,iBAAA,GAAApI,MAAA;MAAA,OAAAb,EAAA,CAAAwB,WAAA,CAAAX,MAAA;IAAA,EAA+B;IAACb,EAAA,CAAAW,UAAA,2BAAA4I,+EAAA;MAAAvJ,EAAA,CAAAe,aAAA,CAAAqI,IAAA;MAAA,MAAA7H,MAAA,GAAAvB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAiBD,MAAA,CAAAiI,mBAAA,EAAqB;IAAA,EAAC;IAGrGxJ,EAFA,CAAAC,SAAA,qBAAiD,sBACI,sBACA;IAI7DD,EAHM,CAAAO,YAAA,EAAY,EACL,EACF,EACL;IAIJP,EADF,CAAAK,cAAA,gBAAsC,gBAC1B;IAaRL,EAZA,CAAA+B,UAAA,KAAA0H,qDAAA,sBAAoF,KAAAC,qDAAA,sBAIE,KAAAC,qDAAA,sBAIN,KAAAC,mDAAA,oBAI7B;IAIvD5J,EADE,CAAAO,YAAA,EAAW,EACP;IAGNP,EAAA,CAAAK,cAAA,gBAA8C;IAkB5CL,EAjBA,CAAA+B,UAAA,KAAA8H,kDAAA,mBAAgF,KAAAC,kDAAA,mBAiBD;IAMjF9J,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAK,cAAA,gBAAkC;IAChCL,EAAA,CAAAC,SAAA,qBAEW;IAEfD,EADE,CAAAO,YAAA,EAAM,EACF;;;;;IAtEMP,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAE,UAAA,aAAAF,EAAA,CAAAkH,eAAA,IAAA6C,GAAA,EAAqB;IAGiB/J,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAgK,gBAAA,YAAAzI,MAAA,CAAAyH,iBAAA,CAA+B;IAIzChJ,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAgK,gBAAA,YAAAzI,MAAA,CAAA0H,iBAAA,CAA+B;IAgC9BjJ,EAAA,CAAAQ,SAAA,IAAyC;IAAzCR,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAA+G,wBAAA,CAAAD,MAAA,KAAyC;IAiB5CrI,EAAA,CAAAQ,SAAA,EAA2C;IAA3CR,EAAA,CAAAE,UAAA,SAAAqB,MAAA,CAAA+G,wBAAA,CAAAD,MAAA,OAA2C;;;ADxcrF,OAAM,MAAO4B,kBAAmB,SAAQrK,WAAW;EAmBjDsK,YACEC,oBAA0C,EAClCC,GAAsB,EACtBC,iBAAoC,EACpCC,KAAqB,EACrBC,GAAsB;IAE9B,KAAK,CAACJ,oBAAoB,CAAC;IALnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,GAAG,GAAHA,GAAG;IAvBb,KAAAC,SAAS,GAAG,IAAIzK,gBAAgB,EAAE;IAClC,KAAA0K,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAA5G,eAAe,GAAQ,IAAI;IAC3B,KAAA6G,OAAO,GAAU,EAAE;IACnB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,iBAAiB,GAAU,EAAE;IAE7B;IACA,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,uBAAuB,GAAG,KAAK;IAC/B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,6BAA6B,GAAG,KAAK;IAuCrC;IACA,KAAA/I,cAAc,GAAQ,IAAI;IAC1B,KAAAgJ,gBAAgB,GAAgB,IAAIC,GAAG,EAAE;IAEzC;IACA,KAAAhD,wBAAwB,GAAa,EAAE;IACvC,KAAAiD,sBAAsB,GAAU,EAAE;IAElC;IACA,KAAArC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAX,wBAAwB,GAAU,EAAE;IAEpC;IACA,KAAAgD,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,iBAAiB,GAAQ,IAAI;IAC7B,KAAAC,gBAAgB,GAAuB,IAAI;IAC3C,KAAAC,cAAc,GAAG,KAAK;IAEtB;IACA,KAAAC,YAAY,GAAG,KAAK,CAAC,CAAE;IACvB,KAAAC,qBAAqB,GAAG,IAAIX,GAAG,EAAU,CAAC,CAAE;IAC5C,KAAAY,gBAAgB,GAAkB,IAAI,CAAC,CAAE;IAEzC;IACA,KAAAC,aAAa,GAAG,CAAC,CAAC,CAAE;IACpB,KAAAC,QAAQ,GAAG,KAAK,CAAC,CAAG;IACpB,KAAAC,aAAa,GAAG,GAAG,CAAC,CAAE;IACtB,KAAAC,aAAa,GAAG,CAAC,CAAC,CAAI;IAEtB;IACA,KAAAC,iBAAiB,GAAG,KAAK,CAAC,CAAM;IAChC,KAAAC,iBAAiB,GAAG,KAAK,CAAC,CAAM;IAChC,KAAAC,mBAAmB,GAAuB,IAAI,CAAC,CAAE;EA/DjD;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAAC/B,QAAQ,GAAG,IAAI,CAACgC,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE;IAChD,IAAI,CAAC/B,QAAQ,GAAG,IAAI,CAAC+B,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE;IAEhDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BlC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBkC,SAAS,EAAE,IAAI,CAACH;KACjB,CAAC;IAEF,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAClB,IAAI,CAACoC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,WAAW,CAAC;MAChD,IAAI,CAACC,YAAY,EAAE;MACnB;IACF;IAEA,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;EACvB;EA+CA;;;EAGAF,SAASA,CAAA;IACP,IAAI,CAAC5F,UAAU,GAAG,IAAI1H,SAAS,CAAC;MAC9BU,KAAK,EAAE,IAAIX,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAACyN,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EC,QAAQ,EAAE,IAAI5N,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3FC,QAAQ,EAAE,IAAI/N,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1F5J,SAAS,EAAE,IAAIlE,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3F3J,QAAQ,EAAE,IAAInE,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1FE,YAAY,EAAE,IAAIhO,WAAW,CAAC,GAAG,CAAC;MAClCiO,QAAQ,EAAE,IAAIjO,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3FI,QAAQ,EAAE,IAAIlO,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1F1J,SAAS,EAAE,IAAIpE,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3FzJ,QAAQ,EAAE,IAAIrE,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAAC2N,GAAG,CAAC,CAAC,CAAC,EAAE3N,UAAU,CAAC4N,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1FK,YAAY,EAAE,IAAInO,WAAW,CAAC,GAAG,CAAC;MAClCoO,MAAM,EAAE,IAAIpO,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACyN,SAAS,CAAC,GAAG,CAAC,CAAC;KACxD,CAAC;IAEF,IAAI,CAAC5F,aAAa,GAAG,IAAI9H,SAAS,CAAC;MACjCoO,YAAY,EAAE,IAAIrO,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwN,QAAQ,CAAC;KACxD,CAAC;IAEF,IAAI,CAACzF,WAAW,GAAG,IAAIhI,SAAS,CAAC;MAC/BqO,aAAa,EAAE,IAAItO,WAAW,CAAC,EAAE,CAAC;MAClCuO,QAAQ,EAAE,IAAIvO,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwN,QAAQ,EAAExN,UAAU,CAACyN,SAAS,CAAC,EAAE,CAAC,CAAC;KAC9E,CAAC;IAEF;IACA,IAAI,CAAChG,UAAU,CAAC6G,YAAY,CAACC,SAAS,CAAC,MAAK;MAC1C,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC;EACJ;EAEA;;;EAGAlB,WAAWA,CAAA;IACT;IACA,IAAI,CAACrC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACD,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC5G,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC5B,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2I,iBAAiB,GAAG,EAAE;IAE3B;IACA,MAAMsD,WAAW,GAAG;MAClBC,IAAI,EAAE;QACJ5D,QAAQ,EAAE,IAAI,CAACA;;KAElB;IAEDiC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyB,WAAW,CAAC;IAExC,IAAI,CAAC/D,iBAAiB,CAACiE,IAAI,CAAC,kBAAkB,EAAEF,WAAW,EAAE,IAAI,CAAChE,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACzFC,IAAI,CAAEC,GAAsB,IAAI;MAC/BhC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+B,GAAG,CAAC;MACjC,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;QACnB,IAAI,CAAC/D,OAAO,GAAG8D,GAAG,CAACL,IAAI,IAAI,EAAE;QAC7B3B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;UAC9BlC,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBmE,QAAQ,EAAE,IAAI,CAAChE,OAAO,CAACvC,MAAM;UAC7BuC,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;QAEF,IAAI,IAAI,CAACA,OAAO,CAACvC,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACsC,aAAa,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACxK,KAAK;UAC1C,IAAI,CAACyO,WAAW,EAAE;QACpB,CAAC,MAAM;UACLnC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/B,IAAI,CAAChC,aAAa,GAAG,EAAE;UACvB,IAAI,CAAC5G,eAAe,GAAG,IAAI;UAC3B,IAAI,CAAC5B,cAAc,GAAG,IAAI;QAC5B;MACF,CAAC,MAAM;QACLuK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+B,GAAG,CAACI,GAAG,CAAC;QACrC,IAAI,CAACjC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACDC,KAAK,CAAEjC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACD,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,UAAU,CAAC;IACjD,CAAC,CAAC;EACN;EAEA;;;EAGAkC,oBAAoBA,CAAC5O,KAAa;IAChC;IACA,MAAMgO,WAAW,GAAG;MAClBC,IAAI,EAAE;QACJ5D,QAAQ,EAAE,IAAI,CAACA;;KAElB;IAEDiC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyB,WAAW,CAAC;IAExC,IAAI,CAAC/D,iBAAiB,CAACiE,IAAI,CAAC,kBAAkB,EAAEF,WAAW,EAAE,IAAI,CAAChE,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACzFC,IAAI,CAAEC,GAAsB,IAAI;MAC/BhC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+B,GAAG,CAAC;MACjC,IAAIA,GAAG,CAACC,EAAE,KAAK,IAAI,EAAE;QACnB,IAAI,CAAC/D,OAAO,GAAG8D,GAAG,CAACL,IAAI,IAAI,EAAE;QAC7B3B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC/B,OAAO,CAAC;QAEzC;QACA,MAAMqE,SAAS,GAAG,IAAI,CAACrE,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAKA,KAAK,CAAC;QAC/D,IAAI6O,SAAS,EAAE;UACb,IAAI,CAACtE,aAAa,GAAGvK,KAAK;UAC1BsM,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEvM,KAAK,CAAC;UAClC,IAAI,CAACyO,WAAW,EAAE;QACpB,CAAC,MAAM,IAAI,IAAI,CAACjE,OAAO,CAACvC,MAAM,GAAG,CAAC,EAAE;UAClC;UACA,IAAI,CAACsC,aAAa,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACxK,KAAK;UAC1C,IAAI,CAACyO,WAAW,EAAE;QACpB,CAAC,MAAM;UACLnC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;MACF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+B,GAAG,CAACI,GAAG,CAAC;QACrC,IAAI,CAACjC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACDC,KAAK,CAAEjC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC,CAAC,CAAC;EACN;EAEA;;;EAGA+B,WAAWA,CAAA;IACTnC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAChC,aAAa,CAAC;IAC/C+B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC/B,OAAO,CAAC;IAE3C;IACA,IAAI,CAACE,iBAAiB,GAAG,EAAE;IAE3B,IAAI,CAAC/G,eAAe,GAAG,IAAI,CAAC6G,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAK,IAAI,CAACuK,aAAa,CAAC;IACjF+B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC5I,eAAe,CAAC;IAEpD,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB,IAAI,CAACqL,oBAAoB,EAAE;IAC7B,CAAC,MAAM;MACL1C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA;;;EAGAyC,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrL,eAAe,EAAE;MACzB2I,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C;IACF;IAEA,MAAM0B,IAAI,GAAG,IAAI,CAACtK,eAAe;IACjC2I,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0B,IAAI,CAAC;IAEvC;IACA,IAAI,CAAClD,gBAAgB,CAACkE,KAAK,EAAE;IAC7B,IAAIhB,IAAI,CAACiB,OAAO,EAAE;MAChB,MAAMC,cAAc,GAAGlB,IAAI,CAACiB,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9C9C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4C,cAAc,CAAC;MAE7C;MACA,MAAME,oBAAoB,GAAG,IAAI,CAACC,8BAA8B,CAACrB,IAAI,CAAC;MACtE3B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8C,oBAAoB,CAAC;MAEtDA,oBAAoB,CAACE,OAAO,CAACC,UAAU,IAAG;QACxC,IAAI,CAACL,cAAc,CAACM,QAAQ,CAACD,UAAU,CAAC,EAAE;UACxC,IAAI,CAACzE,gBAAgB,CAAC2E,GAAG,CAACF,UAAU,CAAC;QACvC;MACF,CAAC,CAAC;MAEFlD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,CAAC,CAAC;IACnE;IAEA;IACA,IAAI,CAAChJ,cAAc,GAAG;MACpBwB,SAAS,EAAE0K,IAAI,CAAC4B,KAAK,IAAI,CAAC;MAC1BrM,QAAQ,EAAEyK,IAAI,CAAC6B,IAAI,IAAI,CAAC;MACxBrM,SAAS,EAAEwK,IAAI,CAAC8B,KAAK,IAAI,CAAC;MAC1BrM,QAAQ,EAAEuK,IAAI,CAAC+B,IAAI,IAAI,CAAC;MACxB5N,cAAc,EAAE,IAAI,CAAC6N,sBAAsB,CAAChC,IAAI,CAACiC,KAAK,EAAEjC,IAAI,CAACkC,KAAK,EAAElC,IAAI,CAAC4B,KAAK,CAAC;MAC/E1N,aAAa,EAAE,IAAI,CAACiO,iBAAiB,CAACnC,IAAI,CAAC6B,IAAI,EAAE7B,IAAI,CAACoC,eAAe,KAAK,GAAG,CAAC;MAC9EpN,cAAc,EAAE,IAAI,CAACqN,sBAAsB,CAACrC,IAAI,CAACsC,KAAK,EAAEtC,IAAI,CAACuC,KAAK,EAAEvC,IAAI,CAAC8B,KAAK,CAAC;MAC/E7M,aAAa,EAAE,IAAI,CAACkN,iBAAiB,CAACnC,IAAI,CAAC+B,IAAI,EAAE/B,IAAI,CAACwC,eAAe,KAAK,GAAG,CAAC;MAC9EzO,aAAa,EAAE,IAAI,CAAC0O,qBAAqB,CAACzC,IAAI,CAACiC,KAAK,EAAEjC,IAAI,CAACkC,KAAK,EAAElC,IAAI,CAAC4B,KAAK,EAAE5B,IAAI,CAAC6B,IAAI,EAAE7B,IAAI,CAACoC,eAAe,KAAK,GAAG,CAAC;MACtHxN,aAAa,EAAE,IAAI,CAAC8N,qBAAqB,CAAC1C,IAAI,CAACsC,KAAK,EAAEtC,IAAI,CAACuC,KAAK,EAAEvC,IAAI,CAAC8B,KAAK,EAAE9B,IAAI,CAAC+B,IAAI,EAAE/B,IAAI,CAACwC,eAAe,KAAK,GAAG;KACtH;IAEDnE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACxK,cAAc,CAAC;IACnD,IAAI,CAAC2I,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAACkG,4BAA4B,EAAE;EACrC;EAEA;;;EAGAA,4BAA4BA,CAAA;IAC1B,IAAI,CAAC3F,sBAAsB,GAAG0E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,CAAC,CAAC8F,GAAG,CAACrB,UAAU,KAAK;MACjF9N,KAAK,EAAE8N,UAAU;MACjBjH,KAAK,EAAEiH;KACR,CAAC,CAAC;IAEH;IACA,IAAI,CAACsB,8BAA8B,EAAE;IAErCxE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACtB,sBAAsB,CAAC;EAC7D;EAEA;;;EAGA8F,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACpN,eAAe,EAAE;MACzB,IAAI,CAAC8I,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,SAAS,CAAC;MAC9C;IACF;IAEA,IAAI,IAAI,CAAC3B,gBAAgB,CAACiG,IAAI,KAAK,CAAC,EAAE;MACpC,IAAI,CAACvE,SAAS,CAAC/M,aAAa,CAACuR,IAAI,EAAE,iBAAiB,CAAC;MACrD;IACF;IAEA;IACA,IAAI,CAACrI,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAAC+H,4BAA4B,EAAE;IAEnC;IACA,IAAI,CAAC5I,wBAAwB,GAAG,EAAE;IAElC;IACA,IAAI,CAAC8C,6BAA6B,GAAG,IAAI;EAC3C;EAEA;;;EAGAoG,qBAAqBA,CAAA;IACnB,IAAI,CAACpG,6BAA6B,GAAG,KAAK;IAC1C,IAAI,CAAC9C,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACY,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,iBAAiB,GAAG,EAAE;EAC7B;EAEA;;;EAGAiI,8BAA8BA,CAAA;IAC5B,IAAIK,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAClG,sBAAsB,CAAC;IAE/C;IACA,IAAI,IAAI,CAACrC,iBAAiB,EAAE;MAC1B,MAAMwI,UAAU,GAAG,IAAI,CAACxI,iBAAiB,CAACyI,WAAW,EAAE;MACvDF,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAAC7P,KAAK,CAAC2P,WAAW,EAAE,CAAC5B,QAAQ,CAAC2B,UAAU,CAAC,CAClD;IACH;IAEA;IACA,IAAI,IAAI,CAACvI,iBAAiB,EAAE;MAC1BsI,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IAAG;QACpC,MAAMC,IAAI,GAAG,IAAI,CAAC/I,eAAe,CAAC8I,QAAQ,CAAChJ,KAAK,CAAC;QACjD+D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;UAC1BiD,UAAU,EAAE+B,QAAQ,CAAChJ,KAAK;UAC1BiJ,IAAI,EAAEA,IAAI;UACVF,MAAM,EAAE,IAAI,CAACzI,iBAAiB;UAC9B4I,KAAK,EAAE,IAAI,CAACC,eAAe,CAACF,IAAI,EAAE,IAAI,CAAC3I,iBAAiB;SACzD,CAAC;QACF,OAAO,IAAI,CAAC6I,eAAe,CAACF,IAAI,EAAE,IAAI,CAAC3I,iBAAiB,CAAC;MAC3D,CAAC,CAAC;IACJ;IAEA,IAAI,CAACX,wBAAwB,GAAGiJ,QAAQ;IACxC7E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1BoF,KAAK,EAAE,IAAI,CAAC1G,sBAAsB,CAAChD,MAAM;MACzCkJ,QAAQ,EAAEA,QAAQ,CAAClJ,MAAM;MACzBmJ,UAAU,EAAE,IAAI,CAACxI,iBAAiB;MAClCgJ,UAAU,EAAE,IAAI,CAAC/I;KAClB,CAAC;EACJ;EAEA;;;EAGAK,eAAeA,CAAA;IACb,IAAI,CAAC4H,8BAA8B,EAAE;EACvC;EAEA;;;EAGA1H,mBAAmBA,CAAA;IACjB,IAAI,CAAC0H,8BAA8B,EAAE;EACvC;EAEA;;;EAGAY,eAAeA,CAACF,IAAY,EAAEF,MAAc;IAC1C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;IAE1B;IACA,IAAIA,MAAM,KAAK,MAAM,IAAIE,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;IACnD,IAAIF,MAAM,KAAK,MAAM,IAAIE,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;IAEnD,OAAO,KAAK;EACd;EAEA;;;EAGA/I,eAAeA,CAAC+G,UAAkB;IAChC,IAAI,CAAC,IAAI,CAACzN,cAAc,EAAE;MACxB,OAAO,IAAI;IACb;IAEA;IACA,IAAI,IAAI,CAACA,cAAc,CAACC,aAAa,EAAE;MACrC,KAAK,MAAM6P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;QAC7D,MAAMuP,QAAQ,GAAGM,aAAa,CAAC/C,IAAI,CAAEgD,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKvC,UAAU,CAAC;QACpE,IAAI+B,QAAQ,EAAE,OAAO,IAAI;MAC3B;IACF;IAEA;IACA,IAAI,IAAI,CAACxP,cAAc,CAACc,aAAa,EAAE;MACrC,KAAK,MAAMgP,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;QAC7D,MAAM0O,QAAQ,GAAGM,aAAa,CAAC/C,IAAI,CAAEgD,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKvC,UAAU,CAAC;QACpE,IAAI+B,QAAQ,EAAE,OAAO,IAAI;MAC3B;IACF;IAEA,OAAO,IAAI;EACb;EAEA;;;EAGAjJ,uBAAuBA,CAACkH,UAAkB;IACxC,MAAMxO,KAAK,GAAG,IAAI,CAACgH,wBAAwB,CAACgK,OAAO,CAACxC,UAAU,CAAC;IAC/D,IAAIxO,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACgH,wBAAwB,CAACiK,MAAM,CAACjR,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACgH,wBAAwB,CAACkK,IAAI,CAAC1C,UAAU,CAAC;IAChD;EACF;EAEA;;;EAGAhH,kBAAkBA,CAACgH,UAAkB;IACnC,OAAO,IAAI,CAACxH,wBAAwB,CAACyH,QAAQ,CAACD,UAAU,CAAC;EAC3D;EAEA;;;EAGA/H,yBAAyBA,CAAA;IACvB,IAAI,CAACS,wBAAwB,CAACqH,OAAO,CAACgC,QAAQ,IAAG;MAC/C,IAAI,CAAC,IAAI,CAAC/I,kBAAkB,CAAC+I,QAAQ,CAAChJ,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACP,wBAAwB,CAACkK,IAAI,CAACX,QAAQ,CAAChJ,KAAK,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEA;;;EAGAX,2BAA2BA,CAAA;IACzB,IAAI,CAACM,wBAAwB,CAACqH,OAAO,CAACgC,QAAQ,IAAG;MAC/C,MAAMvQ,KAAK,GAAG,IAAI,CAACgH,wBAAwB,CAACgK,OAAO,CAACT,QAAQ,CAAChJ,KAAK,CAAC;MACnE,IAAIvH,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACgH,wBAAwB,CAACiK,MAAM,CAACjR,KAAK,EAAE,CAAC,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAACgH,wBAAwB,CAACkK,IAAI,CAACX,QAAQ,CAAChJ,KAAK,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEA;;;EAGAR,qBAAqBA,CAAA;IACnB,IAAI,CAACC,wBAAwB,GAAG,EAAE;EACpC;EAEA;;;EAGAW,iBAAiBA,CAAC3H,KAAa,EAAEmR,IAAS;IACxC,OAAOA,IAAI,CAAC5J,KAAK;EACnB;EAEA;;;EAGA6J,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACzO,eAAe,EAAE;MACzB,IAAI,CAAC8I,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,SAAS,CAAC;MAC9C;IACF;IAEA,IAAI,IAAI,CAAC1E,wBAAwB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACwE,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,YAAY,CAAC;MACjD;IACF;IAEA,MAAM2F,OAAO,GAAG,IAAI,CAAC1O,eAAe;IACpC2I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8F,OAAO,CAAC;IAExC;IACA,MAAMrE,WAAW,GAAG;MAClBsE,KAAK,EAAED,OAAO,CAACN,EAAE;MAAG;MACpBQ,MAAM,EAAE,IAAI,CAACvK,wBAAwB,CAAE;KACxC;IAEDsE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyB,WAAW,CAAC;IAE5C;IACA,IAAI,CAAC/D,iBAAiB,CAACiE,IAAI,CAAC,+CAA+C,EAAEF,WAAW,EAAE,IAAI,CAAChE,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACtHC,IAAI,CAAEmE,QAAa,IAAI;MACtBlG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiG,QAAQ,CAAC;MACvC,IAAIA,QAAQ,IAAIA,QAAQ,CAACjE,EAAE,EAAE;QAC3B,IAAI,CAAC9B,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;QAE9C;QACA,IAAI,CAACzK,wBAAwB,CAACuH,OAAO,CAACC,UAAU,IAAG;UACjD,IAAI,CAACzE,gBAAgB,CAAC2H,MAAM,CAAClD,UAAU,CAAC;UAExC;UACA,IAAI,CAACzN,cAAc,CAACC,aAAa,CAACuN,OAAO,CAAEsC,aAAoB,IAAI;YACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;cAC/B,IAAIA,QAAQ,CAACQ,EAAE,KAAKvC,UAAU,EAAE;gBAC9B+B,QAAQ,CAAC/P,OAAO,GAAG,KAAK;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;UAEF,IAAI,CAACO,cAAc,CAACc,aAAa,CAAC0M,OAAO,CAAEsC,aAAoB,IAAI;YACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;cAC/B,IAAIA,QAAQ,CAACQ,EAAE,KAAKvC,UAAU,EAAE;gBAC9B+B,QAAQ,CAAC/P,OAAO,GAAG,KAAK;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;QACA,IAAI,CAACwG,wBAAwB,GAAG,EAAE;QAElC;QACA,IAAI,CAAC4I,4BAA4B,EAAE;QAEnC;QACA,IAAI,CAAC9F,6BAA6B,GAAG,KAAK;QAE1CwB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,CAAC,CAAC;MACvE,CAAC,MAAM;QACL,IAAI,CAAC0B,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE8F,QAAQ,EAAE9D,GAAG,IAAI,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,CACDC,KAAK,CAAEjC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACD,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,OAAO,CAAC;IAC9C,CAAC,CAAC;EACN;EAEA;;;EAGArL,eAAeA,CAACkQ,QAAa,EAAEC,IAAa,EAAEmB,SAAkB,EAAEC,QAAiB,EAAEC,KAAkB;IACrGvG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BiD,UAAU,EAAE+B,QAAQ,CAACQ,EAAE;MACvBe,eAAe,EAAEvB,QAAQ,CAAChQ,QAAQ;MAClC2J,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BW,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCkH,QAAQ,EAAE,CAAC,CAACF;KACb,CAAC;IAEF,IAAItB,QAAQ,CAAC/P,OAAO,EAAE;MACpB8K,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC,OAAO,CAAC;IACV;IAEA;IACA,IAAIsG,KAAK,EAAE;MACTA,KAAK,CAACG,eAAe,EAAE;MACvBH,KAAK,CAACI,cAAc,EAAE;IACxB;IAEA;IACA,IAAI,IAAI,CAAC/H,UAAU,EAAE;MACnBoB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC;IACF;IAEA;IACA,MAAM2G,kBAAkB,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACvH,aAAa;IAC1D,IAAI,IAAI,CAACA,aAAa,GAAG,CAAC,IAAIqH,kBAAkB,GAAG,EAAE,IAAIA,kBAAkB,GAAG,GAAG,EAAE;MACjF5G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2G,kBAAkB,CAAC;MAC7D;IACF;IAEA;IACA,MAAMG,aAAa,GAAGR,KAAK,GAAIA,KAAK,CAACS,OAAO,IAAIT,KAAK,CAACU,OAAO,GAAI,IAAI,CAAC9H,cAAc;IAEpF;IACA,MAAM+H,WAAW,GAAGjC,QAAQ,CAAChQ,QAAQ;IAErC;IACA;IACAgQ,QAAQ,CAAChQ,QAAQ,GAAG,CAACgQ,QAAQ,CAAChQ,QAAQ;IAEtC,IAAIgQ,QAAQ,CAAChQ,QAAQ,EAAE;MACrB;MACA,IAAI,CAAC,IAAI,CAACmJ,iBAAiB,CAACoE,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC,EAAE;QAC3D,IAAI,CAACrH,iBAAiB,CAACwH,IAAI,CAACX,QAAQ,CAAC;MACvC;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC7G,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC4G,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC;IACnF;IAEA;IACA,IAAI,CAAC5H,GAAG,CAACsJ,aAAa,EAAE;IAExBnH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BiD,UAAU,EAAE+B,QAAQ,CAACQ,EAAE;MACvByB,WAAW,EAAEA,WAAW;MACxBE,WAAW,EAAEnC,QAAQ,CAAChQ,QAAQ;MAC9BoS,aAAa,EAAE,IAAI,CAACjJ,iBAAiB,CAACzC,MAAM;MAC5CyC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACmG,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACC,EAAE;KACxD,CAAC;EACJ;EAEA;;;EAGA6B,eAAeA,CAAA;IACb,IAAI,CAAC5M,UAAU,CAAC6M,KAAK,EAAE;IACvB,IAAI,CAAC7M,UAAU,CAAC8M,UAAU,CAAC;MACzB7G,QAAQ,EAAE,EAAE;MACZG,QAAQ,EAAE,CAAC;MACX7J,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACX6J,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC;MACX9J,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACX8J,YAAY,EAAE;KACf,CAAC;IACF,IAAI,CAACzC,gBAAgB,CAACkE,KAAK,EAAE;IAC7B,IAAI,CAAClB,sBAAsB,EAAE;IAC7B,IAAI,CAACpD,oBAAoB,GAAG,IAAI;EAClC;EAEA;;;EAGAoJ,YAAYA,CAAA;IACV,IAAI,CAACpJ,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAAC5I,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACgJ,gBAAgB,CAACkE,KAAK,EAAE;EAC/B;EAEA;;;EAGAhJ,iBAAiBA,CAAA;IACf+N,UAAU,CAAC,MAAK;MACd,IAAI,CAACjG,sBAAsB,EAAE;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAA,sBAAsBA,CAAA;IACpB,MAAMkG,SAAS,GAAG,IAAI,CAACjN,UAAU,CAACkN,WAAW,EAAE;IAE/C,IAAI,CAACD,SAAS,CAAChH,QAAQ,IAAI,CAACgH,SAAS,CAAC3G,QAAQ,EAAE;MAC9C,IAAI,CAACvL,cAAc,GAAG,IAAI;MAC1B;IACF;IAEA,MAAMwB,SAAS,GAAG0Q,SAAS,CAAC1Q,SAAS,IAAI,CAAC;IAC1C,MAAMC,QAAQ,GAAGyQ,SAAS,CAACzQ,QAAQ,IAAI,CAAC;IACxC,MAAMC,SAAS,GAAGwQ,SAAS,CAACxQ,SAAS,IAAI,CAAC;IAC1C,MAAMC,QAAQ,GAAGuQ,SAAS,CAACvQ,QAAQ,IAAI,CAAC;IAExC,IAAIH,SAAS,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC1B,cAAc,GAAG,IAAI;MAC1B;IACF;IAEA,IAAI,CAACA,cAAc,GAAG;MACpBwB,SAAS;MACTC,QAAQ;MACRC,SAAS;MACTC,QAAQ;MACRtB,cAAc,EAAE,IAAI,CAAC6N,sBAAsB,CAACgE,SAAS,CAAChH,QAAQ,EAAEgH,SAAS,CAAC7G,QAAQ,EAAE7J,SAAS,CAAC;MAC9FpB,aAAa,EAAE,IAAI,CAACiO,iBAAiB,CAAC5M,QAAQ,EAAEyQ,SAAS,CAAC5G,YAAY,KAAK,GAAG,CAAC;MAC/EpK,cAAc,EAAE,IAAI,CAACqN,sBAAsB,CAAC2D,SAAS,CAAC3G,QAAQ,EAAE2G,SAAS,CAAC1G,QAAQ,EAAE9J,SAAS,CAAC;MAC9FP,aAAa,EAAE,IAAI,CAACkN,iBAAiB,CAAC1M,QAAQ,EAAEuQ,SAAS,CAACzG,YAAY,KAAK,GAAG,CAAC;MAC/ExL,aAAa,EAAE,IAAI,CAAC0O,qBAAqB,CAACuD,SAAS,CAAChH,QAAQ,EAAEgH,SAAS,CAAC7G,QAAQ,EAAE7J,SAAS,EAAEC,QAAQ,EAAEyQ,SAAS,CAAC5G,YAAY,KAAK,GAAG,CAAC;MACtIxK,aAAa,EAAE,IAAI,CAAC8N,qBAAqB,CAACsD,SAAS,CAAC3G,QAAQ,EAAE2G,SAAS,CAAC1G,QAAQ,EAAE9J,SAAS,EAAEC,QAAQ,EAAEuQ,SAAS,CAACzG,YAAY,KAAK,GAAG;KACtI;EACH;EAEA;;;EAGA8B,8BAA8BA,CAACrB,IAAS;IACtC,MAAMkG,YAAY,GAAG,EAAE;IAEvB;IACA,IAAIlG,IAAI,CAAC4B,KAAK,GAAG,CAAC,IAAI5B,IAAI,CAAC6B,IAAI,GAAG,CAAC,EAAE;MACnC,MAAM3N,aAAa,GAAG,IAAI,CAACiO,iBAAiB,CAACnC,IAAI,CAAC6B,IAAI,EAAE7B,IAAI,CAACoC,eAAe,KAAK,GAAG,CAAC;MACrF,KAAK,IAAIsC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG1E,IAAI,CAAC4B,KAAK,EAAE8C,SAAS,EAAE,EAAE;QAC3D,MAAMyB,SAAS,GAAGC,MAAM,CAACpG,IAAI,CAACiC,KAAK,GAAGyC,SAAS,GAAG1E,IAAI,CAACkC,KAAK,CAAC,CAACmE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC9E,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGtG,IAAI,CAAC6B,IAAI,EAAEyE,QAAQ,EAAE,EAAE;UACvD,MAAMC,QAAQ,GAAGrS,aAAa,CAACoS,QAAQ,CAAC;UACxCJ,YAAY,CAACjC,IAAI,CAAC,GAAGsC,QAAQ,GAAGJ,SAAS,EAAE,CAAC;QAC9C;MACF;IACF;IAEA;IACA,IAAInG,IAAI,CAAC8B,KAAK,GAAG,CAAC,IAAI9B,IAAI,CAAC+B,IAAI,GAAG,CAAC,EAAE;MACnC,MAAM9M,aAAa,GAAG,IAAI,CAACkN,iBAAiB,CAACnC,IAAI,CAAC+B,IAAI,EAAE/B,IAAI,CAACwC,eAAe,KAAK,GAAG,CAAC;MACrF,KAAK,IAAIkC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG1E,IAAI,CAAC8B,KAAK,EAAE4C,SAAS,EAAE,EAAE;QAC3D,MAAMyB,SAAS,GAAGC,MAAM,CAACpG,IAAI,CAACsC,KAAK,GAAGoC,SAAS,GAAG1E,IAAI,CAACuC,KAAK,CAAC,CAAC8D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC9E,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGtG,IAAI,CAAC+B,IAAI,EAAEuE,QAAQ,EAAE,EAAE;UACvD,MAAMC,QAAQ,GAAGtR,aAAa,CAACqR,QAAQ,CAAC;UACxCJ,YAAY,CAACjC,IAAI,CAAC,GAAGsC,QAAQ,GAAGJ,SAAS,EAAE,CAAC;QAC9C;MACF;IACF;IAEA,OAAOD,YAAY;EACrB;EAEA;;;EAGAlE,sBAAsBA,CAACwE,IAAY,EAAEC,IAAY,EAAEC,KAAa;IAC9D,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;MAC9BD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACI,IAAI,GAAGI,CAAC,GAAGH,IAAI,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACvD;IACA,OAAOM,MAAM;EACf;EAEA;;;EAGAtE,sBAAsBA,CAACmE,IAAY,EAAEC,IAAY,EAAEC,KAAa;IAC9D,MAAMC,MAAM,GAAG,EAAE;IACjB;IACA,KAAK,IAAIC,CAAC,GAAGF,KAAK,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACnCD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACI,IAAI,GAAGI,CAAC,GAAGH,IAAI,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACvD;IACA,OAAOM,MAAM;EACf;EAEA;;;;;;;EAOAxE,iBAAiBA,CAAC0E,IAAY,EAAEC,QAAiB;IAC/C,IAAID,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzB,MAAMF,MAAM,GAAG,EAAE;IACjB,MAAMI,KAAK,GAAGF,IAAI,GAAG,CAAC,KAAK,CAAC;IAC5B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,GAAG,CAAC,CAAC;IAErC;IACA,KAAK,IAAID,CAAC,GAAGI,QAAQ,EAAEJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAClCD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACQ,CAAC,GAAG,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC7C;IAEA;IACA,IAAIU,KAAK,EAAE;MACT,IAAID,QAAQ,EAAE;QACZH,MAAM,CAAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACL0C,MAAM,CAAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MACrB;IACF;IAEA;IACA,IAAI8C,KAAK,IAAID,QAAQ,EAAE;MACrB;MACA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAII,QAAQ,EAAEJ,CAAC,EAAE,EAAE;QAClCD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACjD;IACF,CAAC,MAAM,IAAIU,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC7B;MACA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAII,QAAQ,EAAEJ,CAAC,EAAE,EAAE;QAClCD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACjD;IACF,CAAC,MAAM;MACL;MACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAII,QAAQ,EAAEJ,CAAC,EAAE,EAAE;QAClCD,MAAM,CAAC1C,IAAI,CAACmC,MAAM,CAACQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACjD;IACF;IAEA,OAAOM,MAAM;EACf;EAEA;;;EAGAlE,qBAAqBA,CAAC+D,IAAY,EAAEC,IAAY,EAAEC,KAAa,EAAEG,IAAY,EAAEC,QAAiB;IAC9F,MAAMK,SAAS,GAAG,EAAE;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACjF,iBAAiB,CAAC0E,IAAI,EAAEC,QAAQ,CAAC;IAExD,KAAK,IAAIpC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGgC,KAAK,EAAEhC,SAAS,EAAE,EAAE;MACtD,MAAMd,aAAa,GAAG,EAAE;MACxB,MAAMuC,SAAS,GAAGC,MAAM,CAACI,IAAI,GAAG9B,SAAS,GAAG+B,IAAI,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAElE,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGO,IAAI,EAAEP,QAAQ,EAAE,EAAE;QAClD,MAAMC,QAAQ,GAAGa,SAAS,CAACd,QAAQ,CAAC;QACpC,MAAM/E,UAAU,GAAG,GAAGgF,QAAQ,GAAGJ,SAAS,EAAE;QAE5CvC,aAAa,CAACK,IAAI,CAAC;UACjBH,EAAE,EAAEvC,UAAU;UACd9N,KAAK,EAAE8N,UAAU;UACjBjO,QAAQ,EAAE,KAAK;UACfC,OAAO,EAAE,IAAI,CAACuJ,gBAAgB,CAACuK,GAAG,CAAC9F,UAAU,CAAC;UAC9CmD,SAAS;UACT4B;SACD,CAAC;MACJ;MAEAa,SAAS,CAAClD,IAAI,CAACL,aAAa,CAAC;IAC/B;IAEA,OAAOuD,SAAS;EAClB;EAEA;;;EAGAzE,qBAAqBA,CAAC8D,IAAY,EAAEC,IAAY,EAAEC,KAAa,EAAEG,IAAY,EAAEC,QAAiB;IAC9F,MAAMK,SAAS,GAAG,EAAE;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACjF,iBAAiB,CAAC0E,IAAI,EAAEC,QAAQ,CAAC;IAExD;IACA,KAAK,IAAIpC,SAAS,GAAGgC,KAAK,GAAG,CAAC,EAAEhC,SAAS,IAAI,CAAC,EAAEA,SAAS,EAAE,EAAE;MAC3D,MAAMd,aAAa,GAAG,EAAE;MACxB,MAAMuC,SAAS,GAAGC,MAAM,CAACI,IAAI,GAAG9B,SAAS,GAAG+B,IAAI,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAElE,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGO,IAAI,EAAEP,QAAQ,EAAE,EAAE;QAClD,MAAMC,QAAQ,GAAGa,SAAS,CAACd,QAAQ,CAAC;QACpC,MAAM/E,UAAU,GAAG,GAAGgF,QAAQ,GAAGJ,SAAS,EAAE;QAE5CvC,aAAa,CAACK,IAAI,CAAC;UACjBH,EAAE,EAAEvC,UAAU;UACd9N,KAAK,EAAE8N,UAAU;UACjBjO,QAAQ,EAAE,KAAK;UACfC,OAAO,EAAE,IAAI,CAACuJ,gBAAgB,CAACuK,GAAG,CAAC9F,UAAU,CAAC;UAC9CmD,SAAS;UACT4B;SACD,CAAC;MACJ;MAEAa,SAAS,CAAClD,IAAI,CAACL,aAAa,CAAC;IAC/B;IAEA,OAAOuD,SAAS;EAClB;EAEA;;;EAGAG,UAAUA,CAAA;IACR,KAAK,MAAMV,CAAC,IAAI,IAAI,CAAC7N,UAAU,CAACwO,QAAQ,EAAE;MACxC,IAAI,CAACxO,UAAU,CAACwO,QAAQ,CAACX,CAAC,CAAC,CAACY,WAAW,EAAE;MACzC,IAAI,CAACzO,UAAU,CAACwO,QAAQ,CAACX,CAAC,CAAC,CAACa,sBAAsB,EAAE;IACtD;IAEA,IAAI,IAAI,CAAC1O,UAAU,CAAC2O,OAAO,EAAE;MAC3B;IACF;IAEA;IACA,MAAM3V,KAAK,GAAG,IAAI,CAACgH,UAAU,CAAC4O,GAAG,CAAC,OAAO,CAAC,EAAErN,KAAK;IACjD,IAAI,IAAI,CAACiC,OAAO,CAACqL,IAAI,CAAC9G,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAKA,KAAK,CAAC,EAAE;MACjD,IAAI,CAACyM,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,cAAc,CAAC;MACnD;IACF;IAEA;IACA,MAAMyC,cAAc,GAAG,IAAI,CAAC2G,0BAA0B,EAAE;IACxD,MAAM7B,SAAS,GAAG,IAAI,CAACjN,UAAU,CAACkN,WAAW,EAAE;IAE/C,MAAMlG,WAAW,GAAG;MAClB3D,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBrK,KAAK,EAAEiU,SAAS,CAACjU,KAAK;MACtBkP,OAAO,EAAEC,cAAc,CAAC4G,IAAI,CAAC,GAAG,CAAC;MACjClG,KAAK,EAAEoE,SAAS,CAAC1Q,SAAS;MAC1BwM,KAAK,EAAEkE,SAAS,CAACxQ,SAAS;MAC1BqM,IAAI,EAAEmE,SAAS,CAACzQ,QAAQ;MACxBwM,IAAI,EAAEiE,SAAS,CAACvQ,QAAQ;MACxBwM,KAAK,EAAE+D,SAAS,CAAChH,QAAQ;MACzBkD,KAAK,EAAE8D,SAAS,CAAC7G,QAAQ;MACzBmD,KAAK,EAAE0D,SAAS,CAAC3G,QAAQ;MACzBkD,KAAK,EAAEyD,SAAS,CAAC1G,QAAQ;MACzB8C,eAAe,EAAE4D,SAAS,CAAC5G,YAAY;MACvCoD,eAAe,EAAEwD,SAAS,CAACzG,YAAY;MACvCC,MAAM,EAAEwG,SAAS,CAACxG,MAAM,IAAI;KAC7B;IAED,IAAI,CAACxD,iBAAiB,CAACiE,IAAI,CAAC,aAAa,EAAEF,WAAW,EAAE,IAAI,CAAChE,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACpFC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAC9B,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;QAC9C,IAAI,CAAC9H,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC5I,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACgJ,gBAAgB,CAACkE,KAAK,EAAE;QAC7B;QACA,IAAI,CAACL,oBAAoB,CAACqF,SAAS,CAACjU,KAAK,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACyM,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;;;EAGAoH,0BAA0BA,CAAA;IACxB,MAAM3G,cAAc,GAAG,EAAE;IAEzB,IAAI,IAAI,CAACpN,cAAc,EAAE;MACvB;MACA,KAAK,MAAM8P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;QAC7D,KAAK,MAAMuP,QAAQ,IAAIM,aAAa,EAAE;UACpC,IAAI,CAACN,QAAQ,CAAC/P,OAAO,EAAE;YACrB2N,cAAc,CAAC+C,IAAI,CAACX,QAAQ,CAACQ,EAAE,CAAC;UAClC;QACF;MACF;MAEA;MACA,KAAK,MAAMF,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;QAC7D,KAAK,MAAM0O,QAAQ,IAAIM,aAAa,EAAE;UACpC,IAAI,CAACN,QAAQ,CAAC/P,OAAO,EAAE;YACrB2N,cAAc,CAAC+C,IAAI,CAACX,QAAQ,CAACQ,EAAE,CAAC;UAClC;QACF;MACF;IACF;IAEA,OAAO5C,cAAc;EACvB;EAIA;;;EAGAhI,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACpF,cAAc,EAAE,OAAO,KAAK;IAEtC;IACA,KAAK,MAAM8P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;MAC7D,KAAK,MAAMuP,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1C,OAAO,IAAI;QACb;MACF;IACF;IAEA;IACA,KAAK,MAAMqQ,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;MAC7D,KAAK,MAAM0O,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1C,OAAO,IAAI;QACb;MACF;IACF;IAEA,OAAO,KAAK;EACd;EAEA;;;EAGAgE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzD,cAAc,EAAE,OAAO,CAAC;IAElC,IAAIiU,KAAK,GAAG,CAAC;IAEb;IACA,KAAK,MAAMnE,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;MAC7D,KAAK,MAAMuP,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1CwU,KAAK,EAAE;QACT;MACF;IACF;IAEA;IACA,KAAK,MAAMnE,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;MAC7D,KAAK,MAAM0O,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1CwU,KAAK,EAAE;QACT;MACF;IACF;IAEA,OAAOA,KAAK;EACd;EAEA;;;EAGAzQ,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxD,cAAc,EAAE;IAE1B;IACA,KAAK,MAAM8P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;MAC7D,KAAK,MAAMuP,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1C+P,QAAQ,CAAC/P,OAAO,GAAG,IAAI;UACvB+P,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;UACzB,IAAI,CAACwJ,gBAAgB,CAAC2E,GAAG,CAAC6B,QAAQ,CAACQ,EAAE,CAAC;QACxC;MACF;IACF;IAEA;IACA,KAAK,MAAMF,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;MAC7D,KAAK,MAAM0O,QAAQ,IAAIM,aAAa,EAAE;QACpC,IAAIN,QAAQ,CAAChQ,QAAQ,IAAI,CAACgQ,QAAQ,CAAC/P,OAAO,EAAE;UAC1C+P,QAAQ,CAAC/P,OAAO,GAAG,IAAI;UACvB+P,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;UACzB,IAAI,CAACwJ,gBAAgB,CAAC2E,GAAG,CAAC6B,QAAQ,CAACQ,EAAE,CAAC;QACxC;MACF;IACF;EACF;EAEA;;;EAGApM,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC5D,cAAc,EAAE;IAE1B;IACA,KAAK,MAAM8P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;MAC7D,KAAK,MAAMuP,QAAQ,IAAIM,aAAa,EAAE;QACpCN,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;MAC3B;IACF;IAEA;IACA,KAAK,MAAMsQ,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;MAC7D,KAAK,MAAM0O,QAAQ,IAAIM,aAAa,EAAE;QACpCN,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;MAC3B;IACF;EACF;EAEA;;;EAGM0U,SAASA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAI,CAACD,KAAI,CAAC3L,aAAa,EAAE;QACvB2L,KAAI,CAACzJ,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,WAAW,CAAC;QAChD;MACF;MAEA,MAAM0J,KAAK,SAASF,KAAI,CAACG,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;MACrD,IAAID,KAAK,KAAK3W,gBAAgB,CAAC6W,GAAG,EAAE;QAClC;MACF;MAEA,MAAMjE,OAAO,GAAG6D,KAAI,CAAC1L,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAKkW,KAAI,CAAC3L,aAAa,CAAC;MAC1E,IAAI,CAAC8H,OAAO,EAAE;MAEd6D,KAAI,CAACjM,iBAAiB,CAACyI,MAAM,CAAC,eAAeL,OAAO,CAACN,EAAE,EAAE,EAAEmE,KAAI,CAAClM,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACvFC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV2H,KAAI,CAACzJ,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;UAC9CyD,KAAI,CAACrJ,WAAW,EAAE;QACpB,CAAC,MAAM;UACLqJ,KAAI,CAACzJ,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGA6H,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAChM,aAAa,EAAE;MACvB,IAAI,CAACkC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,WAAW,CAAC;MAChD;IACF;IACA,IAAI,CAACtF,aAAa,CAACyM,KAAK,EAAE;IAC1B,IAAI,CAACjJ,uBAAuB,GAAG,IAAI;EACrC;EAEA;;;EAGA4L,aAAaA,CAAA;IACX,KAAK,MAAM3B,CAAC,IAAI,IAAI,CAACzN,aAAa,CAACoO,QAAQ,EAAE;MAC3C,IAAI,CAACpO,aAAa,CAACoO,QAAQ,CAACX,CAAC,CAAC,CAACY,WAAW,EAAE;MAC5C,IAAI,CAACrO,aAAa,CAACoO,QAAQ,CAACX,CAAC,CAAC,CAACa,sBAAsB,EAAE;IACzD;IAEA,IAAI,IAAI,CAACtO,aAAa,CAACuO,OAAO,EAAE;MAC9B;IACF;IAEA,MAAMjI,YAAY,GAAG,IAAI,CAACtG,aAAa,CAACwO,GAAG,CAAC,cAAc,CAAC,EAAErN,KAAK;IAElE;IACA,MAAMkO,KAAK,GAAG,aAAa;IAC3B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAChJ,YAAY,CAAC,EAAE;MAC7B,IAAI,CAACjB,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,uBAAuB,CAAC;MAC5D;IACF;IAEA,MAAM2F,OAAO,GAAG,IAAI,CAAC7H,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAK,IAAI,CAACuK,aAAa,CAAC;IAC1E,IAAI,CAAC8H,OAAO,EAAE;IAEd;IACA,MAAME,MAAM,GAAG7E,YAAY,CAACiJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACvH,KAAK,CAAC,GAAG,CAAC,CAACkC,MAAM,CAACsF,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE,KAAK,EAAE,CAAC;IAE7G,MAAM7I,WAAW,GAAG;MAClBsE,KAAK,EAAED,OAAO,CAACN,EAAE;MAAG;MACpBQ,MAAM,EAAEA,MAAM,CAAM;KACrB;IAEDjG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyB,WAAW,CAAC;IAE5C,IAAI,CAAC/D,iBAAiB,CAACiE,IAAI,CAAC,+CAA+C,EAAEF,WAAW,EAAE,IAAI,CAAChE,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACtHC,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAC9B,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;QAC9C,IAAI,CAAC7H,uBAAuB,GAAG,KAAK;QACpC,IAAI,CAACiC,WAAW,EAAE;QAClBmH,UAAU,CAAC,MAAM,IAAI,CAACvF,WAAW,EAAE,EAAE,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAChC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACDC,KAAK,CAAEjC,KAAK,IAAI;MACfJ,OAAO,CAACI,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACD,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,YAAY,CAAC;IACnD,CAAC,CAAC;EACN;EAEA;;;EAGMoK,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAZ,iBAAA;MACnB,IAAI,CAACY,MAAI,CAACxM,aAAa,EAAE;QACvBwM,MAAI,CAACtK,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,cAAc,CAAC;QACnD;MACF;MAEA,IAAIqK,MAAI,CAACrM,iBAAiB,CAACzC,MAAM,KAAK,CAAC,EAAE;QACvC8O,MAAI,CAACtK,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,cAAc,CAAC;QACnD;MACF;MAEA,MAAM2F,OAAO,GAAG0E,MAAI,CAACvM,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAK+W,MAAI,CAACxM,aAAa,CAAC;MAC1E,IAAI,CAAC8H,OAAO,EAAE;MAEd;MACA,MAAMrE,WAAW,GAAG;QAClBsE,KAAK,EAAED,OAAO,CAACN,EAAE;QAAG;QACpBQ,MAAM,EAAEwE,MAAI,CAACrM,iBAAiB,CAACmG,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CAAE;OAChD;MAEDzF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyB,WAAW,CAAC;MAE5C;MACA+I,MAAI,CAAC9M,iBAAiB,CAACyI,MAAM,CAAC,+CAA+C,EAAEqE,MAAI,CAAC/M,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,EAAE;QAAE4I,IAAI,EAAEhJ;MAAW,CAAE,CAAC,CAClIK,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACVwI,MAAI,CAACtK,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;UAE9C;UACAsE,MAAI,CAACrM,iBAAiB,CAAC6E,OAAO,CAACgC,QAAQ,IAAG;YACxCA,QAAQ,CAAC/P,OAAO,GAAG,IAAI;YACvB+P,QAAQ,CAAChQ,QAAQ,GAAG,KAAK,CAAC,CAAC;YAC3BwV,MAAI,CAAChM,gBAAgB,CAAC2E,GAAG,CAAC6B,QAAQ,CAACQ,EAAE,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEF;UACAgF,MAAI,CAACrM,iBAAiB,GAAG,EAAE;UAE3B;UACAqM,MAAI,CAACnG,4BAA4B,EAAE;UAEnCtE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,KAAK,CAACC,IAAI,CAACmH,MAAI,CAAChM,gBAAgB,CAAC,CAAC;QACnE,CAAC,MAAM;UACLgM,MAAI,CAACtK,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGAuI,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC1M,aAAa,EAAE;MACvB,IAAI,CAACkC,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,cAAc,CAAC;MACnD;IACF;IAEA,IAAI,CAACpF,WAAW,CAACuM,KAAK,EAAE;IACxB,IAAI,CAACvM,WAAW,CAACwM,UAAU,CAAC;MAC1BnG,aAAa,EAAE,IAAI,CAACpD;KACrB,CAAC;IACF,IAAI,CAACM,qBAAqB,GAAG,IAAI;EACnC;EAEA;;;EAGMqM,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MACf,KAAK,MAAMtB,CAAC,IAAIsC,MAAI,CAAC7P,WAAW,CAACkO,QAAQ,EAAE;QACzC2B,MAAI,CAAC7P,WAAW,CAACkO,QAAQ,CAACX,CAAC,CAAC,CAACY,WAAW,EAAE;QAC1C0B,MAAI,CAAC7P,WAAW,CAACkO,QAAQ,CAACX,CAAC,CAAC,CAACa,sBAAsB,EAAE;MACvD;MAEA,IAAIyB,MAAI,CAAC7P,WAAW,CAACqO,OAAO,EAAE;QAC5B;MACF;MAEA,MAAM/H,QAAQ,GAAGuJ,MAAI,CAAC7P,WAAW,CAACsO,GAAG,CAAC,UAAU,CAAC,EAAErN,KAAK;MAExD;MACA,MAAMkO,KAAK,GAAG,gBAAgB;MAC9B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC9I,QAAQ,CAAC+I,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;QAC5CQ,MAAI,CAAC1K,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE,8BAA8B,CAAC;QACnE;MACF;MAEA;MACA,IAAIyK,MAAI,CAAC3M,OAAO,CAACqL,IAAI,CAAC9G,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAK4N,QAAQ,CAAC,EAAE;QACpD,MAAMwI,KAAK,SAASe,MAAI,CAACd,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC;QAC1D,IAAID,KAAK,KAAK3W,gBAAgB,CAAC6W,GAAG,EAAE;UAClC;QACF;MACF;MAEA,MAAMc,eAAe,GAAGD,MAAI,CAAC3M,OAAO,CAACsE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/O,KAAK,KAAKmX,MAAI,CAAC5M,aAAa,CAAC;MAClF,IAAI,CAAC6M,eAAe,EAAE;MAEtB;MACA,MAAMC,OAAO,GAAG,sBAAsBF,MAAI,CAAC9M,QAAQ,QAAQ8M,MAAI,CAAC5M,aAAa,qBAAqB+M,kBAAkB,CAAC1J,QAAQ,CAAC,EAAE;MAEhIuJ,MAAI,CAAClN,iBAAiB,CAACiE,IAAI,CAACmJ,OAAO,EAAE,EAAE,EAAEF,MAAI,CAACnN,GAAG,CAACmE,WAAW,CAAC,KAAK,CAAC,CAACC,EAAE,CAAC,CACrEC,IAAI,CAAEC,GAAsB,IAAI;QAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;UACV4I,MAAI,CAAC1K,SAAS,CAAC/M,aAAa,CAAC+S,OAAO,EAAE,OAAO,CAAC;UAC9C0E,MAAI,CAACtM,qBAAqB,GAAG,KAAK;UAClC;UACAsM,MAAI,CAACvI,oBAAoB,CAAChB,QAAQ,CAAC;QACrC,CAAC,MAAM;UACLuJ,MAAI,CAAC1K,SAAS,CAAC/M,aAAa,CAACgN,KAAK,EAAE4B,GAAG,CAACI,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IAAC;EACP;EAEA;;;EAGA6I,MAAMA,CAAA;IACJ,IAAI,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EACnC;EAEA;EAEA;;;EAGA1K,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC2K,eAAe,GAAIC,CAAC,IAAI;MAC3B,IAAIA,CAAC,CAACC,GAAG,KAAK,SAAS,IAAID,CAAC,CAACC,GAAG,KAAK,MAAM,EAAE;QAC3C,IAAI,CAAClM,cAAc,GAAG,IAAI;MAC5B;IACF,CAAC;IAED,IAAI,CAACmM,aAAa,GAAIF,CAAC,IAAI;MACzB,IAAIA,CAAC,CAACC,GAAG,KAAK,SAAS,IAAID,CAAC,CAACC,GAAG,KAAK,MAAM,EAAE;QAC3C,IAAI,CAAClM,cAAc,GAAG,KAAK;MAC7B;IACF,CAAC;IAED,IAAI,CAACoM,iBAAiB,GAAIH,CAAC,IAAK,IAAI,CAACI,WAAW,CAACJ,CAAC,CAAC;IACnD,IAAI,CAACK,eAAe,GAAIL,CAAC,IAAK,IAAI,CAACM,SAAS,CAACN,CAAC,CAAC;IAE/C;IACAO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACT,eAAe,CAAC;IAC1DQ,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACN,aAAa,CAAC;IACtDK,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACL,iBAAiB,CAAC;IAC9DI,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACH,eAAe,CAAC;EAC5D;EAEA;;;EAGAI,WAAWA,CAACtF,KAAiB;IAC3B;IACA,IAAIA,KAAK,CAACuF,MAAM,KAAK,CAAC,EAAE;IAExB9L,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5B8L,MAAM,EAAGxF,KAAK,CAACwF,MAAsB,CAACC,SAAS;MAC/CC,cAAc,EAAG1F,KAAK,CAACwF,MAAsB,CAACG,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC;MACjFC,WAAW,EAAEvF,IAAI,CAACC,GAAG;KACtB,CAAC;IAEF;IACA,IAAI,CAACvH,aAAa,GAAGsH,IAAI,CAACC,GAAG,EAAE;IAC/B,IAAI,CAACjI,UAAU,GAAG0H,KAAK,CAAC8F,OAAO;IAC/B,IAAI,CAACvN,UAAU,GAAGyH,KAAK,CAAC+F,OAAO;IAC/B,IAAI,CAAC9M,QAAQ,GAAG,KAAK;IAErB;IACA,MAAMuM,MAAM,GAAGxF,KAAK,CAACwF,MAAqB;IAC1C,IAAIA,MAAM,CAACG,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC9C;MACA,IAAI,CAACxM,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACE,mBAAmB,GAAGkM,MAAM;IACnC,CAAC,MAAM;MACL;MACA,IAAI,CAACnM,iBAAiB,GAAG,IAAI;IAC/B;EACF;EAEA;;;EAGA2M,sBAAsBA,CAAChG,KAAiB,EAAEwF,MAAmB;IAC3D/L,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9B,IAAI,CAACrB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,qBAAqB,CAACsD,KAAK,EAAE;IAElC;IACA,MAAMO,UAAU,GAAG6I,MAAM,CAACS,YAAY,CAAC,kBAAkB,CAAC;IAC1D,IAAItJ,UAAU,EAAE;MACd,IAAI,CAAC7D,qBAAqB,CAAC+D,GAAG,CAACF,UAAU,CAAC;MAC1C,IAAI,CAAC5D,gBAAgB,GAAG4D,UAAU;MAElC;MACA;MAEA;MACA,MAAM+B,QAAQ,GAAG,IAAI,CAACwH,gBAAgB,CAACvJ,UAAU,CAAC;MAClD,IAAI+B,QAAQ,IAAI,CAACA,QAAQ,CAAC/P,OAAO,EAAE;QACjC+P,QAAQ,CAAC9P,aAAa,GAAG,IAAI;QAC7B,IAAI,CAAC0I,GAAG,CAACsJ,aAAa,EAAE;QACxBnH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiD,UAAU,CAAC;MAC/C;IACF;IAEA;IACAqD,KAAK,CAACI,cAAc,EAAE;EACxB;EAEA;;;EAGA+F,2BAA2BA,CAACnG,KAAiB;IAC3C,IAAI,CAAC3H,UAAU,GAAG,IAAI;IACtB,IAAI,CAACQ,YAAY,GAAG,KAAK;IACzB,IAAI,CAACP,UAAU,GAAG0H,KAAK,CAAC8F,OAAO;IAC/B,IAAI,CAACvN,UAAU,GAAGyH,KAAK,CAAC+F,OAAO;IAC/B,IAAI,CAACvN,YAAY,GAAGwH,KAAK,CAAC8F,OAAO;IACjC,IAAI,CAACrN,YAAY,GAAGuH,KAAK,CAAC+F,OAAO;IAEjC;IACA,IAAI,CAACK,kBAAkB,EAAE;IAEzB;IACApG,KAAK,CAACI,cAAc,EAAE;EACxB;EAEA;;;EAGAgG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACzN,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC0N,MAAM,EAAE;IAChC;IAEA,IAAI,CAAC1N,gBAAgB,GAAGyM,QAAQ,CAACkB,aAAa,CAAC,KAAK,CAAC;IACrD,IAAI,CAAC3N,gBAAgB,CAAC8M,SAAS,GAAG,oBAAoB;IACtD,IAAI,CAAC9M,gBAAgB,CAAC4N,KAAK,CAACC,OAAO,GAAG;;;;;;cAM5B,IAAI,CAAClO,UAAU;aAChB,IAAI,CAACC,UAAU;;;KAGvB;IAED6M,QAAQ,CAACjB,IAAI,CAACsC,WAAW,CAAC,IAAI,CAAC9N,gBAAgB,CAAC;EAClD;EAEA;;;EAGAsM,WAAWA,CAACjF,KAAiB;IAC3B;IACA,IAAI,CAAC,IAAI,CAAC3H,UAAU,KAAK,IAAI,CAACe,iBAAiB,IAAI,IAAI,CAACC,iBAAiB,CAAC,EAAE;MAC1E,MAAMqN,MAAM,GAAGrE,IAAI,CAACsE,GAAG,CAAC3G,KAAK,CAAC8F,OAAO,GAAG,IAAI,CAACxN,UAAU,CAAC;MACxD,MAAMsO,MAAM,GAAGvE,IAAI,CAACsE,GAAG,CAAC3G,KAAK,CAAC+F,OAAO,GAAG,IAAI,CAACxN,UAAU,CAAC;MACxD,MAAMsO,QAAQ,GAAGxE,IAAI,CAACyE,IAAI,CAACJ,MAAM,GAAGA,MAAM,GAAGE,MAAM,GAAGA,MAAM,CAAC;MAE7D;MACA,IAAIC,QAAQ,GAAG,IAAI,CAAC1N,aAAa,EAAE;QACjC,IAAI,CAACF,QAAQ,GAAG,IAAI;QAEpB,IAAI,IAAI,CAACG,iBAAiB,IAAI,IAAI,CAACE,mBAAmB,EAAE;UACtDG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmN,QAAQ,CAAC;UAC9C,IAAI,CAACb,sBAAsB,CAAChG,KAAK,EAAE,IAAI,CAAC1G,mBAAmB,CAAC;QAC9D,CAAC,MAAM,IAAI,IAAI,CAACD,iBAAiB,EAAE;UACjCI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmN,QAAQ,CAAC;UAC9C,IAAI,CAACV,2BAA2B,CAACnG,KAAK,CAAC;QACzC;QAEA;QACA,IAAI,CAAC5G,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACC,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;MACjC;IACF;IAEA;IACA,IAAI,IAAI,CAACjB,UAAU,EAAE;MACnB,IAAI,IAAI,CAACQ,YAAY,EAAE;QACrB;QACA,IAAI,CAACkO,kBAAkB,CAAC/G,KAAK,CAAC;MAChC,CAAC,MAAM;QACL;QACA,IAAI,CAACgH,uBAAuB,CAAChH,KAAK,CAAC;MACrC;IACF;EACF;EAEA;;;EAGA+G,kBAAkBA,CAAC/G,KAAiB;IAClC;IACA,MAAMiH,iBAAiB,GAAG7B,QAAQ,CAAC8B,gBAAgB,CAAClH,KAAK,CAAC8F,OAAO,EAAE9F,KAAK,CAAC+F,OAAO,CAAgB;IAEhG,IAAIkB,iBAAiB,IAAIA,iBAAiB,CAACtB,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC9E,MAAMjJ,UAAU,GAAGsK,iBAAiB,CAAChB,YAAY,CAAC,kBAAkB,CAAC;MAErE,IAAItJ,UAAU,IAAIA,UAAU,KAAK,IAAI,CAAC5D,gBAAgB,IAAI,CAAC,IAAI,CAACD,qBAAqB,CAAC2J,GAAG,CAAC9F,UAAU,CAAC,EAAE;QACrG;QACA,MAAM+B,QAAQ,GAAG,IAAI,CAACwH,gBAAgB,CAACvJ,UAAU,CAAC;QAClD,IAAI+B,QAAQ,IAAI,CAACA,QAAQ,CAAC/P,OAAO,EAAE;UACjC,IAAI,CAACmK,qBAAqB,CAAC+D,GAAG,CAACF,UAAU,CAAC;UAC1C,IAAI,CAAC5D,gBAAgB,GAAG4D,UAAU;UAElC;UACA+B,QAAQ,CAAC9P,aAAa,GAAG,IAAI;UAE7B;UACA,IAAI,CAAC0I,GAAG,CAACsJ,aAAa,EAAE;UAExBnH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiD,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC7D,qBAAqB,CAACqF,IAAI,CAAC;QAC3F;MACF;IACF;EACF;EAEA;;;EAGA6I,uBAAuBA,CAAChH,KAAiB;IACvC,IAAI,CAAC,IAAI,CAACrH,gBAAgB,EAAE;IAE5B,IAAI,CAACH,YAAY,GAAGwH,KAAK,CAAC8F,OAAO;IACjC,IAAI,CAACrN,YAAY,GAAGuH,KAAK,CAAC+F,OAAO;IAEjC;IACA,IAAI,CAACoB,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACC,6BAA6B,EAAE;EACtC;EAEA;;;EAGAD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACxO,gBAAgB,EAAE;IAE5B,MAAM0O,IAAI,GAAGhF,IAAI,CAAChI,GAAG,CAAC,IAAI,CAAC/B,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC;IACzD,MAAM8O,GAAG,GAAGjF,IAAI,CAAChI,GAAG,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC;IACxD,MAAM8O,KAAK,GAAGlF,IAAI,CAACsE,GAAG,CAAC,IAAI,CAACnO,YAAY,GAAG,IAAI,CAACF,UAAU,CAAC;IAC3D,MAAMkP,MAAM,GAAGnF,IAAI,CAACsE,GAAG,CAAC,IAAI,CAAClO,YAAY,GAAG,IAAI,CAACF,UAAU,CAAC;IAE5D,IAAI,CAACI,gBAAgB,CAAC4N,KAAK,CAACc,IAAI,GAAG,GAAGA,IAAI,IAAI;IAC9C,IAAI,CAAC1O,gBAAgB,CAAC4N,KAAK,CAACe,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC5C,IAAI,CAAC3O,gBAAgB,CAAC4N,KAAK,CAACgB,KAAK,GAAG,GAAGA,KAAK,IAAI;IAChD,IAAI,CAAC5O,gBAAgB,CAAC4N,KAAK,CAACiB,MAAM,GAAG,GAAGA,MAAM,IAAI;EACpD;EAEA;;;EAGAJ,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAClY,cAAc,EAAE;IAE1B,MAAMuY,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAE7C;IACA,IAAI,IAAI,CAACxY,cAAc,CAACC,aAAa,EAAE;MACrC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACuN,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/B,IAAIA,QAAQ,CAAC/P,OAAO,EAAE;UAEtB,MAAMgZ,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAClJ,QAAQ,CAACQ,EAAE,CAAC;UAC5D,IAAIyI,eAAe,IAAI,IAAI,CAACE,oBAAoB,CAACF,eAAe,EAAEF,aAAa,CAAC,EAAE;YAChF/I,QAAQ,CAAC9P,aAAa,GAAG,IAAI;UAC/B,CAAC,MAAM;YACL8P,QAAQ,CAAC9P,aAAa,GAAG,KAAK;UAChC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACM,cAAc,CAACc,aAAa,EAAE;MACrC,IAAI,CAACd,cAAc,CAACc,aAAa,CAAC0M,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/B,IAAIA,QAAQ,CAAC/P,OAAO,EAAE;UAEtB,MAAMgZ,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAClJ,QAAQ,CAACQ,EAAE,CAAC;UAC5D,IAAIyI,eAAe,IAAI,IAAI,CAACE,oBAAoB,CAACF,eAAe,EAAEF,aAAa,CAAC,EAAE;YAChF/I,QAAQ,CAAC9P,aAAa,GAAG,IAAI;UAC/B,CAAC,MAAM;YACL8P,QAAQ,CAAC9P,aAAa,GAAG,KAAK;UAChC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGA8Y,gBAAgBA,CAAA;IACd,OAAO;MACLL,IAAI,EAAEhF,IAAI,CAAChI,GAAG,CAAC,IAAI,CAAC/B,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC;MAClD8O,GAAG,EAAEjF,IAAI,CAAChI,GAAG,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC;MACjDqP,KAAK,EAAEzF,IAAI,CAAC/H,GAAG,CAAC,IAAI,CAAChC,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC;MACnDuP,MAAM,EAAE1F,IAAI,CAAC/H,GAAG,CAAC,IAAI,CAAC/B,UAAU,EAAE,IAAI,CAACE,YAAY;KACpD;EACH;EAEA;;;EAGAmP,kBAAkBA,CAACjL,UAAkB;IACnC,OAAOyI,QAAQ,CAAC4C,aAAa,CAAC,sBAAsBrL,UAAU,IAAI,CAAgB;EACpF;EAEA;;;EAGAkL,oBAAoBA,CAACI,OAAoB,EAAER,aAAkB;IAC3D,MAAMS,WAAW,GAAGD,OAAO,CAACE,qBAAqB,EAAE;IAEnD,OAAO,EAAED,WAAW,CAACJ,KAAK,GAAGL,aAAa,CAACJ,IAAI,IACtCa,WAAW,CAACb,IAAI,GAAGI,aAAa,CAACK,KAAK,IACtCI,WAAW,CAACH,MAAM,GAAGN,aAAa,CAACH,GAAG,IACtCY,WAAW,CAACZ,GAAG,GAAGG,aAAa,CAACM,MAAM,CAAC;EAClD;EAEA;;;EAGA5C,SAASA,CAACnF,KAAiB;IACzBvG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BrB,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BQ,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BO,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBD,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF;IACA,IAAI,IAAI,CAACX,UAAU,EAAE;MACnB;MACA,MAAM+P,YAAY,GAAG9H,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACvH,aAAa;MACpDS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0O,YAAY,EAAE,IAAI,CAAC;MAEjD;MACA,IAAI,IAAI,CAACvP,YAAY,EAAE;QACrB;QACA,IAAI,CAACwP,yBAAyB,EAAE;MAClC,CAAC,MAAM;QACL;QACA,IAAI,CAACC,8BAA8B,EAAE;MACvC;IACF;IAEA;IACA,IAAI,CAACjQ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACQ,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,qBAAqB,CAACsD,KAAK,EAAE;IAClC,IAAI,CAACrD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACK,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACD,aAAa,GAAG,CAAC;IAEtB;IACA,IAAI,CAACuP,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAAC5P,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC0N,MAAM,EAAE;MAC9B,IAAI,CAAC1N,gBAAgB,GAAG,IAAI;IAC9B;EACF;EAEA;;;EAGA0P,yBAAyBA,CAAA;IACvB5O,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACZ,qBAAqB,CAACqF,IAAI,CAAC;IAEzE;IACA,IAAI,CAACoK,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACzP,qBAAqB,CAAC4D,OAAO,CAACC,UAAU,IAAG;MAC9C,MAAM+B,QAAQ,GAAG,IAAI,CAACwH,gBAAgB,CAACvJ,UAAU,CAAC;MAClD,IAAI+B,QAAQ,IAAI,CAACA,QAAQ,CAAC/P,OAAO,EAAE;QACjC;QACA,IAAI,CAAC+P,QAAQ,CAAChQ,QAAQ,EAAE;UACtBgQ,QAAQ,CAAChQ,QAAQ,GAAG,IAAI;UACxB,IAAI,CAAC,IAAI,CAACmJ,iBAAiB,CAACoE,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC,EAAE;YAC3D,IAAI,CAACrH,iBAAiB,CAACwH,IAAI,CAACX,QAAQ,CAAC;UACvC;QACF;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACpH,GAAG,CAACsJ,aAAa,EAAE;IAExBnH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC7B,iBAAiB,CAACmG,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CAAC;EAChF;EAEA;;;EAGAoJ,8BAA8BA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACpZ,cAAc,EAAE;IAE1B;IACA,IAAI,CAAC,IAAI,CAAC0J,cAAc,EAAE;MACxB,IAAI,CAAC4P,kBAAkB,EAAE;IAC3B;IAEA;IACA,IAAI,CAACC,8BAA8B,EAAE;IAErC;IACA,IAAI,CAACF,kBAAkB,EAAE;EAC3B;EAEA;;;EAGAC,kBAAkBA,CAAA;IAChB,IAAI,CAAC3Q,iBAAiB,GAAG,EAAE;IAE3B,IAAI,IAAI,CAAC3I,cAAc,CAACC,aAAa,EAAE;MACrC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACuN,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/BA,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAI,IAAI,CAACQ,cAAc,CAACc,aAAa,EAAE;MACrC,IAAI,CAACd,cAAc,CAACc,aAAa,CAAC0M,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/BA,QAAQ,CAAChQ,QAAQ,GAAG,KAAK;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGA+Z,8BAA8BA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACvZ,cAAc,EAAE;IAE1B;IACA,IAAI,IAAI,CAACA,cAAc,CAACC,aAAa,EAAE;MACrC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACuN,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/B,IAAIA,QAAQ,CAAC9P,aAAa,IAAI,CAAC8P,QAAQ,CAAC/P,OAAO,EAAE;YAC/C+P,QAAQ,CAAChQ,QAAQ,GAAG,IAAI;YACxB,IAAI,CAAC,IAAI,CAACmJ,iBAAiB,CAACoE,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC,EAAE;cAC3D,IAAI,CAACrH,iBAAiB,CAACwH,IAAI,CAACX,QAAQ,CAAC;YACvC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACxP,cAAc,CAACc,aAAa,EAAE;MACrC,IAAI,CAACd,cAAc,CAACc,aAAa,CAAC0M,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/B,IAAIA,QAAQ,CAAC9P,aAAa,IAAI,CAAC8P,QAAQ,CAAC/P,OAAO,EAAE;YAC/C+P,QAAQ,CAAChQ,QAAQ,GAAG,IAAI;YACxB,IAAI,CAAC,IAAI,CAACmJ,iBAAiB,CAACoE,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC,EAAE;cAC3D,IAAI,CAACrH,iBAAiB,CAACwH,IAAI,CAACX,QAAQ,CAAC;YACvC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAjF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC7B,iBAAiB,CAAC;EAC/D;EAEA;;;EAGA0Q,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrZ,cAAc,EAAE;IAE1B,IAAI,IAAI,CAACA,cAAc,CAACC,aAAa,EAAE;MACrC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACuN,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/BA,QAAQ,CAAC9P,aAAa,GAAG,KAAK;QAChC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAI,IAAI,CAACM,cAAc,CAACc,aAAa,EAAE;MACrC,IAAI,CAACd,cAAc,CAACc,aAAa,CAAC0M,OAAO,CAAEsC,aAAoB,IAAI;QACjEA,aAAa,CAACtC,OAAO,CAACgC,QAAQ,IAAG;UAC/BA,QAAQ,CAAC9P,aAAa,GAAG,KAAK;QAChC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGA8Z,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC9D,eAAe,EAAE;MACxBQ,QAAQ,CAACuD,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC/D,eAAe,CAAC;IAC/D;IACA,IAAI,IAAI,CAACG,aAAa,EAAE;MACtBK,QAAQ,CAACuD,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC5D,aAAa,CAAC;IAC3D;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BI,QAAQ,CAACuD,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC3D,iBAAiB,CAAC;IACnE;IACA,IAAI,IAAI,CAACE,eAAe,EAAE;MACxBE,QAAQ,CAACuD,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACzD,eAAe,CAAC;IAC/D;IAEA;IACA,IAAI,IAAI,CAACvM,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC0N,MAAM,EAAE;MAC9B,IAAI,CAAC1N,gBAAgB,GAAG,IAAI;IAC9B;EACF;EAEA;;;EAGAuN,gBAAgBA,CAACvJ,UAAkB;IACjC,IAAI,CAAC,IAAI,CAACzN,cAAc,EAAE,OAAO,IAAI;IAErC;IACA,IAAI,IAAI,CAACA,cAAc,CAACC,aAAa,EAAE;MACrC,KAAK,MAAM6P,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACC,aAAa,EAAE;QAC7D,MAAMuP,QAAQ,GAAGM,aAAa,CAAC/C,IAAI,CAAEgD,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKvC,UAAU,CAAC;QACpE,IAAI+B,QAAQ,EAAE,OAAOA,QAAQ;MAC/B;IACF;IAEA;IACA,IAAI,IAAI,CAACxP,cAAc,CAACc,aAAa,EAAE;MACrC,KAAK,MAAMgP,aAAa,IAAI,IAAI,CAAC9P,cAAc,CAACc,aAAa,EAAE;QAC7D,MAAM0O,QAAQ,GAAGM,aAAa,CAAC/C,IAAI,CAAEgD,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKvC,UAAU,CAAC;QACpE,IAAI+B,QAAQ,EAAE,OAAOA,QAAQ;MAC/B;IACF;IAEA,OAAO,IAAI;EACb;EAEA;;;EAGAkK,kBAAkBA,CAACjM,UAAkB;IACnC,MAAM+B,QAAQ,GAAG,IAAI,CAACwH,gBAAgB,CAACvJ,UAAU,CAAC;IAClD,IAAI+B,QAAQ,IAAI,CAACA,QAAQ,CAAC/P,OAAO,EAAE;MACjC;MACA,IAAI,CAAC+P,QAAQ,CAAChQ,QAAQ,EAAE;QACtBgQ,QAAQ,CAAChQ,QAAQ,GAAG,IAAI;QACxB,IAAI,CAAC,IAAI,CAACmJ,iBAAiB,CAACoE,IAAI,CAACgD,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,QAAQ,CAACQ,EAAE,CAAC,EAAE;UAC3D,IAAI,CAACrH,iBAAiB,CAACwH,IAAI,CAACX,QAAQ,CAAC;QACvC;QAEA;QACA,IAAI,CAACpH,GAAG,CAACsJ,aAAa,EAAE;MAC1B;IACF;EACF;;;uBAzzDW5J,kBAAkB,EAAAjK,EAAA,CAAA8b,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAhc,EAAA,CAAA8b,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlc,EAAA,CAAA8b,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAApc,EAAA,CAAA8b,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAtc,EAAA,CAAA8b,iBAAA,CAAA9b,EAAA,CAAAuc,iBAAA;IAAA;EAAA;;;YAAlBtS,kBAAkB;MAAAuS,SAAA;MAAAC,QAAA,GAAAzc,EAAA,CAAA0c,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX3Bhd,EAFJ,CAAAK,cAAA,iBAAsE,aAC5C,SAClB;UAAAL,EAAA,CAAAM,MAAA,2CAAM;UAEdN,EAFc,CAAAO,YAAA,EAAK,EACX,EACE;UASFP,EANR,CAAAK,cAAA,iBAAsE,aAE5D,gBACc,aACU,gBAEyC;UAA5BL,EAAA,CAAAW,UAAA,mBAAAuc,oDAAA;YAAA,OAASD,GAAA,CAAAjJ,eAAA,EAAiB;UAAA,EAAC;UAChEhU,EAAA,CAAAM,MAAA,2BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAoE;UAA7BL,EAAA,CAAAW,UAAA,mBAAAwc,qDAAA;YAAA,OAASF,GAAA,CAAA5F,gBAAA,EAAkB;UAAA,EAAC;UACjErX,EAAA,CAAAM,MAAA,4BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAA6D;UAAtBL,EAAA,CAAAW,UAAA,mBAAAyc,qDAAA;YAAA,OAASH,GAAA,CAAA5G,SAAA,EAAW;UAAA,EAAC;UAC1DrW,EAAA,CAAAM,MAAA,4BACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAA4E;UAArCL,EAAA,CAAAW,UAAA,mBAAA0c,qDAAA;YAAA,OAASJ,GAAA,CAAA9L,wBAAA,EAA0B;UAAA,EAAC;UACzEnR,EAAA,CAAAM,MAAA,kCACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAmE;UAA5BL,EAAA,CAAAW,UAAA,mBAAA2c,qDAAA;YAAA,OAASL,GAAA,CAAA/F,eAAA,EAAiB;UAAA,EAAC;UAChElX,EAAA,CAAAM,MAAA,kCACF;UAAAN,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,iBAAgF;UAAzCL,EAAA,CAAAW,UAAA,mBAAA4c,qDAAA;YAAA,OAASN,GAAA,CAAAtF,MAAA,EAAQ;UAAA,EAAC;UACvD3X,EAAA,CAAAM,MAAA,sBACF;UAIRN,EAJQ,CAAAO,YAAA,EAAS,EACL,EACC,EACF,EACD;UAQAP,EALV,CAAAK,cAAA,kBAAyE,cAC/D,iBACc,cACkB,cACR,gBACS;UAAAL,EAAA,CAAAM,MAAA,iCAAK;UAAAN,EAAA,CAAAO,YAAA,EAAO;UAC7CP,EAAA,CAAAK,cAAA,qBAAqG;UAA1FL,EAAA,CAAAkJ,gBAAA,2BAAAsU,gEAAA3c,MAAA;YAAAb,EAAA,CAAAqJ,kBAAA,CAAA4T,GAAA,CAAAtS,aAAA,EAAA9J,MAAA,MAAAoc,GAAA,CAAAtS,aAAA,GAAA9J,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAACb,EAAA,CAAAW,UAAA,2BAAA6c,gEAAA;YAAA,OAAiBP,GAAA,CAAApO,WAAA,EAAa;UAAA,EAAC;UACpE7O,EAAA,CAAA+B,UAAA,KAAA0b,wCAAA,wBAAmF;UAM/Fzd,EALU,CAAAO,YAAA,EAAY,EACR,EACF,EACC,EACF,EACD;UAIRP,EADF,CAAAK,cAAA,mBAAgE,eACO;UAAlCL,EAAA,CAAAW,UAAA,uBAAA+c,sDAAA7c,MAAA;YAAA,OAAaoc,GAAA,CAAA1E,WAAA,CAAA1X,MAAA,CAAmB;UAAA,EAAC;UAClEb,EAAA,CAAA+B,UAAA,KAAA4b,kCAAA,kBAAgD;UAkEpD3d,EADE,CAAAO,YAAA,EAAM,EACE;UAGVP,EAAA,CAAAK,cAAA,oBACmF;UADzEL,EAAA,CAAAkJ,gBAAA,6BAAA0U,iEAAA/c,MAAA;YAAAb,EAAA,CAAAqJ,kBAAA,CAAA4T,GAAA,CAAAlS,oBAAA,EAAAlK,MAAA,MAAAoc,GAAA,CAAAlS,oBAAA,GAAAlK,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UACpCb,EADmD,CAAAW,UAAA,wBAAAkd,4DAAA;YAAA,OAAcZ,GAAA,CAAA9I,YAAA,EAAc;UAAA,EAAC,oBAAA2J,wDAAA;YAAA,OACtEb,GAAA,CAAAtH,UAAA,EAAY;UAAA,EAAC;UAC/B3V,EAAA,CAAA+B,UAAA,KAAAgc,2CAAA,6BAA8B;UAmOhC/d,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAK,cAAA,oBACqF;UAD3EL,EAAA,CAAAkJ,gBAAA,6BAAA8U,iEAAAnd,MAAA;YAAAb,EAAA,CAAAqJ,kBAAA,CAAA4T,GAAA,CAAAjS,uBAAA,EAAAnK,MAAA,MAAAoc,GAAA,CAAAjS,uBAAA,GAAAnK,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAuC;UACvCb,EADsD,CAAAW,UAAA,wBAAAsd,4DAAA;YAAA,OAAAhB,GAAA,CAAAjS,uBAAA,GAAwC,KAAK;UAAA,EAAC,oBAAAkT,wDAAA;YAAA,OAC1FjB,GAAA,CAAArG,aAAA,EAAe;UAAA,EAAC;UAClC5W,EAAA,CAAA+B,UAAA,KAAAoc,2CAAA,2BAA8B;UAYhCne,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAK,cAAA,oBACmF;UADzEL,EAAA,CAAAkJ,gBAAA,6BAAAkV,iEAAAvd,MAAA;YAAAb,EAAA,CAAAqJ,kBAAA,CAAA4T,GAAA,CAAAhS,qBAAA,EAAApK,MAAA,MAAAoc,GAAA,CAAAhS,qBAAA,GAAApK,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UACrCb,EADoD,CAAAW,UAAA,wBAAA0d,4DAAA;YAAA,OAAApB,GAAA,CAAAhS,qBAAA,GAAsC,KAAK;UAAA,EAAC,oBAAAqT,wDAAA;YAAA,OACtFrB,GAAA,CAAA3F,WAAA,EAAa;UAAA,EAAC;UAChCtX,EAAA,CAAA+B,UAAA,KAAAwc,2CAAA,4BAA8B;UAmBhCve,EAAA,CAAAO,YAAA,EAAW;UAGXP,EAAA,CAAAK,cAAA,oBAC8J;UADpJL,EAAA,CAAAkJ,gBAAA,6BAAAsV,iEAAA3d,MAAA;YAAAb,EAAA,CAAAqJ,kBAAA,CAAA4T,GAAA,CAAA/R,6BAAA,EAAArK,MAAA,MAAAoc,GAAA,CAAA/R,6BAAA,GAAArK,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6C;UAC7Cb,EAD6D,CAAAW,UAAA,wBAAA8d,4DAAA;YAAA,OAAcxB,GAAA,CAAA3L,qBAAA,EAAuB;UAAA,EAAC,oBAAAoN,wDAAA;YAAA,OACzFzB,GAAA,CAAAzK,wBAAA,EAA0B;UAAA,EAAC;UAC7CxS,EAAA,CAAA+B,UAAA,KAAA4c,2CAAA,4BAA8B;UA4EhC3e,EAAA,CAAAO,YAAA,EAAW;;;UAreiBP,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAAkH,eAAA,KAAA0X,GAAA,EAAyC;UAOpC5e,EAAA,CAAAQ,SAAA,GAAoC;UAApCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAAkH,eAAA,KAAA2X,GAAA,EAAoC;UAM3C7e,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UAKpBF,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,qBAAoB;UASfF,EAAA,CAAAQ,SAAA,GAAyC;UAAzCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAAkH,eAAA,KAAA4X,GAAA,EAAyC;UAMnD9e,EAAA,CAAAQ,SAAA,GAA2B;UAA3BR,EAAA,CAAAgK,gBAAA,YAAAiT,GAAA,CAAAtS,aAAA,CAA2B;UACT3K,EAAA,CAAAQ,SAAA,EAAU;UAAVR,EAAA,CAAAE,UAAA,YAAA+c,GAAA,CAAArS,OAAA,CAAU;UASnB5K,EAAA,CAAAQ,SAAA,EAAiC;UAAjCR,EAAA,CAAAE,UAAA,gBAAAF,EAAA,CAAAkH,eAAA,KAAA6X,GAAA,EAAiC;UAEjC/e,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAE,UAAA,SAAA+c,GAAA,CAAA9a,cAAA,CAAoB;UAqExCnC,EAAA,CAAAQ,SAAA,EAAoC;UAApCR,EAAA,CAAAgK,gBAAA,cAAAiT,GAAA,CAAAlS,oBAAA,CAAoC;UACoB/K,EAAA,CAAAE,UAAA,iBAAgB;UAuOxEF,EAAA,CAAAQ,SAAA,GAAuC;UAAvCR,EAAA,CAAAgK,gBAAA,cAAAiT,GAAA,CAAAjS,uBAAA,CAAuC;UACoBhL,EAAA,CAAAE,UAAA,gBAAe;UAgB1EF,EAAA,CAAAQ,SAAA,GAAqC;UAArCR,EAAA,CAAAgK,gBAAA,cAAAiT,GAAA,CAAAhS,qBAAA,CAAqC;UACoBjL,EAAA,CAAAE,UAAA,gBAAe;UAuBxEF,EAAA,CAAAQ,SAAA,GAA6C;UAA7CR,EAAA,CAAAgK,gBAAA,cAAAiT,GAAA,CAAA/R,6BAAA,CAA6C;UAC6ClL,EAAhB,CAAAE,UAAA,gBAAe,gBAAAF,EAAA,CAAAkH,eAAA,KAAA8X,GAAA,EAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}