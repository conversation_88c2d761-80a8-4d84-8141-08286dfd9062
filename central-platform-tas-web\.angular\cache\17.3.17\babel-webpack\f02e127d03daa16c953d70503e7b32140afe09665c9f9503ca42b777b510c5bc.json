{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { FepconfigComponent } from './fepconfig.component';\nimport { FepconfigRoutingModule } from './fepconfig-routing.module';\nimport { FepconfigEditComponent } from '@business/tas/fepconfig/fepconfig-edit/fepconfig-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [FepconfigComponent, FepconfigEditComponent];\nexport class FepconfigModule {\n  static {\n    this.ɵfac = function FepconfigModule_Factory(t) {\n      return new (t || FepconfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: FepconfigModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, FepconfigRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FepconfigModule, {\n    declarations: [FepconfigComponent, FepconfigEditComponent],\n    imports: [SharedModule, FepconfigRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "FepconfigComponent", "FepconfigRoutingModule", "FepconfigEditComponent", "COMPONENTS", "FepconfigModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\fepconfig\\fepconfig.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { FepconfigComponent } from './fepconfig.component';\r\nimport { FepconfigRoutingModule } from './fepconfig-routing.module';\r\nimport { FepconfigEditComponent } from '@business/tas/fepconfig/fepconfig-edit/fepconfig-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  FepconfigComponent,\r\n  FepconfigEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, FepconfigRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class FepconfigModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,sBAAsB,QAAQ,iEAAiE;;AAExG,MAAMC,UAAU,GAAG,CACjBH,kBAAkB,EAClBE,sBAAsB,CACvB;AAMD,OAAM,MAAOE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAHhBN,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;IAAA;EAAA;;;2EAGjDK,eAAe;IAAAC,YAAA,GAR1BL,kBAAkB,EAClBE,sBAAsB;IAAAI,OAAA,GAIZR,YAAY,EAAEG,sBAAsB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}