{"ast": null, "code": "import { SharedModule } from '@core/share.module';\nimport { LayoutModule } from '@layout/layout.module';\nimport { ColorComponent } from './color.component';\nimport { ColorRoutingModule } from './color-routing.module';\nimport { ColorEditComponent } from '@business/tas/color/color-edit/color-edit.component';\nimport * as i0 from \"@angular/core\";\nconst COMPONENTS = [ColorComponent, ColorEditComponent];\nexport class ColorModule {\n  static {\n    this.ɵfac = function ColorModule_Factory(t) {\n      return new (t || ColorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ColorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, ColorRoutingModule, LayoutModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ColorModule, {\n    declarations: [ColorComponent, ColorEditComponent],\n    imports: [SharedModule, ColorRoutingModule, LayoutModule]\n  });\n})();", "map": {"version": 3, "names": ["SharedModule", "LayoutModule", "ColorComponent", "ColorRoutingModule", "ColorEditComponent", "COMPONENTS", "ColorModule", "declarations", "imports"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\color\\color.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { SharedModule } from '@core/share.module';\r\nimport { LayoutModule } from '@layout/layout.module';\r\nimport { ColorComponent } from './color.component';\r\nimport { ColorRoutingModule } from './color-routing.module';\r\nimport {ColorEditComponent} from '@business/tas/color/color-edit/color-edit.component';\r\n\r\nconst COMPONENTS = [\r\n  ColorComponent,\r\n  ColorEditComponent\r\n];\r\n\r\n@NgModule({\r\n  imports: [SharedModule, ColorRoutingModule, LayoutModule],\r\n  declarations: [...COMPONENTS]\r\n})\r\nexport class ColorModule {\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAAQC,kBAAkB,QAAO,qDAAqD;;AAEtF,MAAMC,UAAU,GAAG,CACjBH,cAAc,EACdE,kBAAkB,CACnB;AAMD,OAAM,MAAOE,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZN,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;IAAA;EAAA;;;2EAG7CK,WAAW;IAAAC,YAAA,GARtBL,cAAc,EACdE,kBAAkB;IAAAI,OAAA,GAIRR,YAAY,EAAEG,kBAAkB,EAAEF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}