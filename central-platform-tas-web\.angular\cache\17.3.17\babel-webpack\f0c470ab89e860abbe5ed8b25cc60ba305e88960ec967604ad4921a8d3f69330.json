{"ast": null, "code": "/* eslint-disable class-methods-use-this */\nconst {\n  BigInteger\n} = require('jsbn');\nfunction bigintToValue(bigint) {\n  let h = bigint.toString(16);\n  if (h[0] !== '-') {\n    // 正数\n    if (h.length % 2 === 1) h = '0' + h; // 补齐到整字节\n    else if (!h.match(/^[0-7]/)) h = '00' + h; // 非0开头，则补一个全0字节\n  } else {\n    // 负数\n    h = h.substr(1);\n    let len = h.length;\n    if (len % 2 === 1) len += 1; // 补齐到整字节\n    else if (!h.match(/^[0-7]/)) len += 2; // 非0开头，则补一个全0字节\n\n    let mask = '';\n    for (let i = 0; i < len; i++) mask += 'f';\n    mask = new BigInteger(mask, 16);\n\n    // 对绝对值取反，加1\n    h = mask.xor(bigint).add(BigInteger.ONE);\n    h = h.toString(16).replace(/^-/, '');\n  }\n  return h;\n}\nclass ASN1Object {\n  constructor() {\n    this.tlv = null;\n    this.t = '00';\n    this.l = '00';\n    this.v = '';\n  }\n\n  /**\r\n   * 获取 der 编码比特流16进制串\r\n   */\n  getEncodedHex() {\n    if (!this.tlv) {\n      this.v = this.getValue();\n      this.l = this.getLength();\n      this.tlv = this.t + this.l + this.v;\n    }\n    return this.tlv;\n  }\n  getLength() {\n    const n = this.v.length / 2; // 字节数\n    let nHex = n.toString(16);\n    if (nHex.length % 2 === 1) nHex = '0' + nHex; // 补齐到整字节\n\n    if (n < 128) {\n      // 短格式，以 0 开头\n      return nHex;\n    } else {\n      // 长格式，以 1 开头\n      const head = 128 + nHex.length / 2; // 1(1位) + 真正的长度占用字节数(7位) + 真正的长度\n      return head.toString(16) + nHex;\n    }\n  }\n  getValue() {\n    return '';\n  }\n}\nclass DERInteger extends ASN1Object {\n  constructor(bigint) {\n    super();\n    this.t = '02'; // 整型标签说明\n    if (bigint) this.v = bigintToValue(bigint);\n  }\n  getValue() {\n    return this.v;\n  }\n}\nclass DERSequence extends ASN1Object {\n  constructor(asn1Array) {\n    super();\n    this.t = '30'; // 序列标签说明\n    this.asn1Array = asn1Array;\n  }\n  getValue() {\n    this.v = this.asn1Array.map(asn1Object => asn1Object.getEncodedHex()).join('');\n    return this.v;\n  }\n}\n\n/**\r\n * 获取 l 占用字节数\r\n */\nfunction getLenOfL(str, start) {\n  if (+str[start + 2] < 8) return 1; // l 以0开头，则表示短格式，只占一个字节\n  return +str.substr(start + 2, 2) & 0x7f + 1; // 长格式，取第一个字节后7位作为长度真正占用字节数，再加上本身\n}\n\n/**\r\n * 获取 l\r\n */\nfunction getL(str, start) {\n  // 获取 l\n  const len = getLenOfL(str, start);\n  const l = str.substr(start + 2, len * 2);\n  if (!l) return -1;\n  const bigint = +l[0] < 8 ? new BigInteger(l, 16) : new BigInteger(l.substr(2), 16);\n  return bigint.intValue();\n}\n\n/**\r\n * 获取 v 的位置\r\n */\nfunction getStartOfV(str, start) {\n  const len = getLenOfL(str, start);\n  return start + (len + 1) * 2;\n}\nmodule.exports = {\n  /**\r\n   * ASN.1 der 编码，针对 sm2 签名\r\n   */\n  encodeDer(r, s) {\n    const derR = new DERInteger(r);\n    const derS = new DERInteger(s);\n    const derSeq = new DERSequence([derR, derS]);\n    return derSeq.getEncodedHex();\n  },\n  /**\r\n   * 解析 ASN.1 der，针对 sm2 验签\r\n   */\n  decodeDer(input) {\n    // 结构：\n    // input = | tSeq | lSeq | vSeq |\n    // vSeq = | tR | lR | vR | tS | lS | vS |\n    const start = getStartOfV(input, 0);\n    const vIndexR = getStartOfV(input, start);\n    const lR = getL(input, start);\n    const vR = input.substr(vIndexR, lR * 2);\n    const nextStart = vIndexR + vR.length;\n    const vIndexS = getStartOfV(input, nextStart);\n    const lS = getL(input, nextStart);\n    const vS = input.substr(vIndexS, lS * 2);\n    const r = new BigInteger(vR, 16);\n    const s = new BigInteger(vS, 16);\n    return {\n      r,\n      s\n    };\n  }\n};", "map": {"version": 3, "names": ["BigInteger", "require", "bigintToValue", "bigint", "h", "toString", "length", "match", "substr", "len", "mask", "i", "xor", "add", "ONE", "replace", "ASN1Object", "constructor", "tlv", "t", "l", "v", "getEncodedHex", "getValue", "<PERSON><PERSON><PERSON><PERSON>", "n", "nHex", "head", "DERInteger", "DERSequence", "asn1Array", "map", "asn1Object", "join", "getLenOfL", "str", "start", "getL", "intValue", "getStartOfV", "module", "exports", "encodeDer", "r", "s", "derR", "derS", "derSeq", "decodeDer", "input", "vIndexR", "lR", "vR", "nextStart", "vIndexS", "lS", "vS"], "sources": ["G:/web/central-platform-tas-web/node_modules/sm-crypto/src/sm2/asn1.js"], "sourcesContent": ["/* eslint-disable class-methods-use-this */\r\nconst {BigInteger} = require('jsbn')\r\n\r\nfunction bigintToValue(bigint) {\r\n  let h = bigint.toString(16)\r\n  if (h[0] !== '-') {\r\n    // 正数\r\n    if (h.length % 2 === 1) h = '0' + h // 补齐到整字节\r\n    else if (!h.match(/^[0-7]/)) h = '00' + h // 非0开头，则补一个全0字节\r\n  } else {\r\n    // 负数\r\n    h = h.substr(1)\r\n\r\n    let len = h.length\r\n    if (len % 2 === 1) len += 1 // 补齐到整字节\r\n    else if (!h.match(/^[0-7]/)) len += 2 // 非0开头，则补一个全0字节\r\n\r\n    let mask = ''\r\n    for (let i = 0; i < len; i++) mask += 'f'\r\n    mask = new BigInteger(mask, 16)\r\n\r\n    // 对绝对值取反，加1\r\n    h = mask.xor(bigint).add(BigInteger.ONE)\r\n    h = h.toString(16).replace(/^-/, '')\r\n  }\r\n  return h\r\n}\r\n\r\nclass ASN1Object {\r\n  constructor() {\r\n    this.tlv = null\r\n    this.t = '00'\r\n    this.l = '00'\r\n    this.v = ''\r\n  }\r\n\r\n  /**\r\n   * 获取 der 编码比特流16进制串\r\n   */\r\n  getEncodedHex() {\r\n    if (!this.tlv) {\r\n      this.v = this.getValue()\r\n      this.l = this.getLength()\r\n      this.tlv = this.t + this.l + this.v\r\n    }\r\n    return this.tlv\r\n  }\r\n\r\n  getLength() {\r\n    const n = this.v.length / 2 // 字节数\r\n    let nHex = n.toString(16)\r\n    if (nHex.length % 2 === 1) nHex = '0' + nHex // 补齐到整字节\r\n\r\n    if (n < 128) {\r\n      // 短格式，以 0 开头\r\n      return nHex\r\n    } else {\r\n      // 长格式，以 1 开头\r\n      const head = 128 + nHex.length / 2 // 1(1位) + 真正的长度占用字节数(7位) + 真正的长度\r\n      return head.toString(16) + nHex\r\n    }\r\n  }\r\n\r\n  getValue() {\r\n    return ''\r\n  }\r\n}\r\n\r\nclass DERInteger extends ASN1Object {\r\n  constructor(bigint) {\r\n    super()\r\n\r\n    this.t = '02' // 整型标签说明\r\n    if (bigint) this.v = bigintToValue(bigint)\r\n  }\r\n\r\n  getValue() {\r\n    return this.v\r\n  }\r\n}\r\n\r\nclass DERSequence extends ASN1Object {\r\n  constructor(asn1Array) {\r\n    super()\r\n\r\n    this.t = '30' // 序列标签说明\r\n    this.asn1Array = asn1Array\r\n  }\r\n\r\n  getValue() {\r\n    this.v = this.asn1Array.map(asn1Object => asn1Object.getEncodedHex()).join('')\r\n    return this.v\r\n  }\r\n}\r\n\r\n/**\r\n * 获取 l 占用字节数\r\n */\r\nfunction getLenOfL(str, start) {\r\n  if (+str[start + 2] < 8) return 1 // l 以0开头，则表示短格式，只占一个字节\r\n  return +str.substr(start + 2, 2) & 0x7f + 1 // 长格式，取第一个字节后7位作为长度真正占用字节数，再加上本身\r\n}\r\n\r\n/**\r\n * 获取 l\r\n */\r\nfunction getL(str, start) {\r\n  // 获取 l\r\n  const len = getLenOfL(str, start)\r\n  const l = str.substr(start + 2, len * 2)\r\n\r\n  if (!l) return -1\r\n  const bigint = +l[0] < 8 ? new BigInteger(l, 16) : new BigInteger(l.substr(2), 16)\r\n\r\n  return bigint.intValue()\r\n}\r\n\r\n/**\r\n * 获取 v 的位置\r\n */\r\nfunction getStartOfV(str, start) {\r\n  const len = getLenOfL(str, start)\r\n  return start + (len + 1) * 2\r\n}\r\n\r\nmodule.exports = {\r\n  /**\r\n   * ASN.1 der 编码，针对 sm2 签名\r\n   */\r\n  encodeDer(r, s) {\r\n    const derR = new DERInteger(r)\r\n    const derS = new DERInteger(s)\r\n    const derSeq = new DERSequence([derR, derS])\r\n\r\n    return derSeq.getEncodedHex()\r\n  },\r\n\r\n  /**\r\n   * 解析 ASN.1 der，针对 sm2 验签\r\n   */\r\n  decodeDer(input) {\r\n    // 结构：\r\n    // input = | tSeq | lSeq | vSeq |\r\n    // vSeq = | tR | lR | vR | tS | lS | vS |\r\n    const start = getStartOfV(input, 0)\r\n\r\n    const vIndexR = getStartOfV(input, start)\r\n    const lR = getL(input, start)\r\n    const vR = input.substr(vIndexR, lR * 2)\r\n\r\n    const nextStart = vIndexR + vR.length\r\n    const vIndexS = getStartOfV(input, nextStart)\r\n    const lS = getL(input, nextStart)\r\n    const vS = input.substr(vIndexS, lS * 2)\r\n\r\n    const r = new BigInteger(vR, 16)\r\n    const s = new BigInteger(vS, 16)\r\n\r\n    return {r, s}\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA,MAAM;EAACA;AAAU,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AAEpC,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC7B,IAAIC,CAAC,GAAGD,MAAM,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC3B,IAAID,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAChB;IACA,IAAIA,CAAC,CAACE,MAAM,GAAG,CAAC,KAAK,CAAC,EAAEF,CAAC,GAAG,GAAG,GAAGA,CAAC,EAAC;IAAA,KAC/B,IAAI,CAACA,CAAC,CAACG,KAAK,CAAC,QAAQ,CAAC,EAAEH,CAAC,GAAG,IAAI,GAAGA,CAAC,EAAC;EAC5C,CAAC,MAAM;IACL;IACAA,CAAC,GAAGA,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC;IAEf,IAAIC,GAAG,GAAGL,CAAC,CAACE,MAAM;IAClB,IAAIG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAC;IAAA,KACvB,IAAI,CAACL,CAAC,CAACG,KAAK,CAAC,QAAQ,CAAC,EAAEE,GAAG,IAAI,CAAC,EAAC;;IAEtC,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAED,IAAI,IAAI,GAAG;IACzCA,IAAI,GAAG,IAAIV,UAAU,CAACU,IAAI,EAAE,EAAE,CAAC;;IAE/B;IACAN,CAAC,GAAGM,IAAI,CAACE,GAAG,CAACT,MAAM,CAAC,CAACU,GAAG,CAACb,UAAU,CAACc,GAAG,CAAC;IACxCV,CAAC,GAAGA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACU,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EACtC;EACA,OAAOX,CAAC;AACV;AAEA,MAAMY,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,CAAC,GAAG,EAAE;EACb;;EAEA;AACF;AACA;EACEC,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACJ,GAAG,EAAE;MACb,IAAI,CAACG,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACH,CAAC,GAAG,IAAI,CAACI,SAAS,CAAC,CAAC;MACzB,IAAI,CAACN,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,CAAC;IACrC;IACA,OAAO,IAAI,CAACH,GAAG;EACjB;EAEAM,SAASA,CAAA,EAAG;IACV,MAAMC,CAAC,GAAG,IAAI,CAACJ,CAAC,CAACf,MAAM,GAAG,CAAC,EAAC;IAC5B,IAAIoB,IAAI,GAAGD,CAAC,CAACpB,QAAQ,CAAC,EAAE,CAAC;IACzB,IAAIqB,IAAI,CAACpB,MAAM,GAAG,CAAC,KAAK,CAAC,EAAEoB,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAC;;IAE7C,IAAID,CAAC,GAAG,GAAG,EAAE;MACX;MACA,OAAOC,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMC,IAAI,GAAG,GAAG,GAAGD,IAAI,CAACpB,MAAM,GAAG,CAAC,EAAC;MACnC,OAAOqB,IAAI,CAACtB,QAAQ,CAAC,EAAE,CAAC,GAAGqB,IAAI;IACjC;EACF;EAEAH,QAAQA,CAAA,EAAG;IACT,OAAO,EAAE;EACX;AACF;AAEA,MAAMK,UAAU,SAASZ,UAAU,CAAC;EAClCC,WAAWA,CAACd,MAAM,EAAE;IAClB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACgB,CAAC,GAAG,IAAI,EAAC;IACd,IAAIhB,MAAM,EAAE,IAAI,CAACkB,CAAC,GAAGnB,aAAa,CAACC,MAAM,CAAC;EAC5C;EAEAoB,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,CAAC;EACf;AACF;AAEA,MAAMQ,WAAW,SAASb,UAAU,CAAC;EACnCC,WAAWA,CAACa,SAAS,EAAE;IACrB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACX,CAAC,GAAG,IAAI,EAAC;IACd,IAAI,CAACW,SAAS,GAAGA,SAAS;EAC5B;EAEAP,QAAQA,CAAA,EAAG;IACT,IAAI,CAACF,CAAC,GAAG,IAAI,CAACS,SAAS,CAACC,GAAG,CAACC,UAAU,IAAIA,UAAU,CAACV,aAAa,CAAC,CAAC,CAAC,CAACW,IAAI,CAAC,EAAE,CAAC;IAC9E,OAAO,IAAI,CAACZ,CAAC;EACf;AACF;;AAEA;AACA;AACA;AACA,SAASa,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC7B,IAAI,CAACD,GAAG,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,EAAC;EAClC,OAAO,CAACD,GAAG,CAAC3B,MAAM,CAAC4B,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,EAAC;AAC9C;;AAEA;AACA;AACA;AACA,SAASC,IAAIA,CAACF,GAAG,EAAEC,KAAK,EAAE;EACxB;EACA,MAAM3B,GAAG,GAAGyB,SAAS,CAACC,GAAG,EAAEC,KAAK,CAAC;EACjC,MAAMhB,CAAC,GAAGe,GAAG,CAAC3B,MAAM,CAAC4B,KAAK,GAAG,CAAC,EAAE3B,GAAG,GAAG,CAAC,CAAC;EAExC,IAAI,CAACW,CAAC,EAAE,OAAO,CAAC,CAAC;EACjB,MAAMjB,MAAM,GAAG,CAACiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAIpB,UAAU,CAACoB,CAAC,EAAE,EAAE,CAAC,GAAG,IAAIpB,UAAU,CAACoB,CAAC,CAACZ,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAElF,OAAOL,MAAM,CAACmC,QAAQ,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,SAASC,WAAWA,CAACJ,GAAG,EAAEC,KAAK,EAAE;EAC/B,MAAM3B,GAAG,GAAGyB,SAAS,CAACC,GAAG,EAAEC,KAAK,CAAC;EACjC,OAAOA,KAAK,GAAG,CAAC3B,GAAG,GAAG,CAAC,IAAI,CAAC;AAC9B;AAEA+B,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;EACEC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACd,MAAMC,IAAI,GAAG,IAAIjB,UAAU,CAACe,CAAC,CAAC;IAC9B,MAAMG,IAAI,GAAG,IAAIlB,UAAU,CAACgB,CAAC,CAAC;IAC9B,MAAMG,MAAM,GAAG,IAAIlB,WAAW,CAAC,CAACgB,IAAI,EAAEC,IAAI,CAAC,CAAC;IAE5C,OAAOC,MAAM,CAACzB,aAAa,CAAC,CAAC;EAC/B,CAAC;EAED;AACF;AACA;EACE0B,SAASA,CAACC,KAAK,EAAE;IACf;IACA;IACA;IACA,MAAMb,KAAK,GAAGG,WAAW,CAACU,KAAK,EAAE,CAAC,CAAC;IAEnC,MAAMC,OAAO,GAAGX,WAAW,CAACU,KAAK,EAAEb,KAAK,CAAC;IACzC,MAAMe,EAAE,GAAGd,IAAI,CAACY,KAAK,EAAEb,KAAK,CAAC;IAC7B,MAAMgB,EAAE,GAAGH,KAAK,CAACzC,MAAM,CAAC0C,OAAO,EAAEC,EAAE,GAAG,CAAC,CAAC;IAExC,MAAME,SAAS,GAAGH,OAAO,GAAGE,EAAE,CAAC9C,MAAM;IACrC,MAAMgD,OAAO,GAAGf,WAAW,CAACU,KAAK,EAAEI,SAAS,CAAC;IAC7C,MAAME,EAAE,GAAGlB,IAAI,CAACY,KAAK,EAAEI,SAAS,CAAC;IACjC,MAAMG,EAAE,GAAGP,KAAK,CAACzC,MAAM,CAAC8C,OAAO,EAAEC,EAAE,GAAG,CAAC,CAAC;IAExC,MAAMZ,CAAC,GAAG,IAAI3C,UAAU,CAACoD,EAAE,EAAE,EAAE,CAAC;IAChC,MAAMR,CAAC,GAAG,IAAI5C,UAAU,CAACwD,EAAE,EAAE,EAAE,CAAC;IAEhC,OAAO;MAACb,CAAC;MAAEC;IAAC,CAAC;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}