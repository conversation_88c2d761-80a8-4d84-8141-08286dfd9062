{"ast": null, "code": "import _asyncToGenerator from \"G:/web/central-platform-tas-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CwfBaseCrud, CwfOpenModalParam, DialogResultEnum, ModalTypeEnum, PageModeEnum } from 'cwf-ng-library';\nimport { FormControl, Validators } from '@angular/forms';\nimport { TAS_T_VSLVOY } from '@store/TAS/TAS_T_VSLVOY';\nimport { UploadAttachModalComponent } from '@cwfmodal/upload/upload.component';\nimport { HttpHeaders, HttpRequest, HttpResponse } from '@angular/common/http';\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"cwf-ng-library\";\nimport * as i2 from \"@service/globaldata.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@service/cwfuploadService\";\nimport * as i5 from \"@service/cwfRestful.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"angular-svg-icon\";\nimport * as i9 from \"ng-zorro-antd/grid\";\nimport * as i10 from \"ng-zorro-antd/form\";\nimport * as i11 from \"ng-zorro-antd/button\";\nimport * as i12 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i13 from \"ng-zorro-antd/core/wave\";\nimport * as i14 from \"ng-zorro-antd/input\";\nimport * as i15 from \"ng-zorro-antd/select\";\nimport * as i16 from \"ng-zorro-antd/card\";\nimport * as i17 from \"ng-zorro-antd/popconfirm\";\nimport * as i18 from \"ng-zorro-antd/table\";\nimport * as i19 from \"ng-zorro-antd/tooltip\";\nimport * as i20 from \"ng-zorro-antd/icon\";\nimport * as i21 from \"ng-zorro-antd/date-picker\";\nimport * as i22 from \"ng-zorro-antd/upload\";\nimport * as i23 from \"@layout/components/cms-lookup.component\";\nimport * as i24 from \"@ngx-translate/core\";\nconst _c0 = () => ({\n  \"padding\": \"10px\"\n});\nconst _c1 = () => [8, 10];\nconst _c2 = () => ({\n  delFlag: 0\n});\nconst _c3 = () => ({\n  partyType: \"H\"\n});\nconst _c4 = () => ({\n  partyType: \"G\"\n});\nconst _c5 = a0 => ({\n  orgId: a0,\n  bsNm: \"\\u4EF6\\u6742\\u8D27\"\n});\nconst _c6 = () => ({\n  x: \"1000px\"\n});\nfunction VslvoyEditComponent_nz_col_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function VslvoyEditComponent_nz_col_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveData());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function VslvoyEditComponent_nz_col_6_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzLoading\", ctx_r2.loading)(\"nzType\", \"primary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"FP.SAVE\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 6, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction VslvoyEditComponent_nz_col_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-col\", 63)(1, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function VslvoyEditComponent_nz_col_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"FP.CLOSEPAGE\"));\n  }\n}\nfunction VslvoyEditComponent_nz_option_194_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 66);\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r5.label)(\"nzValue\", option_r5.value);\n  }\n}\nfunction VslvoyEditComponent_tr_228_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 67);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 67)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 67)(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 67)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 68)(14, \"a\", 69);\n    i0.ɵɵlistener(\"nzOnConfirm\", function VslvoyEditComponent_tr_228_Template_a_nzOnConfirm_14_listener() {\n      const data_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileDel(data_r7.id));\n    });\n    i0.ɵɵtext(15, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"a\", 70);\n    i0.ɵɵlistener(\"click\", function VslvoyEditComponent_tr_228_Template_a_click_16_listener() {\n      const data_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.preview(data_r7));\n    });\n    i0.ɵɵtext(17, \"\\u9884\\u89C8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"a\", 70);\n    i0.ɵɵlistener(\"click\", function VslvoyEditComponent_tr_228_Template_a_click_18_listener() {\n      const data_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileDownload(data_r7.attachmentId));\n    });\n    i0.ɵɵtext(19, \"\\u4E0B\\u8F7D\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r8 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.originalFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.createdUserName || ctx_r2.translate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzAlign\", \"center\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 8, data_r7.createdTime, \"yyyy-MM-dd HH:mm:ss\"));\n  }\n}\nexport class VslvoyEditComponent extends CwfBaseCrud {\n  constructor(cwfBusContextService, gol, http, uploadService, cwfRestfulService) {\n    super(cwfBusContextService);\n    this.gol = gol;\n    this.http = http;\n    this.uploadService = uploadService;\n    this.cwfRestfulService = cwfRestfulService;\n    this.mainStore = new TAS_T_VSLVOY();\n    this.editStores = [this.mainStore];\n    this.id = \"\";\n    this.companyData = [];\n    this.serviceName = \"\";\n    this.fileList = [];\n    this.fileUploading = false;\n    this.fileloading = false;\n    this.fileStore = new BASE_T_PARFILE();\n    this.baseServiceName = '';\n    this.USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\n    this.fileUploadUrl = this.gol.serverUrl + '/' + this.gol.serviceName['tas'].en + '/storage/new/upload'; // 文件上传请求服务地址\n    // 定义禁用编辑表单的对象，用于控制表单的禁用状态\n    // ALL属性表示是否全局禁用编辑表单的功能\n    this.disabledEditForm = {\n      ALL: false\n    };\n    // 导入文件前\n    this.fileBeforeUpload = file => {\n      console.log('查看id', this.editForm.controls['id'].value);\n      if (this.editForm.controls['id'].value == null || this.editForm.controls['id'].value == '') {\n        this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\n        return false;\n      }\n      this.fileList = [file];\n      console.log(this.fileList[0]);\n      if (this.fileList.length != 1) {\n        this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\n        return false;\n      }\n      if (this.fileList[0].size > this.uploadService.chunkSize) {\n        // 文件大于5M，使用分片上传\n        let params = {\n          pkId: this.editForm.controls['id'].value,\n          expireTime: 60000,\n          module: 'tasModule',\n          businessTypeCode: 'tas_interface',\n          businessTypeName: 'tas_interface'\n        };\n        this.uploadService.uploadData(this.fileList[0], params);\n      } else {\n        // 文件小于5M，普通上传\n        this.fileUpload();\n      }\n      return false;\n    };\n  }\n  /**\n   * 初始化查询条件\n   */\n  initEdit() {\n    return {\n      id: new FormControl('', Validators.nullValidator),\n      // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\n      //理货业务场景下的BC，写死\n      bsCd: new FormControl('BC', Validators.required),\n      //业务场景代码\n      bsNm: new FormControl('散货理货', Validators.required),\n      //业务场景名称\n      bsNmEn: new FormControl('BC', Validators.required),\n      //业务场景英文名称\n      vesselCd: new FormControl('', Validators.required),\n      //船舶代码\n      vesselNm: new FormControl('', Validators.required),\n      //船舶名称\n      vesselNmEn: new FormControl('', Validators.nullValidator),\n      //船舶英文名称\n      voyage: new FormControl('', Validators.required),\n      // 航次\n      ships: new FormControl('', Validators.nullValidator),\n      // 艘次\n      portCd: new FormControl('', Validators.required),\n      //港口代码\n      portNm: new FormControl('', Validators.required),\n      //港口名称\n      portNmEn: new FormControl('', Validators.required),\n      //港口英文名称\n      wharfCd: new FormControl('', Validators.required),\n      //码头代码\n      wharfNm: new FormControl('', Validators.required),\n      //码头名称\n      wharfNmEn: new FormControl('', Validators.required),\n      //码头英文名称\n      berthCd: new FormControl('', Validators.nullValidator),\n      //泊位代码\n      berthNm: new FormControl('', Validators.nullValidator),\n      //泊位名称\n      berthNmEn: new FormControl('', Validators.nullValidator),\n      //泊位英文名称\n      vesselNo: new FormControl('', Validators.nullValidator),\n      //船号（一般跟码头做关联使用）\n      vesselNatureNm: new FormControl('', Validators.nullValidator),\n      //船舶性质\n      operationNatureNm: new FormControl('', Validators.nullValidator),\n      //营运性质\n      workNatureNm: new FormControl('', Validators.nullValidator),\n      //作业性质\n      imo: new FormControl('', Validators.required),\n      //\n      ioId: new FormControl('', Validators.required),\n      //\n      tradeId: new FormControl('', Validators.required),\n      //\n      vesselFlagNm: new FormControl('', Validators.nullValidator),\n      //\n      masterVesselNm: new FormControl('', Validators.nullValidator),\n      //大船船期\n      shipagentNm: new FormControl('', Validators.required),\n      //船舶代理\n      ata: new FormControl('', Validators.required),\n      // 实际到达时间（Actual Time of Arrival）\n      eta: new FormControl('', Validators.nullValidator),\n      // 计划到达时间（Actual Time of Arrival）\n      atb: new FormControl('', Validators.nullValidator),\n      // 实际靠泊时间（Actual Time of Berthing）\n      atd: new FormControl('', Validators.nullValidator),\n      // 实际离港时间（Actual Time of Departure）\n      etd: new FormControl('', Validators.nullValidator),\n      // 计划离港时间（Actual Time of Departure）\n      ast: new FormControl('', Validators.nullValidator),\n      // 实际开始作业时间（Actual Start Time）\n      aet: new FormControl('', Validators.nullValidator),\n      // 实际结束作业时间（Actual End Time）\n      shipLineNm: new FormControl('', Validators.nullValidator),\n      // 航线\n      captain: new FormControl('', Validators.nullValidator),\n      // 船长(大副)\n      captainTel: new FormControl('', Validators.nullValidator),\n      // 联系方式\n      carrierNm: new FormControl('', Validators.nullValidator),\n      // 承运人\n      secondShipTag: new FormControl('', Validators.nullValidator),\n      // 是否二程船\n      dossierNo: new FormControl('', Validators.nullValidator),\n      // 档案号\n      orgId: new FormControl('', Validators.required),\n      orgLevelNo: new FormControl('', Validators.nullValidator),\n      entLevelNo: new FormControl('', Validators.nullValidator),\n      orgNm: new FormControl('', Validators.nullValidator),\n      remark: new FormControl('', Validators.nullValidator),\n      // 备注，初始值为空，验证规则为nullValidator（允许为空）\n      createdUser: new FormControl('', Validators.nullValidator),\n      // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\n      createdTime: new FormControl('', Validators.nullValidator),\n      // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedUser: new FormControl('', Validators.nullValidator),\n      // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\n      modifiedTime: new FormControl('', Validators.nullValidator),\n      // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\n      version: new FormControl('', Validators.nullValidator) // 版本号，初始值为空，验证规则为nullValidator（允许为空）\n      // isDelete: new FormControl('', Validators.nullValidator), // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\n      // tenantId: new FormControl('', Validators.nullValidator) // 租户ID，初始值为空，验证规则为nullValidator（允许为空）\n    };\n  }\n  /**\n   * 页面加载完成后出发\n   */\n  onShow() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.baseServiceName = _this.gol.serviceName['tas'].en;\n      _this.serviceName = _this.gol.serviceName['main'].en;\n      if (_this.openParam['state'] === PageModeEnum.Custom) {\n        _this.disabledEditForm['ALL'] = true;\n        _this.cwfRestfulService.get('/vslvoy/' + _this.openParam['id'], _this.gol.serviceName['tas'].en).then(rps => {\n          if (rps.ok) {\n            _this.editForm.patchValue(rps.data);\n          } else {\n            _this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\n            _this.openMainPage();\n          }\n        });\n      }\n      if (_this.openParam['state'] !== PageModeEnum.Add) {\n        _this.onQueryData();\n      }\n      _this.getOrgData();\n    })();\n  }\n  /**\n   * desc:保存用户数据\n   * by:\n   */\n  saveData() {\n    const url = '/vslvoy';\n    for (const i in this.editForm.controls) {\n      this.editForm.controls[i].markAsDirty();\n      this.editForm.controls[i].updateValueAndValidity();\n    }\n    if (this.editForm.invalid) {\n      return;\n    }\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\n    this.loading = true;\n    if (this.openParam['state'] === PageModeEnum.Add) {\n      this.editForm.removeControl('id');\n      //this.editForm.addControl(\"123\",\"nationCd\");\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    } else {\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then(rps => {\n        this.cwfBusContext.getNotify().removeShow(id);\n        this.loading = false;\n        if (rps.ok) {\n          this.showState(ModalTypeEnum.success, '保存成功！');\n          this.openMainPage({});\n          this.getMainController()['queryList']();\n        } else {\n          this.showState(ModalTypeEnum.error, rps.msg);\n        }\n      });\n    }\n  }\n  onClose() {\n    if (this.checkSave()) {\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\n        switch (state) {\n          case DialogResultEnum.yes:\n            // 是\n            this.saveData();\n            return true;\n          case DialogResultEnum.no:\n            // 否\n            this.openMainPage({});\n            return true;\n          case DialogResultEnum.cancel:\n            // 取消\n            return false;\n        }\n      });\n    } else {\n      this.openMainPage({});\n      return true;\n    }\n  }\n  getEditFromDisabled(feildName) {\n    return this.disabledEditForm[feildName];\n  }\n  onCompanyChange(selectedValues) {\n    if (selectedValues == null) {\n      // 清理数据\n      this.editForm.controls['orgLevelNo'].setValue(\"\");\n      this.editForm.controls['orgNm'].setValue(\"\");\n      this.editForm.controls['entLevelNo'].setValue(\"\");\n    } else {\n      let model = this.companyData.find(item => item.value === selectedValues);\n      this.editForm.controls['orgLevelNo'].setValue(model.orgLevelNo);\n      this.editForm.controls['entLevelNo'].setValue(model.entLevelNo);\n      this.editForm.controls['orgNm'].setValue(model.orgNm);\n    }\n  }\n  getOrgData() {\n    const rdata = {\n      type: null\n    };\n    this.cwfRestfulService.post('/org/getRelateChildren', rdata, this.gol.serviceName['main'].en).then(rps => {\n      if (rps.ok) {\n        // 更新 companyData 数组\n        this.companyData = rps.data.map(item => ({\n          label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\n          value: item.orgId,\n          orgLevelNo: item.orgCode,\n          orgNm: item.orgName,\n          entLevelNo: item.companyCode\n        }));\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  uploadAttach() {\n    const sld = this.editForm.controls;\n    const param = new CwfOpenModalParam();\n    // 设置页面打开模式\n    param.PAGE_MODE = PageModeEnum.Custom;\n    // 设置对话框标题\n    param.CONFIG.width = '80%';\n    param.CONFIG.title = '附件上传';\n    param.CONFIG.top = '50px';\n    param.CONFIG.bodyStyle = {\n      height: '100vh - 230px'\n    };\n    // 是否允许人员按ESC键或者打击空白关闭弹窗\n    param.CONFIG.disableClose = false;\n    // 传入参数数据\n    param.CONFIG.data = {\n      id: sld['id']\n    };\n    this.openModalPage(UploadAttachModalComponent, param).then(value => {});\n  }\n  // 普通上传\n  fileUpload() {\n    let self = this;\n    const formData = new FormData();\n    formData.append('file', this.fileList[0]);\n    formData.append('pkId', this.editForm.controls['id'].value);\n    formData.append('module', 'tasModule');\n    formData.append('businessTypeCode', 'tas_interface');\n    formData.append('businessTypeName', 'tas_interface');\n    formData.append('businessTypeNameEn', 'tas_interface');\n    formData.append('recordTypeCode', 'tas_interface');\n    formData.append('recordTypeName', 'tas_interface');\n    formData.append('recordTypeNameEn', 'tas_interface');\n    formData.append('serviceTypeCode', 'tas_interface');\n    formData.append('serviceTypeName', 'tas_interface');\n    formData.append('serviceTypeNameEn', 'tas_interface');\n    const headers = new HttpHeaders({\n      'x-ccf-token': this.USER_SESSION\n    });\n    const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\n      headers: headers,\n      reportProgress: true,\n      // 启用进度报告\n      withCredentials: false // 携带跨域凭证\n    });\n    this.http.request(req).pipe(filter(e => e instanceof HttpResponse)).subscribe({\n      next(res) {\n        self.fileUploading = false;\n        self.fileList = [];\n        if (res['ok']) {\n          if (res['body']['ok']) {\n            self.showState(ModalTypeEnum.success, '文件上传成功');\n            self.onQueryData();\n          } else {\n            self.showState(ModalTypeEnum.error, res['body']?.['msg'] ?? '文件上传失败');\n          }\n        } else {\n          self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\n        }\n      },\n      error(err) {\n        self.fileUploading = false;\n        self.showState(ModalTypeEnum.error, err.msg);\n      }\n    });\n  }\n  // 附件预览\n  onFilePreview(ATTACH_ID) {\n    console.log('查看进来了么');\n    let condition = {\n      ATTACH_ID: ATTACH_ID,\n      SESSION_USERS: this.USER_SESSION\n    };\n    let downloadurl = this.cwfBusContext.getGlobalData().serverUrl + '/' + this.cwfBusContext.getGlobalData().serviceName['bc'].en + '/spefiledownload' + '?operation=download&actionId=' + ATTACH_ID;\n    console.log('查看下载url', downloadurl);\n    //创建导出iframe\n    let element = document.createElement('iframe');\n    element.setAttribute('style', 'display:none');\n    element.setAttribute('name', 'download');\n    let downloadiframe = document.body.appendChild(element);\n    // 创建下在form\n    let formElement = document.createElement('form');\n    formElement.setAttribute('style', 'display:none');\n    formElement.setAttribute('name', 'downloadForm');\n    let downloadForm = downloadiframe.appendChild(formElement);\n    downloadForm.setAttribute('target', 'download');\n    downloadForm.setAttribute('method', 'post');\n    //设置请求地址\n    downloadForm.setAttribute('action', downloadurl);\n    // 构造传递条件\n    for (let property in condition) {\n      let inputElement = document.createElement('input');\n      inputElement.setAttribute('type', 'hidden');\n      inputElement.setAttribute('name', property);\n      inputElement.setAttribute('value', condition[property]);\n      downloadForm.appendChild(inputElement);\n    }\n    //提交\n    downloadForm.submit();\n  }\n  onQueryData() {\n    this.fileloading = true;\n    let requestData = {\n      pkId: this.editForm.controls['id'].value,\n      module: 'tasModule',\n      businessTypeCode: 'tas_interface'\n    };\n    this.fileStore.clearData();\n    this.cwfRestfulService.post('/storage/new/list', {\n      'data': requestData\n    }, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.fileStore.loadDatas(rps.data ?? []);\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    }).finally(() => {\n      this.fileloading = false;\n    });\n  }\n  // 文件删除\n  onFileDel(id) {\n    this.cwfRestfulService.delete('/storage/new/' + id, this.baseServiceName).then(rps => {\n      if (rps.ok) {\n        this.showState(ModalTypeEnum.success, '删除成功');\n        this.onQueryData();\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  // 文件下载\n  onFileDownload(id) {\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          window.open(downloadUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  preview(data) {\n    var file_name = data['originalFileName'];\n    const fileExtension = file_name.split('.').pop();\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then(rps => {\n      if (rps.ok) {\n        const downloadUrl = rps.data[0];\n        if (downloadUrl) {\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\n          window.open(previewUrl, '_blank');\n        } else {\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\n        }\n      } else {\n        this.showState(ModalTypeEnum.error, rps.msg);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function VslvoyEditComponent_Factory(t) {\n      return new (t || VslvoyEditComponent)(i0.ɵɵdirectiveInject(i1.CwfBusContextService), i0.ɵɵdirectiveInject(i2.GlobalDataService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.CwfUploadService), i0.ɵɵdirectiveInject(i5.CwfRestfulService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VslvoyEditComponent,\n      selectors: [[\"vslvoy-edit\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 229,\n      vars: 118,\n      consts: [[\"fileitem\", \"\"], [1, \"system-index-card\", 3, \"nzBodyStyle\"], [\"nzSpan\", \"10\", 1, \"list-title\"], [3, \"src\"], [1, \"title\"], [\"nzSpan\", \"14\", \"class\", \"list-button\", 4, \"ngIf\"], [\"nz-form\", \"\", 2, \"margin-top\", \"20px\", 3, \"formGroup\"], [\"nz-row\", \"\", 3, \"nzGutter\"], [\"nz-col\", \"\", \"nzSpan\", \"6\", 2, \"display\", \"none\"], [2, \"width\", \"120px\"], [\"nz-input\", \"\", \"formControlName\", \"bsCd\", \"placeholder\", \"\"], [\"nz-input\", \"\", \"formControlName\", \"bsNm\", \"placeholder\", \"\"], [\"nz-input\", \"\", \"formControlName\", \"bsNmEn\", \"placeholder\", \"\"], [\"nz-col\", \"\", \"nzSpan\", \"6\"], [\"nzRequired\", \"\", 2, \"width\", \"120px\"], [\"key\", \"BASE_T_VESSEL\", \"formControlName\", \"vesselNm\", 3, \"nzDisabled\", \"formgroup\"], [\"nz-input\", \"\", \"formControlName\", \"voyage\", \"placeholder\", \"\", \"maxlength\", \"18\", 3, \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"ships\", \"placeholder\", \"\", \"maxlength\", \"18\", 3, \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"imo\", \"placeholder\", \"\", \"maxlength\", \"18\", 3, \"readonly\"], [\"key\", \"BASE_T_SHIP_LINE\", \"formControlName\", \"ioId\", 3, \"readfield\", \"type\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BASE_T_SHIP_LINE\", \"formControlName\", \"tradeId\", 3, \"readfield\", \"type\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BASE_T_COUNTRY\", \"formControlName\", \"vesselFlagNm\", 3, \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\", \"condition\"], [\"nz-input\", \"\", \"formControlName\", \"vesselNo\", \"placeholder\", \"\", \"maxlength\", \"36\", 3, \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"vesselTypeNm\", \"placeholder\", \"\", \"readonly\", \"\"], [\"key\", \"BU_PORT\", \"formControlName\", \"portNm\", 3, \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BU_WHARF\", \"formControlName\", \"wharfNm\", 3, \"type\", \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BU_BERTH\", \"formControlName\", \"berthNm\", 3, \"type\", \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"nzShowTime\", \"\", \"nzFormat\", \"yyyy-MM-dd HH:mm\", \"formControlName\", \"ata\", 2, \"width\", \"100%\", 3, \"nzDisabled\"], [\"nzShowTime\", \"\", \"nzFormat\", \"yyyy-MM-dd HH:mm\", \"formControlName\", \"atb\", 2, \"width\", \"100%\", 3, \"nzDisabled\"], [\"nzShowTime\", \"\", \"nzFormat\", \"yyyy-MM-dd HH:mm\", \"formControlName\", \"atd\", 2, \"width\", \"100%\", 3, \"nzDisabled\"], [\"nzShowTime\", \"\", \"nzFormat\", \"yyyy-MM-dd HH:mm\", \"formControlName\", \"ast\", 2, \"width\", \"100%\", 3, \"nzDisabled\"], [\"nzShowTime\", \"\", \"nzFormat\", \"yyyy-MM-dd HH:mm\", \"formControlName\", \"aet\", \"nzDisabled\", \"\", 2, \"width\", \"100%\", 3, \"nzDisabled\"], [\"key\", \"BASE_T_SHIP_LINE\", \"formControlName\", \"vesselNatureNm\", 3, \"readfield\", \"type\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BASE_T_SHIP_LINE\", \"formControlName\", \"operationNatureNm\", 3, \"readfield\", \"type\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"BASE_T_SHIP_LINE\", \"formControlName\", \"workNatureNm\", 3, \"readfield\", \"type\", \"valuefield\", \"nzDisabled\", \"formgroup\"], [\"key\", \"TAS_T_SHIP_LINE\", \"formControlName\", \"shipLineNm\", 3, \"readfield\", \"valuefield\", \"type\", \"nzDisabled\", \"formgroup\"], [\"nz-input\", \"\", \"formControlName\", \"captain\", \"placeholder\", \"\", \"maxlength\", \"35\", 3, \"readonly\"], [\"nz-input\", \"\", \"formControlName\", \"captainTel\", \"placeholder\", \"\", \"maxlength\", \"36\", 3, \"readonly\"], [\"key\", \"T_PAR_PARTNER\", \"formControlName\", \"shipagentNm\", 3, \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\", \"condition\"], [\"key\", \"T_PAR_PARTNER\", \"formControlName\", \"carrierNm\", 3, \"readfield\", \"valuefield\", \"nzDisabled\", \"formgroup\", \"condition\"], [\"formControlName\", \"secondShipTag\", 3, \"nzPlaceHolder\", \"nzDisabled\"], [\"nzValue\", \"Y\", \"nzLabel\", \"\\u662F\"], [\"nzValue\", \"N\", \"nzLabel\", \"\\u5426\"], [2, \"width\", \"120px\", 3, \"nzRequired\"], [\"key\", \"TAS_T_VSLVOY\", \"formControlName\", \"masterVesselNm\", 3, \"readfield\", \"valuefield\", \"formgroup\", \"condition\", \"nzDisabled\"], [\"nz-input\", \"\", \"formControlName\", \"dossierNo\", \"placeholder\", \"\", \"readonly\", \"\"], [\"nz-col\", \"\", \"nzSpan\", \"20\"], [\"formControlName\", \"orgId\", 3, \"ngModelChange\", \"nzPlaceHolder\", \"nzShowSearch\", \"nzDisabled\"], [3, \"nzLabel\", \"nzValue\", 4, \"ngFor\", \"ngForOf\"], [\"nz-col\", \"\", \"nzSpan\", \"24\"], [\"nz-input\", \"\", \"formControlName\", \"remark\", 2, \"height\", \"100px\", \"resize\", \"none\", 3, \"placeholder\", \"readonly\"], [1, \"list-button\"], [2, \"width\", \"8%\", \"float\", \"left\", \"margin-top\", \"10px\"], [2, \"height\", \"10px\", \"width\", \"10px\", \"color\", \"rgb(20, 96, 237)\", \"border\", \"2px solid\"], [3, \"nzFileListChange\", \"nzFileList\", \"nzBeforeUpload\", \"nzShowUploadList\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"primary\", 3, \"nzLoading\"], [\"nz-icon\", \"\", \"nzType\", \"import\", \"nzTheme\", \"outline\"], [3, \"nzFrontPagination\", \"nzData\", \"nzScroll\", \"nzLoading\"], [\"nzWidth\", \"75px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"300px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzWidth\", \"260px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\", 3, \"nzAlign\"], [\"nzRight\", \"\", \"nzWidth\", \"140px\", 2, \"color\", \"#5f8bd3\", \"background-color\", \"#e6edf5\"], [4, \"ngFor\", \"ngForOf\"], [\"nzSpan\", \"14\", 1, \"list-button\"], [\"nz-button\", \"\", 3, \"click\", \"nzLoading\", \"nzType\"], [\"nz-button\", \"\", 3, \"click\"], [3, \"nzLabel\", \"nzValue\"], [\"nz-tooltip\", \"\", 3, \"nzAlign\"], [\"nzRight\", \"\"], [\"nz-popconfirm\", \"\", \"nzPopconfirmTitle\", \"\\u662F\\u5426\\u5220\\u9664\\u6B64\\u6570\\u636E\\uFF1F\", 2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"nzOnConfirm\"], [2, \"margin-right\", \"15px\", \"font-size\", \"12px\", 3, \"click\"]],\n      template: function VslvoyEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nz-card\", 1)(1, \"nz-row\")(2, \"nz-col\", 2);\n          i0.ɵɵelement(3, \"svg-icon\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtext(5, \"\\u822A\\u6B21\\u4EFB\\u52A1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, VslvoyEditComponent_nz_col_6_Template, 7, 8, \"nz-col\", 5)(7, VslvoyEditComponent_nz_col_7_Template, 4, 3, \"nz-col\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"form\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"nz-form-item\")(12, \"nz-form-label\", 9);\n          i0.ɵɵtext(13, \"\\u822A\\u6B21\\u7C7B\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nz-form-control\");\n          i0.ɵɵelement(15, \"input\", 10)(16, \"input\", 11)(17, \"input\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"nz-form-item\")(20, \"nz-form-label\", 14);\n          i0.ɵɵtext(21, \"\\u8239\\u8236\\u540D\\u79F0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"nz-form-control\");\n          i0.ɵɵelement(23, \"cms-select-table\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 13)(25, \"nz-form-item\")(26, \"nz-form-label\", 14);\n          i0.ɵɵtext(27, \"\\u822A\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nz-form-control\");\n          i0.ɵɵelement(29, \"input\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 13)(31, \"nz-form-item\")(32, \"nz-form-label\", 9);\n          i0.ɵɵtext(33, \"\\u8258\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nz-form-control\");\n          i0.ɵɵelement(35, \"input\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 13)(37, \"nz-form-item\")(38, \"nz-form-label\", 14);\n          i0.ɵɵtext(39, \"IMO\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nz-form-control\");\n          i0.ɵɵelement(41, \"input\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 13)(43, \"nz-form-item\")(44, \"nz-form-label\", 14);\n          i0.ɵɵtext(45, \"\\u8FDB\\u51FA\\u53E3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nz-form-control\");\n          i0.ɵɵelement(47, \"cms-select-table\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 13)(49, \"nz-form-item\")(50, \"nz-form-label\", 14);\n          i0.ɵɵtext(51, \"\\u5185\\u5916\\u8D38\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nz-form-control\");\n          i0.ɵɵelement(53, \"cms-select-table\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 13)(55, \"nz-form-item\")(56, \"nz-form-label\", 9);\n          i0.ɵɵtext(57, \"\\u8239\\u65D7(\\u56FD\\u7C4D)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nz-form-control\");\n          i0.ɵɵelement(59, \"cms-select-table\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 13)(61, \"nz-form-item\")(62, \"nz-form-label\", 9);\n          i0.ɵɵtext(63, \"\\u8239\\u53F7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"nz-form-control\");\n          i0.ɵɵelement(65, \"input\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 13)(67, \"nz-form-item\")(68, \"nz-form-label\", 9);\n          i0.ɵɵtext(69, \"\\u8239\\u8236\\u7C7B\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"nz-form-control\");\n          i0.ɵɵelement(71, \"input\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 13)(73, \"nz-form-item\")(74, \"nz-form-label\", 14);\n          i0.ɵɵtext(75, \"\\u6E2F\\u53E3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"nz-form-control\");\n          i0.ɵɵelement(77, \"cms-select-table\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"div\", 13)(79, \"nz-form-item\")(80, \"nz-form-label\", 14);\n          i0.ɵɵtext(81, \"\\u7801\\u5934\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"nz-form-control\");\n          i0.ɵɵelement(83, \"cms-select-table\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(84, \"div\", 13)(85, \"nz-form-item\")(86, \"nz-form-label\", 9);\n          i0.ɵɵtext(87, \"\\u6CCA\\u4F4D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"nz-form-control\");\n          i0.ɵɵelement(89, \"cms-select-table\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 13)(91, \"nz-form-item\")(92, \"nz-form-label\", 14);\n          i0.ɵɵtext(93, \"\\u5B9E\\u9645\\u62B5\\u6E2F\\u65F6\\u95F4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"nz-form-control\");\n          i0.ɵɵelement(95, \"nz-date-picker\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"div\", 13)(97, \"nz-form-item\")(98, \"nz-form-label\", 9);\n          i0.ɵɵtext(99, \"\\u9760\\u6CCA\\u65F6\\u95F4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"nz-form-control\");\n          i0.ɵɵelement(101, \"nz-date-picker\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(102, \"div\", 13)(103, \"nz-form-item\")(104, \"nz-form-label\", 9);\n          i0.ɵɵtext(105, \"\\u5B9E\\u9645\\u79BB\\u6E2F\\u65F6\\u95F4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"nz-form-control\");\n          i0.ɵɵelement(107, \"nz-date-picker\", 29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(108, \"div\", 13)(109, \"nz-form-item\")(110, \"nz-form-label\", 9);\n          i0.ɵɵtext(111, \"\\u5F00\\u5DE5\\u65F6\\u95F4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"nz-form-control\");\n          i0.ɵɵelement(113, \"nz-date-picker\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 13)(115, \"nz-form-item\")(116, \"nz-form-label\", 9);\n          i0.ɵɵtext(117, \"\\u5B8C\\u5DE5\\u65F6\\u95F4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"nz-form-control\");\n          i0.ɵɵelement(119, \"nz-date-picker\", 31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(120, \"div\", 13)(121, \"nz-form-item\")(122, \"nz-form-label\", 9);\n          i0.ɵɵtext(123, \"\\u8239\\u8236\\u6027\\u8D28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"nz-form-control\");\n          i0.ɵɵelement(125, \"cms-select-table\", 32);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(126, \"div\", 13)(127, \"nz-form-item\")(128, \"nz-form-label\", 9);\n          i0.ɵɵtext(129, \"\\u8425\\u8FD0\\u6027\\u8D28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"nz-form-control\");\n          i0.ɵɵelement(131, \"cms-select-table\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(132, \"div\", 13)(133, \"nz-form-item\")(134, \"nz-form-label\", 9);\n          i0.ɵɵtext(135, \"\\u4F5C\\u4E1A\\u6027\\u8D28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"nz-form-control\");\n          i0.ɵɵelement(137, \"cms-select-table\", 34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(138, \"div\", 13)(139, \"nz-form-item\")(140, \"nz-form-label\", 9);\n          i0.ɵɵtext(141, \"\\u822A\\u7EBF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"nz-form-control\");\n          i0.ɵɵelement(143, \"cms-select-table\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(144, \"div\", 13)(145, \"nz-form-item\")(146, \"nz-form-label\", 9);\n          i0.ɵɵtext(147, \"\\u8239\\u957F(\\u5927\\u526F)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"nz-form-control\");\n          i0.ɵɵelement(149, \"input\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(150, \"div\", 13)(151, \"nz-form-item\")(152, \"nz-form-label\", 9);\n          i0.ɵɵtext(153, \"\\u8054\\u7CFB\\u65B9\\u5F0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"nz-form-control\");\n          i0.ɵɵelement(155, \"input\", 37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(156, \"div\", 13)(157, \"nz-form-item\")(158, \"nz-form-label\", 14);\n          i0.ɵɵtext(159, \"\\u8239\\u8236\\u4EE3\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"nz-form-control\");\n          i0.ɵɵelement(161, \"cms-select-table\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(162, \"div\", 13)(163, \"nz-form-item\")(164, \"nz-form-label\", 9);\n          i0.ɵɵtext(165, \"\\u627F\\u8FD0\\u4EBA\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(166, \"nz-form-control\");\n          i0.ɵɵelement(167, \"cms-select-table\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(168, \"div\", 13)(169, \"nz-form-item\")(170, \"nz-form-label\", 9);\n          i0.ɵɵtext(171, \"\\u662F\\u5426\\u4E8C\\u7A0B\\u8239\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"nz-form-control\")(173, \"nz-select\", 40);\n          i0.ɵɵelement(174, \"nz-option\", 41)(175, \"nz-option\", 42);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(176, \"div\", 13)(177, \"nz-form-item\")(178, \"nz-form-label\", 43);\n          i0.ɵɵtext(179, \"\\u5927\\u8239\\u8239\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"nz-form-control\");\n          i0.ɵɵelement(181, \"cms-select-table\", 44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(182, \"div\", 13)(183, \"nz-form-item\")(184, \"nz-form-label\", 9);\n          i0.ɵɵtext(185, \"\\u6863\\u6848\\u53F7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(186, \"nz-form-control\");\n          i0.ɵɵelement(187, \"input\", 45);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(188, \"div\", 46)(189, \"nz-form-item\")(190, \"nz-form-label\", 14);\n          i0.ɵɵtext(191, \"\\u6240\\u5C5E\\u7EC4\\u7EC7\\u673A\\u6784\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"nz-form-control\")(193, \"nz-select\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function VslvoyEditComponent_Template_nz_select_ngModelChange_193_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCompanyChange($event));\n          });\n          i0.ɵɵtemplate(194, VslvoyEditComponent_nz_option_194_Template, 1, 2, \"nz-option\", 48);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(195, \"div\", 49)(196, \"nz-form-item\")(197, \"nz-form-label\", 9);\n          i0.ɵɵtext(198);\n          i0.ɵɵpipe(199, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"nz-form-control\");\n          i0.ɵɵelement(201, \"textarea\", 50);\n          i0.ɵɵpipe(202, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(203, \"h4\")(204, \"div\", 51)(205, \"div\", 52);\n          i0.ɵɵelement(206, \"span\", 53);\n          i0.ɵɵtext(207, \"\\u00A0\\u9644\\u4EF6\\u4FE1\\u606F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(208, \"nz-upload\", 54);\n          i0.ɵɵtwoWayListener(\"nzFileListChange\", function VslvoyEditComponent_Template_nz_upload_nzFileListChange_208_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.fileList, $event) || (ctx.fileList = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(209, \"button\", 55);\n          i0.ɵɵelement(210, \"span\", 56);\n          i0.ɵɵelementStart(211, \"span\");\n          i0.ɵɵtext(212, \"\\u4E0A\\u4F20\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(213, \"nz-table\", 57, 0)(215, \"thead\")(216, \"tr\")(217, \"th\", 58);\n          i0.ɵɵtext(218, \" \\u5E8F\\u53F7 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(219, \"th\", 59);\n          i0.ɵɵtext(220, \" \\u6587\\u4EF6\\u540D\\u79F0 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(221, \"th\", 60);\n          i0.ɵɵtext(222, \" \\u4E0A\\u4F20\\u4EBA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(223, \"th\", 60);\n          i0.ɵɵtext(224, \" \\u4E0A\\u4F20\\u65F6\\u95F4 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(225, \"th\", 61);\n          i0.ɵɵtext(226, \" \\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(227, \"tbody\");\n          i0.ɵɵtemplate(228, VslvoyEditComponent_tr_228_Template, 20, 11, \"tr\", 62);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_81_0;\n          let tmp_85_0;\n          let tmp_86_0;\n          const fileitem_r9 = i0.ɵɵreference(214);\n          i0.ɵɵproperty(\"nzBodyStyle\", i0.ɵɵpureFunction0(110, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", \"assets/img/icons/filter.svg\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzGutter\", i0.ɵɵpureFunction0(111, _c1));\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"code,name,englishName\")(\"type\", \"system:fms:ieFlag\")(\"valuefield\", \"ioId,ioNm,ioNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"code,name,englishName\")(\"type\", \"system:tms:loadType\")(\"valuefield\", \"tradeId,tradeNm,tradeNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"countryCd,countryNm,countryNmEn\")(\"valuefield\", \"vesselFlagCd,vesselFlagNm,vesselFlagNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm)(\"condition\", i0.ɵɵpureFunction0(112, _c2));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"readfield\", \"portCd,portNm,portNmEn\")(\"valuefield\", \"portCd,portNm,portNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"type\", \"base:wharf\")(\"readfield\", \"wharfCd,wharfNm,wharfNmEn\")(\"valuefield\", \"wharfCd,wharfNm,wharfNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"type\", \"base:berth\")(\"readfield\", \"berthCd,berthNm,berthNmEn\")(\"valuefield\", \"berthCd,berthNm,berthNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"code,name,englishName\")(\"type\", \"system:tas:vesselNature\")(\"valuefield\", \"vesselNatureCd,vesselNatureNm,vesselNatureNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"code,name,englishName\")(\"type\", \"system:tas:operationNature\")(\"valuefield\", \"operationNatureCd,operationNatureNm,operationNatureNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"code,name,englishName\")(\"type\", \"system:tas:workNature\")(\"valuefield\", \"workNatureCd,workNatureNm,workNatureNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"shipLineCd,shipLineNm,shipLineNmEn,shipLineClassCd,shipLineClassNm,shipLineTypeCd,shipLineTypeNm\")(\"valuefield\", \"shipLineCd,shipLineNm,shipLineNmEn,shipLineClassCd,shipLineClassNm,shipLineTypeCd,shipLineTypeNm\")(\"type\", \"tas:shipline\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"partnerCd,partnerNm,partnerNmEn\")(\"valuefield\", \"shipagentCd,shipagentNm,shipagentNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm)(\"condition\", i0.ɵɵpureFunction0(113, _c3));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"readfield\", \"partnerCd,partnerNm,partnerNmEn\")(\"valuefield\", \"carrierCd,carrierNm,carrierNmEn\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"))(\"formgroup\", ctx.editForm)(\"condition\", i0.ɵɵpureFunction0(114, _c4));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"nzRequired\", ((tmp_81_0 = ctx.editForm.get(\"secondShipTag\")) == null ? null : tmp_81_0.value) === \"Y\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"readfield\", \"vesselCd,vesselNm,voyage,ata\")(\"valuefield\", \"masterId,masterVesselCd,masterVesselNm,masterVesselNmEn,masterVoyage\")(\"formgroup\", ctx.editForm)(\"condition\", i0.ɵɵpureFunction1(115, _c5, (tmp_85_0 = ctx.editForm.get(\"orgId\")) == null ? null : tmp_85_0.value))(\"nzDisabled\", ((tmp_86_0 = ctx.editForm.get(\"secondShipTag\")) == null ? null : tmp_86_0.value) !== \"Y\" || ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"nzPlaceHolder\", \"\\u8BF7\\u9009\\u62E9\")(\"nzShowSearch\", true)(\"nzDisabled\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.companyData);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(199, 106, \"TAS.REMARK\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(202, 108, \"TAS.REMARK\"));\n          i0.ɵɵproperty(\"readonly\", ctx.getEditFromDisabled(\"ALL\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"nzFileList\", ctx.fileList);\n          i0.ɵɵproperty(\"nzBeforeUpload\", ctx.fileBeforeUpload)(\"nzShowUploadList\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzLoading\", ctx.fileUploading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzFrontPagination\", false)(\"nzData\", ctx.fileStore.getDatas())(\"nzScroll\", i0.ɵɵpureFunction0(117, _c6))(\"nzLoading\", ctx.fileloading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzAlign\", \"center\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", fileitem_r9.data);\n        }\n      },\n      dependencies: [i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.MaxLengthValidator, i7.NgForOf, i7.NgIf, i6.FormGroupDirective, i6.FormControlName, i8.SvgIconComponent, i9.NzColDirective, i9.NzRowDirective, i10.NzFormDirective, i10.NzFormItemComponent, i10.NzFormLabelComponent, i10.NzFormControlComponent, i11.NzButtonComponent, i12.ɵNzTransitionPatchDirective, i13.NzWaveDirective, i14.NzInputDirective, i15.NzOptionComponent, i15.NzSelectComponent, i16.NzCardComponent, i17.NzPopconfirmDirective, i18.NzTableComponent, i18.NzTableCellDirective, i18.NzThMeasureDirective, i18.NzTheadComponent, i18.NzTbodyComponent, i18.NzTrDirective, i18.NzCellFixedDirective, i18.NzCellAlignDirective, i19.NzTooltipDirective, i20.NzIconDirective, i21.NzDatePickerComponent, i22.NzUploadComponent, i23.CmsLookupComponent, i7.DatePipe, i24.TranslatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CwfBaseCrud", "CwfOpenModalParam", "DialogResultEnum", "ModalTypeEnum", "PageModeEnum", "FormControl", "Validators", "TAS_T_VSLVOY", "UploadAttachModalComponent", "HttpHeaders", "HttpRequest", "HttpResponse", "BASE_T_PARFILE", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "VslvoyEditComponent_nz_col_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "saveData", "ɵɵtext", "ɵɵelementEnd", "VslvoyEditComponent_nz_col_6_Template_button_click_4_listener", "onClose", "ɵɵadvance", "ɵɵproperty", "loading", "ɵɵtextInterpolate", "ɵɵpipeBind1", "VslvoyEditComponent_nz_col_7_Template_button_click_1_listener", "_r4", "ɵɵelement", "option_r5", "label", "value", "VslvoyEditComponent_tr_228_Template_a_nzOnConfirm_14_listener", "data_r7", "_r6", "$implicit", "onFileDel", "id", "VslvoyEditComponent_tr_228_Template_a_click_16_listener", "preview", "VslvoyEditComponent_tr_228_Template_a_click_18_listener", "onFileDownload", "attachmentId", "i_r8", "originalFileName", "createdUserName", "translate", "ɵɵpipeBind2", "createdTime", "VslvoyEditComponent", "constructor", "cwfBusContextService", "gol", "http", "uploadService", "cwfRestfulService", "mainStore", "editStores", "companyData", "serviceName", "fileList", "fileUploading", "fileloading", "fileStore", "baseServiceName", "USER_SESSION", "cwfBusContext", "getContext", "getSessionId", "fileUploadUrl", "serverUrl", "en", "disabledEditForm", "ALL", "fileBeforeUpload", "file", "console", "log", "editForm", "controls", "showState", "error", "length", "size", "chunkSize", "params", "pkId", "expireTime", "module", "businessTypeCode", "businessTypeName", "uploadData", "fileUpload", "initEdit", "nullValidator", "bsCd", "required", "bsNm", "bsNmEn", "vesselCd", "vesselNm", "vesselNmEn", "voyage", "ships", "portCd", "portNm", "portNmEn", "wharfCd", "wharfNm", "wharfNmEn", "berthCd", "berthNm", "berthNmEn", "vesselNo", "vesselNatureNm", "operationNatureNm", "workNatureNm", "imo", "ioId", "tradeId", "vesselFlagNm", "masterVesselNm", "shipagentNm", "ata", "eta", "atb", "atd", "etd", "ast", "aet", "shipLineNm", "captain", "captain<PERSON><PERSON>", "carrierNm", "secondShipTag", "dossierNo", "orgId", "orgLevelNo", "entLevelNo", "orgNm", "remark", "created<PERSON>ser", "modifiedUser", "modifiedTime", "version", "onShow", "_this", "_asyncToGenerator", "openParam", "Custom", "get", "then", "rps", "ok", "patchValue", "data", "openMainPage", "Add", "onQueryData", "getOrgData", "url", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "invalid", "getNotify", "showLoading", "removeControl", "post", "getRawValue", "removeShow", "success", "getMainController", "msg", "put", "checkSave", "showConfirm", "getMsgi18nString", "state", "yes", "no", "cancel", "getEditFromDisabled", "feild<PERSON><PERSON>", "onCompanyChange", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "model", "find", "item", "rdata", "type", "map", "orgCode", "orgName", "companyCode", "companyName", "uploadAttach", "sld", "param", "PAGE_MODE", "CONFIG", "width", "title", "top", "bodyStyle", "height", "disableClose", "openModalPage", "self", "formData", "FormData", "append", "headers", "req", "reportProgress", "withCredentials", "request", "pipe", "e", "subscribe", "next", "res", "err", "onFilePreview", "ATTACH_ID", "condition", "SESSION_USERS", "downloadurl", "getGlobalData", "element", "document", "createElement", "setAttribute", "downloadiframe", "body", "append<PERSON><PERSON><PERSON>", "formElement", "downloadForm", "property", "inputElement", "submit", "requestData", "clearData", "loadDatas", "finally", "delete", "downloadFile", "downloadUrl", "window", "open", "file_name", "fileExtension", "split", "pop", "previewUrl", "btoa", "ɵɵdirectiveInject", "i1", "CwfBusContextService", "i2", "GlobalDataService", "i3", "HttpClient", "i4", "CwfUploadService", "i5", "CwfRestfulService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "VslvoyEditComponent_Template", "rf", "ctx", "ɵɵtemplate", "VslvoyEditComponent_nz_col_6_Template", "VslvoyEditComponent_nz_col_7_Template", "VslvoyEditComponent_Template_nz_select_ngModelChange_193_listener", "$event", "_r1", "VslvoyEditComponent_nz_option_194_Template", "ɵɵtwoWayListener", "VslvoyEditComponent_Template_nz_upload_nzFileListChange_208_listener", "ɵɵtwoWayBindingSet", "VslvoyEditComponent_tr_228_Template", "ɵɵpureFunction0", "_c0", "_c1", "_c2", "_c3", "_c4", "tmp_81_0", "ɵɵpureFunction1", "_c5", "tmp_85_0", "tmp_86_0", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "getDatas", "_c6", "fileitem_r9"], "sources": ["G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vslvoy\\vslvoy-edit\\vslvoy-edit.component.ts", "G:\\web\\central-platform-tas-web\\src\\app\\business\\tas\\vslvoy\\vslvoy-edit\\vslvoy-edit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {CwfBaseCrud, CwfBusContextService, CwfOpenModalParam, DialogResultEnum, ModalTypeEnum, PageModeEnum} from 'cwf-ng-library';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { CwfRestfulService } from '@service/cwfRestful.service';\r\nimport { GlobalDataService } from '@service/globaldata.service';\r\nimport {responseInterface, UploadParams} from 'app/interface/request.interface';\r\nimport { TAS_T_VSLVOY } from '@store/TAS/TAS_T_VSLVOY';\r\nimport {UploadAttachModalComponent} from '@cwfmodal/upload/upload.component';\r\nimport { HttpClient, HttpHeaders, HttpRequest, HttpResponse} from '@angular/common/http';\r\nimport { BASE_T_PARFILE } from '@store/BCD/BASE_T_PARFILE';\r\nimport { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';\r\nimport { CwfUploadService } from '@service/cwfuploadService';\r\nimport {filter} from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'vslvoy-edit',\r\n  templateUrl: './vslvoy-edit.component.html'\r\n})\r\n\r\nexport class VslvoyEditComponent extends CwfBaseCrud {\r\n  editForm: FormGroup;\r\n  mainStore = new TAS_T_VSLVOY();\r\n  editStores = [this.mainStore];\r\n  id = \"\";\r\n  companyData = [];\r\n  serviceName = \"\";\r\n  fileList: NzUploadFile[] = [];\r\n  fileUploading: boolean = false;\r\n  fileloading = false;\r\n  fileStore = new BASE_T_PARFILE();\r\n  baseServiceName = '';\r\n  USER_SESSION = this.cwfBusContext.getContext().getSessionId(); // 用户SESSION\r\n  fileUploadUrl =\r\n    this.gol.serverUrl +\r\n    '/' +\r\n    this.gol.serviceName['tas'].en +\r\n    '/storage/new/upload'; // 文件上传请求服务地址\r\n  // 定义禁用编辑表单的对象，用于控制表单的禁用状态\r\n  // ALL属性表示是否全局禁用编辑表单的功能\r\n  disabledEditForm: Object = {\r\n    ALL: false,\r\n  };\r\n  constructor(cwfBusContextService: CwfBusContextService,\r\n              private gol: GlobalDataService,\r\n              private http: HttpClient,\r\n              private uploadService: CwfUploadService,\r\n              private cwfRestfulService: CwfRestfulService) {\r\n    super(cwfBusContextService);\r\n  }\r\n\r\n  /**\r\n   * 初始化查询条件\r\n   */\r\n  initEdit() {\r\n    return {\r\n      id: new FormControl('', Validators.nullValidator), // 主键ID，初始值为空，验证规则为nullValidator（允许为空）\r\n      //理货业务场景下的BC，写死\r\n      bsCd: new FormControl('BC', Validators.required), //业务场景代码\r\n      bsNm: new FormControl('散货理货', Validators.required), //业务场景名称\r\n      bsNmEn: new FormControl('BC', Validators.required), //业务场景英文名称\r\n\r\n      vesselCd: new FormControl('', Validators.required), //船舶代码\r\n      vesselNm: new FormControl('', Validators.required), //船舶名称\r\n      vesselNmEn: new FormControl('', Validators.nullValidator), //船舶英文名称\r\n\r\n      voyage: new FormControl('', Validators.required), // 航次\r\n      ships: new FormControl('', Validators.nullValidator), // 艘次\r\n\r\n      portCd: new FormControl('', Validators.required), //港口代码\r\n      portNm: new FormControl('', Validators.required), //港口名称\r\n      portNmEn: new FormControl('', Validators.required), //港口英文名称\r\n\r\n      wharfCd: new FormControl('', Validators.required), //码头代码\r\n      wharfNm: new FormControl('', Validators.required), //码头名称\r\n      wharfNmEn: new FormControl('', Validators.required), //码头英文名称\r\n\r\n      berthCd: new FormControl('', Validators.nullValidator), //泊位代码\r\n      berthNm: new FormControl('', Validators.nullValidator), //泊位名称\r\n      berthNmEn: new FormControl('', Validators.nullValidator), //泊位英文名称\r\n\r\n      vesselNo: new FormControl('', Validators.nullValidator), //船号（一般跟码头做关联使用）\r\n      vesselNatureNm: new FormControl('', Validators.nullValidator), //船舶性质\r\n      operationNatureNm: new FormControl('', Validators.nullValidator), //营运性质\r\n      workNatureNm: new FormControl('', Validators.nullValidator), //作业性质\r\n\r\n      imo: new FormControl('', Validators.required), //\r\n      ioId: new FormControl('', Validators.required), //\r\n      tradeId: new FormControl('', Validators.required), //\r\n      vesselFlagNm: new FormControl('', Validators.nullValidator), //\r\n      masterVesselNm: new FormControl('', Validators.nullValidator), //大船船期\r\n      shipagentNm: new FormControl('', Validators.required), //船舶代理\r\n\r\n      ata: new FormControl('', Validators.required), // 实际到达时间（Actual Time of Arrival）\r\n      eta: new FormControl('', Validators.nullValidator), // 计划到达时间（Actual Time of Arrival）\r\n      atb: new FormControl('', Validators.nullValidator), // 实际靠泊时间（Actual Time of Berthing）\r\n      atd: new FormControl('', Validators.nullValidator), // 实际离港时间（Actual Time of Departure）\r\n      etd: new FormControl('', Validators.nullValidator), // 计划离港时间（Actual Time of Departure）\r\n      ast: new FormControl('', Validators.nullValidator), // 实际开始作业时间（Actual Start Time）\r\n      aet: new FormControl('', Validators.nullValidator), // 实际结束作业时间（Actual End Time）\r\n\r\n      shipLineNm: new FormControl('', Validators.nullValidator), // 航线\r\n      captain: new FormControl('', Validators.nullValidator), // 船长(大副)\r\n      captainTel: new FormControl('', Validators.nullValidator), // 联系方式\r\n      carrierNm: new FormControl('', Validators.nullValidator), // 承运人\r\n      secondShipTag: new FormControl('', Validators.nullValidator), // 是否二程船\r\n      dossierNo: new FormControl('', Validators.nullValidator), // 档案号\r\n\r\n\r\n      orgId: new FormControl('', Validators.required),\r\n      orgLevelNo: new FormControl('', Validators.nullValidator),\r\n      entLevelNo: new FormControl('', Validators.nullValidator),\r\n      orgNm: new FormControl('', Validators.nullValidator),\r\n\r\n      remark: new FormControl('', Validators.nullValidator), // 备注，初始值为空，验证规则为nullValidator（允许为空）\r\n      createdUser: new FormControl('', Validators.nullValidator), // 创建用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      createdTime: new FormControl('', Validators.nullValidator), // 创建时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedUser: new FormControl('', Validators.nullValidator), // 修改用户，初始值为空，验证规则为nullValidator（允许为空）\r\n      modifiedTime: new FormControl('', Validators.nullValidator), // 修改时间，初始值为空，验证规则为nullValidator（允许为空）\r\n      version: new FormControl('', Validators.nullValidator), // 版本号，初始值为空，验证规则为nullValidator（允许为空）\r\n      // isDelete: new FormControl('', Validators.nullValidator), // 是否删除（0为未删除），初始值为0，验证规则为nullValidator（允许为空）\r\n      // tenantId: new FormControl('', Validators.nullValidator) // 租户ID，初始值为空，验证规则为nullValidator（允许为空）\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 页面加载完成后出发\r\n   */\r\n  async onShow() {\r\n    this.baseServiceName = this.gol.serviceName['tas'].en;\r\n    this.serviceName = this.gol.serviceName['main'].en;\r\n    if (this.openParam['state'] === PageModeEnum.Custom) {\r\n      this.disabledEditForm['ALL'] = true;\r\n      this.cwfRestfulService.get('/vslvoy/' + this.openParam['id'],this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.editForm.patchValue(rps.data);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, '参数异常或获取数据失败！');\r\n          this.openMainPage();\r\n        }\r\n      });\r\n    }\r\n    if(this.openParam['state'] !== PageModeEnum.Add){\r\n      this.onQueryData();\r\n    }\r\n    this.getOrgData();\r\n  }\r\n\r\n\r\n  /**\r\n   * desc:保存用户数据\r\n   * by:\r\n   */\r\n  saveData() {\r\n    const url = '/vslvoy';\r\n    for (const i in this.editForm.controls) {\r\n      this.editForm.controls[i].markAsDirty();\r\n      this.editForm.controls[i].updateValueAndValidity();\r\n    }\r\n    if (this.editForm.invalid) {\r\n      return;\r\n    }\r\n    const id = this.cwfBusContext.getNotify().showLoading('正在保存数据...');\r\n    this.loading = true;\r\n    if (this.openParam['state'] === PageModeEnum.Add) {\r\n      this.editForm.removeControl('id');\r\n      //this.editForm.addControl(\"123\",\"nationCd\");\r\n      this.cwfRestfulService.post(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    } else {\r\n      this.cwfRestfulService.put(url, this.editForm.getRawValue(), this.gol.serviceName['tas'].en).then((rps: responseInterface) => {\r\n        this.cwfBusContext.getNotify().removeShow(id);\r\n        this.loading = false;\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '保存成功！');\r\n          this.openMainPage({});\r\n          this.getMainController()['queryList']();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    if (this.checkSave()) {\r\n      return this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0028'), 3).then(state => {\r\n        switch (state) {\r\n          case DialogResultEnum.yes:   // 是\r\n            this.saveData();\r\n            return true;\r\n          case DialogResultEnum.no:    // 否\r\n            this.openMainPage({});\r\n            return true;\r\n          case DialogResultEnum.cancel: // 取消\r\n            return false;\r\n        }\r\n      });\r\n    } else {\r\n      this.openMainPage({});\r\n      return true;\r\n    }\r\n  }\r\n  getEditFromDisabled(feildName) {\r\n    return this.disabledEditForm[feildName];\r\n  }\r\n\r\n  onCompanyChange(selectedValues: any[]): void {\r\n    if (selectedValues == null) {\r\n      // 清理数据\r\n      this.editForm.controls['orgLevelNo'].setValue(\"\");\r\n      this.editForm.controls['orgNm'].setValue(\"\");\r\n      this.editForm.controls['entLevelNo'].setValue(\"\");\r\n    } else {\r\n      let model = this.companyData.find(item => item.value === selectedValues);\r\n      this.editForm.controls['orgLevelNo'].setValue(model.orgLevelNo);\r\n      this.editForm.controls['entLevelNo'].setValue(model.entLevelNo);\r\n      this.editForm.controls['orgNm'].setValue(model.orgNm);\r\n    }\r\n  }\r\n  getOrgData() {\r\n    const rdata = { type: null };\r\n    this.cwfRestfulService\r\n      .post(\r\n        '/org/getRelateChildren',\r\n        rdata,\r\n        this.gol.serviceName['main'].en\r\n      )\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          // 更新 companyData 数组\r\n          this.companyData = rps.data.map((item) => ({\r\n            label: item.orgCode + '/' + item.orgName + '-' + item.companyCode + '/' + item.companyName,\r\n            value: item.orgId,\r\n            orgLevelNo: item.orgCode,\r\n            orgNm: item.orgName,\r\n            entLevelNo: item.companyCode\r\n          }));\r\n        }else{\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n\r\n\r\n  uploadAttach() {\r\n    const sld = this.editForm.controls;\r\n\r\n    const param = new CwfOpenModalParam();\r\n    // 设置页面打开模式\r\n    param.PAGE_MODE = PageModeEnum.Custom;\r\n    // 设置对话框标题\r\n    param.CONFIG.width = '80%';\r\n    param.CONFIG.title = '附件上传';\r\n    param.CONFIG.top = '50px';\r\n    param.CONFIG.bodyStyle = {\r\n      height: '100vh - 230px'\r\n    };\r\n    // 是否允许人员按ESC键或者打击空白关闭弹窗\r\n    param.CONFIG.disableClose = false;\r\n    // 传入参数数据\r\n    param.CONFIG.data = {\r\n      id: sld['id']\r\n    };\r\n\r\n    this.openModalPage(UploadAttachModalComponent, param).then(value => {\r\n\r\n    });\r\n  }\r\n\r\n  // 导入文件前\r\n  fileBeforeUpload = (file: NzUploadFile): boolean => {\r\n    console.log('查看id', this.editForm.controls['id'].value);\r\n    if (\r\n      this.editForm.controls['id'].value == null ||\r\n      this.editForm.controls['id'].value == ''\r\n    ) {\r\n      this.showState(ModalTypeEnum.error, '请保存之后再进行上传');\r\n      return false;\r\n    }\r\n    this.fileList = [file];\r\n    console.log(this.fileList[0]);\r\n    if (this.fileList.length != 1) {\r\n      this.showState(ModalTypeEnum.error, '请选择一个文件进行上传');\r\n      return false;\r\n    }\r\n    if (this.fileList[0].size > this.uploadService.chunkSize) {\r\n      // 文件大于5M，使用分片上传\r\n      let params: UploadParams = {\r\n        pkId: this.editForm.controls['id'].value,\r\n        expireTime: 60000,\r\n        module: 'tasModule',\r\n        businessTypeCode: 'tas_interface',\r\n        businessTypeName: 'tas_interface',\r\n      };\r\n      this.uploadService.uploadData(this.fileList[0], params);\r\n    } else {\r\n      // 文件小于5M，普通上传\r\n      this.fileUpload();\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // 普通上传\r\n  fileUpload() {\r\n    let self = this;\r\n    const formData = new FormData();\r\n    formData.append('file', this.fileList[0] as any);\r\n    formData.append('pkId', this.editForm.controls['id'].value);\r\n    formData.append('module', 'tasModule');\r\n    formData.append('businessTypeCode', 'tas_interface');\r\n    formData.append('businessTypeName', 'tas_interface');\r\n    formData.append('businessTypeNameEn', 'tas_interface');\r\n    formData.append('recordTypeCode', 'tas_interface');\r\n    formData.append('recordTypeName', 'tas_interface');\r\n    formData.append('recordTypeNameEn', 'tas_interface');\r\n    formData.append('serviceTypeCode', 'tas_interface');\r\n    formData.append('serviceTypeName', 'tas_interface');\r\n    formData.append('serviceTypeNameEn', 'tas_interface');\r\n    const headers = new HttpHeaders({\r\n      'x-ccf-token': this.USER_SESSION,\r\n    });\r\n    const req = new HttpRequest('POST', this.fileUploadUrl, formData, {\r\n      headers: headers,\r\n      reportProgress: true, // 启用进度报告\r\n      withCredentials: false, // 携带跨域凭证\r\n    });\r\n    this.http\r\n      .request(req)\r\n      .pipe(filter((e) => e instanceof HttpResponse))\r\n      .subscribe({\r\n        next(res): any {\r\n          self.fileUploading = false;\r\n          self.fileList = [];\r\n          if (res['ok']) {\r\n            if (res['body']['ok']) {\r\n              self.showState(ModalTypeEnum.success, '文件上传成功');\r\n              self.onQueryData();\r\n            } else {\r\n              self.showState(\r\n                ModalTypeEnum.error,\r\n                res['body']?.['msg'] ?? '文件上传失败'\r\n              );\r\n            }\r\n          } else {\r\n            self.showState(ModalTypeEnum.error, res['msg'] ?? '文件上传失败');\r\n          }\r\n        },\r\n        error(err): any {\r\n          self.fileUploading = false;\r\n          self.showState(ModalTypeEnum.error, err.msg);\r\n        },\r\n      });\r\n  }\r\n\r\n  // 附件预览\r\n  onFilePreview(ATTACH_ID) {\r\n    console.log('查看进来了么');\r\n    let condition = { ATTACH_ID: ATTACH_ID, SESSION_USERS: this.USER_SESSION };\r\n    let downloadurl =\r\n      this.cwfBusContext.getGlobalData().serverUrl +\r\n      '/' +\r\n      this.cwfBusContext.getGlobalData().serviceName['bc'].en +\r\n      '/spefiledownload' +\r\n      '?operation=download&actionId=' +\r\n      ATTACH_ID;\r\n    console.log('查看下载url', downloadurl);\r\n    //创建导出iframe\r\n    let element = document.createElement('iframe');\r\n    element.setAttribute('style', 'display:none');\r\n    element.setAttribute('name', 'download');\r\n    let downloadiframe = document.body.appendChild(element);\r\n    // 创建下在form\r\n    let formElement = document.createElement('form');\r\n    formElement.setAttribute('style', 'display:none');\r\n    formElement.setAttribute('name', 'downloadForm');\r\n    let downloadForm = downloadiframe.appendChild(formElement);\r\n    downloadForm.setAttribute('target', 'download');\r\n    downloadForm.setAttribute('method', 'post');\r\n    //设置请求地址\r\n    downloadForm.setAttribute('action', downloadurl);\r\n    // 构造传递条件\r\n    for (let property in condition) {\r\n      let inputElement = document.createElement('input');\r\n      inputElement.setAttribute('type', 'hidden');\r\n      inputElement.setAttribute('name', property);\r\n      inputElement.setAttribute('value', condition[property]);\r\n      downloadForm.appendChild(inputElement);\r\n    }\r\n    //提交\r\n    downloadForm.submit();\r\n  }\r\n\r\n  onQueryData() {\r\n    this.fileloading = true;\r\n    let requestData = {\r\n      pkId: this.editForm.controls['id'].value,\r\n      module: 'tasModule',\r\n      businessTypeCode: 'tas_interface',\r\n    };\r\n    this.fileStore.clearData();\r\n    this.cwfRestfulService\r\n      .post('/storage/new/list', {'data': requestData}, this.baseServiceName)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.fileStore.loadDatas(rps.data ?? []);\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      })\r\n      .finally(() => {\r\n        this.fileloading = false;\r\n      });\r\n  }\r\n\r\n  // 文件删除\r\n  onFileDel(id) {\r\n    this.cwfRestfulService\r\n      .delete('/storage/new/' + id, this.baseServiceName)\r\n      .then((rps: responseInterface) => {\r\n        if (rps.ok) {\r\n          this.showState(ModalTypeEnum.success, '删除成功');\r\n          this.onQueryData();\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, rps.msg);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 文件下载\r\n  onFileDownload(id) {\r\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [id], 60000, true).then((rps: responseInterface) => {\r\n      if (rps.ok) {\r\n        const downloadUrl = rps.data[0];\r\n        if (downloadUrl) {\r\n          window.open(downloadUrl, '_blank');\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n        }\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n  preview(data) {\r\n    var file_name = data['originalFileName'];\r\n    const fileExtension = file_name.split('.').pop();\r\n    this.uploadService.downloadFile(this.gol.serviceName['tas'].en, [data['attachmentId']], 600000, true).then((rps: responseInterface) => {\r\n      if (rps.ok) {\r\n        const downloadUrl = rps.data[0];\r\n        if (downloadUrl) {\r\n          let previewUrl = this.gol.previewUrl + btoa(downloadUrl);\r\n          window.open(previewUrl, '_blank');\r\n        } else {\r\n          this.showState(ModalTypeEnum.error, \"文件下载失败\");\r\n        }\r\n      } else {\r\n        this.showState(ModalTypeEnum.error, rps.msg);\r\n      }\r\n    });\r\n  }\r\n\r\n}\r\n", "<nz-card class=\"system-index-card\" [nzBodyStyle]=\"{'padding': '10px' }\">\r\n  <nz-row>\r\n    <nz-col nzSpan=\"10\" class=\"list-title\">\r\n      <svg-icon [src]=\"'assets/img/icons/filter.svg'\"></svg-icon>\r\n      <div class=\"title\">航次任务</div>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"!getEditFromDisabled('ALL')\">\r\n      <button nz-button [nzLoading]=\"loading\" (click)=\"saveData()\" [nzType]=\"'primary'\">{{'FP.SAVE' |\r\n        translate}}</button>\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n    <nz-col nzSpan=\"14\" class=\"list-button\" *ngIf=\"getEditFromDisabled('ALL')\">\r\n      <button nz-button (click)=\"onClose()\">{{'FP.CLOSEPAGE' | translate}}</button>\r\n    </nz-col>\r\n  </nz-row>\r\n  <!-- 编辑、保存表单 -->\r\n  <form style=\"margin-top: 20px;\" nz-form [formGroup]=\"editForm\">\r\n    <div nz-row [nzGutter]=\"[8,10]\">\r\n      <!-- 航次类型 -->\r\n      <div nz-col nzSpan=\"6\" style=\"display: none;\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">航次类型</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"bsCd\" placeholder=\"\" />\r\n            <input nz-input formControlName=\"bsNm\" placeholder=\"\" />\r\n            <input nz-input formControlName=\"bsNmEn\" placeholder=\"\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 船舶 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶名称</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BASE_T_VESSEL\" formControlName=\"vesselNm\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 航次 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">航次</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"voyage\" placeholder=\"\" maxlength=\"18\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 艘次 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">艘次</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"ships\" placeholder=\"\" maxlength=\"18\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- IMO -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">IMO</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"imo\" placeholder=\"\" maxlength=\"18\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 进出口 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">进出口</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BASE_T_SHIP_LINE\" [readfield]=\"'code,name,englishName'\" [type]=\"'system:fms:ieFlag'\"\r\n                              [valuefield]=\"'ioId,ioNm,ioNmEn'\" formControlName=\"ioId\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\"></cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 内外贸 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">内外贸</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BASE_T_SHIP_LINE\" [readfield]=\"'code,name,englishName'\" [type]=\"'system:tms:loadType'\"\r\n                              [valuefield]=\"'tradeId,tradeNm,tradeNmEn'\" formControlName=\"tradeId\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\"></cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n\r\n\r\n\r\n      <!-- 船旗(国籍)\r\n        1：下拉选择控件，取国家表base_t_country，默认条件，删除标志等于0，按照国家代码顺序排序，下拉控件展示：国家代码、国家名称、国家英文名称\r\n        2、对应tas_t_vslvoy表vessel_flag_nm字段，同时赋值vessel_flag_cd、vessel_flag_nm_en\r\n        3、必输\r\n        4、根据所选船舶赋值\r\n       -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船旗(国籍)</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"BASE_T_COUNTRY\"\r\n              [readfield]=\"'countryCd,countryNm,countryNmEn'\"\r\n              [valuefield]=\"'vesselFlagCd,vesselFlagNm,vesselFlagNmEn'\"\r\n              formControlName=\"vesselFlagNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\"\r\n              [condition]=\"{ delFlag: 0 }\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 船号 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船号</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"vesselNo\" placeholder=\"\" maxlength=\"36\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 船舶类型 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶类型</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"vesselTypeNm\" placeholder=\"\" readonly />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 港口 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">港口</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BU_PORT\" formControlName=\"portNm\"\r\n                              [readfield]=\"'portCd,portNm,portNmEn'\"\r\n                              [valuefield]=\"'portCd,portNm,portNmEn'\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\"></cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 码头 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">码头</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BU_WHARF\" formControlName=\"wharfNm\"\r\n                              [type]=\"'base:wharf'\"\r\n                              [readfield]=\"'wharfCd,wharfNm,wharfNmEn'\"\r\n                              [valuefield]=\"'wharfCd,wharfNm,wharfNmEn'\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\"></cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 泊位 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">泊位</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table key=\"BU_BERTH\" formControlName=\"berthNm\"\r\n                              [type]=\"'base:berth'\"\r\n                              [readfield]=\"'berthCd,berthNm,berthNmEn'\"\r\n                              [valuefield]=\"'berthCd,berthNm,berthNmEn'\"\r\n                              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                              [formgroup]=\"editForm\"></cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 实际的抵港时间 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">实际抵港时间</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker\r\n              nzShowTime\r\n              nzFormat=\"yyyy-MM-dd HH:mm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"ata\"\r\n              style=\"width: 100%;\">\r\n            </nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 靠泊时间 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">靠泊时间</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker\r\n              nzShowTime\r\n              nzFormat=\"yyyy-MM-dd HH:mm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"atb\"\r\n              style=\"width: 100%;\">\r\n            </nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 实际的离港时间 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">实际离港时间</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker\r\n              nzShowTime\r\n              nzFormat=\"yyyy-MM-dd HH:mm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"atd\"\r\n              style=\"width: 100%;\">\r\n            </nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 开工时间 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">开工时间</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker\r\n              nzShowTime\r\n              nzFormat=\"yyyy-MM-dd HH:mm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"ast\"\r\n              style=\"width: 100%;\">\r\n            </nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 完工时间 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">完工时间</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-date-picker\r\n              nzShowTime\r\n              nzFormat=\"yyyy-MM-dd HH:mm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              formControlName=\"aet\"\r\n              style=\"width: 100%;\"\r\n              nzDisabled>\r\n            </nz-date-picker>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n\r\n      <!-- 船舶性质 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船舶性质</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"BASE_T_SHIP_LINE\"\r\n              [readfield]=\"'code,name,englishName'\"\r\n              [type]=\"'system:tas:vesselNature'\"\r\n              [valuefield]=\"'vesselNatureCd,vesselNatureNm,vesselNatureNmEn'\"\r\n              formControlName=\"vesselNatureNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 营运性质 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">营运性质</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"BASE_T_SHIP_LINE\"\r\n              [readfield]=\"'code,name,englishName'\"\r\n              [type]=\"'system:tas:operationNature'\"\r\n              [valuefield]=\"'operationNatureCd,operationNatureNm,operationNatureNmEn'\"\r\n              formControlName=\"operationNatureNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 作业性质 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">作业性质</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"BASE_T_SHIP_LINE\"\r\n              [readfield]=\"'code,name,englishName'\"\r\n              [type]=\"'system:tas:workNature'\"\r\n              [valuefield]=\"'workNatureCd,workNatureNm,workNatureNmEn'\"\r\n              formControlName=\"workNatureNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 航线 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">航线</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"TAS_T_SHIP_LINE\"\r\n              [readfield]=\"'shipLineCd,shipLineNm,shipLineNmEn,shipLineClassCd,shipLineClassNm,shipLineTypeCd,shipLineTypeNm'\"\r\n              [valuefield]=\"'shipLineCd,shipLineNm,shipLineNmEn,shipLineClassCd,shipLineClassNm,shipLineTypeCd,shipLineTypeNm'\"\r\n              formControlName=\"shipLineNm\"\r\n              [type]=\"'tas:shipline'\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 船长(大副) -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">船长(大副)</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"captain\" placeholder=\"\" maxlength=\"35\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 联系方式 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">联系方式</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"captainTel\" placeholder=\"\" maxlength=\"36\" [readonly]=\"getEditFromDisabled('ALL')\" />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 船舶代理 -->\r\n      <!-- [condition]=\"{ partyType: '船舶代理' }\" -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">船舶代理</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"T_PAR_PARTNER\"\r\n              [readfield]=\"'partnerCd,partnerNm,partnerNmEn'\"\r\n              [valuefield]=\"'shipagentCd,shipagentNm,shipagentNmEn'\"\r\n              formControlName=\"shipagentNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\"\r\n              [condition]=\"{ partyType: 'H' }\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 承运人 -->\r\n      <!-- [condition]=\"{ partyType: '船公司' }\"-->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">承运人</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"T_PAR_PARTNER\"\r\n              [readfield]=\"'partnerCd,partnerNm,partnerNmEn'\"\r\n              [valuefield]=\"'carrierCd,carrierNm,carrierNmEn'\"\r\n              formControlName=\"carrierNm\"\r\n              [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n              [formgroup]=\"editForm\"\r\n              [condition]=\"{ partyType: 'G' }\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 是否二程船 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">是否二程船</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"secondShipTag\" [nzPlaceHolder]=\"'请选择'\" [nzDisabled]=\"getEditFromDisabled('ALL')\">\r\n              <nz-option nzValue=\"Y\" nzLabel=\"是\"></nz-option>\r\n              <nz-option nzValue=\"N\" nzLabel=\"否\"></nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 大船船期 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label [nzRequired]=\"editForm.get('secondShipTag')?.value === 'Y'\" style=\"width: 120px\">大船船期</nz-form-label>\r\n          <nz-form-control>\r\n            <cms-select-table\r\n              key=\"TAS_T_VSLVOY\"\r\n              [readfield]=\"'vesselCd,vesselNm,voyage,ata'\"\r\n              [valuefield]=\"'masterId,masterVesselCd,masterVesselNm,masterVesselNmEn,masterVoyage'\"\r\n              formControlName=\"masterVesselNm\"\r\n              [formgroup]=\"editForm\"\r\n              [condition]=\"{ orgId: editForm.get('orgId')?.value, bsNm: '件杂货' }\"\r\n              [nzDisabled]=\"editForm.get('secondShipTag')?.value !== 'Y' || getEditFromDisabled('ALL')\">\r\n            </cms-select-table>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!-- 档案号 -->\r\n      <div nz-col nzSpan=\"6\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">档案号</nz-form-label>\r\n          <nz-form-control>\r\n            <input nz-input formControlName=\"dossierNo\" placeholder=\"\" readonly />\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n\r\n      <!-- 所属组织机构名称 -->\r\n      <div nz-col nzSpan=\"20\">\r\n        <nz-form-item>\r\n          <nz-form-label nzRequired style=\"width: 120px\">所属组织机构</nz-form-label>\r\n          <nz-form-control>\r\n            <nz-select formControlName=\"orgId\" [nzPlaceHolder]=\"'请选择'\" [nzShowSearch]=\"true\"\r\n                       [nzDisabled]=\"getEditFromDisabled('ALL')\"\r\n                       (ngModelChange)=\"onCompanyChange($event)\">\r\n              <nz-option *ngFor=\"let option of companyData\" [nzLabel]=\"option.label\" [nzValue]=\"option.value\">\r\n              </nz-option>\r\n            </nz-select>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n\r\n      <!--remark :备注 -->\r\n      <div nz-col nzSpan=\"24\">\r\n        <nz-form-item>\r\n          <nz-form-label style=\"width: 120px\">{{'TAS.REMARK' | translate}}</nz-form-label>\r\n          <nz-form-control>\r\n            <textarea nz-input placeholder=\"{{'TAS.REMARK' | translate}}\" [readonly]=\"getEditFromDisabled('ALL')\"\r\n                      formControlName=\"remark\" style=\"height: 100px;resize: none;\"></textarea>\r\n          </nz-form-control>\r\n        </nz-form-item>\r\n      </div>\r\n    </div>\r\n  </form>\r\n\r\n\r\n  <h4>\r\n    <div class=\"list-button\">\r\n      <div style=\"width: 8%;float:left; margin-top: 10px;\"><span style=\"height: 10px;width: 10px;color: rgb(20, 96, 237);border: 2px solid;\"></span>&nbsp;附件信息</div>\r\n      <nz-upload [(nzFileList)]=\"fileList\" [nzBeforeUpload]=\"fileBeforeUpload\" [nzShowUploadList]=\"false\">\r\n        <button type=\"button\" nz-button nzType=\"primary\" [nzLoading]=\"fileUploading\">\r\n          <span nz-icon nzType=\"import\" nzTheme=\"outline\"></span>\r\n          <span>上传</span>\r\n        </button>\r\n      </nz-upload>\r\n    </div>\r\n  </h4>\r\n  <nz-table #fileitem [nzFrontPagination]=\"false\" [nzData]=\"fileStore.getDatas()\"\r\n            [nzScroll]=\"{ x: '1000px' }\" [nzLoading]=\"fileloading\">\r\n    <thead>\r\n    <tr>\r\n      <!--序号-->\r\n      <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"75px\">\r\n        序号\r\n      </th>\r\n      <!--文件名称-->\r\n      <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"300px\">\r\n        文件名称\r\n      </th>\r\n\r\n      <!--文件类型-->\r\n      <!-- <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n        文件大小\r\n      </th> -->\r\n      <!--文件格式-->\r\n      <!-- <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n        文件格式\r\n      </th> -->\r\n\r\n      <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n        上传人\r\n      </th>\r\n\r\n      <th [nzAlign]=\"'center'\" style=\"color: #5f8bd3; background-color: #e6edf5\" nzWidth=\"260px\">\r\n        上传时间\r\n      </th>\r\n\r\n      <th nzRight nzWidth=\"140px\" style=\"color: #5f8bd3; background-color: #e6edf5\">\r\n        操作\r\n      </th>\r\n      <!-- 操作 -->\r\n    </tr>\r\n    </thead>\r\n    <tbody>\r\n    <tr *ngFor=\"let data of fileitem.data; let i = index\">\r\n      <td nz-tooltip [nzAlign]=\"'center'\">{{ i + 1 }}</td>\r\n      <!-- 文件名称 -->\r\n      <td nz-tooltip [nzAlign]=\"'center'\">\r\n        <span>{{ data.originalFileName }}</span>\r\n      </td>\r\n      <!-- 文件大小 -->\r\n      <!-- <td nz-tooltip [nzAlign]=\"'center'\">\r\n        <span>{{ data.fileSize || 0 }}字节</span>\r\n      </td> -->\r\n      <!-- 文件格式 -->\r\n      <!-- <td nz-tooltip [nzAlign]=\"'center'\">\r\n        <span>{{ data.mediaType }}</span>\r\n      </td> -->\r\n      <td nz-tooltip [nzAlign]=\"'center'\">\r\n        <span>{{ data.createdUserName || translate }}</span>\r\n      </td>\r\n      <td nz-tooltip [nzAlign]=\"'center'\">\r\n        <span>{{ data.createdTime | date:'yyyy-MM-dd HH:mm:ss' }}</span>\r\n      </td>\r\n      <td nzRight>\r\n        <a style=\"margin-right: 15px; font-size: 12px\" nz-popconfirm\r\n           nzPopconfirmTitle=\"是否删除此数据？\" (nzOnConfirm)=\"onFileDel(data.id)\">删除</a>\r\n        <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"preview(data)\">预览</a>\r\n        <a style=\"margin-right: 15px; font-size: 12px\" (click)=\"onFileDownload(data.attachmentId)\">下载</a>\r\n      </td>\r\n    </tr>\r\n    </tbody>\r\n  </nz-table>\r\n\r\n\r\n\r\n</nz-card>\r\n"], "mappings": ";AACA,SAAQA,WAAW,EAAwBC,iBAAiB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,QAAO,gBAAgB;AAClI,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAAQC,0BAA0B,QAAO,mCAAmC;AAC5E,SAAqBC,WAAW,EAAEC,WAAW,EAAEC,YAAY,QAAO,sBAAsB;AACxF,SAASC,cAAc,QAAQ,2BAA2B;AAG1D,SAAQC,MAAM,QAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICL/BC,EADF,CAAAC,cAAA,iBAA4E,iBACQ;IAA1CD,EAAA,CAAAE,UAAA,mBAAAC,8DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAAsBT,EAAA,CAAAU,MAAA,GACrE;;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACtBX,EAAA,CAAAC,cAAA,iBAAsC;IAApBD,EAAA,CAAAE,UAAA,mBAAAU,8DAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;;IAHWX,EAAA,CAAAc,SAAA,EAAqB;IAAsBd,EAA3C,CAAAe,UAAA,cAAAT,MAAA,CAAAU,OAAA,CAAqB,qBAA0C;IAAChB,EAAA,CAAAc,SAAA,EACrE;IADqEd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,kBACrE;IACyBlB,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;;IAGpElB,EADF,CAAAC,cAAA,iBAA2E,iBACnC;IAApBD,EAAA,CAAAE,UAAA,mBAAAiB,8DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAACb,EAAA,CAAAU,MAAA,GAA8B;;IACtEV,EADsE,CAAAW,YAAA,EAAS,EACtE;;;IAD+BX,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,uBAA8B;;;;;IAyb5DlB,EAAA,CAAAqB,SAAA,oBACY;;;;IAD2DrB,EAAzB,CAAAe,UAAA,YAAAO,SAAA,CAAAC,KAAA,CAAwB,YAAAD,SAAA,CAAAE,KAAA,CAAyB;;;;;;IAsEvGxB,EADF,CAAAC,cAAA,SAAsD,aAChB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAGlDX,EADF,CAAAC,cAAA,aAAoC,WAC5B;IAAAD,EAAA,CAAAU,MAAA,GAA2B;IACnCV,EADmC,CAAAW,YAAA,EAAO,EACrC;IAUHX,EADF,CAAAC,cAAA,aAAoC,WAC5B;IAAAD,EAAA,CAAAU,MAAA,GAAuC;IAC/CV,EAD+C,CAAAW,YAAA,EAAO,EACjD;IAEHX,EADF,CAAAC,cAAA,aAAoC,YAC5B;IAAAD,EAAA,CAAAU,MAAA,IAAmD;;IAC3DV,EAD2D,CAAAW,YAAA,EAAO,EAC7D;IAEHX,EADF,CAAAC,cAAA,cAAY,aAEyD;IAAnCD,EAAA,CAAAE,UAAA,yBAAAuB,8DAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAuB,SAAA,CAAAH,OAAA,CAAAI,EAAA,CAAkB;IAAA,EAAC;IAAC9B,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACzEX,EAAA,CAAAC,cAAA,aAAuE;IAAxBD,EAAA,CAAAE,UAAA,mBAAA6B,wDAAA;MAAA,MAAAL,OAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0B,OAAA,CAAAN,OAAA,CAAa;IAAA,EAAC;IAAC1B,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC7EX,EAAA,CAAAC,cAAA,aAA2F;IAA5CD,EAAA,CAAAE,UAAA,mBAAA+B,wDAAA;MAAA,MAAAP,OAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA,EAAAC,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4B,cAAA,CAAAR,OAAA,CAAAS,YAAA,CAAiC;IAAA,EAAC;IAACnC,EAAA,CAAAU,MAAA,oBAAE;IAEjGV,EAFiG,CAAAW,YAAA,EAAI,EAC9F,EACF;;;;;;IAzBYX,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAACf,EAAA,CAAAc,SAAA,EAAW;IAAXd,EAAA,CAAAiB,iBAAA,CAAAmB,IAAA,KAAW;IAEhCpC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAA2B;IAA3Bd,EAAA,CAAAiB,iBAAA,CAAAS,OAAA,CAAAW,gBAAA,CAA2B;IAUpBrC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAiB,iBAAA,CAAAS,OAAA,CAAAY,eAAA,IAAAhC,MAAA,CAAAiC,SAAA,CAAuC;IAEhCvC,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAe,UAAA,qBAAoB;IAC3Bf,EAAA,CAAAc,SAAA,GAAmD;IAAnDd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAwC,WAAA,QAAAd,OAAA,CAAAe,WAAA,yBAAmD;;;ADzgBjE,OAAM,MAAOC,mBAAoB,SAAQxD,WAAW;EAuBlDyD,YAAYC,oBAA0C,EAClCC,GAAsB,EACtBC,IAAgB,EAChBC,aAA+B,EAC/BC,iBAAoC;IACtD,KAAK,CAACJ,oBAAoB,CAAC;IAJT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAzBrC,KAAAC,SAAS,GAAG,IAAIxD,YAAY,EAAE;IAC9B,KAAAyD,UAAU,GAAG,CAAC,IAAI,CAACD,SAAS,CAAC;IAC7B,KAAAnB,EAAE,GAAG,EAAE;IACP,KAAAqB,WAAW,GAAG,EAAE;IAChB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,QAAQ,GAAmB,EAAE;IAC7B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,SAAS,GAAG,IAAI1D,cAAc,EAAE;IAChC,KAAA2D,eAAe,GAAG,EAAE;IACpB,KAAAC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,UAAU,EAAE,CAACC,YAAY,EAAE,CAAC,CAAC;IAC/D,KAAAC,aAAa,GACX,IAAI,CAACjB,GAAG,CAACkB,SAAS,GAClB,GAAG,GACH,IAAI,CAAClB,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,GAC9B,qBAAqB,CAAC,CAAC;IACzB;IACA;IACA,KAAAC,gBAAgB,GAAW;MACzBC,GAAG,EAAE;KACN;IA8OD;IACA,KAAAC,gBAAgB,GAAIC,IAAkB,IAAa;MACjDC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK,CAAC;MACvD,IACE,IAAI,CAAC+C,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK,IAAI,IAAI,IAC1C,IAAI,CAAC+C,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK,IAAI,EAAE,EACxC;QACA,IAAI,CAACiD,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE,YAAY,CAAC;QACjD,OAAO,KAAK;MACd;MACA,IAAI,CAACrB,QAAQ,GAAG,CAACe,IAAI,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI,IAAI,CAACA,QAAQ,CAACsB,MAAM,IAAI,CAAC,EAAE;QAC7B,IAAI,CAACF,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE,aAAa,CAAC;QAClD,OAAO,KAAK;MACd;MACA,IAAI,IAAI,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAACuB,IAAI,GAAG,IAAI,CAAC7B,aAAa,CAAC8B,SAAS,EAAE;QACxD;QACA,IAAIC,MAAM,GAAiB;UACzBC,IAAI,EAAE,IAAI,CAACR,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK;UACxCwD,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,WAAW;UACnBC,gBAAgB,EAAE,eAAe;UACjCC,gBAAgB,EAAE;SACnB;QACD,IAAI,CAACpC,aAAa,CAACqC,UAAU,CAAC,IAAI,CAAC/B,QAAQ,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAAC;MACzD,CAAC,MAAM;QACL;QACA,IAAI,CAACO,UAAU,EAAE;MACnB;MACA,OAAO,KAAK;IACd,CAAC;EAtQD;EAEA;;;EAGAC,QAAQA,CAAA;IACN,OAAO;MACLxD,EAAE,EAAE,IAAIvC,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACnD;MACAC,IAAI,EAAE,IAAIjG,WAAW,CAAC,IAAI,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAClDC,IAAI,EAAE,IAAInG,WAAW,CAAC,MAAM,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACpDE,MAAM,EAAE,IAAIpG,WAAW,CAAC,IAAI,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAEpDG,QAAQ,EAAE,IAAIrG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACpDI,QAAQ,EAAE,IAAItG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACpDK,UAAU,EAAE,IAAIvG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAE3DQ,MAAM,EAAE,IAAIxG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAClDO,KAAK,EAAE,IAAIzG,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAEtDU,MAAM,EAAE,IAAI1G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAClDS,MAAM,EAAE,IAAI3G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAClDU,QAAQ,EAAE,IAAI5G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAEpDW,OAAO,EAAE,IAAI7G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACnDY,OAAO,EAAE,IAAI9G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACnDa,SAAS,EAAE,IAAI/G,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAErDc,OAAO,EAAE,IAAIhH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACxDiB,OAAO,EAAE,IAAIjH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACxDkB,SAAS,EAAE,IAAIlH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAE1DmB,QAAQ,EAAE,IAAInH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACzDoB,cAAc,EAAE,IAAIpH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC/DqB,iBAAiB,EAAE,IAAIrH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAClEsB,YAAY,EAAE,IAAItH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAE7DuB,GAAG,EAAE,IAAIvH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAC/CsB,IAAI,EAAE,IAAIxH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAChDuB,OAAO,EAAE,IAAIzH,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MACnDwB,YAAY,EAAE,IAAI1H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC7D2B,cAAc,EAAE,IAAI3H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC/D4B,WAAW,EAAE,IAAI5H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAEvD2B,GAAG,EAAE,IAAI7H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAAE;MAC/C4B,GAAG,EAAE,IAAI9H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACpD+B,GAAG,EAAE,IAAI/H,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACpDgC,GAAG,EAAE,IAAIhI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACpDiC,GAAG,EAAE,IAAIjI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACpDkC,GAAG,EAAE,IAAIlI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACpDmC,GAAG,EAAE,IAAInI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAEpDoC,UAAU,EAAE,IAAIpI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC3DqC,OAAO,EAAE,IAAIrI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACxDsC,UAAU,EAAE,IAAItI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC3DuC,SAAS,EAAE,IAAIvI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC1DwC,aAAa,EAAE,IAAIxI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC9DyC,SAAS,EAAE,IAAIzI,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAG1D0C,KAAK,EAAE,IAAI1I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACiG,QAAQ,CAAC;MAC/CyC,UAAU,EAAE,IAAI3I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MACzD4C,UAAU,EAAE,IAAI5I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MACzD6C,KAAK,EAAE,IAAI7I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAEpD8C,MAAM,EAAE,IAAI9I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MACvD+C,WAAW,EAAE,IAAI/I,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC5D9C,WAAW,EAAE,IAAIlD,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC5DgD,YAAY,EAAE,IAAIhJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC7DiD,YAAY,EAAE,IAAIjJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC;MAAE;MAC7DkD,OAAO,EAAE,IAAIlJ,WAAW,CAAC,EAAE,EAAEC,UAAU,CAAC+F,aAAa,CAAC,CAAE;MACxD;MACA;KACD;EACH;EAEA;;;EAGMmD,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACVD,KAAI,CAAClF,eAAe,GAAGkF,KAAI,CAAC9F,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE;MACrD2E,KAAI,CAACvF,WAAW,GAAGuF,KAAI,CAAC9F,GAAG,CAACO,WAAW,CAAC,MAAM,CAAC,CAACY,EAAE;MAClD,IAAI2E,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKvJ,YAAY,CAACwJ,MAAM,EAAE;QACnDH,KAAI,CAAC1E,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;QACnC0E,KAAI,CAAC3F,iBAAiB,CAAC+F,GAAG,CAAC,UAAU,GAAGJ,KAAI,CAACE,SAAS,CAAC,IAAI,CAAC,EAACF,KAAI,CAAC9F,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,CAAC,CAACgF,IAAI,CAAEC,GAAsB,IAAI;UAC3H,IAAIA,GAAG,CAACC,EAAE,EAAE;YACVP,KAAI,CAACpE,QAAQ,CAAC4E,UAAU,CAACF,GAAG,CAACG,IAAI,CAAC;UACpC,CAAC,MAAM;YACLT,KAAI,CAAClE,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE,cAAc,CAAC;YACnDiE,KAAI,CAACU,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ;MACA,IAAGV,KAAI,CAACE,SAAS,CAAC,OAAO,CAAC,KAAKvJ,YAAY,CAACgK,GAAG,EAAC;QAC9CX,KAAI,CAACY,WAAW,EAAE;MACpB;MACAZ,KAAI,CAACa,UAAU,EAAE;IAAC;EACpB;EAGA;;;;EAIA/I,QAAQA,CAAA;IACN,MAAMgJ,GAAG,GAAG,SAAS;IACrB,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACnF,QAAQ,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,QAAQ,CAACC,QAAQ,CAACkF,CAAC,CAAC,CAACC,WAAW,EAAE;MACvC,IAAI,CAACpF,QAAQ,CAACC,QAAQ,CAACkF,CAAC,CAAC,CAACE,sBAAsB,EAAE;IACpD;IACA,IAAI,IAAI,CAACrF,QAAQ,CAACsF,OAAO,EAAE;MACzB;IACF;IACA,MAAM/H,EAAE,GAAG,IAAI,CAAC6B,aAAa,CAACmG,SAAS,EAAE,CAACC,WAAW,CAAC,WAAW,CAAC;IAClE,IAAI,CAAC/I,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAAC6H,SAAS,CAAC,OAAO,CAAC,KAAKvJ,YAAY,CAACgK,GAAG,EAAE;MAChD,IAAI,CAAC/E,QAAQ,CAACyF,aAAa,CAAC,IAAI,CAAC;MACjC;MACA,IAAI,CAAChH,iBAAiB,CAACiH,IAAI,CAACR,GAAG,EAAE,IAAI,CAAClF,QAAQ,CAAC2F,WAAW,EAAE,EAAE,IAAI,CAACrH,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,CAAC,CAACgF,IAAI,CAAEC,GAAsB,IAAI;QAC5H,IAAI,CAACtF,aAAa,CAACmG,SAAS,EAAE,CAACK,UAAU,CAACrI,EAAE,CAAC;QAC7C,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAIiI,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACzE,SAAS,CAACpF,aAAa,CAAC+K,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAACf,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAACgB,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAC5F,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACtH,iBAAiB,CAACuH,GAAG,CAACd,GAAG,EAAE,IAAI,CAAClF,QAAQ,CAAC2F,WAAW,EAAE,EAAE,IAAI,CAACrH,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,CAAC,CAACgF,IAAI,CAAEC,GAAsB,IAAI;QAC3H,IAAI,CAACtF,aAAa,CAACmG,SAAS,EAAE,CAACK,UAAU,CAACrI,EAAE,CAAC;QAC7C,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAIiI,GAAG,CAACC,EAAE,EAAE;UACV,IAAI,CAACzE,SAAS,CAACpF,aAAa,CAAC+K,OAAO,EAAE,OAAO,CAAC;UAC9C,IAAI,CAACf,YAAY,CAAC,EAAE,CAAC;UACrB,IAAI,CAACgB,iBAAiB,EAAE,CAAC,WAAW,CAAC,EAAE;QACzC,CAAC,MAAM;UACL,IAAI,CAAC5F,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;QAC9C;MACF,CAAC,CAAC;IACJ;EACF;EAEAzJ,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC2J,SAAS,EAAE,EAAE;MACpB,OAAO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAACA,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC1B,IAAI,CAAC2B,KAAK,IAAG;QACxG,QAAQA,KAAK;UACX,KAAKvL,gBAAgB,CAACwL,GAAG;YAAI;YAC3B,IAAI,CAACnK,QAAQ,EAAE;YACf,OAAO,IAAI;UACb,KAAKrB,gBAAgB,CAACyL,EAAE;YAAK;YAC3B,IAAI,CAACxB,YAAY,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI;UACb,KAAKjK,gBAAgB,CAAC0L,MAAM;YAAE;YAC5B,OAAO,KAAK;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACzB,YAAY,CAAC,EAAE,CAAC;MACrB,OAAO,IAAI;IACb;EACF;EACA0B,mBAAmBA,CAACC,SAAS;IAC3B,OAAO,IAAI,CAAC/G,gBAAgB,CAAC+G,SAAS,CAAC;EACzC;EAEAC,eAAeA,CAACC,cAAqB;IACnC,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC1B;MACA,IAAI,CAAC3G,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC2G,QAAQ,CAAC,EAAE,CAAC;MACjD,IAAI,CAAC5G,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC2G,QAAQ,CAAC,EAAE,CAAC;MAC5C,IAAI,CAAC5G,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC2G,QAAQ,CAAC,EAAE,CAAC;IACnD,CAAC,MAAM;MACL,IAAIC,KAAK,GAAG,IAAI,CAACjI,WAAW,CAACkI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9J,KAAK,KAAK0J,cAAc,CAAC;MACxE,IAAI,CAAC3G,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC2G,QAAQ,CAACC,KAAK,CAAClD,UAAU,CAAC;MAC/D,IAAI,CAAC3D,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,CAAC2G,QAAQ,CAACC,KAAK,CAACjD,UAAU,CAAC;MAC/D,IAAI,CAAC5D,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC2G,QAAQ,CAACC,KAAK,CAAChD,KAAK,CAAC;IACvD;EACF;EACAoB,UAAUA,CAAA;IACR,MAAM+B,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAI,CAAE;IAC5B,IAAI,CAACxI,iBAAiB,CACnBiH,IAAI,CACH,wBAAwB,EACxBsB,KAAK,EACL,IAAI,CAAC1I,GAAG,CAACO,WAAW,CAAC,MAAM,CAAC,CAACY,EAAE,CAChC,CACAgF,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV;QACA,IAAI,CAAC/F,WAAW,GAAG8F,GAAG,CAACG,IAAI,CAACqC,GAAG,CAAEH,IAAI,KAAM;UACzC/J,KAAK,EAAE+J,IAAI,CAACI,OAAO,GAAG,GAAG,GAAGJ,IAAI,CAACK,OAAO,GAAG,GAAG,GAAGL,IAAI,CAACM,WAAW,GAAG,GAAG,GAAGN,IAAI,CAACO,WAAW;UAC1FrK,KAAK,EAAE8J,IAAI,CAACrD,KAAK;UACjBC,UAAU,EAAEoD,IAAI,CAACI,OAAO;UACxBtD,KAAK,EAAEkD,IAAI,CAACK,OAAO;UACnBxD,UAAU,EAAEmD,IAAI,CAACM;SAClB,CAAC,CAAC;MACL,CAAC,MAAI;QACH,IAAI,CAACnH,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAIAwB,YAAYA,CAAA;IACV,MAAMC,GAAG,GAAG,IAAI,CAACxH,QAAQ,CAACC,QAAQ;IAElC,MAAMwH,KAAK,GAAG,IAAI7M,iBAAiB,EAAE;IACrC;IACA6M,KAAK,CAACC,SAAS,GAAG3M,YAAY,CAACwJ,MAAM;IACrC;IACAkD,KAAK,CAACE,MAAM,CAACC,KAAK,GAAG,KAAK;IAC1BH,KAAK,CAACE,MAAM,CAACE,KAAK,GAAG,MAAM;IAC3BJ,KAAK,CAACE,MAAM,CAACG,GAAG,GAAG,MAAM;IACzBL,KAAK,CAACE,MAAM,CAACI,SAAS,GAAG;MACvBC,MAAM,EAAE;KACT;IACD;IACAP,KAAK,CAACE,MAAM,CAACM,YAAY,GAAG,KAAK;IACjC;IACAR,KAAK,CAACE,MAAM,CAAC9C,IAAI,GAAG;MAClBtH,EAAE,EAAEiK,GAAG,CAAC,IAAI;KACb;IAED,IAAI,CAACU,aAAa,CAAC/M,0BAA0B,EAAEsM,KAAK,CAAC,CAAChD,IAAI,CAACxH,KAAK,IAAG,CAEnE,CAAC,CAAC;EACJ;EAmCA;EACA6D,UAAUA,CAAA;IACR,IAAIqH,IAAI,GAAG,IAAI;IACf,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACxJ,QAAQ,CAAC,CAAC,CAAQ,CAAC;IAChDsJ,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACtI,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK,CAAC;IAC3DmL,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;IACtCF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAE,eAAe,CAAC;IACtDF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC;IAClDF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC;IAClDF,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;IACnDF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC;IACnDF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAE,eAAe,CAAC;IACrD,MAAMC,OAAO,GAAG,IAAInN,WAAW,CAAC;MAC9B,aAAa,EAAE,IAAI,CAAC+D;KACrB,CAAC;IACF,MAAMqJ,GAAG,GAAG,IAAInN,WAAW,CAAC,MAAM,EAAE,IAAI,CAACkE,aAAa,EAAE6I,QAAQ,EAAE;MAChEG,OAAO,EAAEA,OAAO;MAChBE,cAAc,EAAE,IAAI;MAAE;MACtBC,eAAe,EAAE,KAAK,CAAE;KACzB,CAAC;IACF,IAAI,CAACnK,IAAI,CACNoK,OAAO,CAACH,GAAG,CAAC,CACZI,IAAI,CAACpN,MAAM,CAAEqN,CAAC,IAAKA,CAAC,YAAYvN,YAAY,CAAC,CAAC,CAC9CwN,SAAS,CAAC;MACTC,IAAIA,CAACC,GAAG;QACNb,IAAI,CAACpJ,aAAa,GAAG,KAAK;QAC1BoJ,IAAI,CAACrJ,QAAQ,GAAG,EAAE;QAClB,IAAIkK,GAAG,CAAC,IAAI,CAAC,EAAE;UACb,IAAIA,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;YACrBb,IAAI,CAACjI,SAAS,CAACpF,aAAa,CAAC+K,OAAO,EAAE,QAAQ,CAAC;YAC/CsC,IAAI,CAACnD,WAAW,EAAE;UACpB,CAAC,MAAM;YACLmD,IAAI,CAACjI,SAAS,CACZpF,aAAa,CAACqF,KAAK,EACnB6I,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,QAAQ,CACjC;UACH;QACF,CAAC,MAAM;UACLb,IAAI,CAACjI,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE6I,GAAG,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC;QAC7D;MACF,CAAC;MACD7I,KAAKA,CAAC8I,GAAG;QACPd,IAAI,CAACpJ,aAAa,GAAG,KAAK;QAC1BoJ,IAAI,CAACjI,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE8I,GAAG,CAAClD,GAAG,CAAC;MAC9C;KACD,CAAC;EACN;EAEA;EACAmD,aAAaA,CAACC,SAAS;IACrBrJ,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACrB,IAAIqJ,SAAS,GAAG;MAAED,SAAS,EAAEA,SAAS;MAAEE,aAAa,EAAE,IAAI,CAAClK;IAAY,CAAE;IAC1E,IAAImK,WAAW,GACb,IAAI,CAAClK,aAAa,CAACmK,aAAa,EAAE,CAAC/J,SAAS,GAC5C,GAAG,GACH,IAAI,CAACJ,aAAa,CAACmK,aAAa,EAAE,CAAC1K,WAAW,CAAC,IAAI,CAAC,CAACY,EAAE,GACvD,kBAAkB,GAClB,+BAA+B,GAC/B0J,SAAS;IACXrJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuJ,WAAW,CAAC;IACnC;IACA,IAAIE,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC9CF,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;IAC7CH,OAAO,CAACG,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;IACxC,IAAIC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;IACvD;IACA,IAAIO,WAAW,GAAGN,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAChDK,WAAW,CAACJ,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC;IACjDI,WAAW,CAACJ,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAChD,IAAIK,YAAY,GAAGJ,cAAc,CAACE,WAAW,CAACC,WAAW,CAAC;IAC1DC,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC/CK,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3C;IACAK,YAAY,CAACL,YAAY,CAAC,QAAQ,EAAEL,WAAW,CAAC;IAChD;IACA,KAAK,IAAIW,QAAQ,IAAIb,SAAS,EAAE;MAC9B,IAAIc,YAAY,GAAGT,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAClDQ,YAAY,CAACP,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MAC3CO,YAAY,CAACP,YAAY,CAAC,MAAM,EAAEM,QAAQ,CAAC;MAC3CC,YAAY,CAACP,YAAY,CAAC,OAAO,EAAEP,SAAS,CAACa,QAAQ,CAAC,CAAC;MACvDD,YAAY,CAACF,WAAW,CAACI,YAAY,CAAC;IACxC;IACA;IACAF,YAAY,CAACG,MAAM,EAAE;EACvB;EAEAnF,WAAWA,CAAA;IACT,IAAI,CAAChG,WAAW,GAAG,IAAI;IACvB,IAAIoL,WAAW,GAAG;MAChB5J,IAAI,EAAE,IAAI,CAACR,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAChD,KAAK;MACxCyD,MAAM,EAAE,WAAW;MACnBC,gBAAgB,EAAE;KACnB;IACD,IAAI,CAAC1B,SAAS,CAACoL,SAAS,EAAE;IAC1B,IAAI,CAAC5L,iBAAiB,CACnBiH,IAAI,CAAC,mBAAmB,EAAE;MAAC,MAAM,EAAE0E;IAAW,CAAC,EAAE,IAAI,CAAClL,eAAe,CAAC,CACtEuF,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAAC1F,SAAS,CAACqL,SAAS,CAAC5F,GAAG,CAACG,IAAI,IAAI,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAAC3E,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,CACDwE,OAAO,CAAC,MAAK;MACZ,IAAI,CAACvL,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;EACN;EAEA;EACA1B,SAASA,CAACC,EAAE;IACV,IAAI,CAACkB,iBAAiB,CACnB+L,MAAM,CAAC,eAAe,GAAGjN,EAAE,EAAE,IAAI,CAAC2B,eAAe,CAAC,CAClDuF,IAAI,CAAEC,GAAsB,IAAI;MAC/B,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,IAAI,CAACzE,SAAS,CAACpF,aAAa,CAAC+K,OAAO,EAAE,MAAM,CAAC;QAC7C,IAAI,CAACb,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAC9E,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACN;EAEA;EACApI,cAAcA,CAACJ,EAAE;IACf,IAAI,CAACiB,aAAa,CAACiM,YAAY,CAAC,IAAI,CAACnM,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,EAAE,CAAClC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAACkH,IAAI,CAAEC,GAAsB,IAAI;MACjH,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,MAAM+F,WAAW,GAAGhG,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI6F,WAAW,EAAE;UACfC,MAAM,CAACC,IAAI,CAACF,WAAW,EAAE,QAAQ,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAACxK,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;EAEAtI,OAAOA,CAACoH,IAAI;IACV,IAAIgG,SAAS,GAAGhG,IAAI,CAAC,kBAAkB,CAAC;IACxC,MAAMiG,aAAa,GAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAChD,IAAI,CAACxM,aAAa,CAACiM,YAAY,CAAC,IAAI,CAACnM,GAAG,CAACO,WAAW,CAAC,KAAK,CAAC,CAACY,EAAE,EAAE,CAACoF,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAACJ,IAAI,CAAEC,GAAsB,IAAI;MACpI,IAAIA,GAAG,CAACC,EAAE,EAAE;QACV,MAAM+F,WAAW,GAAGhG,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI6F,WAAW,EAAE;UACf,IAAIO,UAAU,GAAG,IAAI,CAAC3M,GAAG,CAAC2M,UAAU,GAAGC,IAAI,CAACR,WAAW,CAAC;UACxDC,MAAM,CAACC,IAAI,CAACK,UAAU,EAAE,QAAQ,CAAC;QACnC,CAAC,MAAM;UACL,IAAI,CAAC/K,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAE,QAAQ,CAAC;QAC/C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,SAAS,CAACpF,aAAa,CAACqF,KAAK,EAAEuE,GAAG,CAACqB,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;EACJ;;;uBAncW5H,mBAAmB,EAAA1C,EAAA,CAAA0P,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA5P,EAAA,CAAA0P,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA9P,EAAA,CAAA0P,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAhQ,EAAA,CAAA0P,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAlQ,EAAA,CAAA0P,iBAAA,CAAAS,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAnB1N,mBAAmB;MAAA2N,SAAA;MAAAC,QAAA,GAAAtQ,EAAA,CAAAuQ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjB5B7Q,EAFJ,CAAAC,cAAA,iBAAwE,aAC9D,gBACiC;UACrCD,EAAA,CAAAqB,SAAA,kBAA2D;UAC3DrB,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAU,MAAA,+BAAI;UACzBV,EADyB,CAAAW,YAAA,EAAM,EACtB;UAMTX,EALA,CAAA+Q,UAAA,IAAAC,qCAAA,oBAA4E,IAAAC,qCAAA,oBAKD;UAG7EjR,EAAA,CAAAW,YAAA,EAAS;UAODX,EALR,CAAAC,cAAA,cAA+D,aAC7B,cAEgB,oBAC9B,wBACwB;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,uBAAiB;UAGfD,EAFA,CAAAqB,SAAA,iBAAwD,iBACA,iBACE;UAGhErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACnEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAGmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACjEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAAiH;UAGvHrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACtDX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAAgH;UAGtHrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,WAAG;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAClEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAA8G;UAGpHrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,0BAAG;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAClEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAG4D;UAGlErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,0BAAG;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAClEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAG4D;UAGlErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAaFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,kCAAM;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAC1DX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACtDX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAAmH;UAGzHrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,iBAAyE;UAG/ErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACjEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAI4D;UAGlErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACjEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAK4D;UAGlErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,oBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACtDX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,4BAK4D;UAGlErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,yBACmC;UAAAD,EAAA,CAAAU,MAAA,4CAAM;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACrEX,EAAA,CAAAC,cAAA,uBAAiB;UACfD,EAAA,CAAAqB,SAAA,0BAMiB;UAGvBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,eAAuB,oBACP,wBACwB;UAAAD,EAAA,CAAAU,MAAA,gCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,2BAMiB;UAGvBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,6CAAM;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAC1DX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,2BAMiB;UAGvBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,2BAMiB;UAGvBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,2BAOiB;UAGvBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAMFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAOFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,qBAAE;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACtDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,mCAAM;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAC1DX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,kBAAkH;UAGxHrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACxDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,kBAAqH;UAG3HrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAMFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,0BACmC;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACnEX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAMFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,2BAAG;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACvDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,uCAAK;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAEvDX,EADF,CAAAC,cAAA,wBAAiB,sBAC8F;UAE3GD,EADA,CAAAqB,SAAA,sBAA+C,sBACA;UAIvDrB,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,0BACoF;UAAAD,EAAA,CAAAU,MAAA,iCAAI;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACpHX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,6BAQmB;UAGzBrB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAuB,qBACP,yBACwB;UAAAD,EAAA,CAAAU,MAAA,2BAAG;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UACvDX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,kBAAsE;UAG5ErB,EAFI,CAAAW,YAAA,EAAkB,EACL,EACX;UAMFX,EAFJ,CAAAC,cAAA,gBAAwB,qBACR,0BACmC;UAAAD,EAAA,CAAAU,MAAA,6CAAM;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAEnEX,EADF,CAAAC,cAAA,wBAAiB,sBAGsC;UAA1CD,EAAA,CAAAE,UAAA,2BAAAgR,kEAAAC,MAAA;YAAAnR,EAAA,CAAAI,aAAA,CAAAgR,GAAA;YAAA,OAAApR,EAAA,CAAAQ,WAAA,CAAiBsQ,GAAA,CAAA7F,eAAA,CAAAkG,MAAA,CAAuB;UAAA,EAAC;UAClDnR,EAAA,CAAA+Q,UAAA,MAAAM,0CAAA,wBAAgG;UAKxGrR,EAHM,CAAAW,YAAA,EAAY,EACI,EACL,EACX;UAKFX,EAFJ,CAAAC,cAAA,gBAAwB,qBACR,yBACwB;UAAAD,EAAA,CAAAU,MAAA,KAA4B;;UAAAV,EAAA,CAAAW,YAAA,EAAgB;UAChFX,EAAA,CAAAC,cAAA,wBAAiB;UACfD,EAAA,CAAAqB,SAAA,qBACkF;;UAK5FrB,EAJQ,CAAAW,YAAA,EAAkB,EACL,EACX,EACF,EACD;UAKHX,EAFJ,CAAAC,cAAA,WAAI,gBACuB,gBAC8B;UAAAD,EAAA,CAAAqB,SAAA,iBAAyF;UAAArB,EAAA,CAAAU,MAAA,uCAAU;UAAAV,EAAA,CAAAW,YAAA,EAAM;UAC9JX,EAAA,CAAAC,cAAA,sBAAoG;UAAzFD,EAAA,CAAAsR,gBAAA,8BAAAC,qEAAAJ,MAAA;YAAAnR,EAAA,CAAAI,aAAA,CAAAgR,GAAA;YAAApR,EAAA,CAAAwR,kBAAA,CAAAV,GAAA,CAAAzN,QAAA,EAAA8N,MAAA,MAAAL,GAAA,CAAAzN,QAAA,GAAA8N,MAAA;YAAA,OAAAnR,EAAA,CAAAQ,WAAA,CAAA2Q,MAAA;UAAA,EAAyB;UAClCnR,EAAA,CAAAC,cAAA,mBAA6E;UAC3ED,EAAA,CAAAqB,SAAA,iBAAuD;UACvDrB,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAU,MAAA,qBAAE;UAIhBV,EAJgB,CAAAW,YAAA,EAAO,EACR,EACC,EACR,EACH;UAMDX,EALJ,CAAAC,cAAA,wBACiE,cACxD,WACH,eAEwF;UACxFD,EAAA,CAAAU,MAAA,uBACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,mCACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAWLX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,6BACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAU,MAAA,mCACF;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAELX,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAU,MAAA,uBACF;UAGFV,EAHE,CAAAW,YAAA,EAAK,EAEF,EACG;UACRX,EAAA,CAAAC,cAAA,cAAO;UACPD,EAAA,CAAA+Q,UAAA,MAAAU,mCAAA,mBAAsD;UAgC1DzR,EALI,CAAAW,YAAA,EAAQ,EACC,EAIH;;;;;;;UA1iByBX,EAAA,CAAAe,UAAA,gBAAAf,EAAA,CAAA0R,eAAA,MAAAC,GAAA,EAAoC;UAGvD3R,EAAA,CAAAc,SAAA,GAAqC;UAArCd,EAAA,CAAAe,UAAA,sCAAqC;UAGRf,EAAA,CAAAc,SAAA,GAAiC;UAAjCd,EAAA,CAAAe,UAAA,UAAA+P,GAAA,CAAA/F,mBAAA,QAAiC;UAKjC/K,EAAA,CAAAc,SAAA,EAAgC;UAAhCd,EAAA,CAAAe,UAAA,SAAA+P,GAAA,CAAA/F,mBAAA,QAAgC;UAKnC/K,EAAA,CAAAc,SAAA,EAAsB;UAAtBd,EAAA,CAAAe,UAAA,cAAA+P,GAAA,CAAAvM,QAAA,CAAsB;UAChDvE,EAAA,CAAAc,SAAA,EAAmB;UAAnBd,EAAA,CAAAe,UAAA,aAAAf,EAAA,CAAA0R,eAAA,MAAAE,GAAA,EAAmB;UAmBL5R,EAAA,CAAAc,SAAA,IAAyC;UACzCd,EADA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAW+BvE,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAUxC/K,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAUzC/K,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAUlE/K,EAAA,CAAAc,SAAA,GAAqC;UAG5Dd,EAHuB,CAAAe,UAAA,sCAAqC,6BAA6B,kCACxD,eAAA+P,GAAA,CAAA/F,mBAAA,QACQ,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAUCvE,EAAA,CAAAc,SAAA,GAAqC;UAG5Dd,EAHuB,CAAAe,UAAA,sCAAqC,+BAA+B,2CACjD,eAAA+P,GAAA,CAAA/F,mBAAA,QACD,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAoBtCvE,EAAA,CAAAc,SAAA,GAA+C;UAK/Cd,EALA,CAAAe,UAAA,gDAA+C,0DACU,eAAA+P,GAAA,CAAA/F,mBAAA,QAEhB,cAAA+F,GAAA,CAAAvM,QAAA,CACnB,cAAAvE,EAAA,CAAA0R,eAAA,MAAAG,GAAA,EACM;UAW2C7R,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAqB9F/K,EAAA,CAAAc,SAAA,IAAsC;UAGtCd,EAHA,CAAAe,UAAA,uCAAsC,wCACC,eAAA+P,GAAA,CAAA/F,mBAAA,QACE,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAWtBvE,EAAA,CAAAc,SAAA,GAAqB;UAIrBd,EAJA,CAAAe,UAAA,sBAAqB,0CACoB,2CACC,eAAA+P,GAAA,CAAA/F,mBAAA,QACD,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAWtBvE,EAAA,CAAAc,SAAA,GAAqB;UAIrBd,EAJA,CAAAe,UAAA,sBAAqB,0CACoB,2CACC,eAAA+P,GAAA,CAAA/F,mBAAA,QACD,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAatCvE,EAAA,CAAAc,SAAA,GAAyC;UAAzCd,EAAA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC;UAgBzC/K,EAAA,CAAAc,SAAA,GAAyC;UAAzCd,EAAA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC;UAgBzC/K,EAAA,CAAAc,SAAA,GAAyC;UAAzCd,EAAA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC;UAgBzC/K,EAAA,CAAAc,SAAA,GAAyC;UAAzCd,EAAA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC;UAgBzC/K,EAAA,CAAAc,SAAA,GAAyC;UAAzCd,EAAA,CAAAe,UAAA,eAAA+P,GAAA,CAAA/F,mBAAA,QAAyC;UAiBzC/K,EAAA,CAAAc,SAAA,GAAqC;UAKrCd,EALA,CAAAe,UAAA,sCAAqC,mCACH,gEAC6B,eAAA+P,GAAA,CAAA/F,mBAAA,QAEtB,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAatBvE,EAAA,CAAAc,SAAA,GAAqC;UAKrCd,EALA,CAAAe,UAAA,sCAAqC,sCACA,yEACmC,eAAA+P,GAAA,CAAA/F,mBAAA,QAE/B,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAatBvE,EAAA,CAAAc,SAAA,GAAqC;UAKrCd,EALA,CAAAe,UAAA,sCAAqC,iCACL,0DACyB,eAAA+P,GAAA,CAAA/F,mBAAA,QAEhB,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAetBvE,EAAA,CAAAc,SAAA,GAAgH;UAKhHd,EALA,CAAAe,UAAA,iHAAgH,kHACC,wBAE1F,eAAA+P,GAAA,CAAA/F,mBAAA,QACkB,cAAA+F,GAAA,CAAAvM,QAAA,CACnB;UAWgDvE,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAUpC/K,EAAA,CAAAc,SAAA,GAAuC;UAAvCd,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAahH/K,EAAA,CAAAc,SAAA,GAA+C;UAK/Cd,EALA,CAAAe,UAAA,gDAA+C,uDACO,eAAA+P,GAAA,CAAA/F,mBAAA,QAEb,cAAA+F,GAAA,CAAAvM,QAAA,CACnB,cAAAvE,EAAA,CAAA0R,eAAA,MAAAI,GAAA,EACU;UAchC9R,EAAA,CAAAc,SAAA,GAA+C;UAK/Cd,EALA,CAAAe,UAAA,gDAA+C,iDACC,eAAA+P,GAAA,CAAA/F,mBAAA,QAEP,cAAA+F,GAAA,CAAAvM,QAAA,CACnB,cAAAvE,EAAA,CAAA0R,eAAA,MAAAK,GAAA,EACU;UAWS/R,EAAA,CAAAc,SAAA,GAAuB;UAACd,EAAxB,CAAAe,UAAA,uCAAuB,eAAA+P,GAAA,CAAA/F,mBAAA,QAA0C;UAW/F/K,EAAA,CAAAc,SAAA,GAA2D;UAA3Dd,EAAA,CAAAe,UAAA,iBAAAiR,QAAA,GAAAlB,GAAA,CAAAvM,QAAA,CAAAwE,GAAA,oCAAAiJ,QAAA,CAAAxQ,KAAA,UAA2D;UAItExB,EAAA,CAAAc,SAAA,GAA4C;UAK5Cd,EALA,CAAAe,UAAA,6CAA4C,sFACyC,cAAA+P,GAAA,CAAAvM,QAAA,CAE/D,cAAAvE,EAAA,CAAAiS,eAAA,MAAAC,GAAA,GAAAC,QAAA,GAAArB,GAAA,CAAAvM,QAAA,CAAAwE,GAAA,4BAAAoJ,QAAA,CAAA3Q,KAAA,EAC4C,iBAAA4Q,QAAA,GAAAtB,GAAA,CAAAvM,QAAA,CAAAwE,GAAA,oCAAAqJ,QAAA,CAAA5Q,KAAA,aAAAsP,GAAA,CAAA/F,mBAAA,QACuB;UAsBxD/K,EAAA,CAAAc,SAAA,IAAuB;UAC/Cd,EADwB,CAAAe,UAAA,uCAAuB,sBAAsB,eAAA+P,GAAA,CAAA/F,mBAAA,QAC5B;UAEpB/K,EAAA,CAAAc,SAAA,EAAc;UAAdd,EAAA,CAAAe,UAAA,YAAA+P,GAAA,CAAA3N,WAAA,CAAc;UAUZnD,EAAA,CAAAc,SAAA,GAA4B;UAA5Bd,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAkB,WAAA,yBAA4B;UAE3ClB,EAAA,CAAAc,SAAA,GAA0C;UAA1Cd,EAAA,CAAAqS,qBAAA,gBAAArS,EAAA,CAAAkB,WAAA,yBAA0C;UAAClB,EAAA,CAAAe,UAAA,aAAA+P,GAAA,CAAA/F,mBAAA,QAAuC;UAYhG/K,EAAA,CAAAc,SAAA,GAAyB;UAAzBd,EAAA,CAAAsS,gBAAA,eAAAxB,GAAA,CAAAzN,QAAA,CAAyB;UAAqCrD,EAApC,CAAAe,UAAA,mBAAA+P,GAAA,CAAA3M,gBAAA,CAAmC,2BAA2B;UAChDnE,EAAA,CAAAc,SAAA,EAA2B;UAA3Bd,EAAA,CAAAe,UAAA,cAAA+P,GAAA,CAAAxN,aAAA,CAA2B;UAO9DtD,EAAA,CAAAc,SAAA,GAA2B;UACRd,EADnB,CAAAe,UAAA,4BAA2B,WAAA+P,GAAA,CAAAtN,SAAA,CAAA+O,QAAA,GAAgC,aAAAvS,EAAA,CAAA0R,eAAA,MAAAc,GAAA,EACzC,cAAA1B,GAAA,CAAAvN,WAAA,CAA0B;UAIxDvD,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAIpBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAapBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAIpBf,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAe,UAAA,qBAAoB;UAWLf,EAAA,CAAAc,SAAA,GAAkB;UAAlBd,EAAA,CAAAe,UAAA,YAAA0R,WAAA,CAAArJ,IAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}