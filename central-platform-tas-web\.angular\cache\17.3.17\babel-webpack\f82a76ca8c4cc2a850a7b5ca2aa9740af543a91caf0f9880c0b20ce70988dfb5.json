{"ast": null, "code": "/* eslint-disable no-bitwise, no-mixed-operators, no-use-before-define, max-len */\nconst {\n  BigInteger,\n  SecureRandom\n} = require('jsbn');\nconst {\n  ECCurveFp\n} = require('./ec');\nconst rng = new SecureRandom();\nconst {\n  curve,\n  G,\n  n\n} = generateEcparam();\n\n/**\n * 获取公共椭圆曲线\n */\nfunction getGlobalCurve() {\n  return curve;\n}\n\n/**\n * 生成ecparam\n */\nfunction generateEcparam() {\n  // 椭圆曲线\n  const p = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF', 16);\n  const a = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC', 16);\n  const b = new BigInteger('28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93', 16);\n  const curve = new ECCurveFp(p, a, b);\n\n  // 基点\n  const gxHex = '32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7';\n  const gyHex = 'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0';\n  const G = curve.decodePointHex('04' + gxHex + gyHex);\n  const n = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123', 16);\n  return {\n    curve,\n    G,\n    n\n  };\n}\n\n/**\n * 生成密钥对：publicKey = privateKey * G\n */\nfunction generateKeyPairHex(a, b, c) {\n  const random = a ? new BigInteger(a, b, c) : new BigInteger(n.bitLength(), rng);\n  const d = random.mod(n.subtract(BigInteger.ONE)).add(BigInteger.ONE); // 随机数\n  const privateKey = leftPad(d.toString(16), 64);\n  const P = G.multiply(d); // P = dG，p 为公钥，d 为私钥\n  const Px = leftPad(P.getX().toBigInteger().toString(16), 64);\n  const Py = leftPad(P.getY().toBigInteger().toString(16), 64);\n  const publicKey = '04' + Px + Py;\n  return {\n    privateKey,\n    publicKey\n  };\n}\n\n/**\n * 生成压缩公钥\n */\nfunction compressPublicKeyHex(s) {\n  if (s.length !== 130) throw new Error('Invalid public key to compress');\n  const len = (s.length - 2) / 2;\n  const xHex = s.substr(2, len);\n  const y = new BigInteger(s.substr(len + 2, len), 16);\n  let prefix = '03';\n  if (y.mod(new BigInteger('2')).equals(BigInteger.ZERO)) prefix = '02';\n  return prefix + xHex;\n}\n\n/**\n * utf8串转16进制串\n */\nfunction utf8ToHex(input) {\n  input = unescape(encodeURIComponent(input));\n  const length = input.length;\n\n  // 转换到字数组\n  const words = [];\n  for (let i = 0; i < length; i++) {\n    words[i >>> 2] |= (input.charCodeAt(i) & 0xff) << 24 - i % 4 * 8;\n  }\n\n  // 转换到16进制\n  const hexChars = [];\n  for (let i = 0; i < length; i++) {\n    const bite = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n    hexChars.push((bite >>> 4).toString(16));\n    hexChars.push((bite & 0x0f).toString(16));\n  }\n  return hexChars.join('');\n}\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input;\n  return new Array(num - input.length + 1).join('0') + input;\n}\n\n/**\n * 转成16进制串\n */\nfunction arrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16);\n    return item.length === 1 ? '0' + item : item;\n  }).join('');\n}\n\n/**\n * 转成utf8串\n */\nfunction arrayToUtf8(arr) {\n  const words = [];\n  let j = 0;\n  for (let i = 0; i < arr.length * 2; i += 2) {\n    words[i >>> 3] |= parseInt(arr[j], 10) << 24 - i % 8 * 4;\n    j++;\n  }\n  try {\n    const latin1Chars = [];\n    for (let i = 0; i < arr.length; i++) {\n      const bite = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n      latin1Chars.push(String.fromCharCode(bite));\n    }\n    return decodeURIComponent(escape(latin1Chars.join('')));\n  } catch (e) {\n    throw new Error('Malformed UTF-8 data');\n  }\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = [];\n  let hexStrLength = hexStr.length;\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1);\n  }\n  hexStrLength = hexStr.length;\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16));\n  }\n  return words;\n}\n\n/**\n * 验证公钥是否为椭圆曲线上的点\n */\nfunction verifyPublicKey(publicKey) {\n  const point = curve.decodePointHex(publicKey);\n  if (!point) return false;\n  const x = point.getX();\n  const y = point.getY();\n\n  // 验证 y^2 是否等于 x^3 + ax + b\n  return y.square().equals(x.multiply(x.square()).add(x.multiply(curve.a)).add(curve.b));\n}\n\n/**\n * 验证公钥是否等价，等价返回true\n */\nfunction comparePublicKeyHex(publicKey1, publicKey2) {\n  const point1 = curve.decodePointHex(publicKey1);\n  if (!point1) return false;\n  const point2 = curve.decodePointHex(publicKey2);\n  if (!point2) return false;\n  return point1.equals(point2);\n}\nmodule.exports = {\n  getGlobalCurve,\n  generateEcparam,\n  generateKeyPairHex,\n  compressPublicKeyHex,\n  utf8ToHex,\n  leftPad,\n  arrayToHex,\n  arrayToUtf8,\n  hexToArray,\n  verifyPublicKey,\n  comparePublicKeyHex\n};", "map": {"version": 3, "names": ["BigInteger", "SecureRandom", "require", "ECCurveFp", "rng", "curve", "G", "n", "generateEcparam", "getGlobalCurve", "p", "a", "b", "gxHex", "gyHex", "decodePointHex", "generateKeyPairHex", "c", "random", "bitLength", "d", "mod", "subtract", "ONE", "add", "privateKey", "leftPad", "toString", "P", "multiply", "Px", "getX", "toBigInteger", "P<PERSON>", "getY", "public<PERSON>ey", "compressPublicKeyHex", "s", "length", "Error", "len", "xHex", "substr", "y", "prefix", "equals", "ZERO", "utf8ToHex", "input", "unescape", "encodeURIComponent", "words", "i", "charCodeAt", "hexChars", "bite", "push", "join", "num", "Array", "arrayToHex", "arr", "map", "item", "arrayToUtf8", "j", "parseInt", "latin1Chars", "String", "fromCharCode", "decodeURIComponent", "escape", "e", "hexToArray", "hexStr", "hexStr<PERSON>ength", "verifyPublicKey", "point", "x", "square", "comparePublicKeyHex", "publicKey1", "publicKey2", "point1", "point2", "module", "exports"], "sources": ["G:/web/central-platform-tas-web/node_modules/sm-crypto/src/sm2/utils.js"], "sourcesContent": ["/* eslint-disable no-bitwise, no-mixed-operators, no-use-before-define, max-len */\nconst {BigInteger, SecureRandom} = require('jsbn')\nconst {ECCurveFp} = require('./ec')\n\nconst rng = new SecureRandom()\nconst {curve, G, n} = generateEcparam()\n\n/**\n * 获取公共椭圆曲线\n */\nfunction getGlobalCurve() {\n  return curve\n}\n\n/**\n * 生成ecparam\n */\nfunction generateEcparam() {\n  // 椭圆曲线\n  const p = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF', 16)\n  const a = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC', 16)\n  const b = new BigInteger('28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93', 16)\n  const curve = new ECCurveFp(p, a, b)\n\n  // 基点\n  const gxHex = '32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7'\n  const gyHex = 'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0'\n  const G = curve.decodePointHex('04' + gxHex + gyHex)\n\n  const n = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123', 16)\n\n  return {curve, G, n}\n}\n\n/**\n * 生成密钥对：publicKey = privateKey * G\n */\nfunction generateKeyPairHex(a, b, c) {\n  const random = a ? new BigInteger(a, b, c) : new BigInteger(n.bitLength(), rng)\n  const d = random.mod(n.subtract(BigInteger.ONE)).add(BigInteger.ONE) // 随机数\n  const privateKey = leftPad(d.toString(16), 64)\n\n  const P = G.multiply(d) // P = dG，p 为公钥，d 为私钥\n  const Px = leftPad(P.getX().toBigInteger().toString(16), 64)\n  const Py = leftPad(P.getY().toBigInteger().toString(16), 64)\n  const publicKey = '04' + Px + Py\n\n  return {privateKey, publicKey}\n}\n\n/**\n * 生成压缩公钥\n */\nfunction compressPublicKeyHex(s) {\n  if (s.length !== 130) throw new Error('Invalid public key to compress')\n\n  const len = (s.length - 2) / 2\n  const xHex = s.substr(2, len)\n  const y = new BigInteger(s.substr(len + 2, len), 16)\n\n  let prefix = '03'\n  if (y.mod(new BigInteger('2')).equals(BigInteger.ZERO)) prefix = '02'\n\n  return prefix + xHex\n}\n\n/**\n * utf8串转16进制串\n */\nfunction utf8ToHex(input) {\n  input = unescape(encodeURIComponent(input))\n\n  const length = input.length\n\n  // 转换到字数组\n  const words = []\n  for (let i = 0; i < length; i++) {\n    words[i >>> 2] |= (input.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8)\n  }\n\n  // 转换到16进制\n  const hexChars = []\n  for (let i = 0; i < length; i++) {\n    const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\n    hexChars.push((bite >>> 4).toString(16))\n    hexChars.push((bite & 0x0f).toString(16))\n  }\n\n  return hexChars.join('')\n}\n\n/**\n * 补全16进制字符串\n */\nfunction leftPad(input, num) {\n  if (input.length >= num) return input\n\n  return (new Array(num - input.length + 1)).join('0') + input\n}\n\n/**\n * 转成16进制串\n */\nfunction arrayToHex(arr) {\n  return arr.map(item => {\n    item = item.toString(16)\n    return item.length === 1 ? '0' + item : item\n  }).join('')\n}\n\n/**\n * 转成utf8串\n */\nfunction arrayToUtf8(arr) {\n  const words = []\n  let j = 0\n  for (let i = 0; i < arr.length * 2; i += 2) {\n    words[i >>> 3] |= parseInt(arr[j], 10) << (24 - (i % 8) * 4)\n    j++\n  }\n\n  try {\n    const latin1Chars = []\n\n    for (let i = 0; i < arr.length; i++) {\n      const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff\n      latin1Chars.push(String.fromCharCode(bite))\n    }\n\n    return decodeURIComponent(escape(latin1Chars.join('')))\n  } catch (e) {\n    throw new Error('Malformed UTF-8 data')\n  }\n}\n\n/**\n * 转成字节数组\n */\nfunction hexToArray(hexStr) {\n  const words = []\n  let hexStrLength = hexStr.length\n\n  if (hexStrLength % 2 !== 0) {\n    hexStr = leftPad(hexStr, hexStrLength + 1)\n  }\n\n  hexStrLength = hexStr.length\n\n  for (let i = 0; i < hexStrLength; i += 2) {\n    words.push(parseInt(hexStr.substr(i, 2), 16))\n  }\n  return words\n}\n\n/**\n * 验证公钥是否为椭圆曲线上的点\n */\nfunction verifyPublicKey(publicKey) {\n  const point = curve.decodePointHex(publicKey)\n  if (!point) return false\n\n  const x = point.getX()\n  const y = point.getY()\n\n  // 验证 y^2 是否等于 x^3 + ax + b\n  return y.square().equals(x.multiply(x.square()).add(x.multiply(curve.a)).add(curve.b))\n}\n\n/**\n * 验证公钥是否等价，等价返回true\n */\nfunction comparePublicKeyHex(publicKey1, publicKey2) {\n  const point1 = curve.decodePointHex(publicKey1)\n  if (!point1) return false\n\n  const point2 = curve.decodePointHex(publicKey2)\n  if (!point2) return false\n\n  return point1.equals(point2)\n}\n\nmodule.exports = {\n  getGlobalCurve,\n  generateEcparam,\n  generateKeyPairHex,\n  compressPublicKeyHex,\n  utf8ToHex,\n  leftPad,\n  arrayToHex,\n  arrayToUtf8,\n  hexToArray,\n  verifyPublicKey,\n  comparePublicKeyHex,\n}\n"], "mappings": "AAAA;AACA,MAAM;EAACA,UAAU;EAAEC;AAAY,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AAClD,MAAM;EAACC;AAAS,CAAC,GAAGD,OAAO,CAAC,MAAM,CAAC;AAEnC,MAAME,GAAG,GAAG,IAAIH,YAAY,CAAC,CAAC;AAC9B,MAAM;EAACI,KAAK;EAAEC,CAAC;EAAEC;AAAC,CAAC,GAAGC,eAAe,CAAC,CAAC;;AAEvC;AACA;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACxB,OAAOJ,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASG,eAAeA,CAAA,EAAG;EACzB;EACA,MAAME,CAAC,GAAG,IAAIV,UAAU,CAAC,kEAAkE,EAAE,EAAE,CAAC;EAChG,MAAMW,CAAC,GAAG,IAAIX,UAAU,CAAC,kEAAkE,EAAE,EAAE,CAAC;EAChG,MAAMY,CAAC,GAAG,IAAIZ,UAAU,CAAC,kEAAkE,EAAE,EAAE,CAAC;EAChG,MAAMK,KAAK,GAAG,IAAIF,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;;EAEpC;EACA,MAAMC,KAAK,GAAG,kEAAkE;EAChF,MAAMC,KAAK,GAAG,kEAAkE;EAChF,MAAMR,CAAC,GAAGD,KAAK,CAACU,cAAc,CAAC,IAAI,GAAGF,KAAK,GAAGC,KAAK,CAAC;EAEpD,MAAMP,CAAC,GAAG,IAAIP,UAAU,CAAC,kEAAkE,EAAE,EAAE,CAAC;EAEhG,OAAO;IAACK,KAAK;IAAEC,CAAC;IAAEC;EAAC,CAAC;AACtB;;AAEA;AACA;AACA;AACA,SAASS,kBAAkBA,CAACL,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAE;EACnC,MAAMC,MAAM,GAAGP,CAAC,GAAG,IAAIX,UAAU,CAACW,CAAC,EAAEC,CAAC,EAAEK,CAAC,CAAC,GAAG,IAAIjB,UAAU,CAACO,CAAC,CAACY,SAAS,CAAC,CAAC,EAAEf,GAAG,CAAC;EAC/E,MAAMgB,CAAC,GAAGF,MAAM,CAACG,GAAG,CAACd,CAAC,CAACe,QAAQ,CAACtB,UAAU,CAACuB,GAAG,CAAC,CAAC,CAACC,GAAG,CAACxB,UAAU,CAACuB,GAAG,CAAC,EAAC;EACrE,MAAME,UAAU,GAAGC,OAAO,CAACN,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAE9C,MAAMC,CAAC,GAAGtB,CAAC,CAACuB,QAAQ,CAACT,CAAC,CAAC,EAAC;EACxB,MAAMU,EAAE,GAAGJ,OAAO,CAACE,CAAC,CAACG,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACL,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC5D,MAAMM,EAAE,GAAGP,OAAO,CAACE,CAAC,CAACM,IAAI,CAAC,CAAC,CAACF,YAAY,CAAC,CAAC,CAACL,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EAC5D,MAAMQ,SAAS,GAAG,IAAI,GAAGL,EAAE,GAAGG,EAAE;EAEhC,OAAO;IAACR,UAAU;IAAEU;EAAS,CAAC;AAChC;;AAEA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,CAAC,EAAE;EAC/B,IAAIA,CAAC,CAACC,MAAM,KAAK,GAAG,EAAE,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EAEvE,MAAMC,GAAG,GAAG,CAACH,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC;EAC9B,MAAMG,IAAI,GAAGJ,CAAC,CAACK,MAAM,CAAC,CAAC,EAAEF,GAAG,CAAC;EAC7B,MAAMG,CAAC,GAAG,IAAI3C,UAAU,CAACqC,CAAC,CAACK,MAAM,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,CAAC,EAAE,EAAE,CAAC;EAEpD,IAAII,MAAM,GAAG,IAAI;EACjB,IAAID,CAAC,CAACtB,GAAG,CAAC,IAAIrB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC6C,MAAM,CAAC7C,UAAU,CAAC8C,IAAI,CAAC,EAAEF,MAAM,GAAG,IAAI;EAErE,OAAOA,MAAM,GAAGH,IAAI;AACtB;;AAEA;AACA;AACA;AACA,SAASM,SAASA,CAACC,KAAK,EAAE;EACxBA,KAAK,GAAGC,QAAQ,CAACC,kBAAkB,CAACF,KAAK,CAAC,CAAC;EAE3C,MAAMV,MAAM,GAAGU,KAAK,CAACV,MAAM;;EAE3B;EACA,MAAMa,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,EAAEc,CAAC,EAAE,EAAE;IAC/BD,KAAK,CAACC,CAAC,KAAK,CAAC,CAAC,IAAI,CAACJ,KAAK,CAACK,UAAU,CAACD,CAAC,CAAC,GAAG,IAAI,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE;EACtE;;EAEA;EACA,MAAME,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,EAAEc,CAAC,EAAE,EAAE;IAC/B,MAAMG,IAAI,GAAIJ,KAAK,CAACC,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,IAAI;IAC3DE,QAAQ,CAACE,IAAI,CAAC,CAACD,IAAI,KAAK,CAAC,EAAE5B,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxC2B,QAAQ,CAACE,IAAI,CAAC,CAACD,IAAI,GAAG,IAAI,EAAE5B,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC3C;EAEA,OAAO2B,QAAQ,CAACG,IAAI,CAAC,EAAE,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,SAAS/B,OAAOA,CAACsB,KAAK,EAAEU,GAAG,EAAE;EAC3B,IAAIV,KAAK,CAACV,MAAM,IAAIoB,GAAG,EAAE,OAAOV,KAAK;EAErC,OAAQ,IAAIW,KAAK,CAACD,GAAG,GAAGV,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC,CAAEmB,IAAI,CAAC,GAAG,CAAC,GAAGT,KAAK;AAC9D;;AAEA;AACA;AACA;AACA,SAASY,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,GAAG,CAACC,IAAI,IAAI;IACrBA,IAAI,GAAGA,IAAI,CAACpC,QAAQ,CAAC,EAAE,CAAC;IACxB,OAAOoC,IAAI,CAACzB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGyB,IAAI,GAAGA,IAAI;EAC9C,CAAC,CAAC,CAACN,IAAI,CAAC,EAAE,CAAC;AACb;;AAEA;AACA;AACA;AACA,SAASO,WAAWA,CAACH,GAAG,EAAE;EACxB,MAAMV,KAAK,GAAG,EAAE;EAChB,IAAIc,CAAC,GAAG,CAAC;EACT,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,CAACvB,MAAM,GAAG,CAAC,EAAEc,CAAC,IAAI,CAAC,EAAE;IAC1CD,KAAK,CAACC,CAAC,KAAK,CAAC,CAAC,IAAIc,QAAQ,CAACL,GAAG,CAACI,CAAC,CAAC,EAAE,EAAE,CAAC,IAAK,EAAE,GAAIb,CAAC,GAAG,CAAC,GAAI,CAAE;IAC5Da,CAAC,EAAE;EACL;EAEA,IAAI;IACF,MAAME,WAAW,GAAG,EAAE;IAEtB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,CAACvB,MAAM,EAAEc,CAAC,EAAE,EAAE;MACnC,MAAMG,IAAI,GAAIJ,KAAK,CAACC,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,IAAI;MAC3De,WAAW,CAACX,IAAI,CAACY,MAAM,CAACC,YAAY,CAACd,IAAI,CAAC,CAAC;IAC7C;IAEA,OAAOe,kBAAkB,CAACC,MAAM,CAACJ,WAAW,CAACV,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACzD,CAAC,CAAC,OAAOe,CAAC,EAAE;IACV,MAAM,IAAIjC,KAAK,CAAC,sBAAsB,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AACA,SAASkC,UAAUA,CAACC,MAAM,EAAE;EAC1B,MAAMvB,KAAK,GAAG,EAAE;EAChB,IAAIwB,YAAY,GAAGD,MAAM,CAACpC,MAAM;EAEhC,IAAIqC,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;IAC1BD,MAAM,GAAGhD,OAAO,CAACgD,MAAM,EAAEC,YAAY,GAAG,CAAC,CAAC;EAC5C;EAEAA,YAAY,GAAGD,MAAM,CAACpC,MAAM;EAE5B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,YAAY,EAAEvB,CAAC,IAAI,CAAC,EAAE;IACxCD,KAAK,CAACK,IAAI,CAACU,QAAQ,CAACQ,MAAM,CAAChC,MAAM,CAACU,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOD,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASyB,eAAeA,CAACzC,SAAS,EAAE;EAClC,MAAM0C,KAAK,GAAGxE,KAAK,CAACU,cAAc,CAACoB,SAAS,CAAC;EAC7C,IAAI,CAAC0C,KAAK,EAAE,OAAO,KAAK;EAExB,MAAMC,CAAC,GAAGD,KAAK,CAAC9C,IAAI,CAAC,CAAC;EACtB,MAAMY,CAAC,GAAGkC,KAAK,CAAC3C,IAAI,CAAC,CAAC;;EAEtB;EACA,OAAOS,CAAC,CAACoC,MAAM,CAAC,CAAC,CAAClC,MAAM,CAACiC,CAAC,CAACjD,QAAQ,CAACiD,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACvD,GAAG,CAACsD,CAAC,CAACjD,QAAQ,CAACxB,KAAK,CAACM,CAAC,CAAC,CAAC,CAACa,GAAG,CAACnB,KAAK,CAACO,CAAC,CAAC,CAAC;AACxF;;AAEA;AACA;AACA;AACA,SAASoE,mBAAmBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACnD,MAAMC,MAAM,GAAG9E,KAAK,CAACU,cAAc,CAACkE,UAAU,CAAC;EAC/C,IAAI,CAACE,MAAM,EAAE,OAAO,KAAK;EAEzB,MAAMC,MAAM,GAAG/E,KAAK,CAACU,cAAc,CAACmE,UAAU,CAAC;EAC/C,IAAI,CAACE,MAAM,EAAE,OAAO,KAAK;EAEzB,OAAOD,MAAM,CAACtC,MAAM,CAACuC,MAAM,CAAC;AAC9B;AAEAC,MAAM,CAACC,OAAO,GAAG;EACf7E,cAAc;EACdD,eAAe;EACfQ,kBAAkB;EAClBoB,oBAAoB;EACpBW,SAAS;EACTrB,OAAO;EACPkC,UAAU;EACVI,WAAW;EACXS,UAAU;EACVG,eAAe;EACfI;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}