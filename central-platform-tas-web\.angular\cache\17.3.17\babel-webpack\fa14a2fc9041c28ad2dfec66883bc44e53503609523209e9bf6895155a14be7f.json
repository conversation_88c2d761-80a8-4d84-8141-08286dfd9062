{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'dưới 1 giây',\n    other: 'dưới {{count}} giây'\n  },\n  xSeconds: {\n    one: '1 giây',\n    other: '{{count}} giây'\n  },\n  halfAMinute: 'nửa phút',\n  lessThanXMinutes: {\n    one: 'dưới 1 phút',\n    other: 'dưới {{count}} phút'\n  },\n  xMinutes: {\n    one: '1 phút',\n    other: '{{count}} phút'\n  },\n  aboutXHours: {\n    one: 'khoảng 1 giờ',\n    other: 'khoảng {{count}} giờ'\n  },\n  xHours: {\n    one: '1 giờ',\n    other: '{{count}} giờ'\n  },\n  xDays: {\n    one: '1 ngày',\n    other: '{{count}} ngày'\n  },\n  aboutXWeeks: {\n    one: 'khoảng 1 tuần',\n    other: 'khoảng {{count}} tuần'\n  },\n  xWeeks: {\n    one: '1 tuần',\n    other: '{{count}} tuần'\n  },\n  aboutXMonths: {\n    one: 'khoảng 1 tháng',\n    other: 'khoảng {{count}} tháng'\n  },\n  xMonths: {\n    one: '1 tháng',\n    other: '{{count}} tháng'\n  },\n  aboutXYears: {\n    one: 'khoảng 1 năm',\n    other: 'khoảng {{count}} năm'\n  },\n  xYears: {\n    one: '1 năm',\n    other: '{{count}} năm'\n  },\n  overXYears: {\n    one: 'hơn 1 năm',\n    other: 'hơn {{count}} năm'\n  },\n  almostXYears: {\n    one: 'gần 1 năm',\n    other: 'gần {{count}} năm'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' nữa';\n    } else {\n      return result + ' trước';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/vi/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'dưới 1 giây',\n    other: 'dưới {{count}} giây'\n  },\n  xSeconds: {\n    one: '1 giây',\n    other: '{{count}} giây'\n  },\n  halfAMinute: 'nửa phút',\n  lessThanXMinutes: {\n    one: 'dưới 1 phút',\n    other: 'dưới {{count}} phút'\n  },\n  xMinutes: {\n    one: '1 phút',\n    other: '{{count}} phút'\n  },\n  aboutXHours: {\n    one: 'khoảng 1 giờ',\n    other: 'khoảng {{count}} giờ'\n  },\n  xHours: {\n    one: '1 giờ',\n    other: '{{count}} giờ'\n  },\n  xDays: {\n    one: '1 ngày',\n    other: '{{count}} ngày'\n  },\n  aboutXWeeks: {\n    one: 'khoảng 1 tuần',\n    other: 'khoảng {{count}} tuần'\n  },\n  xWeeks: {\n    one: '1 tuần',\n    other: '{{count}} tuần'\n  },\n  aboutXMonths: {\n    one: 'khoảng 1 tháng',\n    other: 'khoảng {{count}} tháng'\n  },\n  xMonths: {\n    one: '1 tháng',\n    other: '{{count}} tháng'\n  },\n  aboutXYears: {\n    one: 'khoảng 1 năm',\n    other: 'khoảng {{count}} năm'\n  },\n  xYears: {\n    one: '1 năm',\n    other: '{{count}} năm'\n  },\n  overXYears: {\n    one: 'hơn 1 năm',\n    other: 'hơn {{count}} năm'\n  },\n  almostXYears: {\n    one: 'gần 1 năm',\n    other: 'gần {{count}} năm'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' nữa';\n    } else {\n      return result + ' trước';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}