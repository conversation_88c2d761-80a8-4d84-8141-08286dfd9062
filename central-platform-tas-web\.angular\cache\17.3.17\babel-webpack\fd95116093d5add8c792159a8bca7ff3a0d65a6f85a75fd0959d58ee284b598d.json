{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: 'mniej niż sekunda',\n      past: 'mniej niż sekundę',\n      future: 'mniej niż sekundę'\n    },\n    twoFour: 'mniej niż {{count}} sekundy',\n    other: 'mniej niż {{count}} sekund'\n  },\n  xSeconds: {\n    one: {\n      regular: 'sekunda',\n      past: 'sekundę',\n      future: 'sekundę'\n    },\n    twoFour: '{{count}} sekundy',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: {\n    one: 'pół minuty',\n    twoFour: 'pół minuty',\n    other: 'pół minuty'\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: 'mniej niż minuta',\n      past: 'mniej niż minutę',\n      future: 'mniej niż minutę'\n    },\n    twoFour: 'mniej niż {{count}} minuty',\n    other: 'mniej niż {{count}} minut'\n  },\n  xMinutes: {\n    one: {\n      regular: 'minuta',\n      past: 'minutę',\n      future: 'minutę'\n    },\n    twoFour: '{{count}} minuty',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: {\n      regular: 'około godziny',\n      past: 'około godziny',\n      future: 'około godzinę'\n    },\n    twoFour: 'około {{count}} godziny',\n    other: 'około {{count}} godzin'\n  },\n  xHours: {\n    one: {\n      regular: 'godzina',\n      past: 'godzinę',\n      future: 'godzinę'\n    },\n    twoFour: '{{count}} godziny',\n    other: '{{count}} godzin'\n  },\n  xDays: {\n    one: {\n      regular: 'dzień',\n      past: 'dzień',\n      future: '1 dzień'\n    },\n    twoFour: '{{count}} dni',\n    other: '{{count}} dni'\n  },\n  aboutXWeeks: {\n    one: 'około tygodnia',\n    twoFour: 'około {{count}} tygodni',\n    other: 'około {{count}} tygodni'\n  },\n  xWeeks: {\n    one: 'tydzień',\n    twoFour: '{{count}} tygodnie',\n    other: '{{count}} tygodni'\n  },\n  aboutXMonths: {\n    one: 'około miesiąc',\n    twoFour: 'około {{count}} miesiące',\n    other: 'około {{count}} miesięcy'\n  },\n  xMonths: {\n    one: 'miesiąc',\n    twoFour: '{{count}} miesiące',\n    other: '{{count}} miesięcy'\n  },\n  aboutXYears: {\n    one: 'około rok',\n    twoFour: 'około {{count}} lata',\n    other: 'około {{count}} lat'\n  },\n  xYears: {\n    one: 'rok',\n    twoFour: '{{count}} lata',\n    other: '{{count}} lat'\n  },\n  overXYears: {\n    one: 'ponad rok',\n    twoFour: 'ponad {{count}} lata',\n    other: 'ponad {{count}} lat'\n  },\n  almostXYears: {\n    one: 'prawie rok',\n    twoFour: 'prawie {{count}} lata',\n    other: 'prawie {{count}} lat'\n  }\n};\nfunction declensionGroup(scheme, count) {\n  if (count === 1) {\n    return scheme.one;\n  }\n  var rem100 = count % 100;\n\n  // ends with 11-20\n  if (rem100 <= 20 && rem100 > 10) {\n    return scheme.other;\n  }\n  var rem10 = rem100 % 10;\n\n  // ends with 2, 3, 4\n  if (rem10 >= 2 && rem10 <= 4) {\n    return scheme.twoFour;\n  }\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  var group = declensionGroup(scheme, count);\n  var finalText = typeof group === 'string' ? group : group[time];\n  return finalText.replace('{{count}}', String(count));\n}\nvar formatDistance = function formatDistance(token, count, options) {\n  var scheme = formatDistanceLocale[token];\n  if (!(options !== null && options !== void 0 && options.addSuffix)) {\n    return declension(scheme, count, 'regular');\n  }\n  if (options.comparison && options.comparison > 0) {\n    return 'za ' + declension(scheme, count, 'future');\n  } else {\n    return declension(scheme, count, 'past') + ' temu';\n  }\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "regular", "past", "future", "twoFour", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "declensionGroup", "scheme", "count", "rem100", "rem10", "declension", "time", "group", "finalText", "replace", "String", "formatDistance", "token", "options", "addSuffix", "comparison"], "sources": ["G:/web/central-platform-tas-web/node_modules/date-fns/esm/locale/pl/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: 'mniej niż sekunda',\n      past: 'mniej niż sekundę',\n      future: 'mniej niż sekundę'\n    },\n    twoFour: 'mniej niż {{count}} sekundy',\n    other: 'mniej niż {{count}} sekund'\n  },\n  xSeconds: {\n    one: {\n      regular: 'sekunda',\n      past: 'sekundę',\n      future: 'sekundę'\n    },\n    twoFour: '{{count}} sekundy',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: {\n    one: 'pół minuty',\n    twoFour: 'pół minuty',\n    other: 'pół minuty'\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: 'mniej niż minuta',\n      past: 'mniej niż minutę',\n      future: 'mniej niż minutę'\n    },\n    twoFour: 'mniej niż {{count}} minuty',\n    other: 'mniej niż {{count}} minut'\n  },\n  xMinutes: {\n    one: {\n      regular: 'minuta',\n      past: 'minutę',\n      future: 'minutę'\n    },\n    twoFour: '{{count}} minuty',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: {\n      regular: 'około godziny',\n      past: 'około godziny',\n      future: 'około godzinę'\n    },\n    twoFour: 'około {{count}} godziny',\n    other: 'około {{count}} godzin'\n  },\n  xHours: {\n    one: {\n      regular: 'godzina',\n      past: 'godzinę',\n      future: 'godzinę'\n    },\n    twoFour: '{{count}} godziny',\n    other: '{{count}} godzin'\n  },\n  xDays: {\n    one: {\n      regular: 'dzień',\n      past: 'dzień',\n      future: '1 dzień'\n    },\n    twoFour: '{{count}} dni',\n    other: '{{count}} dni'\n  },\n  aboutXWeeks: {\n    one: 'około tygodnia',\n    twoFour: 'około {{count}} tygodni',\n    other: 'około {{count}} tygodni'\n  },\n  xWeeks: {\n    one: 'tydzień',\n    twoFour: '{{count}} tygodnie',\n    other: '{{count}} tygodni'\n  },\n  aboutXMonths: {\n    one: 'około miesiąc',\n    twoFour: 'około {{count}} miesiące',\n    other: 'około {{count}} miesięcy'\n  },\n  xMonths: {\n    one: 'miesiąc',\n    twoFour: '{{count}} miesiące',\n    other: '{{count}} miesięcy'\n  },\n  aboutXYears: {\n    one: 'około rok',\n    twoFour: 'około {{count}} lata',\n    other: 'około {{count}} lat'\n  },\n  xYears: {\n    one: 'rok',\n    twoFour: '{{count}} lata',\n    other: '{{count}} lat'\n  },\n  overXYears: {\n    one: 'ponad rok',\n    twoFour: 'ponad {{count}} lata',\n    other: 'ponad {{count}} lat'\n  },\n  almostXYears: {\n    one: 'prawie rok',\n    twoFour: 'prawie {{count}} lata',\n    other: 'prawie {{count}} lat'\n  }\n};\nfunction declensionGroup(scheme, count) {\n  if (count === 1) {\n    return scheme.one;\n  }\n  var rem100 = count % 100;\n\n  // ends with 11-20\n  if (rem100 <= 20 && rem100 > 10) {\n    return scheme.other;\n  }\n  var rem10 = rem100 % 10;\n\n  // ends with 2, 3, 4\n  if (rem10 >= 2 && rem10 <= 4) {\n    return scheme.twoFour;\n  }\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  var group = declensionGroup(scheme, count);\n  var finalText = typeof group === 'string' ? group : group[time];\n  return finalText.replace('{{count}}', String(count));\n}\nvar formatDistance = function formatDistance(token, count, options) {\n  var scheme = formatDistanceLocale[token];\n  if (!(options !== null && options !== void 0 && options.addSuffix)) {\n    return declension(scheme, count, 'regular');\n  }\n  if (options.comparison && options.comparison > 0) {\n    return 'za ' + declension(scheme, count, 'future');\n  } else {\n    return declension(scheme, count, 'past') + ' temu';\n  }\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE;MACHC,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,mBAAmB;MACzBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,6BAA6B;IACtCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRN,GAAG,EAAE;MACHC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE;IACXP,GAAG,EAAE,YAAY;IACjBI,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDG,gBAAgB,EAAE;IAChBR,GAAG,EAAE;MACHC,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,4BAA4B;IACrCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRT,GAAG,EAAE;MACHC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,kBAAkB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXV,GAAG,EAAE;MACHC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,yBAAyB;IAClCC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNX,GAAG,EAAE;MACHC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLZ,GAAG,EAAE;MACHC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBI,OAAO,EAAE,yBAAyB;IAClCC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdI,OAAO,EAAE,oBAAoB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZf,GAAG,EAAE,eAAe;IACpBI,OAAO,EAAE,0BAA0B;IACnCC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPhB,GAAG,EAAE,SAAS;IACdI,OAAO,EAAE,oBAAoB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXjB,GAAG,EAAE,WAAW;IAChBI,OAAO,EAAE,sBAAsB;IAC/BC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNlB,GAAG,EAAE,KAAK;IACVI,OAAO,EAAE,gBAAgB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVnB,GAAG,EAAE,WAAW;IAChBI,OAAO,EAAE,sBAAsB;IAC/BC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZpB,GAAG,EAAE,YAAY;IACjBI,OAAO,EAAE,uBAAuB;IAChCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,SAASgB,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACtC,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf,OAAOD,MAAM,CAACtB,GAAG;EACnB;EACA,IAAIwB,MAAM,GAAGD,KAAK,GAAG,GAAG;;EAExB;EACA,IAAIC,MAAM,IAAI,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC/B,OAAOF,MAAM,CAACjB,KAAK;EACrB;EACA,IAAIoB,KAAK,GAAGD,MAAM,GAAG,EAAE;;EAEvB;EACA,IAAIC,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE;IAC5B,OAAOH,MAAM,CAAClB,OAAO;EACvB;EACA,OAAOkB,MAAM,CAACjB,KAAK;AACrB;AACA,SAASqB,UAAUA,CAACJ,MAAM,EAAEC,KAAK,EAAEI,IAAI,EAAE;EACvC,IAAIC,KAAK,GAAGP,eAAe,CAACC,MAAM,EAAEC,KAAK,CAAC;EAC1C,IAAIM,SAAS,GAAG,OAAOD,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAACD,IAAI,CAAC;EAC/D,OAAOE,SAAS,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACR,KAAK,CAAC,CAAC;AACtD;AACA,IAAIS,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,KAAK,EAAEW,OAAO,EAAE;EAClE,IAAIZ,MAAM,GAAGxB,oBAAoB,CAACmC,KAAK,CAAC;EACxC,IAAI,EAAEC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,CAAC,EAAE;IAClE,OAAOT,UAAU,CAACJ,MAAM,EAAEC,KAAK,EAAE,SAAS,CAAC;EAC7C;EACA,IAAIW,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;IAChD,OAAO,KAAK,GAAGV,UAAU,CAACJ,MAAM,EAAEC,KAAK,EAAE,QAAQ,CAAC;EACpD,CAAC,MAAM;IACL,OAAOG,UAAU,CAACJ,MAAM,EAAEC,KAAK,EAAE,MAAM,CAAC,GAAG,OAAO;EACpD;AACF,CAAC;AACD,eAAeS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}