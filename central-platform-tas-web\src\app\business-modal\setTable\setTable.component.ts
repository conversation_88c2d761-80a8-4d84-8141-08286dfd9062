import {CdkDrag, CdkDragDrop, moveItemInArray, transferArrayItem} from '@angular/cdk/drag-drop';
import {T_CBC_CONFIGPAGE} from '@store/CBC/T_CBC_CONFIGPAGE';
import {GlobalDataService} from '@service/globaldata.service';
import {CwfModalCrud} from '@core/cwfmodalcrud';
import {Component} from '@angular/core';
import {NzMessageService} from 'ng-zorro-antd/message';
import {NzModalRef} from 'ng-zorro-antd/modal';
import {CommonService} from '@service/common.service';
import {CwfBusContextService, CwfRequest} from 'cwf-ng-library';
import {CwfNewRequest} from '@core/cwfNewRequest';
import {CwfRestfulService} from '@service/cwfRestful.service';
import {responseInterface} from '../../interface/request.interface';

@Component({
  selector: 'app-setTable',
  templateUrl: './setTable.component.html',
  styles: [`
    .editable-cell {
      position: relative;
      padding: 5px 12px;
      cursor: pointer;
    }

    .editable-row:hover .editable-cell {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 4px 11px;
    }
  `],
})

/**
 * 查询页面自定义配置
 * <AUTHOR>
 */
export class SetTableComponent extends CwfModalCrud {

  /** 构造器 */
  constructor(
    private Context: CwfBusContextService,
    matDialogRef: NzModalRef,
    private glo: GlobalDataService,
    private message: NzMessageService,
    private cwfRestfulService: CwfRestfulService,
    private commonService: CommonService) {
    super(Context, matDialogRef);
    this.scope = this;
  }
  /** 页面主仓库 */
  mainStore = new T_CBC_CONFIGPAGE();
  scope: any;
  sysData = [];
  /** 页面标题 */
  colLeftSource = [];
  colRightSource = [];
  // cmsAlert = '个人显示列设置';//提示信息
  IS_ADMIN = 'N';
  USER_CD = '';
  system_cd = this.commonService.getSystem_cd();
  // system_version = this.cwfBusContext.getContext().getSystemVersion(); // 获取维度代码
  xScroll: any;

  editId = ''; // 修改列

  /** 页面加载 */
  onLoad() {
    this.onQueryInit();
  }

  onShow() {
  }

  // 查询数据
  onQueryInit() {
    const obj = {
      systemCd: this.config['system_cd'],
      pageCd: this.config['page_cd'],
      compCd: this.config['comp_cd'],
      compType: 'TABLE',
      tableName: this.config['type']
    };
    const request = new CwfNewRequest();
    request.ISPAGING = true;
    request.ACTIONID = 'cmsbasecode.PageColumnConfigService';
    request.OPERATION = 'query';
    request.CONDITION = obj;
    request.BU_CD = 'admin';
    request.BU_NM = 'admin';
    // request.SYSTEM_CD = this.config['system_cd'];
    request.SYSTEM_CD = this.system_cd;
    request.SYSTEMVERSION = 'admin';

    this.cwfRestfulService.post('/PageColumnConfigService/query', obj, 'system-service').then((reps: responseInterface) => {
      if (reps.ok) {
        let arr = reps.data;
        let width = 90;
        this.sysData = this.config['sysData'];
        if (!(arr?.length > 0)) {
          arr = this.config['sysData'];
        }
        arr.forEach(attr => {
          this.colLeftSource.push(attr);
          width += Number(attr.tableWidth);
        });
        this.xScroll = width + 'px';
        this.sysData.forEach(info => {
          // tslint:disable-next-line:max-line-length
          if (!this.colLeftSource.find(c => c.controlname ===  info.controlname && c.columnKey === info.columnKey)) {
            this.colRightSource.push(info);
          }
        });
      }
    });
  }

  // 保存接口
  onSaveInit() {
    const sysData = {
        'system_cd': this.config['system_cd'],
        'gridArray': []
    };
    sysData.gridArray = this.colLeftSource.map((info, i) => ({
      attr: {
        required: info.requiredFlag,
        displayFlag: 1,
        formControlName: info.controlname,
        remark: info.remark,
        seq: i + 1,
        key: info.columnKey,
        id: info.id,
        nzWidth: info.tableWidth,
        customizedName: info.customizedName
      },
      event: {}
    }));

    const obj = {
      modalId: this.config['modalId'],
      data: sysData,
      pageCd: this.config['page_cd'],
      pageNm: this.config['page_cd'],
      compCd: this.config['comp_cd'],
      compNm: this.config['comp_cd'],
      compType: 'TABLE',
      tableName: this.config['type']
    };
    // const request = new CwfNewRequest();
    // request.ISPAGING = true;
    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';
    // request.OPERATION = 'save';
    // request.CONDITION = obj;
    //
    // request.BU_CD = 'admin';
    // request.BU_NM = 'admin';
    // // request.SYSTEM_CD = this.config['system_cd'];
    // request.SYSTEM_CD = this.system_cd;
    // request.SYSTEMVERSION = 'admin';
    return  this.cwfRestfulService.post('/PageColumnConfigService/save', obj, 'system-service').then((reps: responseInterface) => {
      if (reps.ok) {
        this.message.success('保存成功,刷新后生效！');
        // 构建返回数据，包含更新后的列配置
        const returnData = {
          array: sysData.gridArray
        };
        this.matDialogRef.close(returnData);
      }
      }
    );
  }

  startEdit(id: string): void {
    this.editId = id;
  }

  stopEdit(): void {
    this.editId = null;
  }

  onConfirm() {
    this.onSaveInit();
  }
  onCancel() {
    this.matDialogRef.close();
  }
  onReview() {
    const obj = {
      modalId: this.config['modalId'],
      pageCd: this.config['page_cd'],
      pageNm: this.config['page_cd'],
      compCd: this.config['comp_cd'],
      compNm: this.config['comp_cd'],
      compType: 'TABLE',
      tableName: this.config['type']
    };
    // const request = new CwfNewRequest();
    // request.ISPAGING = true;
    // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';
    // request.OPERATION = 'delete';
    // request.CONDITION = obj;
    // request.BU_CD = 'admin';
    // request.BU_NM = 'admin';
    // request.SYSTEM_CD = this.config['system_cd'];
    // request.SYSTEMVERSION = 'admin';

    this.cwfRestfulService.post('/PageColumnConfigService/delete', obj, 'system-service').then((reps: responseInterface) => {
      if (reps.ok) {
        this.message.success('恢复默认成功,刷新后生效！');
        // 构建默认配置数据，使用原始sysData作为默认配置
        const defaultData = {
          array: this.sysData.map((info, i) => ({
            attr: {
              required: info.requiredFlag,
              displayFlag: info.displayFlag,
              formControlName: info.controlname,
              remark: info.remark,
              seq: i + 1,
              key: info.columnKey,
              id: info.id,
              nzWidth: info.tableWidth,
              customizedName: info.customizedName
            },
            event: {}
          }))
        };
        this.matDialogRef.close(defaultData);
      } else {
        this.message.error(reps.msg);
      }
    });
  }

  /** 只接受右边的list的非必输的数据 */
  evenPredicate(item: CdkDrag<number>) {
    const gridInfo = item.data;
    return gridInfo['requiredFlag'] !== '1';
  }

  /**
  * 拖动的时候，list交换item或者单个list里面item位置的变换
  */
  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex);
    }
  }
}
