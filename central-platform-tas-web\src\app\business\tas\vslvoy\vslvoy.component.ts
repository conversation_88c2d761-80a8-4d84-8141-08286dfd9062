import {Component} from '@angular/core';
import {
  CwfBaseCrud,
  CwfBusContextService,
  CwfModel,
  CwfOpenParam,
  CwfStore,
  DialogResultEnum,
  ModalTypeEnum,
  PageModeEnum
} from 'cwf-ng-library';
import { TAS_T_VSLVOY } from '@store/TAS/TAS_T_VSLVOY';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {CwfRestfulService} from '@service/cwfRestful.service';
import {CommonService} from '@service/common.service';
import {responseInterface} from '../../../interface/request.interface';
import {GlobalDataService} from '@service/globaldata.service';
import {CwfBaseCrudcto} from '@core/cwfbasecrudcto';
import {endOfWeek, startOfWeek, addDays} from 'date-fns';

@Component({
  selector: 'tas-vslvoy-app',
  templateUrl: './vslvoy.component.html'
})

export class VslvoyComponent extends CwfBaseCrudcto {
  scope: any;
  conditionForm: FormGroup;

  mainStore = new TAS_T_VSLVOY();

  system_cd = this.commonservice.getSystem_cd(); // 板块
  page = 'main';
  nzScroll = {x: '15000px', y: '710px'};
  page_cd = 'vslvoy';
  checkEffectiveProductFlag = false;
  customerCondition = {
    "PROPERTY": "C,A",
    "isRelate":"Y"
  };

  airwaysNmCondition = {
    "party_type": "G",
    "isRelate":"Y"
  };

  airportCondition = {
    "isRelate":"Y"
  };

  citydoorCondition = {
    "isRelate":"Y"
  };

  constructor(
    cwfBusContextService: CwfBusContextService,
    private commonservice: CommonService,
    private gol: GlobalDataService,
    private cwfRestfulService: CwfRestfulService,
    private fb: FormBuilder
  ) {
    super(cwfBusContextService);
    this.scope = this;
  }

  FormArray = [
    { // 船舶
      'attr': {
        'key': 'BASE_T_VESSEL',
        'valuefield': 'vesselvesselCd,vesselNmEn',
        'readfield': 'vesselNm,vesselCd,vesselNmEn',
        'displayfield': 'vesselNm',
        'formControlName': 'vesselNm',
        'i18n_cd': 'DB.VESSEL_NM',
        'display': true,
        'nzspan': 6,
        'nzWidth': 110
      },
      'event': {}
    },

    { // 航次
      'attr': {
        'key': 'input_text',
        'formControlName': 'voyage',
        'i18n_cd': '航次',
        'display': true,
        'nzspan': 6,
        'nzWidth': 110
      },
      'event': {}
    },


    { // 进出口
      'attr': {
        'key': 'BASE_T_SHIP_LINE',
        'formControlName': 'ioId',
        'i18n_cd': '进出口',
        'display': true,
        'nzspan': 6,
        'nzWidth': 120,
        'readfield': 'code,name,englishName',
        'valuefield': 'ioId,ioNm,ioNmEn',
        'type': 'system:fms:ieFlag'
      },
      'event': {}
    },

    { // 内外贸
      'attr': {
        'key': 'BASE_T_SHIP_LINE',
        'formControlName': 'tradeId',
        'i18n_cd': '内外贸',
        'display': true,
        'nzspan': 6,
        'nzWidth': 120,
        'readfield': 'code,name,englishName',
        'valuefield': 'tradeId,tradeNm,tradeNmEn',
        'type': 'system:tms:loadType'
      },
      'event': {}
    },

    { // 抵港时间
      'attr': {
        'key': 'input_datetime',
        'formControlName': 'ata',
        'i18n_cd': '抵港时间',
        'display': true,
        'nzspan': 6,
        'nzWidth': 120,
        'format': 'yyyy-MM-dd HH:mm'
      },
      'event': {}
    },

    { // 港口
      'attr': {
        'key': 'BU_PORT',
        'formControlName': 'portNm',
        'i18n_cd': '港口',
        'display': true,
        'nzspan': 6,
        'nzWidth': 120,
        'readfield': 'portCd,portNm,portNmEn',
        'valuefield': 'portCd,portNm,portNmEn'
      },
      'event': {}
    },

    { // 码头
      'attr': {
        'key': 'BU_WHARF',
        'formControlName': 'wharfNm',
        'i18n_cd': '码头',
        'display': true,
        'nzspan': 6,
        'nzWidth': 120,
        'readfield': 'wharfCd,wharfNm,wharfNmEn',
        'valuefield': 'wharfCd,wharfNm,wharfNmEn'
      },
      'event': {}
    },

    { // 所属组织机构名称
      'attr': {
        'key': 'input_select_multiple',
        'formControlName': 'orgIds',
        'i18n_cd': '所属组织机构',
        'display': true,
        'nzspan': 12,
        'nzWidth': 120,
        'placeholder': '请选择',
        'options': 'companyData'
      },
      'event': {}
    }








    //
    // { // 到港时间
    //   'attr': {
    //     'key': 'input_datetime_range',
    //     'formControlName': 'eta',
    //     'i18n_cd': 'DB.EST_ARRIVAL_TIME',
    //     'display': true,
    //     'nzspan': 6,
    //     'nzWidth': 110
    //   },
    //   'event': {}
    // },

  ];

  // 主页面查询结果列表
  GridArray = [
    {
      "attr": {
        "key": "input_text",
        "formControlName": "endTag",
        "i18n_cd": "DB.END_TAG",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselCd",
        "i18n_cd": "船舶代码",
        "nzWidth": 150
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselNm",
        "i18n_cd": "船名",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselNmEn",
        "i18n_cd": "英文名称",
        "nzWidth": 110
      },
      "event": {}
    },


    {
      "attr": {
        "key": "input_text",
        "formControlName": "voyage",
        "i18n_cd": "航次",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "ships",
        "i18n_cd": "艘次",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "imo",
        "i18n_cd": "IMO",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "ioId",
        "i18n_cd": "进出口",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "tradeId",
        "i18n_cd": "内外贸",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselTypeNm",
        "i18n_cd": "船舶类型",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "shipLineNm",
        "i18n_cd": "航线",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "shipLineClassNm",
        "i18n_cd": "航线大类",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "shipLineTypeNm",
        "i18n_cd": "航线类型",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "portNm",
        "i18n_cd": "港口",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "wharfNm",
        "i18n_cd": "码头",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "berthNm",
        "i18n_cd": "泊位",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselFlagNm",
        "i18n_cd": "船旗(国籍)",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselNo",
        "i18n_cd": "船号",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "shipagentNm",
        "i18n_cd": "船舶代理",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "carrierNm",
        "i18n_cd": "承运人",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "vesselNatureNm",
        "i18n_cd": "船舶性质",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "operationNatureNm",
        "i18n_cd": "运管性质",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "workNatureNm",
        "i18n_cd": "作业性质",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "ata",
        "i18n_cd": "实际的抵港时间",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "atb",
        "i18n_cd": "靠泊时间",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "atd",
        "i18n_cd": "实际的离港时间",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "ast",
        "i18n_cd": "开工时间",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "aet",
        "i18n_cd": "完工时间",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "captain",
        "i18n_cd": "船长(大副)",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "captainTel",
        "i18n_cd": "联系电话",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "orgLevelNo",
        "i18n_cd": "所属组织机构代码",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "orgNm",
        "i18n_cd": "所属组织机构名",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "remark",
        "i18n_cd": "备注",
        "nzWidth": 110
      },
      "event": {}
    },



    {
      "attr": {
        "key": "input_text",
        "formControlName": "createdUserName",
        "i18n_cd": "DB.CREATED_USER",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "createdTime",
        "i18n_cd": "DB.CREATED_TIME",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "input_text",
        "formControlName": "modifiedUserName",
        "i18n_cd": "DB.MODIFIED_USER",
        "nzWidth": 110
      },
      "event": {}
    },
    {
      "attr": {
        "key": "datetime_local",
        "formControlName": "modifiedTime",
        "i18n_cd": "DB.MODIFIED_TIME",
        "nzWidth": 110
      },
      "event": {}
    }
  ];

  onLoad() {
    this.conditionForm = this.setFormControl({});
    this.queryList();
  }


  searchData_S(storeF: CwfStore, reset: boolean = false) {
    this.queryList(true);
  }

  queryList(reset?: boolean) {
    for (const i in this.conditionForm.controls) {
      this.conditionForm.controls[i].markAsDirty();
      this.conditionForm.controls[i].updateValueAndValidity();
    }
    if (this.conditionForm.invalid) {
      return;
    }

    setTimeout(() => {
      if (reset) {
        this.mainStore.pageing.PAGE = 1;
      }
      const requestData = {
        page: this.mainStore.pageing.PAGE,
        size: this.mainStore.pageing.LIMIT,
        sortBy: {
          createdTime: 'DESC',
          id: 'ASC'
        }
      };
      const conditionData = {};
      for (const form in this.conditionForm.controls) {
        if (this.conditionForm.controls[form].value !== '' && this.conditionForm.controls[form].value !== null) {
          if(form == 'orderDt' || form == 'departureDate' || form == 'estDepartureTime' || form == 'estArrivalTime' || form == 'createdTime'){
            this.splitDateRange(this.conditionForm.controls[form].value, form, conditionData)
          }else {
            conditionData[form] = this.conditionForm.controls[form].value;
          }
        }
      }
      requestData['data'] = conditionData;
      this.mainStore.clearData();
      this.loading = true;

      const id = this.cwfBusContext.getNotify().showLoading(this.getMsgi18nString("WEB0009"));
      this.cwfRestfulService.post('/vslvoy/list/page', requestData, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {
        if (rps.ok === true) {
          this.loading = false;
          this.cwfBusContext.getNotify().removeShow(id);
          this.mainStore.loadDatas(rps.data.content);
          this.mainStore.pageing.TOTAL = rps.data.totalElements; // 总条数
        } else {
          this.showState(ModalTypeEnum.error, rps.msg);
        }
      });
    });
  }

  /**
   * 保存当前搜索条件为默认船期条件
   * 将当前表单的值保存到localStorage中，以便在其他页面或下次访问时使用
   */
  saveDefault() {
    // 获取当前表单的值
    const formValue = this.conditionForm.getRawValue();

    // 将表单值存储到localStorage中
    localStorage.setItem('vslvoySearchCondition', JSON.stringify(formValue));

    // 显示成功消息
    this.showState(ModalTypeEnum.success, '搜索条件已保存为默认船期！');

    return {
      id: '',
      vesselCd: '',
      vesselNm: '',
      voyage: '',
      ioId: '',
      tradeId: '',
      eta: '',
      portCd: '',
      wharfCd: '',
      orgIds: [],
      isDelete: '0',
      createdTime: '',
      createdBy: '',
      updatedTime: '',
      updatedBy: '',
      endTag: '0'
    };
  }


  splitDateRange(dates: string[], targetObj: any, reqObj: object): void {
    const start: string = targetObj + 'Start';
    const end: string = targetObj + 'End';
    reqObj[start] = dates[0];
    reqObj[end] = dates[1];
  }

  async OnDel() {
    const sld: CwfModel[] = this.mainStore.getSelectedDatas();
    if (sld.length == 0) {
      this.showAlert(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK7003'));
      return false;
    }
    const requestData = [];
    let preorderStatusFlag = false;

    sld.forEach(item => {
      if (item['preorderStatus'] == '10') {
        preorderStatusFlag = true;
        return;
      }
      requestData.push({id :item['id']});
    });

    if (preorderStatusFlag) {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK8079"));
      return false;
    }


    this.loading = true;
    const id = this.cwfBusContext.getNotify().showLoading(this.getMsgi18nString("WEB0009"));

    let state = await this.showConfirm(this.getMsgi18nString('FK0018'), this.getMsgi18nString('FK0024'));

    if (!(state === DialogResultEnum.yes)) {
      return false;
    }

    this.cwfRestfulService.delete('/vslvoy/batch', this.gol.serviceName['tas'].en, {body: requestData}).then((rps: responseInterface) => {
      if (rps.ok) {
        this.loading = false;
        this.cwfBusContext.getNotify().removeShow(id);
        this.showState(ModalTypeEnum.success, '删除成功！');
        this.queryList();
      } else {
        this.showState(ModalTypeEnum.error, rps.msg);
      }
    });
  }

  onVslvoyModify() {
    let selRows = this.mainStore.getSelectedDatas();
    if (selRows.length != 1) {
      this.showAlert('提示', '一次只允许修改一条记录');
      return false;
    }
debugger
    if (selRows[0]["endTag"] == "1") {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK8025E"));
      return false;
    }

    for (let info of this.mainStore.getSelecteds()) {
      info.setDirty();
    }


    this.onModify();
  }


  OnView() {
    let records = this.mainStore.getSelectedDatas();
    if (records.length == 0) {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK7003"));
      return false;
    } else if (records.length > 1) {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK8024"));
      return false;
    }
    let item = this.mainStore.getSelectedDatas();
    const param = new CwfOpenParam();
    // 设置页面操作状态
    const objparam: object = {};
    objparam[this.PAGE_STATE] = PageModeEnum.Custom;
    this.openPage('/tas/vslvoy/list-edit', { id: item[0]['id'], state: "custom" });
  }

  OnRelate() {
    const sld: CwfModel[] = this.mainStore.getSelectedDatas();
    if (sld.length == 0) {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK7003"));
      return false;
    }
    const requestData = []
    sld.forEach(item => {
      requestData.push(item);
    });
    this.loading = true;
    this.cwfRestfulService.post('/vslvoy/relate', requestData, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {
      this.loading = false;
      if (rps.ok) {
        this.showState(ModalTypeEnum.success, '结航成功！');
        this.queryList();
      } else {
        this.showState(ModalTypeEnum.error, rps.msg);
      }
    });
  }

  OnCancelRelate() {
    const sld: CwfModel[] = this.mainStore.getSelectedDatas();
    if (sld.length == 0) {
      this.showAlert(this.getMsgi18nString("FK0018"), this.getMsgi18nString("FK7003"));
      return false;
    }
    const requestData = []
    sld.forEach(item => {
      requestData.push(item);
    });
    this.loading = true;
    this.cwfRestfulService.post('/vslvoy/cancelRelate', requestData, this.gol.serviceName['tas'].en).then((rps: responseInterface) => {
      this.loading = false;
      if (rps.ok) {
        this.showState(ModalTypeEnum.success, '取消结航成功！');
        this.queryList();
      } else {
        this.showState(ModalTypeEnum.error, rps.msg);
      }
    });
  }

  getReturnData($event: any) {

  }

  nzPageIndexChangeEvent($event: any) {
    this.queryList();
  }



}


