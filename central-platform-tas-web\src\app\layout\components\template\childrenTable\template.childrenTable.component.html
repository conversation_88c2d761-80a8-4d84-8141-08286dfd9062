<div *ngIf="isInitData">
	<!-- <button (click)="onSaveData()">初始化数据入库</button> -->
	<button (click)="onEditOpen()">维护数据</button>
	<span style="margin-left:10px;">page: <span style="font-weight: bold;">{{ page_cd }}</span></span>
	<span style="margin-left:10px;">comp: <span style="font-weight: bold;">{{ comp_cd }}</span></span>
	<nz-alert *ngFor="let info of errList" nzType="warning" [nzMessage]="info"></nz-alert>
</div>
<!-- <div *ngIf="!isInit">
	<nz-alert *ngFor="let info of errList" nzType="warning" [nzMessage]="info"></nz-alert>
</div> -->
<!--<nz-alert *ngIf="!isInit; else mmain" nzType="warning" nzMessage="当前模块没有初始化入库数据"></nz-alert>-->

<ng-container #mmain>
	<div *ngIf="yxts!=='' && page=='main'" style="height: 29px;">
		<strong style="color:#5b5b5b">已选记录：{{ yxts }}条 ； {{ revtotal_s }} </strong>
		<div style="float:right;position:relative;top:0px;" *ngIf="system_cd === 'NBCS'">
			<nz-pagination [nzTotal]="store.pageing.TOTAL" [nzSize]="'small'"
			               [(nzPageIndex)]="store.pageing.PAGE" [(nzPageSize)]="store.pageing.LIMIT"
			               [nzPageSizeOptions]="nzPageSizeOptions"
			               nzShowSizeChanger
			               (nzPageIndexChange)="parentContainer.searchData_S(store)"
			               (nzPageSizeChange)="parentContainer.searchData_S(store,true)"
			></nz-pagination>
		</div>
	</div>
	<strong>{{ filtermessage }}</strong>
	<div *ngIf="page=='main'" style="word-wrap:break-word">
		<nz-table #rTable nzShowSizeChanger [nzBordered]="true" [nzSize]="'middle'" [nzScroll]="nzScroll"
		          [nzLoading]="loading" [nzFrontPagination]="false" [nzTotal]="store.pageing.TOTAL"
		          [(nzPageIndex)]="store.pageing.PAGE" [(nzPageSize)]="store.pageing.LIMIT"
		          [nzShowTotal]="rangeTemplate"
		          (nzPageIndexChange)="nzPageIndexChange()"
		          (nzPageSizeChange)="setCookie($event)" [nzData]="store.getDatas()"
		          [nzPageSizeOptions]="nzPageSizeOptions">
			<thead (nzSortOrderChange)="sort($event)">
			<tr (click)="show($event)" cdkDropList cdkDropListOrientation="horizontal" cdkDropListLockAxis="x"
			    (cdkDropListDropped)="drop($event)">
				<th nzLeft *ngIf="checkbox_place=='0'&&!showcheckAll&&showcheck" nzWidth="45px"></th>
				<!-- <th nzLeft *ngIf="checkbox_place=='0'&&showcheckAll&&showcheck" nzWidth = "45px" nzShowCheckbox [nzChecked]="isAllDisplayDataChecked"
					[nzIndeterminate]="isIndeterminate" (nzCheckedChange)="checkAll($event)"></th> -->
				<th nzLeft *ngIf="checkbox_place=='0'&&showcheckAll&&showcheck&&!showcheck2" nzWidth="45px"
				    nzShowCheckbox [nzChecked]="parentContainer.isAllDisplayDataChecked"
				    [nzIndeterminate]="parentContainer.isIndeterminate" (nzCheckedChange)="checkAll($event)"></th>
				<th nzLeft *ngIf="checkbox_place=='0'&&showcheckAll&&showcheck&&showcheck2" nzWidth="45px"
				    nzShowCheckbox [nzChecked]="isAllDisplayDataChecked_X"
				    [nzIndeterminate]="isIndeterminate_X" (nzCheckedChange)="checkAll($event)"></th>
				
				<th nzWidth="50px">{{ 'OTH.SEQ' | translate }}</th>
				<ng-container *ngFor="let gridinfo of GridArray;let i = index;">
					<ng-container *ngIf="isInit?gridinfo.attr.display:true">
						<th *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&!showcheckAll&&showcheck"
						    nzWidth="30px"></th>
						<!-- <th *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheckAll&&showcheck" nzWidth="30px" nzShowCheckbox
							[nzChecked]="isAllDisplayDataChecked" [nzIndeterminate]="isIndeterminate"
							(nzCheckedChange)="checkAll($event)" ></th> -->
						
						<th *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheckAll&&showcheck&&!showcheck2"
						    nzWidth="30px" nzShowCheckbox
						    [nzChecked]="parentContainer.isAllDisplayDataChecked"
						    [nzIndeterminate]="parentContainer.isIndeterminate"
						    (nzCheckedChange)="checkAll($event)"></th>
						<th *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheckAll&&showcheck&&showcheck2"
						    nzWidth="30px" nzShowCheckbox
						    [nzChecked]="isAllDisplayDataChecked_X" [nzIndeterminate]="isIndeterminate_X"
						    (nzCheckedChange)="checkAll($event)"></th>
						
						<!--带过滤-->
						<th [ngStyle]="thstyle(gridinfo)"
						    id="{{isdata(gridinfo)}}"
						    nz-resizable
						    cdkDrag
						    nzBounds="window"
						    [nzMaxWidth]="2048"
						    [nzMinWidth]="60"
						    (nzResizeEnd)="onResize($event, gridinfo)"
						    nzShowSort
						    nzColumnKey="{{isField(gridinfo,'formControlName')}}"
						    *ngIf="getshowFilter(gridinfo)"
						    [nzFilters]="getfilterlist(gridinfo)"
						    (nzFilterChange)="filter($event,isField(gridinfo,'formControlName'))"
						>
							<div [ngStyle]="thstyle(gridinfo)">
									<span *ngIf="gridinfo.attr?.customizedName; else name1">
										{{ gridinfo.attr?.customizedName }}
									</span>
								<ng-template #name1>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
							</div>
							<nz-resize-handle nzDirection="right">
								<div class="resize-trigger"></div>
							</nz-resize-handle>
						</th>
						<!--不带过滤-->
						<th [ngStyle]="thstyle(gridinfo)"
						    id="{{isdata(gridinfo)}}"
						    nz-resizable
						    nzBounds="window"
						    [nzMaxWidth]="2048"
						    [nzMinWidth]="60"
						    (nzResizeEnd)="onResize($event, gridinfo)"
						    nzShowSort
						    nzColumnKey="{{isField(gridinfo,'formControlName')}}"
						    *ngIf="!getshowFilter(gridinfo)"
						>
							<div cdkDrag [ngStyle]="thstyle(gridinfo)">
									<span *ngIf="gridinfo.attr?.customizedName; else name2">
										{{ gridinfo.attr?.customizedName }}
									</span>
								<ng-template #name2>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
							</div>
							<nz-resize-handle nzDirection="right">
								<div class="resize-trigger"></div>
							</nz-resize-handle>
						</th>
					</ng-container>
				</ng-container>
			</tr>
			</thead>
			<tbody>
			<ng-container *ngFor="let data of rTable.data;let i = index">
				<tr (dblclick)="onTableRowDblClick($event)" *ngIf="data['VISIBLE'] !== 'FALSE'"
				    (click)="setSelectRow($event,data) || showlinedata($event,data)" (change)="change($event,data)"
				    (keyup)="onKeyup($event,data)"
				    [ngStyle]="rowstyle(data)">
					<td nzLeft class="text" *ngIf="checkbox_place=='0'&&showcheck" nzShowCheckbox
					    [nzChecked]="data.SELECTED"
					    [ngStyle]="ischeckstyle(data)" (nzCheckedChange)="onCheckV(data)"></td>
					<td class="num">
						{{ i + 1 }}
						<!--							<div *ngIf="tableDataName == 'Oracle' " style="width: 30px;text-align:center;">{{ data['ROW_ID'] }}</div>-->
						<!--							<div *ngIf="tableDataName == 'MySql' " style="width: 30px;text-align:center;">{{ (store.pageing.LIMIT*(store.pageing.PAGE-1)+i+1) }}</div>-->
					</td>
					<ng-container *ngFor="let gridinfo of GridArray;let i = index;">
						<ng-container *ngIf="isInit?gridinfo.attr.display:true">
							<td class="text"
							    *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheck"
							    nzShowCheckbox [nzChecked]="data.SELECTED"
							    [ngStyle]="ischeckstyle(data)" (nzCheckedChange)="onCheckV(data)">
							</td>
							<ng-container *ngIf="dataisedit(data,gridinfo)!=true || isedit(data,gridinfo) !=true">
								<td class="text" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isField(gridinfo,'xtype')!='time'&&isField(gridinfo,'xtype')!='number'&&isField(gridinfo,'xtype')!='innerHTMLtext'">
									
									<div title="{{data[isdata(gridinfo)] | constantPipe : isField(gridinfo,'pipe')}}"
									     style="text-overflow :ellipsis;white-space :nowrap;overflow : hidden;"
									     (click)="columnClick(gridinfo,data)" [ngStyle]="columnStyle(gridinfo,data)">
										{{ data[isdata(gridinfo)] | constantPipe : isField(gridinfo, 'pipe') }}
									</div>
								</td>
								<td class="num" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isField(gridinfo,'xtype')=='number'">
									<div style="text-align: right;"
									     title="{{isnum(data[isdata(gridinfo)]) | constantPipe : isField(gridinfo,'pipe')}}"
									     (click)="columnClick(gridinfo,data)" [ngStyle]="columnStyle(gridinfo,data)">
										{{ isnum(data[isdata(gridinfo)]) | constantPipe : isField(gridinfo, 'pipe') }}
									</div>
								</td>
								<td class="text" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isField(gridinfo,'xtype')=='time'">
									<div title="{{data[isdata(gridinfo)] | date : isField(gridinfo,'time_type')}}"
									     (click)="columnClick(gridinfo,data)" [ngStyle]="columnStyle(gridinfo,data)">
										{{ data[isdata(gridinfo)] | date : isField(gridinfo, 'time_type') }}
									</div>
								</td>
								<td class="text" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isField(gridinfo,'xtype')=='innerHTMLtext'"
								    [innerHTML]="data[isdata(gridinfo)] | constantPipe:isField(gridinfo,'pipe')"
								>
								</td>
							</ng-container>
							<td class="num" [ngStyle]="linetyle(data,gridinfo)"
							    *ngIf="isedit(data,gridinfo)==true&&dataisedit(data,gridinfo)==true&&isField(gridinfo,'xtype')=='number'">
								<!-- 纯数字 -->
								<input nz-input *ngIf="isField(gridinfo,'xtype')=='number'"
								       min="-999999999" max="9999999999" step="0.0000001"
								       id="{{isdata(gridinfo)}}"
								       [(ngModel)]="data[isdata(gridinfo)]" type="number" style="text-align:right;"/>
							</td>
							<td class="text" [ngStyle]="linetyle(data,gridinfo)"
							    *ngIf="isedit(data,gridinfo)==true&&dataisedit(data,gridinfo)==true&&isField(gridinfo,'xtype')!='number'">
								<!--输入框无限制-->
								<input nz-input *ngIf="isField(gridinfo,'xtype')=='text'"
								       id="{{isdata(gridinfo)}}" [(ngModel)]="data[isdata(gridinfo)]"/>
								
								<!-- 纯英文 -->
								<input nz-input *ngIf="isField(gridinfo,'xtype')=='english'"
								       onkeyup="value=value.replace(/[^a-zA-Z]/g,'')"
								       id="{{isdata(gridinfo)}}" [(ngModel)]="data[isdata(gridinfo)]"/>
								<!-- 时间选择器 -->
								<nz-date-picker *ngIf="isField(gridinfo,'xtype')=='time'"
								                [nzFormat]="dateFormat" id="{{isdata(gridinfo)}}"
								                [(ngModel)]="data[isdata(gridinfo)]"
								                (ngModelChange)="setchangetime($event,data,isdata(gridinfo))">
								</nz-date-picker>
								
								<!-- 时间选择器 年-->
								<nz-year-picker *ngIf="isField(gridinfo,'xtype')=='time_year'"
								                [nzFormat]="dateFormat" id="{{isdata(gridinfo)}}"
								                [(ngModel)]="data[isdata(gridinfo)]"
								                (ngModelChange)="setchangetime_year($event,data,isdata(gridinfo))">
								</nz-year-picker>
								
								<!-- 时间选择器 年月-->
								<nz-month-picker *ngIf="isField(gridinfo,'xtype')=='time_month'"
								                 [nzFormat]="dateFormat" id="{{isdata(gridinfo)}}"
								                 [(ngModel)]="data[isdata(gridinfo)]"
								                 (ngModelChange)="setchangetime_month($event,data,isdata(gridinfo))">
								</nz-month-picker>
								<!-- 下拉选择 -->
								<ng-container *ngIf="isField(gridinfo,'xtype')=='lookup'">
									<cms-select-table key="{{gridinfo.attr.key}}"
									                  [condition]="gridinfo.attr.condition"
									                  [hasAll]="isField(gridinfo,'hasAll')"
									                  readfield="{{isField(gridinfo,'readfield')}}"
									                  valuefield="{{isField(gridinfo,'valuefield')}}"
									                  [(ngModel)]="data[isdata(gridinfo)]"
									                  [ngModelOptions]="{standalone: true}"
									                  (ngModelChange)="setChanged($event,data,gridinfo)">
									</cms-select-table>
								</ng-container>
								<!-- combox -->
								<ng-container *ngIf="isField(gridinfo,'xtype')=='combox'">
									<cms-combox key="{{gridinfo.attr.key}}"
									            [(ngModel)]="data[isdata(gridinfo)]"
									            [ngModelOptions]="{standalone: true}"
									            [hasAll]="isField(gridinfo,'hasAll')">
									</cms-combox>
								</ng-container>
								<!-- pop控件 -->
								<ng-container *ngIf="isField(gridinfo,'xtype')=='pop'">
									<div style="float: left;">
										<input type="text" nz-input placeholder="请选择"
										       [(ngModel)]="data[isdata(gridinfo)]" [ngStyle]="poptyle(data,gridinfo)"
										       style="background-color: white;cursor:text;" readonly/>
									</div>
									<div style=" float:left;padding-left: 1px;height: 24px;">
										<button nz-button [nzType]="'primary'" style="float: right;height: 24px;"
										        [disabled]="viewReadOnly" (click)="click(gridinfo,data)">
											<i nz-icon nzType="search"></i>
										</button>
									</div>
									<div style="clear: both;"></div>
								</ng-container>
							</td>
						</ng-container>
					</ng-container>
				</tr>
			</ng-container>
			</tbody>
		</nz-table>
		<ng-template #rangeTemplate let-range="range" let-total>
			第{{ range[0] }}-{{ range[1] }}条 总数 {{ total }} 条
		</ng-template>
	</div>
	
	<div *ngIf="page !=='main'" nz-row nzGutter="32">
		<div *ngIf="show_button" nz-col nzSpan="24" class="text-right" style="margin-bottom:5px;">
			<button nz-button [disabled]="addRowFlag" (click)="onAddRate()" [nzType]="'primary'">
				<i nz-icon nzType="plus"></i>
				<span>{{ 'FP.INSERT' | translate }}</span>
			</button>
			<button nz-button [disabled]="delRowFlag" nzType="danger" (click)="onDeleteRate()">
				<i nz-icon nzType="delete"></i>
				<span>{{ 'FP.DELETE' | translate }}</span>
			</button>
		</div>
		<div style="word-wrap:break-word; width: 100%;margin-left: 16px;">
			<nz-table #rTable
			          [nzBordered]="true"
			          [nzScroll]="nzScroll"
			          [nzData]="store.getDatas()"
			          [nzWidthConfig]="nzWidthConfig"
			          [nzFrontPagination]="false"
			          [nzShowPagination]="false">
				<thead>
				<tr (click)="show($event)" cdkDropList cdkDropListOrientation="horizontal" lockAxis='x'
				    (cdkDropListDropped)="drop($event)">
					<th nzLeft *ngIf="checkbox_place=='0'&&!showcheckAll&&showcheck" nzWidth="30px" style="width:30px"></th>
					<th nzLeft *ngIf="checkbox_place=='0'&&showcheckAll&&showcheck" nzWidth="30px" style="width:30px"
					    nzShowCheckbox [nzChecked]="isAllDisplayDataChecked_X"
					    [nzIndeterminate]="isIndeterminate_X" (nzCheckedChange)="checkAll($event)">
					</th>
					<th nzWidth="50px">
						{{ 'OTH.SEQ' | translate }}
					</th>
					<ng-container *ngFor="let gridinfo of GridArray;let i = index;">
						<ng-container *ngIf="isInit?gridinfo.attr.display:true">
							<th *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheck"
							    nzWidth="30px" style="width:30px" nzShowCheckbox
							    [nzChecked]="isAllDisplayDataChecked_X" [nzIndeterminate]="isIndeterminate_X"
							    (nzCheckedChange)="checkAll($event)">
							</th>
							<!--带过滤-->
							<th *ngIf="isField(gridinfo,'Required')==true && getshowFilter(gridinfo)" Style="color:red;"
							    [ngStyle]="thstyle(gridinfo)"
							    nz-resizable
							    nzBounds="window"
							    [nzMaxWidth]="2048"
							    [nzMinWidth]="60"
							    (nzResizeEnd)="onResize($event, gridinfo)"
							    nzShowSort
							    nzColumnKey="{{isField(gridinfo,'formControlName')}}"
							    nzShowFilter
							    [nzFilters]="getfilterlist(gridinfo)"
							    (nzFilterChange)="filter($event,isField(gridinfo,'formControlName'))"
							>
								<div cdkDrag [ngStyle]="thstyle(gridinfo)">
										<span *ngIf="gridinfo.attr?.customizedName; else name3">
											{{ gridinfo.attr?.customizedName }}
										</span>
									<ng-template #name3>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
								</div>
								<nz-resize-handle nzDirection="right">
									<div class="resize-trigger"></div>
								</nz-resize-handle>
							</th>
							<!--不带过滤-->
							<th *ngIf="isField(gridinfo,'Required')==true&&!getshowFilter(gridinfo)" Style="color:red;"
							    [ngStyle]="thstyle(gridinfo)"
							    nz-resizable
							    nzBounds="window"
							    [nzMaxWidth]="2048"
							    [nzMinWidth]="60"
							    (nzResizeEnd)="onResize($event, gridinfo)"
							    nzShowSort
							    nzColumnKey="{{isField(gridinfo,'formControlName')}}"
							>
								<div cdkDrag [ngStyle]="thstyle(gridinfo)">
										<span *ngIf="gridinfo.attr?.customizedName; else name4">
											{{ gridinfo.attr?.customizedName }}
										</span>
									<ng-template #name4>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
								</div>
								<nz-resize-handle nzDirection="right">
									<div class="resize-trigger"></div>
								</nz-resize-handle>
							</th>
							<!--带过滤-->
							<th *ngIf="isField(gridinfo,'Required')!=true&&getshowFilter(gridinfo)"
							    [ngStyle]="thstyle(gridinfo)"
							    nz-resizable
							    nzBounds="window"
							    [nzMaxWidth]="2048"
							    [nzMinWidth]="60"
							    (nzResizeEnd)="onResize($event, gridinfo)"
							    nzShowSort
							>
								<div cdkDrag [ngStyle]="thstyle(gridinfo)">
										<span *ngIf="gridinfo.attr?.customizedName; else name5">
											{{ gridinfo.attr?.customizedName }}
										</span>
									<ng-template #name5>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
								</div>
								<nz-resize-handle nzDirection="right">
									<div class="resize-trigger"></div>
								</nz-resize-handle>
							</th>
							<!--不带过滤-->
							<th *ngIf="isField(gridinfo,'Required')!=true&&!getshowFilter(gridinfo)"
							    [ngStyle]="thstyle(gridinfo)"
							    nz-resizable
							    nzBounds="window"
							    [nzMaxWidth]="2048"
							    [nzMinWidth]="60"
							    (nzResizeEnd)="onResize($event, gridinfo)"
							    nzShowSort
							    nzColumnKey="{{isField(gridinfo,'formControlName')}}"
							>
								<div cdkDrag [ngStyle]="thstyle(gridinfo)">
										<span *ngIf="gridinfo.attr?.customizedName; else name6">
											{{ gridinfo.attr?.customizedName }}
										</span>
									<ng-template #name6>{{ isField(gridinfo, 'i18n_cd') | translate }}</ng-template>
								</div>
								<nz-resize-handle nzDirection="right">
									<div class="resize-trigger"></div>
								</nz-resize-handle>
							</th>
						</ng-container>
					</ng-container>
				</tr>
				</thead>
				<tbody>
				<ng-container *ngFor="let data of rTable.data; let i = index">
					<tr (click)="setSelectRow($event,data)" (change)="change($event,data)"
					    *ngIf="data['VISIBLE'] !== 'FALSE'"
					    (keyup)="onKeyup($event,data)" [ngStyle]="rowstyle(data)" (click)="showlinedata($event,data)">
						<td nzLeft class="text" *ngIf="checkbox_place=='0'&&showcheck" nzShowCheckbox
						    [nzChecked]="data.SELECTED"
						    [ngStyle]="ischeckstyle(data)" (nzCheckedChange)="onCheckV(data)"></td>
						<td class="num">
							<div *ngIf="tableDataName == 'Oracle' "
							     style="width: 30px;text-align:center;">{{ data['ROW_ID'] }}
							</div>
							<div *ngIf="tableDataName == 'MySql' "
							     style="width: 30px;text-align:center;">{{ (store.pageing.LIMIT * (store.pageing.PAGE - 1) + i + 1) }}
							</div>
						</td>
						<ng-container *ngFor="let gridinfo of GridArray;let i = index;">
							<ng-container *ngIf="isInit?gridinfo.attr.display:true">
								<td class="text"
								    *ngIf="isField(gridinfo,'formControlName') == checkbox_place && checkbox_place != '0'&&showcheck"
								    nzShowCheckbox
								    [nzChecked]="data.SELECTED" [ngStyle]="ischeckstyle(data)"
								    (nzCheckedChange)="onCheckV(data)">
								</td>
								<ng-container *ngIf="dataisedit(data,gridinfo)!=true || !isedit(data,gridinfo)">
									<td class="text" [ngStyle]="linetyle(data,gridinfo)"
									    *ngIf="isField(gridinfo,'xtype')!='time'&&isField(gridinfo,'xtype')!='number'&&isField(gridinfo,'xtype')!='innerHTMLtext'">
										
										<div title="{{data[isdata(gridinfo)] | constantPipe : isField(gridinfo,'pipe')}}"
										     style="text-overflow :ellipsis;white-space :nowrap;overflow : hidden;"
										     (click)="columnClick(gridinfo,data)"
										     [ngStyle]="columnStyle(gridinfo,data)">
											{{ data[isdata(gridinfo)] | constantPipe : isField(gridinfo, 'pipe') }}
										</div>
									</td>
									
									<td class="text" [ngStyle]="linetyle(data,gridinfo)"
									    *ngIf="isField(gridinfo,'xtype')=='innerHTMLtext'"
									    [innerHTML]="data[isdata(gridinfo)] | constantPipe:isField(gridinfo,'pipe')"
									>
									</td>
									
									<td class="num" [ngStyle]="linetyle(data,gridinfo)"
									    *ngIf="isField(gridinfo,'xtype')=='number'">
										
										<div style="text-align: right;"
										     title="{{isnum(data[isdata(gridinfo)]) | constantPipe : isField(gridinfo,'pipe')}}"
										     (click)="columnClick(gridinfo,data)"
										     [ngStyle]="columnStyle(gridinfo,data)">
											
											{{ isnum(data[isdata(gridinfo)]) | constantPipe : isField(gridinfo, 'pipe') }}
										
										</div>
									</td>
									<td class="text" [ngStyle]="linetyle(data,gridinfo)"
									    *ngIf="isField(gridinfo,'xtype')=='time'">
										<div title="{{data[isdata(gridinfo)] | date : isField(gridinfo,'time_type')}}"
										     (click)="columnClick(gridinfo,data)"
										     [ngStyle]="columnStyle(gridinfo,data)">
											{{ data[isdata(gridinfo)] | date : isField(gridinfo, 'time_type') }}
										</div>
									</td>
								</ng-container>
								<td class="num" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isedit(data,gridinfo)==true&&dataisedit(data,gridinfo)==true&&isField(gridinfo,'xtype')=='number'">
									<!-- 纯数字 -->
									<input nz-input *ngIf="isField(gridinfo,'xtype')=='number'"
									       min="-999999999" max="9999999999" step="0.0000001"
									       id="{{isdata(gridinfo)}}"
									       [(ngModel)]="data[isdata(gridinfo)]" type="number"
									       style="text-align:right;"/>
								</td>
								<td class="text" [ngStyle]="linetyle(data,gridinfo)"
								    *ngIf="isedit(data,gridinfo)==true&&dataisedit(data,gridinfo)==true&&isField(gridinfo,'xtype')!='number'">
									<!--输入框无限制-->
									<input nz-input *ngIf="isField(gridinfo,'xtype')=='text'"
									       id="{{isdata(gridinfo)}}" [(ngModel)]="data[isdata(gridinfo)]"/>
									
									<!-- 纯英文 -->
									<input nz-input *ngIf="isField(gridinfo,'xtype')=='english'"
									       onkeyup="value=value.replace(/[^a-zA-Z]/g,'')"
									       id="{{isdata(gridinfo)}}" [(ngModel)]="data[isdata(gridinfo)]"/>
									<!-- 时间选择器 -->
									<nz-date-picker *ngIf="isField(gridinfo,'xtype')=='time'"
									                [nzFormat]="dateFormat" id="{{isdata(gridinfo)}}"
									                [(ngModel)]="data[isdata(gridinfo)]"
									                (ngModelChange)="setchangetime($event,data,isdata(gridinfo))">
									</nz-date-picker>
									
									<!-- 时间选择器 年-->
									<nz-year-picker *ngIf="isField(gridinfo,'xtype')=='time_year'"
									                [nzFormat]="dateFormat" id="{{isdata(gridinfo)}}"
									                [(ngModel)]="data[isdata(gridinfo)]"
									                (ngModelChange)="setchangetime_year($event,data,isdata(gridinfo))">
									</nz-year-picker>
									
									<!-- 时间选择器 年月-->
									<nz-month-picker *ngIf="isField(gridinfo,'xtype')=='time_month'"
									                 [nzFormat]="'yyyy-MM'" id="{{isdata(gridinfo)}}"
									                 [(ngModel)]="data[isdata(gridinfo)]"
									                 (ngModelChange)="setchangetime_month($event,data,isdata(gridinfo))">
									</nz-month-picker>
									<!-- 下拉选择 -->
									<ng-container *ngIf="isField(gridinfo,'xtype')=='lookup'">
										<cms-select-table key="{{gridinfo.attr.key}}"
										                  [condition]="gridinfo.attr.condition"
										                  [hasAll]="isField(gridinfo,'hasAll')"
										                  readfield="{{isField(gridinfo,'readfield')}}"
										                  valuefield="{{isField(gridinfo,'valuefield')}}"
										                  [(ngModel)]="data[isdata(gridinfo)]"
										                  [ngModelOptions]="{standalone: true}"
										                  (ngModelChange)="setChanged($event,data,gridinfo)">
										</cms-select-table>
									</ng-container>
									<!-- combox -->
									<ng-container *ngIf="isField(gridinfo,'xtype')=='combox'">
										<cms-combox key="{{gridinfo.attr.key}}"
										            [(ngModel)]="data[isdata(gridinfo)]"
										            [ngModelOptions]="{standalone: true}"
										            [hasAll]="isField(gridinfo,'hasAll')">
										</cms-combox>
									</ng-container>
									<!-- pop控件 -->
									<ng-container *ngIf="isField(gridinfo,'xtype')=='pop'">
										<div style="float: left;">
											<input type="text" nz-input placeholder="请选择"
											       [(ngModel)]="data[isdata(gridinfo)]"
											       [ngStyle]="poptyle(data,gridinfo)"
											       style="background-color: white;cursor:text;" readonly/>
										</div>
										<div style=" float:left;padding-left: 1px;height: 24px;">
											<button nz-button [nzType]="'primary'" style="float: right;height: 24px;"
											        [disabled]="viewReadOnly" (click)="click(gridinfo,data)">
												<i nz-icon nzType="search"></i>
											</button>
										</div>
										<div style="clear: both;"></div>
									</ng-container>
								</td>
							</ng-container>
						</ng-container>
					</tr>
				</ng-container>
				</tbody>
			</nz-table>
		</div>
	</div>
</ng-container>
<nz-modal
		[(nzVisible)]="isVisible"
		nzTitle="数据维护"
		nzDraggable
		[nzStyle]="{top:'0'}"
		(nzOnCancel)="isVisible = false"
		nzWidth="1600px"
		[nzMaskClosable]="false"
		(nzOnOk)="handleOk()"
>
	<ng-container *nzModalContent>
		<div style="display: flex;align-items: center;margin-bottom: 14px;width:350px">
			<div style="width: 70px;">板块：</div>
			<input nz-input [(ngModel)]="editSystem_cd" placeholder="请输入板块代码（可逗号分割）">
			<button nz-button (click)="onReset()" [nzType]="'primary'" style="margin: 0 auto;margin-left: 5px;">
				<i nz-icon nzType="filter"></i>
				<span>重置</span>
			</button>
		</div>
		<nz-table #editRowTable nzBordered [nzData]="arrY" [nzFrontPagination]="'false'">
			<thead>
			<tr>
				<th nzWidth="60px">序号</th>
				<th>名称</th>
				<th>FormControlName</th>
				<th nzWidth="200px">宽度(0-24)</th>
				<th>备注</th>
				<th>板块</th>
				<th nzWidth="65px">是否必输</th>
				<th nzWidth="65px">选择默认</th>
				<th nzWidth="65px">默认显示</th>
				<th nzWidth="70px">展开/收起</th>
			</tr>
			</thead>
			<tbody>
			<tr *ngFor="let data of editRowTable.data" class="editable-row">
				<td>
					<div class="editable-cell" [hidden]="editId === data.id+'a'" (click)="startEdit(data.id+'a')">
						{{ data.seq }}
					</div>
					<input [hidden]="editId !== data.id+'a'" type="text" nz-input [(ngModel)]="data.seq"
					       (blur)="stopEdit('seq',data)"/>
				</td>
				<td>
					<div class="editable-cell" [hidden]="editId === data.id+'e'" (click)="startEdit(data.id+'e')">
						{{ data.customizedName }}
					</div>
					<input [hidden]="editId !== data.id+'e'" type="text" nz-input [(ngModel)]="data.customizedName"
					       (blur)="stopEdit('customizedName',data)"/>
				</td>
				<td>
					<div class="editable-cell" [hidden]="editId === data.id+'c'" (click)="startEdit(data.id+'c')">
						{{ data.controlname }}
					</div>
					<input [hidden]="editId !== data.id+'c'" type="text" nz-input [(ngModel)]="data.controlname"
					       (blur)="stopEdit('controlname',data)"/>
				</td>
				<td>
					<div class="editable-cell" [hidden]="editId === data.id+'d'" (click)="startEdit(data.id+'d')">
						{{ data.tableWidth }}
					</div>
					<nz-input-number nzMin="0" [hidden]="editId !== data.id+'d'" type="text" nz-input
					                 [(ngModel)]="data.tableWidth"
					                 (blur)="stopEdit('tableWidth',data)"></nz-input-number>
				</td>
				<td>
					<div class="editable-cell" [hidden]="editId === data.id+'b'" (click)="startEdit(data.id+'b')">
						{{ data.remark }}
					</div>
					<input [hidden]="editId !== data.id+'b'" type="text" nz-input [(ngModel)]="data.remark"
					       (blur)="stopEdit('remark',data)"/>
				</td>
				<td>
					<div class="editable-cell">
						{{ data.system_cd }}
					</div>
				</td>
				<td>
					<nz-switch [ngModel]="data.requiredFlag == '1'"
					           (ngModelChange)="onSwitch($event, data, 'requiredFlag')"></nz-switch>
				</td>
				<!-- 选择默认 -->
				<td>
					<nz-switch [ngModel]="data.defaultFlag == '1'"
					           (ngModelChange)="onSwitch($event, data, 'defaultFlag')"></nz-switch>
				</td>
				<!-- 默认显示 -->
				<td>
					<nz-switch [ngModel]="data.displayFlag == '1'"
					           (ngModelChange)="onSwitch($event, data, 'displayFlag')"></nz-switch>
				</td>
				<!-- 展开/收起 -->
				<td>
					<nz-switch [ngModel]="data.expandFlag == 'SZ'" (ngModelChange)="onSwitch2($event, data)"></nz-switch>
				</td>
			</tr>
			</tbody>
		</nz-table>
	</ng-container>
</nz-modal>
