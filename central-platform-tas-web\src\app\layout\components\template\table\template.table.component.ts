import {Component, EventEmitter, forwardRef, Input, Output, ViewEncapsulation} from '@angular/core';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';
import {NzMessageService} from 'ng-zorro-antd/message';
import componentData from '@layout/components/component-data';
import {SetTableComponent} from '@cwfmodal/setTable/setTable.component';
import {CacheDataService} from '@service/cachedata.service';
import {CwfNotifytService} from '@service/cwfnotify.service';
import {ShowLineDataComponent} from '@cwfmodal/showlinedata/showLinedata.component';
import {PublicTableFieldCommonService} from '@service/publicTableFieldcommon.service';
import {GlobalDataService} from '@service/globaldata.service';
import {CwfNewRequest} from '@core/cwfNewRequest';
import {CommonService} from '@service/common.service';
import {CwfBaseService, CwfBusContextService, CwfStore, PageModeEnum} from 'cwf-ng-library';
import {CwfNewOpenParam} from '@core/cwfNewOpenParam';
import {BooleanInput} from '@delon/util/decorator';
import {CwfRestfulService} from '@service/cwfRestful.service';
import {responseInterface} from '../../../../interface/request.interface';

@Component({
    selector: 'template-table',
    templateUrl: './template.table.component.html',
    styles: [`
        .editable-cell {
            position: relative;
            padding: 5px 12px;
            cursor: pointer;
        }

        .editable-row:hover .editable-cell {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 4px 11px;
        }
    `],
    providers: [{
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => TemplateTableComponent),
        multi: true
    }],
    encapsulation: ViewEncapsulation.Emulated
})

/**
 * table组件封装
 *
 */
export class TemplateTableComponent implements ControlValueAccessor {
    constructor(
        // 测试显示
        protected Context: CwfBusContextService,
        private cacheData: CacheDataService,
        private message: NzMessageService,
        protected tablefieldservice: PublicTableFieldCommonService,
        private notifytService: CwfNotifytService,
        private global: GlobalDataService,
        private cwfBaseService: CwfBaseService,
        private commonservice: CommonService,
        private cwfRestfulService: CwfRestfulService
    ) {
        // super(Context);
    }

    timePeriods = [
        'Bronze age',
        'Iron age',
        'Middle ages',
        'Early modern period',
        'Long nineteenth century',
    ];
    listOfData = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park'
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park'
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sidney No. 1 Lake Park'
        }
    ];

    addRowFlag: BooleanInput;
    viewReadOnly: BooleanInput;
    dateFormat: string;
    delRowFlag: BooleanInput;


    // 数据源
    @Input() parentContainer: any;
    @Input() queryFunc: string;
    @Input() children_queryFunc: string;
    @Input() GridArray: any;
    @Input() children_GridArray: any;
    @Input() store: CwfStore; // table界面用到的store
    @Input() children_store: CwfStore; // table界面用到的store
    @Input() page: any; // table页面用在哪个界面 是main或者edit（edit页面会存在可编辑列表情况）
    @Input() children_page: any; // table页面用在哪个界面 是main或者edit（edit页面会存在可编辑列表情况）
    @Input() edit: any; // 当为edit页面时，判断本行记录是否可以编辑的条件（page非edit时，无用）例：true、false、STATUS=='00'
    @Input() children_edit: any; // 当为edit页面时，判断本行记录是否可以编辑的条件（page非edit时，无用）例：true、false、STATUS=='00'
    @Input() system_cd: any;
    @Input() children_system_cd: any;
    @Input() nzScroll: any = {x: '1000px'};
    @Input() nzChildrenScroll: any = {x: '1000px', y: '400px'};
    @Input() muti_select = false; // 多次点击行，行选中状态不会消失
    @Input() Is_select = true; // 点击行时，行选中状态不触发
    @Input() show_button = true; // 是否显示添加、删除按钮
    @Input() checkbox_place = '0'; // 复选框显示位置（传字段名，若传0或者不传都则复选框位置在前，若没对应上字段则不显示复选框）
    @Input() children_checkbox_place = '0'; // 复选框显示位置（传字段名，若传0或者不传都则复选框位置在前，若没对应上字段则不显示复选框）
    @Input() checkbox_ground = '#FFFFFF'; // 默认背景颜色
    @Output() tableRowDblClickEvent = new EventEmitter<any>();
    @Input() linebackground: any; // 行背景颜色 色号或者英文颜色 可以多个以逗号分隔
    @Input() lineStatus: any; // 传true false 或者字段（若是字段则该字段为true 或者 false）可以多个仍然是以逗号分隔（必须是和颜色一一对应）
    @Input() linefontcolor: any; // 行字体颜色 色号或者英文颜色 可以多个以逗号分隔
    @Input() linefontStatus: any; // 传true false 或者字段（若是字段则该字段为true 或者 false）可以多个仍然是以逗号分隔（必须是和颜色一一对应）
    @Input() comp_cd = ''; // 列表代码
    @Input() children_comp_cd = ''; // 子表列表代码
    @Input() page_cd = ''; // 页面代码
    @Input() children_page_cd = ''; // 子表页面代码
    @Input() Arrayname = ''; // 调用页面数组名称
    @Input() children_Arrayname = ''; // 调用页面数组名称
    @Input() showcheckAll = true; // 是否显示列表头复选框
    @Input() children_showcheckAll = true; // 是否显示列表头复选框
    @Input() showcheck = true; // 是否显示列表里复选框
    @Input() children_showcheck = true; // 是否显示列表里复选框
    @Input() nzWidthConfig = []; // 列表宽度数组
    @Input() nzChildrenWidthConfig = []; // 列表宽度数组
    @Input() yxts = ''; // 已选条数
    @Input() children_yxts = ''; // 已选条数
    @Input() revtotal_s = ''; // 费用选择描述
    @Input() children_revtotal_s = ''; // 费用选择描述
    @Input() feetabStatus = false; // 是否费用列表（应收，应付，预收，预付）
    @Input() children_feetabStatus = false; // 是否费用列表（应收，应付，预收，预付）
    @Input() feetype = ''; // 费用类型（01应收，02应付，03预收，04预付）只有feetabStatus为true时开始使用
    @Input() children_feetype = ''; // 费用类型（01应收，02应付，03预收，04预付）只有feetabStatus为true时开始使用
    @Input() loading: boolean = false; // 费用类型（01应收，02应付，03预收，04预付）只有feetabStatus为true时开始使用
    @Input() isShowChildrenThead: boolean = true; // 是否显示子表表头
    @Input() isChildren: boolean = false; // 是否嵌套子表

    @Input() showcheck2 = false; //一页有多个列表多选框分别显示
    @Input() isIndeterminate_X: boolean = false; //为每个table单独使用isIndeterminate
    @Input() isAllDisplayDataChecked_X: boolean = false; //为每个table单独使用isAllDisplayDataChecked

    @Output() returnArrayDataEvent = new EventEmitter<any>();
    @Output() nzPageIndexChangeEvent = new EventEmitter<any>();

    isInit = false;

    // nzPageSizeOptions = [15, 30, 45, 60, 100, 200];//分页条数列表
    nzPageSizeOptions = [15, 30, 45, 60, 100, 200, 300, 400, 500, 1000, 3000, 5000, 10000]; // 分页条数列表
    oldArray = []; // 代码级数组
    isVisible = false; // 修改弹框是否出现
    modalId = '';
    arrY = []; // 数据库原始列表
    editId = ''; // 修改列
    editSystem_cd = ''; // 修改时选中板块
    editSystemList = ['PRO', 'LOGIS', 'NBCS', 'HNGHBK', 'ZYTJBK'];
    errList = [];
    cacheArray = []; // 缓存级数组
    cacherow = {}; // 缓存数据
    filterFn = {}; // 过滤列表用的list集合
    filterdata = {}; // 过滤条件
    filtermessage = ''; // 过滤费用描述
    GridArraySystem = []; // 代码级数组
    PAGECOUNT = '15';
    selectRowId = 0;
    isInitData = false;
    tableDataName = this.global.tableDataName;

    systemObj = {
        PRO: 'PRO',
        LOGIS: 'LOGIS',
        NBCS: 'NBCS',
        ZYTJBK: 'PRO',
        HNGHBK: 'LOGIS',
    };
    rowcount = 0;

    writeValue(obj: any): void {

    }

    registerOnChange(fn: any): void {

    }

    registerOnTouched(fn: any): void {

    }

    setDisabledState?(isDisabled: boolean): void {

    }

    ngOnInit(): void {
        // 获取必要数据
        const url = window.location.href.split('/');
        let modalId = url[url.length - 2] + url[url.length - 1];
        modalId = modalId.split('?')[0];
        this.modalId = modalId;
        this.system_cd = this.commonservice.getSystemVersion(); // 获取维度代码
        // this.editSystem_cd = this.system_cd;
        this.isInitData = localStorage.getItem('isInitData') == 'true' ? true : false;//this.global.isInitData;

        const cookie = this.cwfBaseService.getCookies(this.page_cd);
        if (cookie !== undefined && cookie !== null && cookie !== '') {
            this.store.pageing.LIMIT = cookie * 1;
            this.PAGECOUNT = cookie;
        }
        // 获取费用公共字段
        if (this.feetabStatus && '' !== this.feetype) {
            const sCD = this.system_cd;
            this.GridArray = this.tablefieldservice.getfeetypeArray(this.GridArray, this.feetype, sCD, this.page_cd);
        }
        // 补齐并保存传入数据，筛选重复数据
        this.saveSystemByData();
        this.oldArray = JSON.parse(JSON.stringify(this.GridArray));
        for (let i = 0; i < this.oldArray.length; i++) {
            for (let j = i + 1; j < this.oldArray.length; j++) {
                if (this.oldArray[i].attr.formControlName === this.oldArray[j].attr.formControlName &&
                    this.oldArray[i].attr.key === this.oldArray[j].attr.key) {
                    this.errList.push(`key: ${this.oldArray[i].attr.key}, formControlName: ${this.oldArray[i].attr.formControlName}有重复`);
                }
            }
        }

        // 获取数据库展示数据
        this.onQueryInitZ();

        // 根据板块过滤数据
        // this.findSystemByData();


        // 20230227chensw
        // 主要修改内容:
        // 1 界面的GridArray 存放在GridArrayAll 字段中,由GridArrayAll和缓存中自定义的顺序来动态生成GridArray
        // 2 缓存T_CBC_CONFIGPAGE 中不在存放所有数据 仅仅只存放
        // 获取费用公共字段
        // if (this.feetabStatus && "" !== this.feetype) {
        //   let sCD = this.system_cd;
        //   this.GridArray = this.tablefieldservice.getfeetypeArray(this.GridArray, this.feetype, sCD, this.page_cd);
        // }

        // this.getNzwithconfig();
        // setTimeout(() => {
        //   if (this.Arrayname !== '' && this.parentContainer?.[this.Arrayname] !== undefined) {
        //     this.parentContainer[this.Arrayname] = this.GridArray;
        //   }
        // }, 300);
    }

    // 初始化保存数据
    onSaveData() {
        const me = this;
        const sysData: Array<any> = [
            {
                'system_cd': this.system_cd,
                'gridArray': []
            },
        ];
        this.oldArray.forEach(info => {
            const name = info.attr.formControlName;
            const key = info.attr.key;
            if (!sysData[0].gridArray.find(({attr}) => attr.formControlName === name && attr.key === key)) {
                if (info.attr.system_cd === '*all' || info.attr.system_cd.includes(me.system_cd)) {
                    sysData[0].gridArray.push(info);
                }
            }
        });
        // sysData.forEach(item=>{
        //   for (let i = 0; i < arr.length; i++) {
        //     let oldinfo = JSON.parse(JSON.stringify(arr[i]));
        //     if (oldinfo.attr.system_cd === '*all'){
        //       item.gridArray.push(oldinfo);
        //     }else{
        //       let spl = oldinfo.attr.system_cd.split(',');
        //       for (let s = 0 ; s < spl.length ; s++){
        //         if (item.system_cd === spl[s]){
        //           item.gridArray.push(oldinfo);
        //         }
        //       }
        //     }
        //   }
        // })
        if (this.feetabStatus && !!this.feetype) {
            sysData[0].gridArray = this.tablefieldservice.getfeetypeArray(sysData[0].gridArray, this.feetype, this.system_cd, this.page_cd);
        }
        sysData[0].gridArray.forEach((info, i) => {
            if (info?.attr) {
                // 补齐重要字段
                if (!info.attr['i18n_cd']) {
                    info.attr['i18n_cd'] = componentData[info.attr.key]['i18n_cd'];
                }
                if (!info.attr['formControlName']) {
                    info.attr['formControlName'] = componentData[info.attr.key]['formControlName'] || '*';
                }

                info.attr.remark = this.Context.getTranslateService().geti18nString(info.attr.i18n_cd);
                info.attr.CUSTOMIZED_NAME = info.attr.remark;
                info.attr.display_flag = '1';
                if (info.attr.display !== undefined && !info.attr.display) {// 部分不显示的维护了display_flag=false，大多数需要显示的没有维护 默认=1
                    info.attr.display_flag = '0';
                }
                info.attr.seq = i + 1;
                delete info.attr.system_cd;
            }
        });
        this.onSaveInit(sysData);
    }

    // 保存接口
    onSaveInit(data: any) {
        const obj = {
            modalId: this.modalId,
            data,
            pageCd: this.page_cd || this.modalId,
            pageNm: this.page_cd || this.modalId,
            compCd: this.comp_cd,
            compNm: this.comp_cd,
            compType: 'TABLE',
            tableName: 'cbc_t_column_sys'
        };
        // const request = new CwfNewRequest();
        // request.ISPAGING = true;
        // request.ACTIONID = 'cmsbasecode.PageColumnConfigService';
        // request.OPERATION = 'save';
        // request.CONDITION = obj;
        //
        // request.BU_CD = 'admin';
        // request.BU_NM = 'admin';
        // request.SYSTEM_CD = 'admin';
        // request.SYSTEMVERSION = 'admin';

        return this.cwfRestfulService.post('/PageColumnConfigService/save', obj, 'system-service').then((rps: responseInterface) => {
            return rps.ok;
        });
    }

    onQueryInit() {

        const me = this;
        this.editSystem_cd = ''; // 清空过滤条件
        const obj = {
            systemCd: this.system_cd,
            pageCd: this.page_cd || this.modalId,
            compCd: this.comp_cd || this.modalId,
            compType: 'TABLE',
            tableName: 'cbc_t_column_sys'
        };
        const request = new CwfNewRequest();
        request.ISPAGING = true;
        request.ACTIONID = 'cmsbasecode.PageColumnConfigService';
        request.OPERATION = 'query';
        request.CONDITION = obj;

        request.BU_CD = 'admin';
        request.BU_NM = 'admin';
        request.SYSTEM_CD = 'admin';
        request.SYSTEMVERSION = 'admin';


        return this.cwfRestfulService.post('/PageColumnConfigService/query', obj, 'system-service').then((rps: responseInterface) => {
            if (rps.ok) {
                const arr = rps.data;
                // 20250311 -- liwz -- 修改逻辑，显示所有程序中json串中所有字段（包括不同system_cd）,通过是否显示来控制，显示的字段需存入数据库
                this.arrY = this.commonservice.getArrayForPage(this.oldArray, arr, null, 'T');
            } else {
                alert(rps.msg);
            }
        });
    }

    // 从缓存获取个人配置
    onQueryInitZ() {
        setTimeout(() => {
            const [sys, bu, user] = [
                this.cacheData.T_CBC_COLUMN_SYS?.T,
                this.cacheData.T_CBC_COLUMN_BU?.T,
                this.cacheData.T_CBC_COLUMN_USER?.T,
            ];

            if (user && user[this.page_cd] && user[this.page_cd][this.comp_cd]) { // 用户级
                const sysArr = sys[this.page_cd][this.comp_cd];
                const a = user[this.page_cd][this.comp_cd].map(info => {
                    const sysInfo = sysArr.find(item => item.COLUMN_KEY === info.columnKey && item.CONTROLNAME === info.controlname);
                    return {...sysInfo, ...info};
                });
                if (a?.length) {
                    this.isInit = true;
                    this.setListData(a, true);
                }
            } else if (bu && bu[this.page_cd] && bu[this.page_cd][this.comp_cd]) { // 公司级
                const sysArr = sys[this.page_cd][this.comp_cd];
                const a = bu[this.page_cd][this.comp_cd].map(info => {
                    const sysInfo = sysArr.find(item => item.COLUMN_KEY === info.columnKey && item.CONTROLNAME === info.controlname);
                    return {...sysInfo, ...info};
                });
                if (a?.length) {
                    this.isInit = true;
                    this.setListData(a, true);
                }
            } else if (sys && sys[this.page_cd] && sys[this.page_cd][this.comp_cd]) {
                const a = sys[this.page_cd][this.comp_cd];
                if (a?.length) {
                    this.isInit = true;
                    this.setListData(a);
                }
            }
            if (sys && sys[this.page_cd] && sys[this.page_cd][this.comp_cd]) {
                const a = sys[this.page_cd][this.comp_cd];
                if (a?.length) {
                    this.arrY = a;
                }
            }
        }, 500);

    }

    setListData(arr: any, b = false) {
        const me = this;
        if (arr?.length) {
            const viewData = [];
            arr.forEach(info => {
                const resData = this.oldArray.find((item) =>
                    item.attr.formControlName === info.controlname && item.attr.key === info.columnKey
                );
                if (resData) {
                    resData.attr.key = info.columnKey;
                    resData.attr.formControlName = info.controlname;
                    resData.attr.required = info.requiredFlag === '1';
                    resData.attr.display = info.displayFlag === '1';
                    if (b) {
                        resData.attr.display = true;
                    }
                    resData.attr.nzWidth = info.tableWidth === '0' ? '150' : info.tableWidth; // 如果宽度为0自动设置为150 xuxin 2024.04.10
                    resData.attr.customizedName = info.customizedName;
                    resData.attr.seq = Number(info.seq);
                    viewData.push(resData);
                }
            });
            this.GridArray = viewData;
            this.GridArray.sort((a, b) => a.attr.seq - b.attr.seq);
            // this.filterArray();// 按板块过滤 xuxin 2024.04.09
            this.getNzwithconfig();
            // this.realgrid();
            if (this.Arrayname !== '' && this.parentContainer?.[this.Arrayname] !== undefined) {
                this.returnArrayDataEvent.emit(this.GridArray);
            }
        }
    }

    onEditOpen() {
        this.isVisible = true;
        this.onQueryInit();
    }

    handleOk() {
        this.isVisible = false;
        const sysData = {
            'system_cd': this.system_cd,
            'gridArray': []
        };
        sysData.gridArray = this.arrY.map(info => ({
            attr: {
                required: info.requiredFlag === '1',
                displayFlag: info.displayFlag,
                formControlName: info.controlname,
                remark: info.remark,
                seq: info.seq,
                key: info.columnKey,
                id: info.id,
                formCols: info.formCols,
                nzWidth: info.tableWidth,
                customizedName: info.customizedName,
                defaultFlag: info.defaultFlag// DEFAULT_FLAG = 1时，才往数据库中插入
            },
            event: {}
        }));
        this.onSaveInit(sysData).then(res => {
            if (res) {
                this.message.success('保存成功，刷新界面后生效');
            }
        });
    }

    startEdit(id: string): void {
        this.editId = id;
    }

    stopEdit(column, data): void {
        this.editId = null;
        // 如果是序号离开事件，则需重新排序
        if (column === 'seq') {
            if (data.seq * 1 !== 0) {// 0不排，全放最后面
                /**
                 * 将 this.arrY 按照seq字段进行从小到大排序，并将seq=0的放最后面
                 */
                this.arrY.sort((a, b) => {
                    if (a.seq === 0) {
                        return 1;
                    }
                    if (b.seq === 0) {
                        return -1;
                    }
                    return a.seq - b.seq;
                });
            }
        }
    }

    onSwitch(e: boolean, data: any, name: string) {
        data[name] = e ? '1' : '0';
    }

    onSwitch2(e: boolean, data: any) {
        data.expandFlag = e ? 'SZ' : 'ZK';
    }

    onCheckV(info) {

        // 基类单选
        this.parentContainer.onCheck_S(info, this.store);
        // 判断是否为删除状态
        if (info.deleteFlag === 'Y') {
            this.parentContainer.isDelete = false;
        } else {
            this.parentContainer.isDelete = true;
        }
        // 重载
        if (this.parentContainer.oncheckV !== undefined) {
            this.parentContainer.oncheckV(info);
        }
        // 记录最后一次点击的rowid
        this.selectRowId = info['ROW_ID'] * 1;
    }

    checkAll($event) {
        this.parentContainer.checkAll_S($event, this.store);
        // 重载
        if (this.parentContainer.checkAllV !== undefined) {
            this.parentContainer.checkAllV($event, this.store);
        }
    }

    // 是否显示组件增加不显示字段
    isshowwithundisplay(info, system_cd) {
        let pagesystem_cd = info.attr.system_cd;
        if (pagesystem_cd === undefined || pagesystem_cd === '') {

            pagesystem_cd = componentData[info.attr.key].system_cd;
        }
        if (pagesystem_cd === undefined || pagesystem_cd === '') {
            return false;
        }
        if (pagesystem_cd === '*all' || pagesystem_cd === system_cd) {
            return true;
        } else if (pagesystem_cd.split(',').length > 1) {
            for (let i = 0; i < pagesystem_cd.split(',').length; i++) {
                if (pagesystem_cd.split(',')[i] === system_cd) {
                    return true;
                }
            }
        }
        return false;
    }

    // 新建行
    onAddRate() {
        if (this.verification() === false) {
            return false;
        }
        const addRow = this.createOtherRow();
        addRow.ROW_ID = this.getMaxSEQ();
        addRow['expandChildren'] = false;
        this.store.add(addRow);
    }

    // 新建行时，初始化赋值
    createOtherRow() {
        const row = {ROW_ID: 1,};
        for (let i = 0; i < this.GridArray.length; i++) {
            const row_cd = this.isField(this.GridArray[i], 'formControlName');
            let row_cds = undefined;
            if (this.isField(this.GridArray[i], 'xtype') === 'lookup') {
                row_cds = this.isField(this.GridArray[i], 'valuefield');
            }
            let row_value = this.GridArray[i].attr.initvalue;
            if (row_value === undefined) {
                row_value = null;
            }
            if (row_cds !== undefined) {// valuefield的逗号分隔的字段必须与initvalue逗号分隔的值必须数量一致
                for (let j = 0; j < row_cds.split(',').length; j++) {
                    const r_cd = row_cds.split(',')[j];
                    if (row_value !== null && (row[r_cd] === '' || row[r_cd] == null)) {// 如果已经给该字段赋初值了，则无法再次赋值
                        row[r_cd] = row_value.split(',')[j];
                    } else if (row_value == null) {
                        row[r_cd] = null;
                    }
                }
            } else {
                if (row[row_cd] == null) {// 如果已经给该字段赋初值了，则无法再次赋值
                    row[row_cd] = row_value;
                }
            }
        }
        return row;
    }

    getMaxSEQ() {
        return this.store.getDatas().length + 1;
    }

    // 删除行
    onDeleteRate() {
        const me = this;

        if (me.store.getSelecteds().length > 0) {
            me.store.getSelecteds().forEach(function (itm) {
                me.store.remove(itm);
            });
            this.updateLoatSEQ();
        } else {

            this.message.info('请先选择要操作的记录!');
            // me.showAlert(`${this.geti18n('MSG.FK0018')}`, `${this.geti18n('MSG.FK0019')}`);
        }
    }

    updateLoatSEQ() {
        let m = 1;
        for (let i = 0; i < this.store.getDatas().length; i++) {
            this.store.getDatas()[i]['SEQ_NO'] = m;
            m = m + 1;
        }
    }

    // 20221013 -- liwz -- 增加监听快捷键 ctrl 和 shift 功能，由于逻辑复杂 此功能代码非必要请不要修改，修改后如有问题请回退至20221013版本
    setSelectRow(value, data) {

        if (!this.Is_select) {
            return;
        }
        if (this.page === 'main') {// 主界面单击单行时，默认其他行取消选择，编辑界面选择时则和复选框效果一致
            // 全部置成未选中选中
            if (value.ctrlKey || value.shiftKey) {

            } else {
                this.store.getDatas().map(item => item.SELECTED = false);
            }
            // 20230517 -- liwz -- ctrl  shift  勾选逻辑
            this.ctrlShiftKey(value, data);
            // 判断是否为删除状态
            if (data.DELETE_FLG === 'Y') {
                this.parentContainer.isDelete = false;
            } else {
                this.parentContainer.isDelete = true;
            }
        } else {
            if (value.ctrlKey || value.shiftKey) {
                // 20230517 -- liwz -- ctrl  shift  勾选逻辑
                this.ctrlShiftKey(value, data);
            } else {
                this.onCheckV(data);
            }
        }
        // 重载
        if (this.parentContainer.oncheckV !== undefined) {
            this.parentContainer.oncheckV(data);
        }
    }

    setChanged($event, data, gridinfo) {
        let readfield = gridinfo.attr.readfield;
        if (readfield === undefined || readfield === '') {
            readfield = componentData[gridinfo.attr.key].readfield;
        }
        let valuefield = gridinfo.attr.valuefield;
        if (valuefield === undefined || valuefield === '') {
            valuefield = componentData[gridinfo.attr.key].valuefield;
        }
        const val = {};
        if (readfield.split(',').length >= valuefield.split(',').length && valuefield.split(',').length > 0) {
            for (let i = 0; i < readfield.split(',').length; i++) {
                if (i <= valuefield.split(',').length) {
                    if ($event == null) {
                        data[valuefield.split(',')[i]] = null;
                        val[valuefield.split(',')[i]] = null;
                    } else {
                        data[valuefield.split(',')[i]] = $event[readfield.split(',')[i]];
                        val[valuefield.split(',')[i]] = $event[readfield.split(',')[i]];
                    }
                }
            }
        }
        const func = gridinfo.event.ngModelChange;
        // 20210119 -- liwz -- 当grid中数据被清空时，对应字段值已被赋为空，不再调用页面中方法，此时$event !== null
        if (func !== undefined && func !== '' && $event !== null) {
            this.parentContainer[func]($event, data);
        } else {
            return;
        }
    }

    // 针对lookup或者combox不传formControlName的情况
    isdata(gridinfo) {
        let formControlName = gridinfo.attr.formControlName;
        if (formControlName === '' || formControlName === undefined) {
            formControlName = componentData[gridinfo.attr.key].formControlName;
        }
        return formControlName;
    }

    isnumstyle(gridinfo) {
        const style = {};
        style['width'] = this.isField(gridinfo, 'nzWidth') + 'px';
        style['text-align'] = 'right';
        return style;
    }

    thstyle(gridinfo) {
        const style = {};
        // tslint:disable-next-line:radix
        //style['width'] = parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13 + 'px';
        //style['text-align'] = 'center';
        // style['width'] = parseInt(this.isField(gridinfo, 'nzWidth') + "") + 'px';
        style['text-align'] = 'center';
        return style;
    }

    isnum(data) {
        if (data !== null) {
            data = data + '';
        } else {
            data = '';
        }

        if ('' !== data && undefined !== data && null !== data && data.indexOf('.') === 0) {
            return '0' + data;
        } else {
            return data;
        }
    }

    // 行数据发生变化时，监听数据（只能获取到input标签的数据）
    change(value, data) {
        const id = value.target.id;
        const val = value.target.value;
        for (let i = 0; i < this.GridArray.length; i++) {
            if (this.isField(this.GridArray[i], 'formControlName') === id) {
                const func = this.GridArray[i].event.change;
                if (func !== undefined && func !== '') {
                    this.parentContainer[func](val, data);
                } else {
                    return;
                }
            }
        }
    }

    onKeyup(value, data) {
        const id = value.target.id;
        const val = value.target.value;
        for (let i = 0; i < this.GridArray.length; i++) {
            if (this.isField(this.GridArray[i], 'formControlName') === id) {
                const func = this.GridArray[i].event.keyup;
                if (func !== undefined && func !== '') {
                    this.parentContainer[func](val, data);
                } else {
                    return;
                }
            }
        }
    }

    isedit(data, gridinfo) {
        if (this.edit === '' || this.edit === undefined) {
            this.edit = false;
        }
        if (this.edit === true || this.edit === 'true') {
            return true;
        }
        if (this.edit === false || this.edit === 'false') {
            return false;
        }
        const edit = this.edit.split(',');
        let status = 0;
        for (let i = 0; i < edit.length; i++) {
            if (edit[i].indexOf('==') !== -1) {
                const childedit = edit[i].split('==');
                if (childedit.length !== 2) {
                    return false;
                }
                const key = childedit[0];
                const value = childedit[1];
                if (data[key] === value) {
                    status++;
                } else {
                    return false;
                }
            } else if (edit[i].indexOf('!==') !== -1) {
                const childedit = edit[i].split('!==');
                if (childedit.length !== 2) {
                    return false;
                }
                const key = childedit[0];
                const value = childedit[1];
                if (data[key] !== value) {
                    status++;
                } else {
                    return false;
                }
            }
        }
        if (status > 0) {
            return true;
        }
        return false;
    }

    // 校验新增行时，必输项是否有填值
    verification() {
        const data = this.store.getDatas();
        for (let i = 0; i < data.length; i++) {
            for (let j = 0; j < this.GridArray.length; j++) {
                const cd = this.isField(this.GridArray[j], 'formControlName');
                if (this.isField(this.GridArray[j], 'Required') === true && (data[i][cd] === '' || data[i][cd] == null)) {
                    // let msg = this.Context.getTranslateService().geti18nString(this.GridArray[j].i18n_cd);
                    this.message.info('必输项' + '不能为空！');
                    return false;
                }
            }
        }
    }

    // 获取readfield或者valuefield
    isField(gridinfo, cdstr) {
        let retfield = gridinfo.attr[cdstr];
        if (retfield === false) {
            return false;
        }
        if (retfield === undefined || retfield === '') {
            if (componentData[gridinfo.attr.key]) {
                retfield = componentData[gridinfo.attr.key][cdstr];
            } else {
                return false;
            }
        }
        return retfield;
    }

    // 判断当前字段是否可编辑
    dataisedit(data, gridinfo) {
        let editstatus = this.isField(gridinfo, 'edit');
        if (editstatus === '' || editstatus === undefined) {
            editstatus = false;
        }
        if (editstatus === true || editstatus === 'true') {
            return true;
        }
        if (editstatus === false || editstatus === 'false') {
            return false;
        }
        const edit = editstatus.split(',');
        let status = 0;
        for (let i = 0; i < edit.length; i++) {
            if (edit[i].indexOf('==') !== -1) {
                const childedit = edit[i].split('==');
                if (childedit.length !== 2) {
                    return false;
                }
                const key = childedit[0];
                const value = childedit[1];
                if (data[key] === value) {
                    status++;
                } else {
                    return false;
                }
            } else if (edit[i].indexOf('!==') !== -1) {
                const childedit = edit[i].split('!==');
                if (childedit.length !== 2) {
                    return false;
                }
                const key = childedit[0];
                const value = childedit[1];
                if (data[key] !== value) {
                    status++;
                } else {
                    return false;
                }
            }
        }
        if (status > 0) {
            return true;
        }
        return false;
    }

    setchangetime($event, data, formControlName) {
        if ($event == null) {
            data[formControlName] = null;
        } else {
            data[formControlName] = this.getdata($event);
        }
    }

    getdata(date) {
        const year = date.getFullYear();
        const mouth = date.getMonth() + 1;
        const day = date.getDate();
        let daystr: string;
        let mouthstr: string;
        if (day < 9 && day > 0) {
            daystr = '0' + day;
        } else {
            daystr = day.toString();
        }
        if (mouth < 9 && mouth > 0) {
            mouthstr = '0' + mouth;
        } else {
            mouthstr = mouth.toString();
        }
        return year + '-' + mouthstr + '-' + daystr;
    }

    setchangetime_year($event, data, formControlName) {
        if ($event == null) {
            data[formControlName] = null;
        } else {
            data[formControlName] = this.getdata($event);
        }
    }

    setchangetime_month($event, data, formControlName) {
        if ($event == null) {
            data[formControlName] = null;
        } else {
            data[formControlName] = this.getdata_month($event);
        }
    }

    getdata_month(date) {
        const year = date.getFullYear();
        const mouth = date.getMonth() + 1;
        let mouthstr: string;
        if (mouth <= 9 && mouth > 0) {
            mouthstr = '0' + mouth;
        } else {
            mouthstr = mouth.toString();
        }
        return year + '-' + mouthstr;
    }

    click(info, data) {
        const click = info.event.click;
        if (click !== undefined && click !== '') {
            this.parentContainer[info.event.click](data);
            return;
        }
    }

    onTableRowDblClick($event) {
        this.tableRowDblClickEvent.emit();
    }

    rowstyle(data) {
        const style = {};
        const selectdata = this.store.getSelecteds();
        const alldata = this.store.getDatas();

        for (let i = 0; i < selectdata.length; i++) {
            if (data === selectdata[i].data) {
                style['background-color'] = '#C7EDA8';
            }

        }
        // 行背景颜色设置
        if (this.linebackground !== '' && this.linebackground !== undefined) {
            const colorArray = this.linebackground.split(',');
            const colorStatus = this.lineStatus.split(',');
            if (colorArray.length === colorStatus.length) {
                for (let j = 0; j < colorArray.length; j++) {
                    const showstatus = colorStatus[j];
                    const showcolor = colorArray[j];
                    if (showstatus === 'true' || data[showstatus] === 'true') {
                        style['background-color'] = showcolor;
                        break;
                    }
                }
            }
        }

        // 行字体颜色设置

        if (this.linefontcolor !== '' && this.linefontcolor !== undefined) {
            const colorArray = this.linefontcolor.split(',');
            const colorStatus = this.linefontStatus.split(',');
            if (colorArray.length === colorStatus.length) {
                for (let j = 0; j < colorArray.length; j++) {
                    const showstatus = colorStatus[j];
                    const showcolor = colorArray[j];
                    if (showstatus === 'true' || data[showstatus] === 'true') {
                        style['color'] = showcolor;
                        break;
                    }
                }
            }
        }

        return style;
    }

    linetyle(data, gridinfo) {
        const style = {};
        const selectdata = this.store.getSelecteds();
        for (let i = 0; i < selectdata.length; i++) {
            if (data === selectdata[i].data) {
                return style;
            }
        }
        const background = this.isField(gridinfo, 'background');
        if (background !== undefined && background !== '') {
            style['background-color'] = background;
        }
        return style;
    }

    poptyle(data, gridinfo) {
        const style = {};
        style['width'] = this.isField(gridinfo, 'nzWidth') - 32 + 'px';
        return style;
    }

    ischeckstyle(data) {
        const style = {};
        const selectdata = this.store.getSelecteds();
        for (let i = 0; i < selectdata.length; i++) {
            if (data === selectdata[i].data) {
                return style;
            }
        }
        style['background-color'] = this.checkbox_ground;
        return style;
    }

    // 列表lookup联动相关方法
    onConditionChangeEvent($event, data, gridinfo) {
        const func = gridinfo.event.conditionChangeEvent;
        if (func !== undefined && func !== '') {
            this.parentContainer[func]($event, data);
        } else {
            return;
        }
    }

    columnClick(gridinfo, data) {
        const func = gridinfo.event.columnClick;
        if (func !== undefined && func !== '') {
            this.parentContainer[func](data);
        }
    }

    columnStyle(gridinfo, data) {
        const style = {};
        style['margin'] = '0 auto';
        // 接收columncolor属性（例子：STATUS==0:red,STATUS==1:blue）STATUS为字段名 0,1为该字段的值 :后边的颜色为颜色结果,
        // 也可不拼条件直接写颜色,这种情况不能以逗号分隔 <= >= < > 这四个判断条件需要先确认该字段是否是数字型
        const columncolor = this.isField(gridinfo, 'columncolor');
        if (columncolor) {
            const stylearray = columncolor.split(',');
            for (let i = 0; i < stylearray.length; i++) {
                const styles = stylearray[i].split(':');
                if (styles.length === 2) {
                    const color = styles[1];
                    if (styles[0].indexOf('!==') !== -1) {
                        if (this.iscolor(data, styles[0], '!==')) {
                            style['color'] = color;
                        }
                    } else if (styles[0].indexOf('==') !== -1) {
                        if (this.iscolor(data, styles[0], '==')) {
                            style['color'] = color;
                        }
                    } else if (styles[0].indexOf('>=') !== -1) {
                        if (this.iscolor(data, styles[0], '>=')) {
                            style['color'] = color;
                        }
                    } else if (styles[0].indexOf('<=') !== -1) {
                        if (this.iscolor(data, styles[0], '<=')) {
                            style['color'] = color;
                        }
                    } else if (styles[0].indexOf('>') !== -1) {
                        if (this.iscolor(data, styles[0], '>')) {
                            style['color'] = color;
                        }
                    } else if (styles[0].indexOf('<') !== -1) {
                        if (this.iscolor(data, styles[0], '<')) {
                            style['color'] = color;
                        }
                    }
                } else if (stylearray.length === 1) {
                    style['color'] = styles;
                }

            }
        }


        // 判断columnClick是否配置方法名 若配置了则将该位置光标变成小手
        const func = gridinfo.event.columnClick;
        if (func !== undefined && func !== '') {
            style['cursor'] = 'pointer';
        }
        return style;
    }

    iscolor(data, style0, code,) {

        if (style0.split(code).length === 2) {
            const datacolumn = style0.split(code)[0];
            if (code === '==') {
                if (data[datacolumn] === style0.split(code)[1]) {
                    return true;
                }
            } else if (code === '!==') {
                if (data[datacolumn] !== style0.split(code)[1]) {
                    return true;
                }
            } else if (code === '>=') {
                if (parseFloat(data[datacolumn]) >= parseFloat(style0.split(code)[1])) {
                    return true;
                }
            } else if (code === '<=') {
                if (parseFloat(data[datacolumn]) <= parseFloat(style0.split(code)[1])) {
                    return true;
                }
            } else if (code === '>') {
                if (parseFloat(data[datacolumn]) > parseFloat(style0.split(code)[1])) {
                    return true;
                }
            } else if (code === '<') {
                if (parseFloat(data[datacolumn]) < parseFloat(style0.split(code)[1])) {
                    return true;
                }
            }

        }
        return false;
    }

    // 根据接收到的数组，给列表页的所有列设置列宽度的绝对值，
    getNzwithconfig() {
        let width = 0;
        const nzWidthConfig = [];
        if (this.checkbox_place === '0' && this.showcheck) {
            // nzWidthConfig +=",30px";
            nzWidthConfig.splice(nzWidthConfig.length, 0, '30px');
            width += 30;
        }
        nzWidthConfig.splice(nzWidthConfig.length, 0, '60px');
        width += 60;
        if (this.isInit) {
            for (let i = 0; i < this.GridArray.length; i++) {
                const gridinfo = this.GridArray[i];
                if (gridinfo.attr.display) {
                    if (this.isField(gridinfo, 'formControlName') === this.checkbox_place && this.checkbox_place !== '0') {
                        if (this.showcheck) {
                            nzWidthConfig.splice(nzWidthConfig.length, 0, '30px');
                            width += 30;
                        }
                        // tslint:disable-next-line:radix
                        nzWidthConfig.splice(nzWidthConfig.length, 0, parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13 + 'px');
                        // tslint:disable-next-line:radix
                        width += parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13;
                    } else {
                        // tslint:disable-next-line:radix
                        nzWidthConfig.splice(nzWidthConfig.length, 0, parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13 + 'px');
                        // tslint:disable-next-line:radix
                        width += parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13;
                    }
                }
            }
        } else {
            for (let i = 0; i < this.GridArray.length; i++) {
                const gridinfo = this.GridArray[i];
                if (this.isField(gridinfo, 'formControlName') === this.checkbox_place && this.checkbox_place !== '0') {
                    if (this.showcheck) {
                        nzWidthConfig.splice(nzWidthConfig.length, 0, '30px');
                        width += 30;
                    }
                    // tslint:disable-next-line:radix
                    nzWidthConfig.splice(nzWidthConfig.length, 0, parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13 + 'px');
                    // tslint:disable-next-line:radix
                    width += parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13;
                } else {
                    // tslint:disable-next-line:radix
                    nzWidthConfig.splice(nzWidthConfig.length, 0, parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13 + 'px');
                    // tslint:disable-next-line:radix
                    width += parseInt(this.isField(gridinfo, 'nzWidth') + '') + 13;
                }
            }
        }
        this.nzScroll.x = width + 'px';
        this.nzWidthConfig = nzWidthConfig;
    }

    drop(event: CdkDragDrop<string[]>): void {
        moveItemInArray(this.GridArray, event.previousIndex, event.currentIndex);
        if (this.Arrayname !== '' && this.parentContainer[this.Arrayname] !== undefined) {
            this.parentContainer[this.Arrayname] = this.GridArray;
        }
        this.getNzwithconfig();
    }

    // 拉伸列的方法
    onResize($event, gridinfo) {
        const width = $event.width;
        for (let i = 0; i < this.GridArray.length; i++) {
            const gridinfo1 = this.GridArray[i];
            if (this.isField(gridinfo, 'formControlName') === this.isField(gridinfo1, 'formControlName')) {
                gridinfo1.attr.nzWidth = width;
            }
            if (this.Arrayname !== '' && this.parentContainer[this.Arrayname] !== undefined) {
                this.parentContainer[this.Arrayname] = this.GridArray;
            }
            this.getNzwithconfig();
        }
    }

    setArray() {
        const array = [];
        // for(let i = 0;i<this.GridArray.length;i++){
        //   if(i==0){
        //     continue;
        //   }
        //   array[i-1]=this.GridArray[i];
        // }
        // this.GridArray = array
        this.test();
    }

    test() {
        this.parentContainer.goTop(); // 列表页回到顶部，以保证拖拽位置不变
        // 参数
        const param = new CwfNewOpenParam();
        const userInfo = this.Context.getContext().getUserInfo();
        const IS_ADMIN = userInfo['IS_ADMIN'];
        let type = 'cbc_t_column_user';
        param.CONFIG.title = `显示列设置(个人)`;
        if ('Y' === IS_ADMIN) {
            param.CONFIG.title = `显示列设置(公司)`;
            type = 'cbc_t_column_bu';
        }
        param.CONFIG.width = '60%';
        param.CONFIG.height = '900px';
        param.CONFIG.disableClose = false;
        param.CONFIG.closeOnNavigation = false;
        param.CONFIG.className = 'proStyle';
        param.PAGE_MODE = PageModeEnum.Add;
        param.CONFIG.data = {
            system_cd: this.system_cd,
            modalId: this.modalId,
            page_cd: this.page_cd || this.modalId,
            comp_cd: this.comp_cd,
            type,
            sysData: this.arrY
        };
        return this.notifytService.showDialog(SetTableComponent, param).then(returnDataArray => {
            if (returnDataArray instanceof Array) {
                const returnData = returnDataArray[0];
                if (returnData) {
                    this.completeData(returnData['array']);
                    if (this.Arrayname !== '' && this.parentContainer[this.Arrayname] !== undefined) {
                        this.parentContainer[this.Arrayname] = this.GridArray;
                    }
                    this.getNzwithconfig();
                }
            } else if (returnDataArray instanceof Object) {
                const returnData = returnDataArray;
                this.completeData(returnData['array']);
                if (this.Arrayname !== '' && this.parentContainer[this.Arrayname] !== undefined) {
                    this.parentContainer[this.Arrayname] = this.GridArray;
                }
                this.getNzwithconfig();
            } else {
            }
            if (this.Arrayname !== '' && this.parentContainer[this.Arrayname] !== undefined) {
                this.parentContainer[this.Arrayname] = this.GridArray;
            }
        });
    }

    show(event) {
        if (event.altKey && this.comp_cd !== '' && this.page_cd !== '' && this.cacherow !== '') {
            this.setArray();
        }
    }

    sortfn(gridinfo) {
        const formControlName = this.isField(gridinfo, 'formControlName');
        const xtype = this.isField(gridinfo, 'xtype'); // time,number,
        if ('number' === xtype) {
            return (a: number, b: number) => a[formControlName] - b[formControlName];
        } else {
            return (a: string, b: string) => a[formControlName].localeCompare(b[formControlName]);
        }
    }

    // 排序方法
    sort(sort: { key: string, value: string }): void {
        const sortName = sort.key;
        const sortValue = sort.value;
        if (sortName) {
            if (sortValue === 'ascend') {
                this.store.sort(sortName, 'ASC');
            } else if (sortValue === 'descend') {
                this.store.sort(sortName, 'DESC');
            } else {
                this.store.sort('id', 'ASC');
            }
        }
    }

    // 判断是否显示过滤
    getshowFilter(gridinfo) {
        let showFilter = this.isField(gridinfo, 'showFilter');
        if (undefined === showFilter || true !== showFilter) {
            showFilter = false;
        }
        return showFilter;
    }

    filter(selectlist: string[], formControlName: string): void {
        const value = selectlist.toString();
        this.filterdata[formControlName] = value;
        this.search();
    }

    search(): void {
        this.filtermessage = '';
        const amount = {};
        let filterdataisempty = false;
        for (let i = 0; i < this.store.getDatas().length; i++) {
            const data = this.store.getDatas()[i];
            if (data['SELECTED']) {// 当前行为勾选状态则取消勾选
                data['SELECTED'] = false;
                this.store.getAt(i).commit();
            }
            let status = 'TRUE';
            for (const key of Object.keys(this.filterdata)) {
                const formControlName = key;
                const value = this.filterdata[key];
                if (value !== undefined && value !== '') {
                    filterdataisempty = true;
                    let valstatus = false;
                    const valuelist = value.split(',');
                    for (let j = 0; j < valuelist.length; j++) {
                        if (data[formControlName] === valuelist[j]) {
                            valstatus = true;
                            break;
                        }
                    }
                    if (!valstatus) {
                        status = 'FALSE';
                    }
                }
                if (status === 'FALSE') {
                    break;
                }
            }
            data['VISIBLE'] = status;
        }
    }

    getfilterlist(gridinfo) {
        const formControlName = this.isField(gridinfo, 'formControlName');
        if (this.rowcount !== this.store.getCount() || undefined === this.filterFn[formControlName]) {
            const filterfn = this.isField(gridinfo, 'filterfn'); // 数组中配置filterfn[{text:'',value:''},{text:'',value:''}]
            let list = [];
            let liststr = '';
            const listarr = [];
            for (let i = 0; i < this.store.getDatas().length; i++) {
                const data = this.store.getDatas()[i];
                const value = data[formControlName];
                if (null !== value && undefined !== value && '' !== value) {
                    if (liststr.indexOf('\'' + value + '\'') === -1) {
                        const json = {text: value, value: value};
                        list.push(json);
                    }
                    liststr += ',\'' + value + '\'';
                    listarr.push(value);
                }
            }
            liststr = listarr.sort().toString();
            if (this.filterFn[formControlName] !== undefined
                && this.filterFn[formControlName + 'str'] === undefined) {// 这种情况为数组中有配过滤列表并且已经放入到list中

            } else if (this.filterFn[formControlName] !== undefined
                && this.filterFn[formControlName + 'str'] === liststr) {// 这种情况是已经遍历了过滤列表并且展示列表无变化

            } else if (filterfn !== undefined) {
                list = filterfn;
                this.filterFn[formControlName] = list;
            } else {

                this.filterFn[formControlName + 'str'] = liststr;
                this.filterFn[formControlName] = list;
            }
        }
        this.rowcount = this.store.getCount();
        return this.filterFn[formControlName];
    }

    // 展示当前列数据
    showlinedata(event, data) {
        if (event.altKey && this.comp_cd !== '' && this.cacherow !== '') {
            this.showline(data);
        }
    }

    showline(data) {
        // 参数
        const param = new CwfNewOpenParam();
        param.CONFIG.title = `展示当前列数据`;
        param.CONFIG.width = '600px';
        param.CONFIG.height = '700px';
        param.CONFIG.top = '20px';
        param.CONFIG.disableClose = false;
        param.CONFIG.closeOnNavigation = false;
        param.CONFIG.className = 'proStyle';
        param.PAGE_MODE = PageModeEnum.Add;
        param.CONFIG.data = {
            scop: this,
            data: data,
            GridArray: this.GridArray
        };

        return this.notifytService.showDialog(ShowLineDataComponent, param).then(returnDataArray => {
        });
    }

    // 针对列表拖动没效果以及更改数组配置有时候功能不显示问题
    realgrid() {
        const retArray = [];
        if (this.cacheArray.length > 0) {
            for (let i = 0; i < this.cacheArray.length; i++) {
                const cacheinfo = this.cacheArray[i];
                const formControlName = this.isField(cacheinfo, 'formControlName');
                const newinfo = this.getattr(formControlName);
                const nzWidth = cacheinfo.attr.nzWidth;
                if (undefined === newinfo) {// 这种情况就是数组有改动 新的数组中无该字段了
                    continue;
                }
                if ('' !== nzWidth) {
                    newinfo.attr.nzWidth = nzWidth;
                }
                // if (this.isshowwithundisplay(cacheinfo, this.system_cd)) {
                retArray.push(newinfo);
                // }
            }
            // 原始数组中增加新属性 是否默认展示：Defaultdisplay 默认为true(没有该属性则为true,只有填写false才不显示但是在用户自定义界面不展示列表中出现该字段)
            // for (let i = 0; i < this.GridArrayAll.length; i++) {
            //   let info = this.GridArrayAll[i];
            //   let formControlName = this.isField(info, 'formControlName');
            //   if (this.isshowwithundisplay(info, this.system_cd) && this.isnewinfo(formControlName)) {
            //     let Defaultdisplay = this.isField(info, 'Defaultdisplay');
            //     if (false !== Defaultdisplay) {
            //       retArray.push(info);
            //     }
            //   }
            // }
        } else {
            for (let i = 0; i < this.GridArray.length; i++) {
                const attr = this.GridArray[i];
                // if (this.isshowwithundisplay(attr, this.system_cd)) {
                const Defaultdisplay = this.isField(attr, 'Defaultdisplay');
                if (false !== Defaultdisplay) {
                    retArray.push(attr);
                }
                // }
            }
        }
        this.GridArray = JSON.parse(JSON.stringify(retArray));
    }

    getattr(formControlName) {
        for (let i = 0; i < this.oldArray.length; i++) {
            const info = this.oldArray[i];
            if (formControlName === this.isField(info, 'formControlName')) {
                return info;
            }
        }
    }

    isnewinfo(formControlName) {
        for (let i = 0; i < this.cacheArray.length; i++) {
            const info = this.cacheArray[i];
            if (formControlName === this.isField(info, 'formControlName')) {
                return false;
            }
        }
        return true;
    }

    setCookie($event) {
        // 将页面的分页数量  写入浏览器cookie
        this.cwfBaseService.setCookies(this.page_cd, $event);
        this.store.pageing.LIMIT = $event;
        this.parentContainer.searchData_S(this.store);
    }


    saveSystemByData() {
        // 补充板块
        for (let i = 0; i < this.GridArray.length; i++) {
            const oldinfo = this.GridArray[i];
            let system_cdx = oldinfo.attr['system_cd'];
            if (system_cdx === undefined || system_cdx === '') {
                system_cdx = componentData[oldinfo.attr.key]['system_cd'];
                this.GridArray[i].attr['system_cd'] = system_cdx;
            }

            let formControlNamex = oldinfo.attr['formControlName'];
            if (formControlNamex === undefined || formControlNamex === '') {
                formControlNamex = componentData[oldinfo.attr.key]['formControlName'];
                this.GridArray[i].attr['formControlName'] = formControlNamex;
            }

            let i18n_cdx = oldinfo.attr['i18n_cd'];
            if (i18n_cdx === undefined || i18n_cdx === '') {
                i18n_cdx = componentData[oldinfo.attr.key]['i18n_cd'];
                this.GridArray[i].attr['i18n_cd'] = i18n_cdx;
            }

        }
    }

    findSystemByData() {
        // 清空 GridArray 或者
        const txt = [];
        for (let m = 0; m < this.GridArray.length; m++) {
            const info = this.GridArray[m];
            const system_cdx = info.attr['system_cd'];
            if (this.system_cd === system_cdx || system_cdx === '*all') {
                txt.push(info);
            } else if (system_cdx.split(',').length > 1) {
                for (let i = 0; i < system_cdx.split(',').length; i++) {
                    if (system_cdx.split(',')[i] === this.system_cd) {
                        // display属性 grid列表判断是否显示
                        if (info.attr.undisplay === true) {

                        } else {
                            txt.push(info);
                        }
                    }
                }
            }
        }
        this.GridArray = txt;
    }

    completeData(genArray) {
        const GridArrayx = [];
        for (let m = 0; m < genArray.length; m++) {
            const info = genArray[m];
            const formControlName1 = info.attr['formControlName'];
            const key1 = info.attr['key'];
            for (let i = 0; i < this.oldArray.length; i++) {
                const oldinfo = this.oldArray[i];
                const formControlName2 = oldinfo.attr['formControlName'];
                const key2 = oldinfo.attr['key'];
                if (formControlName1 === formControlName2 && key1 === key2) {
                    oldinfo.attr['nzWidth'] = info.attr['nzWidth'];
                    GridArrayx.push(oldinfo);
                }
            }
        }
        this.GridArray = JSON.parse(JSON.stringify(GridArrayx));
        this.cacheArray = JSON.parse(JSON.stringify(GridArrayx));
    }

    // 20230517 -- liwz -- ctrl shift 按键勾选逻辑，每个组件单独写，如修改需要修改每个组件
    ctrlShiftKey(value, data) {
        if (value.shiftKey) {
            // 20230517 -- liwz -- 循环store，判断有没有勾选，如果没有勾选说明是按住shift后点的第一次，只执行单行勾选
            let count = 0;
            for (let i = 0; i < this.store.getDatas().length; i++) {
                const record = this.store.getDatas()[i];
                const selected = record['SELECTED'];
                if (selected) {
                    count = count + 1;
                }
            }
            if (count === 0) {// 没有勾选
                this.parentContainer.onCheck_S(data, this.store);
                this.selectRowId = data['ROW_ID'] * 1;
            } else {
                const second = data;
                let a = this.selectRowId;
                let b = second['ROW_ID'] * 1;
                // 存在分页问题，处理分页后的结果，例，当每页15条时，第二页的第一条ROW_ID = 16，需变为1,取余数
                const pagesize = this.store.pageSize;
                a = a % pagesize;
                b = b % pagesize;
                if (a === 0) {
                    a = this.store.pageSize;
                }
                if (b === 0) {
                    b = this.store.pageSize;
                }
                // 清空所有勾选
                this.store.getDatas().map(item => item.SELECTED = false);
                // 判断第一次、第二次  勾选 之间关系
                if (a === b) {
                    this.parentContainer.onCheck_S(data, this.store);
                    this.selectRowId = b; // 视为第一次点击
                } else if (a < b) {
                    for (let i = a - 1; i < b; i++) {// 第一行为0行。例如4-10行，第一次点4行时已勾选，本循环开始勾选从5行开始，i<10 勾选到9行结束
                        const datas = this.store.getDatas()[i];
                        this.parentContainer.onCheck_S(datas, this.store);
                    }
                } else if (a > b) {
                    for (let i = b - 1; i < a; i++) {// 第一行未被勾选过，所以i = b-1
                        const datas = this.store.getDatas()[i];
                        this.parentContainer.onCheck_S(datas, this.store);
                    }
                }
            }
        } else {
            // 基类单选
            this.parentContainer.onCheck_S(data, this.store);
            this.selectRowId = data['ROW_ID'] * 1;
        }
    }

    // 过滤列表项 xuxin 2024.04.09
    filterArray() {
        const array = [];
        for (let i = 0; i < this.GridArray.length; i++) {
            const item = this.GridArray[i];
            const system = item['attr']['system_cd'];
            if (system === this.system_cd || system === '*all' || system.includes(this.system_cd)) {
                array.push(item);
            }
        }
        this.GridArray = [];
        this.GridArray = array;
    }

    // 重置按钮
    /**
     * 按照输入的板块过滤
     *
     */
    onReset() {
        // 20250311 -- liwz -- 修改逻辑，显示所有程序中json串中所有字段（包括不同system_cd）,通过是否显示来控制，显示的字段需存入数据库
        this.arrY = this.commonservice.getArrayForPage(this.oldArray, null, this.editSystem_cd, 'T');
    }

    nzPageIndexChange() {
        this.nzPageIndexChangeEvent.emit(this.store);
    }

    // nzSortOrderChange($event, controlName: any) {
    //   if ($event === 'ascend') {
    //     this.store.sort(controlName, 'ASC');
    //  } else if ($event === 'descend') {
    //    this.store.sort(controlName, 'DESC');
    //  } else {
    //     this.store.sort('id', 'ASC');
    //   }
    // }
}
